package org.example.logisticsdelivery.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.example.logisticsdelivery.entity.Courier;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 配送员服务接口
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
public interface CourierService {

    /**
     * 创建配送员
     */
    Courier createCourier(Courier courier);

    /**
     * 更新配送员信息
     */
    Courier updateCourier(Courier courier);

    /**
     * 根据ID查询配送员
     */
    Courier getCourierById(Long id);

    /**
     * 根据配送员编号查询
     */
    Courier getCourierByCode(String courierCode);

    /**
     * 根据用户ID查询配送员
     */
    Courier getCourierByUserId(Long userId);

    /**
     * 根据状态查询配送员列表
     */
    List<Courier> getCouriersByStatus(Integer status);

    /**
     * 根据工作区域查询配送员
     */
    List<Courier> getCouriersByWorkArea(String workArea);

    /**
     * 查询指定范围内的配送员
     */
    List<Courier> getCouriersInRange(BigDecimal minLng, BigDecimal maxLng,
                                     BigDecimal minLat, BigDecimal maxLat);

    /**
     * 查询可用的配送员
     */
    List<Courier> getAvailableCouriers(String workArea);

    /**
     * 更新配送员位置
     */
    boolean updateCourierLocation(Long id, BigDecimal longitude,
                                  BigDecimal latitude, String address);

    /**
     * 更新配送员状态
     */
    boolean updateCourierStatus(Long id, Integer status);

    /**
     * 分页查询配送员
     */
    IPage<Courier> getCouriersPage(Page<Courier> page, Integer status,
                                   String workArea, String courierName);

    /**
     * 统计各状态配送员数量
     */
    Map<String, Long> getStatusStatistics();

    /**
     * 删除配送员
     */
    boolean deleteCourier(Long id);

    /**
     * 自动分配最佳配送员
     */
    Courier assignBestCourier(String workArea, BigDecimal longitude, BigDecimal latitude);

    /**
     * 批量更新配送员状态
     */
    boolean batchUpdateStatus(List<Long> courierIds, Integer status);
}