<template>
  <transition name="toast" appear>
    <div class="notification-toast" :class="toastClass">
      <div class="toast-icon">
        <el-icon :size="20">
          <component :is="iconComponent" />
        </el-icon>
      </div>
      
      <div class="toast-content">
        <h4 class="toast-title" v-if="title">{{ title }}</h4>
        <p class="toast-message">{{ message }}</p>
        <div class="toast-actions" v-if="actions.length > 0">
          <el-button
            v-for="action in actions"
            :key="action.key"
            :type="action.type || 'text'"
            size="small"
            @click="handleAction(action.key)"
          >
            {{ action.label }}
          </el-button>
        </div>
      </div>
      
      <div class="toast-close" v-if="closable" @click="handleClose">
        <el-icon :size="16">
          <Close />
        </el-icon>
      </div>
      
      <!-- 进度条 -->
      <div class="toast-progress" v-if="showProgress">
        <div 
          class="progress-bar" 
          :style="{ width: `${progressPercentage}%` }"
        ></div>
      </div>
    </div>
  </transition>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { 
  CircleCheckFilled, 
  WarningFilled, 
  CircleCloseFilled, 
  InfoFilled,
  Close 
} from '@element-plus/icons-vue'

// Props
interface Action {
  key: string
  label: string
  type?: string
}

interface Props {
  type?: 'success' | 'warning' | 'error' | 'info'
  title?: string
  message: string
  duration?: number
  closable?: boolean
  showProgress?: boolean
  actions?: Action[]
}

const props = withDefaults(defineProps<Props>(), {
  type: 'info',
  duration: 4000,
  closable: true,
  showProgress: false,
  actions: () => []
})

// Emits
const emit = defineEmits<{
  close: []
  action: [key: string]
}>()

// 响应式数据
const progressPercentage = ref(100)
let timer: NodeJS.Timeout | null = null
let progressTimer: NodeJS.Timeout | null = null

// 计算属性
const toastClass = computed(() => {
  return `toast-${props.type}`
})

const iconComponent = computed(() => {
  const icons = {
    success: CircleCheckFilled,
    warning: WarningFilled,
    error: CircleCloseFilled,
    info: InfoFilled
  }
  return icons[props.type]
})

// 方法
const handleClose = () => {
  emit('close')
}

const handleAction = (key: string) => {
  emit('action', key)
}

const startAutoClose = () => {
  if (props.duration > 0) {
    timer = setTimeout(() => {
      handleClose()
    }, props.duration)
    
    if (props.showProgress) {
      const interval = 50
      const step = 100 / (props.duration / interval)
      
      progressTimer = setInterval(() => {
        progressPercentage.value -= step
        if (progressPercentage.value <= 0) {
          progressPercentage.value = 0
          if (progressTimer) {
            clearInterval(progressTimer)
          }
        }
      }, interval)
    }
  }
}

const stopAutoClose = () => {
  if (timer) {
    clearTimeout(timer)
    timer = null
  }
  if (progressTimer) {
    clearInterval(progressTimer)
    progressTimer = null
  }
}

// 生命周期
onMounted(() => {
  startAutoClose()
})

onUnmounted(() => {
  stopAutoClose()
})
</script>

<style scoped>
.notification-toast {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-secondary);
  min-width: 320px;
  max-width: 480px;
  position: relative;
  overflow: hidden;
}

/* ========== 类型样式 ========== */
.toast-success {
  border-left: 4px solid var(--success-color);
}

.toast-success .toast-icon {
  color: var(--success-color);
}

.toast-warning {
  border-left: 4px solid var(--warning-color);
}

.toast-warning .toast-icon {
  color: var(--warning-color);
}

.toast-error {
  border-left: 4px solid var(--error-color);
}

.toast-error .toast-icon {
  color: var(--error-color);
}

.toast-info {
  border-left: 4px solid var(--info-color);
}

.toast-info .toast-icon {
  color: var(--info-color);
}

/* ========== 图标区域 ========== */
.toast-icon {
  flex-shrink: 0;
  margin-top: 2px;
}

/* ========== 内容区域 ========== */
.toast-content {
  flex: 1;
  min-width: 0;
}

.toast-title {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.4;
}

.toast-message {
  margin: 0;
  font-size: var(--font-sm);
  color: var(--text-secondary);
  line-height: 1.5;
  word-break: break-word;
}

.toast-actions {
  margin-top: var(--spacing-md);
  display: flex;
  gap: var(--spacing-sm);
}

/* ========== 关闭按钮 ========== */
.toast-close {
  flex-shrink: 0;
  cursor: pointer;
  color: var(--text-tertiary);
  transition: color var(--duration-normal);
  padding: var(--spacing-xs);
  margin: -var(--spacing-xs) -var(--spacing-xs) 0 0;
  border-radius: var(--radius-sm);
}

.toast-close:hover {
  color: var(--text-secondary);
  background: var(--bg-tertiary);
}

/* ========== 进度条 ========== */
.toast-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--border-light);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: var(--primary-color);
  transition: width 50ms linear;
}

.toast-success .progress-bar {
  background: var(--success-color);
}

.toast-warning .progress-bar {
  background: var(--warning-color);
}

.toast-error .progress-bar {
  background: var(--error-color);
}

.toast-info .progress-bar {
  background: var(--info-color);
}

/* ========== 动画效果 ========== */
.toast-enter-active {
  transition: all var(--duration-normal) ease-out;
}

.toast-leave-active {
  transition: all var(--duration-normal) ease-in;
}

.toast-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.toast-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

/* ========== 响应式设计 ========== */
@media (max-width: 768px) {
  .notification-toast {
    min-width: 280px;
    max-width: calc(100vw - 32px);
    margin: 0 var(--spacing-lg);
  }
  
  .toast-content {
    font-size: var(--font-sm);
  }
  
  .toast-title {
    font-size: var(--font-sm);
  }
  
  .toast-message {
    font-size: var(--font-xs);
  }
  
  .toast-actions {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
}
</style>
