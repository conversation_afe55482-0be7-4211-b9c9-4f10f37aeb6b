<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.example.logisticsorder.mapper.OrderStatusLogMapper">

    <!-- 订单状态日志基础结果映射 -->
    <resultMap id="BaseResultMap" type="org.example.logisticsorder.entity.OrderStatusLog">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="order_id" property="orderId" jdbcType="BIGINT"/>
        <result column="order_number" property="orderNumber" jdbcType="VARCHAR"/>
        <result column="old_status" property="oldStatus" jdbcType="VARCHAR"/>
        <result column="new_status" property="newStatus" jdbcType="VARCHAR"/>
        <result column="operator_id" property="operatorId" jdbcType="BIGINT"/>
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR"/>
        <result column="operator_type" property="operatorType" jdbcType="VARCHAR"/>
        <result column="change_reason" property="changeReason" jdbcType="VARCHAR"/>
        <result column="location" property="location" jdbcType="VARCHAR"/>
        <result column="remarks" property="remarks" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        id, order_id, order_number, old_status, new_status, operator_id, operator_name,
        operator_type, change_reason, location, remarks, create_time, update_time
    </sql>

    <!-- 根据订单ID查询状态变更日志 -->
    <select id="selectByOrderId" parameterType="long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM order_status_log
        WHERE order_id = #{orderId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据订单号查询状态变更日志 -->
    <select id="selectByOrderNumber" parameterType="string" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM order_status_log
        WHERE order_number = #{orderNumber}
        ORDER BY create_time DESC
    </select>

    <!-- 查询指定状态的变更记录 -->
    <select id="selectByStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM order_status_log
        WHERE new_status = #{newStatus}
        <if test="startTime != null and endTime != null">
            AND create_time BETWEEN #{startTime} AND #{endTime}
        </if>
        ORDER BY create_time DESC
    </select>

</mapper>