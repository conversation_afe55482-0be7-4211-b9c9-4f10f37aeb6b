package org.example.logisticsuser.vo;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 登录响应VO
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
public class LoginResponseVO {

    /**
     * JWT令牌
     */
    private String token;

    /**
     * 令牌类型
     */
    private String tokenType = "Bearer";

    /**
     * 过期时间（秒）
     */
    private Long expiresIn;

    /**
     * 用户信息
     */
    private UserInfoVO userInfo;

    /**
     * 刷新令牌
     */
    private String refreshToken;

    /**
     * 登录时间
     */
    private LocalDateTime loginTime;
}
