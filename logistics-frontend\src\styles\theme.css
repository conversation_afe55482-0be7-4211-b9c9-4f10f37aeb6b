/* 物流系统专业主题色彩方案 */

:root {
  /* ========== 主色调 ========== */
  --primary-color: #1890ff;          /* 主蓝色 - 专业、可靠 */
  --primary-light: #40a9ff;          /* 浅蓝色 */
  --primary-lighter: #69c0ff;        /* 更浅蓝色 */
  --primary-dark: #096dd9;           /* 深蓝色 */
  --primary-darker: #0050b3;         /* 更深蓝色 */

  /* ========== 辅助色彩 ========== */
  --secondary-color: #722ed1;        /* 紫色 - 科技感 */
  --accent-color: #13c2c2;           /* 青色 - 活力 */
  --warning-color: #fa8c16;          /* 橙色 - 警告 */
  --success-color: #52c41a;          /* 绿色 - 成功 */
  --error-color: #f5222d;            /* 红色 - 错误 */
  --info-color: #1890ff;             /* 信息色 */

  /* ========== 物流状态专用色彩 ========== */
  --status-pending: #faad14;         /* 待处理 - 金黄色 */
  --status-paid: #1890ff;            /* 已支付 - 蓝色 */
  --status-pickup: #52c41a;          /* 已揽收 - 绿色 */
  --status-transit: #722ed1;         /* 运输中 - 紫色 */
  --status-sorting: #fa8c16;         /* 分拣中 - 橙色 */
  --status-delivering: #13c2c2;      /* 派送中 - 青色 */
  --status-completed: #52c41a;       /* 已完成 - 绿色 */
  --status-cancelled: #f5222d;       /* 已取消 - 红色 */
  --status-exception: #ff4d4f;       /* 异常 - 亮红色 */

  /* ========== 中性色彩 ========== */
  --text-primary: #262626;           /* 主要文字 */
  --text-secondary: #595959;         /* 次要文字 */
  --text-tertiary: #8c8c8c;          /* 第三级文字 */
  --text-disabled: #bfbfbf;          /* 禁用文字 */
  --text-white: #ffffff;             /* 白色文字 */

  /* ========== 背景色彩 ========== */
  --bg-primary: #ffffff;             /* 主背景 */
  --bg-secondary: #fafafa;           /* 次背景 */
  --bg-tertiary: #f5f5f5;           /* 第三背景 */
  --bg-quaternary: #f0f0f0;          /* 第四背景 */
  --bg-disabled: #f5f5f5;            /* 禁用背景 */

  /* ========== 边框色彩 ========== */
  --border-primary: #d9d9d9;         /* 主边框 */
  --border-secondary: #e8e8e8;       /* 次边框 */
  --border-light: #f0f0f0;           /* 浅边框 */
  --border-dark: #bfbfbf;            /* 深边框 */

  /* ========== 阴影效果 ========== */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
  --shadow-xl: 0 16px 48px rgba(0, 0, 0, 0.16);

  /* ========== 圆角尺寸 ========== */
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-xl: 12px;
  --radius-2xl: 16px;

  /* ========== 间距尺寸 ========== */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-2xl: 24px;
  --spacing-3xl: 32px;
  --spacing-4xl: 40px;

  /* ========== 字体尺寸 ========== */
  --font-xs: 12px;
  --font-sm: 14px;
  --font-md: 16px;
  --font-lg: 18px;
  --font-xl: 20px;
  --font-2xl: 24px;
  --font-3xl: 30px;
  --font-4xl: 36px;

  /* ========== 动画时长 ========== */
  --duration-fast: 0.15s;
  --duration-normal: 0.3s;
  --duration-slow: 0.5s;

  /* ========== 层级 ========== */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* ========== 深色主题 ========== */
[data-theme="dark"] {
  --primary-color: #177ddc;
  --bg-primary: #141414;
  --bg-secondary: #1f1f1f;
  --bg-tertiary: #262626;
  --bg-quaternary: #2f2f2f;
  --text-primary: #ffffff;
  --text-secondary: #d9d9d9;
  --text-tertiary: #8c8c8c;
  --border-primary: #434343;
  --border-secondary: #303030;
}

/* ========== 全局样式重置 ========== */
* {
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ========== 通用工具类 ========== */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-white { color: var(--text-white); }

.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-tertiary { background-color: var(--bg-tertiary); }

.border-primary { border-color: var(--border-primary); }
.border-secondary { border-color: var(--border-secondary); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }

/* ========== 状态指示器 ========== */
.status-indicator {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-size: var(--font-xs);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-pending { 
  background-color: rgba(250, 173, 20, 0.1); 
  color: var(--status-pending); 
  border: 1px solid rgba(250, 173, 20, 0.2);
}

.status-paid { 
  background-color: rgba(24, 144, 255, 0.1); 
  color: var(--status-paid); 
  border: 1px solid rgba(24, 144, 255, 0.2);
}

.status-pickup { 
  background-color: rgba(82, 196, 26, 0.1); 
  color: var(--status-pickup); 
  border: 1px solid rgba(82, 196, 26, 0.2);
}

.status-transit { 
  background-color: rgba(114, 46, 209, 0.1); 
  color: var(--status-transit); 
  border: 1px solid rgba(114, 46, 209, 0.2);
}

.status-delivering { 
  background-color: rgba(19, 194, 194, 0.1); 
  color: var(--status-delivering); 
  border: 1px solid rgba(19, 194, 194, 0.2);
}

.status-completed { 
  background-color: rgba(82, 196, 26, 0.1); 
  color: var(--status-completed); 
  border: 1px solid rgba(82, 196, 26, 0.2);
}

.status-cancelled { 
  background-color: rgba(245, 34, 45, 0.1); 
  color: var(--status-cancelled); 
  border: 1px solid rgba(245, 34, 45, 0.2);
}

.status-exception { 
  background-color: rgba(255, 77, 79, 0.1); 
  color: var(--status-exception); 
  border: 1px solid rgba(255, 77, 79, 0.2);
}

/* ========== 动画效果 ========== */
.fade-enter-active, .fade-leave-active {
  transition: opacity var(--duration-normal);
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active, .slide-up-leave-active {
  transition: all var(--duration-normal);
}

.slide-up-enter-from {
  transform: translateY(20px);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(-20px);
  opacity: 0;
}

/* ========== 响应式断点 ========== */
@media (max-width: 768px) {
  :root {
    --spacing-lg: 12px;
    --spacing-xl: 16px;
    --spacing-2xl: 20px;
  }
}

@media (max-width: 480px) {
  :root {
    --font-sm: 12px;
    --font-md: 14px;
    --font-lg: 16px;
  }
}
