<template>
  <div class="order-detail">
    <div class="page-header">
      <el-button @click="$router.back()" :icon="ArrowLeft">返回</el-button>
      <h2>订单详情</h2>
      <div class="header-actions">
        <el-button @click="refreshOrder" :loading="loading" :icon="Refresh">刷新</el-button>
        <el-button v-if="canAssign" type="primary" @click="showAssignDialog = true">
          分配配送员
        </el-button>
        <el-button v-if="canCancel" type="danger" @click="cancelOrder">
          取消订单
        </el-button>
      </div>
    </div>

    <div v-if="order" class="order-content">
      <!-- 订单基本信息 -->
      <el-card class="order-info">
        <template #header>
          <div class="card-header">
            <span>订单信息</span>
            <el-tag :type="getStatusType(order.orderStatus)" size="large">
              {{ getStatusText(order.orderStatus) }}
            </el-tag>
          </div>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-descriptions :column="1" border>
              <el-descriptions-item label="订单号">{{ order.orderNumber }}</el-descriptions-item>
              <el-descriptions-item label="创建时间">{{ formatTime(order.createTime) }}</el-descriptions-item>
              <el-descriptions-item label="更新时间">{{ formatTime(order.updateTime) }}</el-descriptions-item>
              <el-descriptions-item label="预计送达">
                {{ order.estimatedDeliveryTime ? formatTime(order.estimatedDeliveryTime) : '未设置' }}
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
          <el-col :span="12">
            <el-descriptions :column="1" border>
              <el-descriptions-item label="服务类型">{{ getServiceTypeText(order.serviceType) }}</el-descriptions-item>
              <el-descriptions-item label="支付方式">{{ getPaymentMethodText(order.paymentMethod) }}</el-descriptions-item>
              <el-descriptions-item label="支付状态">
                <el-tag :type="order.paymentStatus === 'PAID' ? 'success' : 'warning'">
                  {{ order.paymentStatus === 'PAID' ? '已支付' : '未支付' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="总金额">
                <span class="amount">¥{{ order.totalFee?.toFixed(2) || '0.00' }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row>
      </el-card>

      <!-- 地址信息 -->
      <el-card class="address-info">
        <template #header>
          <span>地址信息</span>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="address-section">
              <h4><el-icon><User /></el-icon> 寄件人信息</h4>
              <div class="address-content">
                <p><strong>{{ order.senderName }}</strong> {{ order.senderPhone }}</p>
                <p>{{ order.senderAddress }}</p>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="address-section">
              <h4><el-icon><LocationInformation /></el-icon> 收件人信息</h4>
              <div class="address-content">
                <p><strong>{{ order.receiverName }}</strong> {{ order.receiverPhone }}</p>
                <p>{{ order.receiverAddress }}</p>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 物品信息 -->
      <el-card class="item-info">
        <template #header>
          <span>物品信息</span>
        </template>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>物品名称：</label>
              <span>{{ order.itemName || '包裹' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>物品重量：</label>
              <span>{{ order.itemWeight || 0 }}kg</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>物品价值：</label>
              <span>¥{{ order.itemValue || 0 }}</span>
            </div>
          </el-col>
        </el-row>

        <div class="info-item" v-if="order.remarks">
          <label>备注：</label>
          <span>{{ order.remarks }}</span>
        </div>
      </el-card>

      <!-- 配送信息 -->
      <el-card class="delivery-info" v-if="order.courierName">
        <template #header>
          <span>配送信息</span>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="courier-info">
              <h4>配送员信息</h4>
              <p><strong>{{ order.courierName }}</strong></p>
              <p>电话：{{ order.courierPhone || '未设置' }}</p>
              <p>状态：
                <el-tag :type="getCourierStatusType(order.courierStatus)">
                  {{ getCourierStatusText(order.courierStatus) }}
                </el-tag>
              </p>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="delivery-times">
              <h4>配送时间</h4>
              <p v-if="order.pickupTime">揽件时间：{{ formatTime(order.pickupTime) }}</p>
              <p v-if="order.deliveryTime">开始配送：{{ formatTime(order.deliveryTime) }}</p>
              <p v-if="order.signTime">签收时间：{{ formatTime(order.signTime) }}</p>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 物流轨迹 -->
      <el-card class="tracking-info">
        <template #header>
          <div class="card-header">
            <span>物流轨迹</span>
            <el-button type="text" @click="refreshTracking">刷新</el-button>
          </div>
        </template>

        <el-timeline v-if="trackingData.length > 0">
          <el-timeline-item
            v-for="(item, index) in trackingData"
            :key="index"
            :timestamp="formatTime(item.createTime)"
            :type="getTimelineType(item.status)"
          >
            <div class="timeline-content">
              <div class="timeline-title">{{ item.description }}</div>
              <div class="timeline-location" v-if="item.location">
                <el-icon><Location /></el-icon>
                {{ item.location }}
              </div>
              <div class="timeline-operator" v-if="item.operatorName">
                操作人：{{ item.operatorName }}
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>

        <el-empty v-else description="暂无物流信息" />
      </el-card>

      <!-- 操作日志 -->
      <el-card class="operation-logs">
        <template #header>
          <span>操作日志</span>
        </template>

        <el-table :data="operationLogs" style="width: 100%">
          <el-table-column prop="operationType" label="操作类型" width="120" />
          <el-table-column prop="operatorName" label="操作人" width="100" />
          <el-table-column prop="description" label="操作描述" />
          <el-table-column prop="createTime" label="操作时间" width="160">
            <template #default="scope">
              {{ formatTime(scope.row.createTime) }}
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 分配配送员对话框 -->
    <el-dialog v-model="showAssignDialog" title="分配配送员" width="600px">
      <el-form :model="assignForm" label-width="100px">
        <el-form-item label="选择配送员" required>
          <el-select v-model="assignForm.courierId" placeholder="请选择配送员" style="width: 100%">
            <el-option
              v-for="courier in availableCouriers"
              :key="courier.id"
              :label="`${courier.name} (${courier.phone})`"
              :value="courier.id"
            >
              <div class="courier-option">
                <span>{{ courier.name }}</span>
                <span class="courier-status">
                  <el-tag :type="getCourierStatusType(courier.status)" size="small">
                    {{ getCourierStatusText(courier.status) }}
                  </el-tag>
                </span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="分配备注">
          <el-input
            v-model="assignForm.notes"
            type="textarea"
            :rows="3"
            placeholder="可选，添加分配备注"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showAssignDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmAssign" :loading="assigning">
          确认分配
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Refresh,
  User,
  LocationInformation,
  Location
} from '@element-plus/icons-vue'
import { orderApi } from '@/api/order'
import { operatorApi } from '@/api/operator'
import { trackingApi } from '@/api/tracking'
import {
  getOrderStatusText,
  getOrderStatusType
} from '@/utils/orderStatus'
import dayjs from 'dayjs'

const route = useRoute()
const router = useRouter()

// 状态
const loading = ref(false)
const assigning = ref(false)
const showAssignDialog = ref(false)
const order = ref<any>(null)
const trackingData = ref<any[]>([])
const operationLogs = ref<any[]>([])
const availableCouriers = ref<any[]>([])

// 表单
const assignForm = reactive({
  courierId: '',
  notes: ''
})

// 计算属性
const canAssign = computed(() => {
  return order.value && ['PENDING', 'PAID'].includes(order.value.orderStatus)
})

const canCancel = computed(() => {
  return order.value && ['PENDING', 'PAID', 'PICKUP_ASSIGNED'].includes(order.value.orderStatus)
})

// 加载订单详情
const loadOrderDetail = async () => {
  const orderId = route.params.id as string
  if (!orderId) return

  loading.value = true
  try {
    const response = await orderApi.getOrderById(Number(orderId))
    if (response.code === 200) {
      order.value = response.data
      await Promise.all([
        loadTrackingData(),
        loadOperationLogs(),
        loadAvailableCouriers()
      ])
    } else {
      ElMessage.error('订单不存在')
      router.back()
    }
  } catch (error) {
    console.error('加载订单详情失败:', error)
    ElMessage.error('加载订单详情失败')
  } finally {
    loading.value = false
  }
}

// 加载物流轨迹
const loadTrackingData = async () => {
  if (!order.value) return

  try {
    const response = await trackingApi.getTrackingByOrderNumber(order.value.orderNumber)
    if (response.code === 200) {
      trackingData.value = response.data.trackingNodes || []
    }
  } catch (error) {
    console.error('加载物流轨迹失败:', error)
    trackingData.value = []
  }
}

// 加载操作日志
const loadOperationLogs = async () => {
  if (!order.value) return

  try {
    const response = await orderApi.getOrderStatusLog(order.value.id)
    if (response.code === 200) {
      operationLogs.value = response.data || []
    }
  } catch (error) {
    console.error('加载操作日志失败:', error)
    operationLogs.value = []
  }
}

// 加载可用配送员
const loadAvailableCouriers = async () => {
  try {
    const response = await operatorApi.getAvailableCouriers()
    if (response.code === 200) {
      availableCouriers.value = response.data || []
    }
  } catch (error) {
    console.error('加载配送员列表失败:', error)
    availableCouriers.value = []
  }
}

// 刷新订单
const refreshOrder = () => {
  loadOrderDetail()
}

// 刷新轨迹
const refreshTracking = () => {
  loadTrackingData()
}

// 分配配送员
const confirmAssign = async () => {
  if (!assignForm.courierId) {
    ElMessage.warning('请选择配送员')
    return
  }

  assigning.value = true
  try {
    await operatorApi.assignOrder(order.value.id, Number(assignForm.courierId), assignForm.notes)
    ElMessage.success('分配成功')
    showAssignDialog.value = false
    await loadOrderDetail()
    
    // 重置表单
    assignForm.courierId = ''
    assignForm.notes = ''
  } catch (error) {
    console.error('分配失败:', error)
    ElMessage.error('分配失败')
  } finally {
    assigning.value = false
  }
}

// 取消订单
const cancelOrder = async () => {
  try {
    await ElMessageBox.confirm('确定要取消这个订单吗？', '确认取消', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await orderApi.cancelOrder(order.value.id)
    ElMessage.success('订单已取消')
    await loadOrderDetail()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消订单失败:', error)
      ElMessage.error('取消订单失败')
    }
  }
}

// 工具函数
const getStatusType = (status: string) => {
  return getOrderStatusType(status)
}

const getStatusText = (status: string) => {
  return getOrderStatusText(status)
}

const getServiceTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    STANDARD: '标准快递',
    EXPRESS: '特快专递',
    URGENT: '加急服务'
  }
  return typeMap[type] || type
}

const getPaymentMethodText = (method: string) => {
  const methodMap: Record<string, string> = {
    ONLINE: '在线支付',
    COD: '货到付款',
    ALIPAY: '支付宝',
    WECHAT: '微信支付'
  }
  return methodMap[method] || method
}

const getCourierStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    ONLINE: 'success',
    BUSY: 'warning',
    OFFLINE: 'info',
    REST: 'info'
  }
  return typeMap[status] || 'info'
}

const getCourierStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    ONLINE: '在线',
    BUSY: '忙碌',
    OFFLINE: '离线',
    REST: '休息'
  }
  return textMap[status] || status
}

const getTimelineType = (status: string) => {
  const typeMap: Record<string, string> = {
    CREATED: 'info',
    PAID: 'success',
    PICKUP_ASSIGNED: 'warning',
    PICKUP: 'primary',
    IN_TRANSIT: 'warning',
    DELIVERED: 'success',
    CANCELLED: 'danger'
  }
  return typeMap[status] || 'info'
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

onMounted(() => {
  loadOrderDetail()
})
</script>

<style scoped>
.order-detail {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
  flex: 1;
  margin-left: 15px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.order-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.address-section {
  padding: 15px;
  background: #f5f7fa;
  border-radius: 8px;
  height: 100%;
}

.address-section h4 {
  margin: 0 0 10px 0;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.address-content p {
  margin: 5px 0;
  color: #666;
}

.info-item {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.info-item label {
  color: #666;
  min-width: 80px;
}

.amount {
  color: #f56c6c;
  font-weight: bold;
  font-size: 16px;
}

.courier-info, .delivery-times {
  padding: 15px;
  background: #f5f7fa;
  border-radius: 8px;
}

.courier-info h4, .delivery-times h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.courier-info p, .delivery-times p {
  margin: 5px 0;
  color: #666;
}

.timeline-content {
  padding-left: 15px;
}

.timeline-title {
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.timeline-location, .timeline-operator {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #666;
  font-size: 14px;
  margin-bottom: 3px;
}

.courier-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.courier-status {
  margin-left: 10px;
}
</style> 