package org.example.logisticsorder.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.example.logisticsorder.dto.CreateOrderDTO;
import org.example.logisticsorder.dto.OrderStatusUpdateDTO;
import org.example.logisticsorder.entity.Order;
import org.example.logisticsorder.vo.OrderVO;
import org.example.logisticsorder.service.OrderLifecycleService;
import org.example.logisticsorder.service.OrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.logistics.common.constants.OrderStatus;

import java.util.Arrays;
import java.util.List;
import java.util.ArrayList;

/**
 * 订单生命周期服务实现类
 * 负责订单全生命周期的业务流程管理
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Slf4j
@Service
public class OrderLifecycleServiceImpl implements OrderLifecycleService {

    @Autowired
    private OrderService orderService;

    @Override
    @Transactional
    public Order createOrderWithLifecycle(CreateOrderDTO createOrderDTO) {
        log.info("开始创建订单生命周期，寄件人: {}", createOrderDTO.getSenderName());

        try {
            // 1. 创建订单 - 这里应该从认证上下文获取用户ID，暂时使用默认值
            Long customerId = 5L; // 对应customer01用户
            OrderVO orderVO = orderService.createOrder(customerId, createOrderDTO);
            if (orderVO == null) {
                throw new RuntimeException("订单创建失败");
            }

            // 2. 初始化物流轨迹（暂时跳过，等物流服务启动后启用）
            log.info("物流轨迹创建已跳过（等待物流服务启动），订单号: {}", orderVO.getOrderNumber());

            // 3. 发送订单创建通知（暂时跳过，等通知服务启动后启用）
            log.info("订单创建通知已跳过（等待通知服务启动），订单号: {}", orderVO.getOrderNumber());

            log.info("订单生命周期创建成功，订单号: {}", orderVO.getOrderNumber());
            return convertToOrder(orderVO);

        } catch (Exception e) {
            log.error("创建订单生命周期失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建订单生命周期失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean processPaymentSuccess(String orderNumber, String paymentMethod) {
        log.info("处理支付成功，订单号: {}, 支付方式: {}", orderNumber, paymentMethod);

        try {
            // 使用OrderService的支付成功方法
            boolean updated = orderService.paymentSuccess(orderNumber, paymentMethod);
            if (!updated) {
                log.error("更新订单支付状态失败，订单号: {}", orderNumber);
                return false;
            }

            // 2. 添加物流轨迹（暂时跳过）
            log.info("物流轨迹更新已跳过（等待物流服务启动），订单号: {}", orderNumber);

            // 3. 发送支付成功通知（暂时跳过）
            log.info("支付成功通知已跳过（等待通知服务启动），订单号: {}", orderNumber);

            log.info("支付成功处理完成，订单号: {}", orderNumber);
            return true;

        } catch (Exception e) {
            log.error("处理支付成功失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean assignPickupCourier(Long orderId, Long courierId, String operatorName) {
        log.info("分配揽件员，订单ID: {}, 配送员ID: {}, 操作员: {}", orderId, courierId, operatorName);

        try {
            // 使用OrderService的分配揽件员方法
            boolean updated = orderService.assignPickupCourier(orderId, courierId);
            if (!updated) {
                log.error("分配揽件员失败，订单ID: {}", orderId);
                return false;
            }

            // 2. 创建配送任务（暂时跳过）
            log.info("配送任务创建已跳过（等待配送服务启动），订单ID: {}", orderId);

            // 3. 更新物流轨迹（暂时跳过）
            log.info("物流轨迹更新已跳过（等待物流服务启动），订单ID: {}", orderId);

            // 4. 发送配送员任务通知（暂时跳过）
            log.info("配送员任务通知已跳过（等待通知服务启动），订单ID: {}", orderId);

            log.info("揽件员分配成功，订单ID: {}", orderId);
            return true;

        } catch (Exception e) {
            log.error("分配揽件员失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean processPickupCompleted(Long orderId, Long courierId, String location, String remarks) {
        log.info("处理揽件完成，订单ID: {}, 配送员ID: {}, 揽件地点: {}", orderId, courierId, location);

        try {
            // 创建状态更新DTO
            OrderStatusUpdateDTO updateDTO = new OrderStatusUpdateDTO();
            updateDTO.setOrderId(orderId);
            updateDTO.setNewStatus(OrderStatus.PICKUP.getCode());
            updateDTO.setOperatorId(courierId);
            updateDTO.setOperatorType("COURIER");
            updateDTO.setChangeReason("揽件完成");
            updateDTO.setRemarks(remarks != null ? remarks : ("揽件地点: " + location));

            boolean updated = orderService.updateOrderStatus(updateDTO);
            if (!updated) {
                log.error("更新订单状态失败，订单ID: {}", orderId);
                return false;
            }

            // 2. 更新物流轨迹（暂时跳过）
            log.info("物流轨迹更新已跳过（等待物流服务启动），订单ID: {}", orderId);

            // 3. 发送揽件完成通知（暂时跳过）
            log.info("揽件完成通知已跳过（等待通知服务启动），订单ID: {}", orderId);

            log.info("揽件完成处理成功，订单ID: {}", orderId);
            return true;

        } catch (Exception e) {
            log.error("处理揽件完成失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean processArriveStation(Long orderId, String stationCode, String operatorName) {
        log.info("处理货物到达网点，订单ID: {}, 网点代码: {}, 操作员: {}", orderId, stationCode, operatorName);

        try {
            OrderStatusUpdateDTO updateDTO = new OrderStatusUpdateDTO();
            updateDTO.setOrderId(orderId);
            updateDTO.setNewStatus(OrderStatus.ARRIVED.getCode());
            updateDTO.setOperatorName(operatorName);
            updateDTO.setOperatorType("STATION");
            updateDTO.setChangeReason("货物到达网点");
            updateDTO.setRemarks("到达网点: " + stationCode);

            boolean updated = orderService.updateOrderStatus(updateDTO);
            if (!updated) {
                log.error("更新订单状态失败，订单ID: {}", orderId);
                return false;
            }

            // 2. 更新物流轨迹（暂时跳过）
            log.info("物流轨迹更新已跳过（等待物流服务启动），订单ID: {}", orderId);

            log.info("货物到达网点处理成功，订单ID: {}", orderId);
            return true;

        } catch (Exception e) {
            log.error("处理货物到达网点失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean processDepartStation(Long orderId, String stationCode, String nextStation, String operatorName) {
        log.info("处理货物离开网点，订单ID: {}, 当前网点: {}, 下一站: {}, 操作员: {}",
                orderId, stationCode, nextStation, operatorName);

        try {
            OrderStatusUpdateDTO updateDTO = new OrderStatusUpdateDTO();
            updateDTO.setOrderId(orderId);
            updateDTO.setNewStatus(OrderStatus.IN_TRANSIT.getCode());
            updateDTO.setOperatorName(operatorName);
            updateDTO.setOperatorType("STATION");
            updateDTO.setChangeReason("货物离开网点");
            updateDTO.setRemarks(String.format("从 %s 发往 %s", stationCode, nextStation));

            boolean updated = orderService.updateOrderStatus(updateDTO);
            if (!updated) {
                log.error("更新订单状态失败，订单ID: {}", orderId);
                return false;
            }

            // 2. 更新物流轨迹（暂时跳过）
            log.info("物流轨迹更新已跳过（等待物流服务启动），订单ID: {}", orderId);

            log.info("货物离开网点处理成功，订单ID: {}", orderId);
            return true;

        } catch (Exception e) {
            log.error("处理货物离开网点失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean assignDeliveryCourier(Long orderId, Long courierId, String operatorName) {
        log.info("分配派送员，订单ID: {}, 配送员ID: {}, 操作员: {}", orderId, courierId, operatorName);

        try {
            // 使用OrderService的分配配送员方法
            boolean updated = orderService.assignDeliveryCourier(orderId, courierId);
            if (!updated) {
                log.error("分配派送员失败，订单ID: {}", orderId);
                return false;
            }

            // 2. 创建配送任务（暂时跳过）
            log.info("配送任务创建已跳过（等待配送服务启动），订单ID: {}", orderId);

            // 3. 更新物流轨迹（暂时跳过）
            log.info("物流轨迹更新已跳过（等待物流服务启动），订单ID: {}", orderId);

            // 4. 发送配送员任务通知（暂时跳过）
            log.info("配送员任务通知已跳过（等待通知服务启动），订单ID: {}", orderId);

            log.info("派送员分配成功，订单ID: {}", orderId);
            return true;

        } catch (Exception e) {
            log.error("分配派送员失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean processDeliveryCompleted(Long orderId, Long courierId, String signProof, String remarks) {
        log.info("处理配送完成，订单ID: {}, 配送员ID: {}, 签收凭证: {}", orderId, courierId, signProof);

        try {
            // 使用OrderService的签收方法
            boolean updated = orderService.signOrder(orderId, signProof);
            if (!updated) {
                log.error("更新订单状态失败，订单ID: {}", orderId);
                return false;
            }

            // 2. 更新物流轨迹（暂时跳过）
            log.info("物流轨迹更新已跳过（等待物流服务启动），订单ID: {}", orderId);

            // 3. 发送配送完成通知（暂时跳过）
            log.info("配送完成通知已跳过（等待通知服务启动），订单ID: {}", orderId);

            log.info("配送完成处理成功，订单ID: {}", orderId);
            return true;

        } catch (Exception e) {
            log.error("处理配送完成失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean processException(Long orderId, String exceptionType, String description, String operatorName) {
        log.info("处理订单异常，订单ID: {}, 异常类型: {}, 描述: {}, 操作员: {}",
                orderId, exceptionType, description, operatorName);

        try {
            OrderStatusUpdateDTO updateDTO = new OrderStatusUpdateDTO();
            updateDTO.setOrderId(orderId);
            updateDTO.setNewStatus(OrderStatus.EXCEPTION.getCode());
            updateDTO.setOperatorName(operatorName);
            updateDTO.setOperatorType("SYSTEM");
            updateDTO.setChangeReason("订单异常: " + exceptionType);
            updateDTO.setRemarks(description);

            boolean updated = orderService.updateOrderStatus(updateDTO);
            if (!updated) {
                log.error("更新订单异常状态失败，订单ID: {}", orderId);
                return false;
            }

            // 2. 更新物流轨迹（暂时跳过）
            log.info("物流轨迹更新已跳过（等待物流服务启动），订单ID: {}", orderId);

            // 3. 发送异常通知（暂时跳过）
            log.info("异常通知已跳过（等待通知服务启动），订单ID: {}", orderId);

            log.info("订单异常处理成功，订单ID: {}", orderId);
            return true;

        } catch (Exception e) {
            log.error("处理订单异常失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean processCancelOrder(Long orderId, String cancelReason, String operatorName) {
        log.info("处理取消订单，订单ID: {}, 取消原因: {}, 操作员: {}", orderId, cancelReason, operatorName);

        try {
            OrderStatusUpdateDTO updateDTO = new OrderStatusUpdateDTO();
            updateDTO.setOrderId(orderId);
            updateDTO.setNewStatus(OrderStatus.CANCELLED.getCode());
            updateDTO.setOperatorName(operatorName);
            updateDTO.setOperatorType("SYSTEM");
            updateDTO.setChangeReason("订单取消");
            updateDTO.setRemarks(cancelReason);

            boolean updated = orderService.updateOrderStatus(updateDTO);
            if (!updated) {
                log.error("取消订单失败，订单ID: {}", orderId);
                return false;
            }

            // 2. 更新物流轨迹（暂时跳过）
            log.info("物流轨迹更新已跳过（等待物流服务启动），订单ID: {}", orderId);

            // 3. 发送取消通知（暂时跳过）
            log.info("取消通知已跳过（等待通知服务启动），订单ID: {}", orderId);

            log.info("订单取消处理成功，订单ID: {}", orderId);
            return true;

        } catch (Exception e) {
            log.error("处理订单取消失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<String> getAvailableActions(String orderStatus) {
        List<String> actions = new ArrayList<>();

        if (orderStatus == null) {
            return actions;
        }

        try {
            OrderStatus status = OrderStatus.fromCode(orderStatus.toUpperCase());

            switch (status) {
                case PENDING:
                    actions.addAll(Arrays.asList("PAY", "CANCEL"));
                    break;

                case PAID:
                    actions.addAll(Arrays.asList("ASSIGN_PICKUP", "CANCEL"));
                    break;

                case PICKUP_ASSIGNED:
                    actions.addAll(Arrays.asList("PICKUP_COMPLETE", "REASSIGN_PICKUP", "CANCEL"));
                    break;

                case PICKUP:
                    actions.addAll(Arrays.asList("DEPART_STATION", "REPORT_EXCEPTION"));
                    break;

                case IN_TRANSIT:
                    actions.addAll(Arrays.asList("ARRIVE_STATION", "REPORT_EXCEPTION"));
                    break;

                case ARRIVED:
                    actions.addAll(Arrays.asList("ASSIGN_DELIVERY", "DEPART_STATION", "REPORT_EXCEPTION"));
                    break;

                case OUT_FOR_DELIVERY:
                    actions.addAll(Arrays.asList("DELIVERY_COMPLETE", "DELIVERY_FAILED", "REPORT_EXCEPTION"));
                    break;

                case EXCEPTION:
                    actions.addAll(Arrays.asList("RESOLVE_EXCEPTION", "CANCEL"));
                    break;

                default:
                    // 最终状态无可用操作
                    break;
            }

        } catch (Exception e) {
            log.warn("获取可用操作失败，订单状态: {}", orderStatus, e);
        }

        return actions;
    }

    @Override
    public boolean validateStatusTransition(String currentStatus, String targetStatus) {
        if (currentStatus == null || targetStatus == null) {
            return false;
        }

        try {
            OrderStatus current = OrderStatus.fromCode(currentStatus.toUpperCase());
            OrderStatus target = OrderStatus.fromCode(targetStatus.toUpperCase());

            // 相同状态不需要转换
            if (current == target) {
                return true;
            }

            // 最终状态不能转换
            if (current.isFinalStatus()) {
                return false;
            }

            // 定义状态转换规则
            switch (current) {
                case PENDING:
                    return target == OrderStatus.PAID || target == OrderStatus.CANCELLED;

                case PAID:
                    return target == OrderStatus.PICKUP_ASSIGNED || target == OrderStatus.CANCELLED;

                case PICKUP_ASSIGNED:
                    return target == OrderStatus.PICKUP || target == OrderStatus.CANCELLED || target == OrderStatus.EXCEPTION;

                case PICKUP:
                    return target == OrderStatus.IN_TRANSIT || target == OrderStatus.EXCEPTION;

                case IN_TRANSIT:
                    return target == OrderStatus.ARRIVED || target == OrderStatus.EXCEPTION;

                case ARRIVED:
                    return target == OrderStatus.OUT_FOR_DELIVERY || target == OrderStatus.IN_TRANSIT || target == OrderStatus.EXCEPTION;

                case OUT_FOR_DELIVERY:
                    return target == OrderStatus.DELIVERED || target == OrderStatus.SIGNED ||
                            target == OrderStatus.REJECTED || target == OrderStatus.EXCEPTION;

                case EXCEPTION:
                    // 异常状态可以转换到任何前序状态或取消
                    return !target.isFinalStatus() || target == OrderStatus.CANCELLED;

                default:
                    return false;
            }

        } catch (Exception e) {
            log.warn("验证状态转换失败，当前状态: {}, 目标状态: {}", currentStatus, targetStatus, e);
            return false;
        }
    }

    /**
     * 将OrderVO转换为Order实体
     */
    private Order convertToOrder(OrderVO orderVO) {
        if (orderVO == null) {
            return null;
        }
        Order order = new Order();
        order.setId(orderVO.getId());
        order.setOrderNumber(orderVO.getOrderNumber());
        order.setSenderName(orderVO.getSenderName());
        order.setSenderPhone(orderVO.getSenderPhone());
        order.setSenderAddress(orderVO.getSenderAddress());
        order.setReceiverName(orderVO.getReceiverName());
        order.setReceiverPhone(orderVO.getReceiverPhone());
        order.setReceiverAddress(orderVO.getReceiverAddress());
        order.setItemName(orderVO.getItemName());
        order.setItemWeight(orderVO.getItemWeight());
        order.setItemValue(orderVO.getItemValue());
        order.setServiceType(orderVO.getServiceType());
        order.setTotalFee(orderVO.getTotalFee());
        order.setPaymentStatus(orderVO.getPaymentStatus());
        order.setOrderStatus(orderVO.getOrderStatus());
        order.setRemarks(orderVO.getRemarks());
        order.setCreateTime(orderVO.getCreateTime());
        order.setUpdateTime(orderVO.getUpdateTime());
        return order;
    }
}