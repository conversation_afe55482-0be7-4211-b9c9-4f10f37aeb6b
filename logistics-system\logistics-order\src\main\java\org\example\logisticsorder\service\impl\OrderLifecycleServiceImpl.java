package org.example.logisticsorder.service.impl;

import com.logistics.common.constants.OrderStatus;
import lombok.extern.slf4j.Slf4j;
import org.example.logisticsorder.dto.CreateOrderDTO;
import org.example.logisticsorder.dto.OrderStatusUpdateDTO;
import org.example.logisticsorder.entity.Order;
import org.example.logisticsorder.vo.OrderVO;
import org.example.logisticsorder.service.OrderLifecycleService;
import org.example.logisticsorder.service.OrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 订单生命周期管理服务实现
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Slf4j
@Service
public class OrderLifecycleServiceImpl implements OrderLifecycleService {

    @Autowired
    private OrderService orderService;

    @Autowired
    private LogisticsServiceClient logisticsServiceClient;

    @Autowired
    private NotificationServiceClient notificationServiceClient;

    @Autowired
    private DeliveryServiceClient deliveryServiceClient;

    @Override
    @Transactional
    public Order createOrderWithLifecycle(CreateOrderDTO createOrderDTO) {
        log.info("开始创建订单生命周期，订单信息: {}", createOrderDTO);

        try {
            // 1. 创建订单
            // 这里暂时使用默认的客户ID，实际应该从认证上下文获取
            Long customerId = 5L; // 对应customer01用户
            OrderVO orderVO = orderService.createOrder(customerId, createOrderDTO);
            if (orderVO == null) {
                throw new RuntimeException("订单创建失败");
            }

            // 2. 初始化物流轨迹
            Map<String, Object> trackingData = new HashMap<>();
            trackingData.put("orderNumber", orderVO.getOrderNumber());
            trackingData.put("orderId", orderVO.getId());
            trackingData.put("status", "ORDER_CREATED");
            trackingData.put("description", "订单已创建，等待支付");
            trackingData.put("operatorName", "系统");
            trackingData.put("operatorType", "SYSTEM");

            boolean trackingCreated = logisticsServiceClient.createTracking(trackingData);
            if (!trackingCreated) {
                log.warn("物流轨迹创建失败，订单号: {}", orderVO.getOrderNumber());
            }

            // 3. 发送订单创建通知
            try {
                notificationServiceClient.sendOrderCreatedNotification(
                        orderVO.getOrderNumber(),
                        createOrderDTO.getSenderName(),
                        createOrderDTO.getSenderPhone(),
                        orderVO.getTotalFee().toString()
                );
            } catch (Exception e) {
                log.warn("发送订单创建通知失败: {}", e.getMessage());
            }

            log.info("订单生命周期创建成功，订单号: {}", orderVO.getOrderNumber());
            // 注意：这里返回类型需要调整，因为方法返回Order但我们有OrderVO
            return convertToOrder(orderVO);

        } catch (Exception e) {
            log.error("创建订单生命周期失败", e);
            throw new RuntimeException("创建订单失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean processPaymentSuccess(String orderNumber, String paymentMethod) {
        log.info("处理支付成功，订单号: {}", orderNumber);

        try {
            // 1. 更新订单支付状态
            boolean paymentUpdated = orderService.paymentSuccess(orderNumber, paymentMethod);
            if (!paymentUpdated) {
                return false;
            }

            // 2. 更新物流轨迹
            Map<String, Object> trackingData = new HashMap<>();
            trackingData.put("orderNumber", orderNumber);
            trackingData.put("status", "PAYMENT_SUCCESS");
            trackingData.put("description", "支付成功，等待揽件");
            trackingData.put("operatorName", "系统");
            trackingData.put("operatorType", "SYSTEM");

            logisticsServiceClient.addTrackingNode(trackingData);

            // 3. 发送支付成功通知
            OrderVO orderVO = orderService.getOrderByNumber(orderNumber);
            if (orderVO != null) {
                notificationServiceClient.sendPaymentSuccessNotification(
                        orderNumber,
                        orderVO.getSenderName(),
                        orderVO.getSenderPhone(),
                        orderVO.getTotalFee().toString()
                );
            }

            return true;

        } catch (Exception e) {
            log.error("处理支付成功失败，订单号: {}", orderNumber, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean assignPickupCourier(Long orderId, Long courierId, String operatorName) {
        log.info("分配揽件员，订单ID: {}, 配送员ID: {}", orderId, courierId);

        try {
            // 1. 更新订单状态
            OrderStatusUpdateDTO updateDTO = new OrderStatusUpdateDTO();
            updateDTO.setOrderId(orderId);
            updateDTO.setNewStatus(OrderStatus.PICKUP_ASSIGNED.getCode());
            updateDTO.setOperatorType("OPERATOR");
            updateDTO.setChangeReason("分配揽件员");
            updateDTO.setRemarks("操作员: " + operatorName);

            boolean statusUpdated = orderService.updateOrderStatus(updateDTO);
            if (!statusUpdated) {
                return false;
            }

            // 2. 创建配送任务
            OrderVO orderVO = orderService.getOrderById(orderId);
            Map<String, Object> taskData = new HashMap<>();
            taskData.put("orderNumber", orderVO.getOrderNumber());
            taskData.put("orderId", orderId);
            taskData.put("courierId", courierId);
            taskData.put("taskType", "PICKUP");
            taskData.put("priority", 2);

            boolean taskCreated = deliveryServiceClient.createDeliveryTask(taskData);
            if (!taskCreated) {
                log.warn("创建配送任务失败，订单ID: {}", orderId);
            }

            // 3. 更新物流轨迹
            Map<String, Object> trackingData = new HashMap<>();
            trackingData.put("orderNumber", orderVO.getOrderNumber());
            trackingData.put("status", "PICKUP_ASSIGNED");
            trackingData.put("description", "已分配揽件员，即将上门取件");
            trackingData.put("operatorName", operatorName);
            trackingData.put("operatorType", "OPERATOR");

            logisticsServiceClient.addTrackingNode(trackingData);

            // 4. 发送配送员任务通知
            notificationServiceClient.sendCourierTaskNotification(
                    orderVO.getOrderNumber(),
                    courierId,
                    "PICKUP"
            );

            return true;

        } catch (Exception e) {
            log.error("分配揽件员失败，订单ID: {}", orderId, e);
            return false;
        }
    }

    @Override
    public List<String> getAvailableActions(String orderStatus) {
        List<String> actions = new ArrayList<>();
        
        switch (orderStatus) {
            case "PENDING":
                actions.add("支付订单");
                actions.add("取消订单");
                break;
            case "PAID":
                actions.add("分配揽件员");
                actions.add("取消订单");
                break;
            case "PICKUP_ASSIGNED":
                actions.add("开始揽件");
                actions.add("重新分配");
                break;
            case "PICKUP":
                actions.add("到达网点");
                actions.add("异常处理");
                break;
            case "SORTING":
                actions.add("分拣完成");
                actions.add("异常处理");
                break;
            case "DISPATCHING":
                actions.add("发车完成");
                break;
            case "ARRIVED":
                actions.add("分配派送员");
                break;
            case "DELIVERING":
                actions.add("配送完成");
                actions.add("异常处理");
                break;
            default:
                break;
        }
        
        return actions;
    }

    @Override
    public boolean validateStatusTransition(String currentStatus, String targetStatus) {
        // 定义合法的状态流转
        Map<String, List<String>> validTransitions = new HashMap<>();
        validTransitions.put("PENDING", Arrays.asList("PAID", "CANCELLED"));
        validTransitions.put("PAID", Arrays.asList("PICKUP_ASSIGNED", "CANCELLED"));
        validTransitions.put("PICKUP_ASSIGNED", Arrays.asList("PICKUP", "CANCELLED"));
        validTransitions.put("PICKUP", Arrays.asList("SORTING", "TRANSIT", "EXCEPTION"));
        validTransitions.put("SORTING", Arrays.asList("DISPATCHING", "EXCEPTION"));
        validTransitions.put("DISPATCHING", Arrays.asList("TRANSFERRING", "ARRIVED", "EXCEPTION"));
        validTransitions.put("TRANSFERRING", Arrays.asList("ARRIVED", "EXCEPTION"));
        validTransitions.put("ARRIVED", Arrays.asList("DELIVERING", "EXCEPTION"));
        validTransitions.put("DELIVERING", Arrays.asList("SIGNED", "DELIVERED", "EXCEPTION", "REJECTED"));
        
        List<String> allowedTargets = validTransitions.get(currentStatus);
        return allowedTargets != null && allowedTargets.contains(targetStatus);
    }

    /**
     * 将OrderVO转换为Order实体
     */
    private Order convertToOrder(OrderVO orderVO) {
        if (orderVO == null) {
            return null;
        }
        Order order = new Order();
        order.setId(orderVO.getId());
        order.setOrderNumber(orderVO.getOrderNumber());
        order.setSenderName(orderVO.getSenderName());
        order.setSenderPhone(orderVO.getSenderPhone());
        order.setSenderAddress(orderVO.getSenderAddress());
        order.setReceiverName(orderVO.getReceiverName());
        order.setReceiverPhone(orderVO.getReceiverPhone());
        order.setReceiverAddress(orderVO.getReceiverAddress());
        order.setItemName(orderVO.getItemName());
        order.setItemWeight(orderVO.getItemWeight());
        order.setItemValue(orderVO.getItemValue());
        order.setServiceType(orderVO.getServiceType());
        order.setTotalFee(orderVO.getTotalFee());
        order.setPaymentStatus(orderVO.getPaymentStatus());
        order.setOrderStatus(orderVO.getOrderStatus());
        order.setRemarks(orderVO.getRemarks());
        order.setCreateTime(orderVO.getCreateTime());
        order.setUpdateTime(orderVO.getUpdateTime());
        return order;
    }

    // Feign客户端接口定义
    @FeignClient(name = "logistics-logistics", path = "/logistics")
    interface LogisticsServiceClient {
        @PostMapping("/tracking/create")
        boolean createTracking(@RequestBody Map<String, Object> trackingData);
        
        @PostMapping("/tracking/add-node")
        boolean addTrackingNode(@RequestBody Map<String, Object> nodeData);
    }

    @FeignClient(name = "logistics-notification", path = "/notification")
    interface NotificationServiceClient {
        @PostMapping("/email/order-created")
        boolean sendOrderCreatedNotification(@RequestParam String orderNumber,
                                           @RequestParam String customerName,
                                           @RequestParam String customerEmail,
                                           @RequestParam String totalFee);
        
        @PostMapping("/email/payment-success")
        boolean sendPaymentSuccessNotification(@RequestParam String orderNumber,
                                             @RequestParam String customerName,
                                             @RequestParam String customerEmail,
                                             @RequestParam String paymentAmount);
        
        @PostMapping("/email/courier-task")
        boolean sendCourierTaskNotification(@RequestParam String orderNumber,
                                          @RequestParam Long courierId,
                                          @RequestParam String taskType);
    }

    @FeignClient(name = "logistics-delivery", path = "/delivery")
    interface DeliveryServiceClient {
        @PostMapping("/task/create")
        boolean createDeliveryTask(@RequestBody Map<String, Object> taskData);
    }

    // 其他方法的实现将在下一部分添加...
    @Override
    public boolean processPickupCompleted(Long orderId, Long courierId, String location, String remarks) {
        // TODO: 实现揽件完成处理
        return false;
    }

    @Override
    public boolean processArriveStation(Long orderId, String stationCode, String operatorName) {
        // TODO: 实现到达网点处理
        return false;
    }

    @Override
    public boolean processDepartStation(Long orderId, String stationCode, String nextStation, String operatorName) {
        // TODO: 实现离开网点处理
        return false;
    }

    @Override
    public boolean assignDeliveryCourier(Long orderId, Long courierId, String operatorName) {
        // TODO: 实现分配派送员
        return false;
    }

    @Override
    public boolean processDeliveryCompleted(Long orderId, Long courierId, String signProof, String remarks) {
        // TODO: 实现配送完成处理
        return false;
    }

    @Override
    public boolean processException(Long orderId, String exceptionType, String description, String operatorName) {
        // TODO: 实现异常处理
        return false;
    }

    @Override
    public boolean processCancelOrder(Long orderId, String cancelReason, String operatorName) {
        // TODO: 实现取消订单处理
        return false;
    }
}
