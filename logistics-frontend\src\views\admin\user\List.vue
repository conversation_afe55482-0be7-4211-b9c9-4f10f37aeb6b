<template>
  <div class="admin-user-list">
    <div class="page-header">
      <h2>用户管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          新增用户
        </el-button>
        <el-button @click="exportUsers">
          <el-icon><Download /></el-icon>
          导出用户
        </el-button>
        <el-button @click="refreshList">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="用户名">
          <el-input v-model="searchForm.username" placeholder="输入用户名" style="width: 200px" />
        </el-form-item>

        <el-form-item label="手机号">
          <el-input v-model="searchForm.phone" placeholder="输入手机号" style="width: 200px" />
        </el-form-item>

        <el-form-item label="邮箱">
          <el-input v-model="searchForm.email" placeholder="输入邮箱" style="width: 200px" />
        </el-form-item>

        <el-form-item label="用户类型">
          <el-select v-model="searchForm.userType" placeholder="全部类型" clearable>
            <el-option label="普通用户" value="CUSTOMER" />
            <el-option label="操作员" value="OPERATOR" />
            <el-option label="配送员" value="COURIER" />
            <el-option label="管理员" value="ADMIN" />
          </el-select>
        </el-form-item>

        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="全部状态" clearable>
            <el-option label="正常" value="ACTIVE" />
            <el-option label="禁用" value="DISABLED" />
            <el-option label="锁定" value="LOCKED" />
          </el-select>
        </el-form-item>

        <el-form-item label="注册时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 用户列表 -->
    <el-card>
      <el-table
        :data="userList"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column prop="id" label="用户ID" width="80" />

        <el-table-column label="头像" width="80">
          <template #default="{ row }">
            <el-avatar :size="40" :src="row.avatar">
              {{ row.realName?.charAt(0) || row.username?.charAt(0) }}
            </el-avatar>
          </template>
        </el-table-column>

        <el-table-column label="用户信息" width="200">
          <template #default="{ row }">
            <div>
              <div class="user-name">{{ row.realName || row.username }}</div>
              <div class="text-gray">@{{ row.username }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="联系方式" width="200">
          <template #default="{ row }">
            <div>
              <div v-if="row.phone">{{ row.phone }}</div>
              <div v-if="row.email" class="text-gray">{{ row.email }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="用户类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getUserTypeTagType(row.userType)">
              {{ getUserTypeText(row.userType) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              active-value="ACTIVE"
              inactive-value="DISABLED"
              @change="updateUserStatus(row)"
            />
          </template>
        </el-table-column>

        <el-table-column label="最后登录" width="160">
          <template #default="{ row }">
            <div v-if="row.lastLoginTime">
              <div>{{ formatTime(row.lastLoginTime) }}</div>
              <div class="text-gray">{{ row.lastLoginIp }}</div>
            </div>
            <span v-else class="text-gray">从未登录</span>
          </template>
        </el-table-column>

        <el-table-column label="注册时间" width="160">
          <template #default="{ row }">
            {{ formatTime(row.createTime) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" @click="viewUserDetail(row)">详情</el-button>
            <el-button type="text" @click="editUser(row)">编辑</el-button>
            <el-button type="text" @click="resetPassword(row)">重置密码</el-button>
            <el-dropdown>
              <el-button type="text">
                更多<el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="viewUserOrders(row)">查看订单</el-dropdown-item>
                  <el-dropdown-item @click="lockUser(row)" v-if="row.status !== 'LOCKED'"
                    >锁定用户</el-dropdown-item
                  >
                  <el-dropdown-item @click="unlockUser(row)" v-if="row.status === 'LOCKED'"
                    >解锁用户</el-dropdown-item
                  >
                  <el-dropdown-item @click="deleteUser(row)" divided class="danger"
                    >删除用户</el-dropdown-item
                  >
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑用户对话框 -->
    <el-dialog v-model="showAddDialog" :title="editingUser ? '编辑用户' : '新增用户'" width="600px">
      <el-form :model="userForm" :rules="userRules" ref="userFormRef" label-width="80px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户名" prop="username">
              <el-input v-model="userForm.username" :disabled="!!editingUser" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="真实姓名" prop="realName">
              <el-input v-model="userForm.realName" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="userForm.phone" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="userForm.email" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户类型" prop="userType">
              <el-select v-model="userForm.userType" style="width: 100%">
                <el-option label="普通用户" value="CUSTOMER" />
                <el-option label="操作员" value="OPERATOR" />
                <el-option label="配送员" value="COURIER" />
                <el-option label="管理员" value="ADMIN" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="userForm.status" style="width: 100%">
                <el-option label="正常" value="ACTIVE" />
                <el-option label="禁用" value="DISABLED" />
                <el-option label="锁定" value="LOCKED" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="身份证号" prop="idCard">
          <el-input v-model="userForm.idCard" />
        </el-form-item>

        <el-form-item label="密码" prop="password" v-if="!editingUser">
          <el-input v-model="userForm.password" type="password" show-password />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveUser" :loading="saving">
          {{ editingUser ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 用户详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="用户详情" width="800px">
      <div v-if="selectedUser" class="user-detail">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="user-avatar">
              <el-avatar :size="80" :src="selectedUser.avatar">
                {{ selectedUser.realName?.charAt(0) || selectedUser.username?.charAt(0) }}
              </el-avatar>
            </div>
          </el-col>
          <el-col :span="18">
            <div class="user-info">
              <h3>{{ selectedUser.realName || selectedUser.username }}</h3>
              <p>@{{ selectedUser.username }}</p>
              <el-tag :type="getUserTypeTagType(selectedUser.userType)">
                {{ getUserTypeText(selectedUser.userType) }}
              </el-tag>
            </div>
          </el-col>
        </el-row>

        <el-divider />

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>手机号：</label>
              <span>{{ selectedUser.phone || '未填写' }}</span>
            </div>
            <div class="detail-item">
              <label>邮箱：</label>
              <span>{{ selectedUser.email || '未填写' }}</span>
            </div>
            <div class="detail-item">
              <label>身份证号：</label>
              <span>{{ selectedUser.idCard || '未填写' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>注册时间：</label>
              <span>{{ formatTime(selectedUser.createTime) }}</span>
            </div>
            <div class="detail-item">
              <label>最后登录：</label>
              <span>{{
                selectedUser.lastLoginTime ? formatTime(selectedUser.lastLoginTime) : '从未登录'
              }}</span>
            </div>
            <div class="detail-item">
              <label>登录IP：</label>
              <span>{{ selectedUser.lastLoginIp || '无' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Download, Refresh, ArrowDown } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { userApi } from '@/api/user'
import dayjs from 'dayjs'

const router = useRouter()

// 状态
const loading = ref(false)
const saving = ref(false)
const userList = ref<any[]>([])
const selectedUsers = ref<any[]>([])
const total = ref(0)
const showAddDialog = ref(false)
const showDetailDialog = ref(false)
const editingUser = ref<any>(null)
const selectedUser = ref<any>(null)

// 表单引用
const userFormRef = ref<FormInstance>()

// 搜索表单
const searchForm = reactive({
  username: '',
  phone: '',
  email: '',
  userType: '',
  status: '',
})

// 日期范围
const dateRange = ref<[string, string] | null>(null)

// 分页
const pagination = reactive({
  current: 1,
  size: 20,
})

// 用户表单
const userForm = reactive({
  username: '',
  realName: '',
  phone: '',
  email: '',
  userType: 'CUSTOMER',
  status: 'ACTIVE',
  idCard: '',
  password: '',
})

// 表单验证规则
const userRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' },
  ],
  realName: [{ required: true, message: '请输入真实姓名', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
  ],
  userType: [{ required: true, message: '请选择用户类型', trigger: 'change' }],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度为6-20个字符', trigger: 'blur' },
  ],
}

// 加载用户列表
const loadUserList = async () => {
  loading.value = true
  try {
    // TODO: 调用用户列表API
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.size,
      keyword: searchForm.keyword || undefined,
      userType: searchForm.userType || undefined,
      status: searchForm.status || undefined,
    }

    const response = await userApi.getUserPage(params)
    userList.value = response.data.records
    total.value = response.data.total
    pagination.current = response.data.current
    pagination.size = response.data.size
  } catch (error) {
    console.error('加载用户列表失败:', error)
    ElMessage.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadUserList()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    username: '',
    phone: '',
    email: '',
    userType: '',
    status: '',
  })
  dateRange.value = null
  pagination.current = 1
  loadUserList()
}

// 刷新列表
const refreshList = () => {
  loadUserList()
}

// 分页变化
const handleSizeChange = () => {
  pagination.current = 1
  loadUserList()
}

const handleCurrentChange = () => {
  loadUserList()
}

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedUsers.value = selection
}

// 更新用户状态
const updateUserStatus = async (user: any) => {
  try {
    // TODO: 调用更新状态API
    ElMessage.success('状态更新成功')
  } catch (error) {
    console.error('更新状态失败:', error)
    ElMessage.error('更新失败')
    // 恢复原状态
    user.status = user.status === 'ACTIVE' ? 'DISABLED' : 'ACTIVE'
  }
}

// 查看用户详情
const viewUserDetail = (user: any) => {
  selectedUser.value = user
  showDetailDialog.value = true
}

// 编辑用户
const editUser = (user: any) => {
  editingUser.value = user
  Object.assign(userForm, {
    username: user.username,
    realName: user.realName,
    phone: user.phone,
    email: user.email,
    userType: user.userType,
    status: user.status,
    idCard: user.idCard || '',
    password: '',
  })
  showAddDialog.value = true
}

// 保存用户
const saveUser = async () => {
  if (!userFormRef.value) return

  await userFormRef.value.validate(async (valid) => {
    if (valid) {
      saving.value = true
      try {
        if (editingUser.value) {
          // TODO: 调用更新用户API
          ElMessage.success('用户更新成功')
        } else {
          // TODO: 调用创建用户API
          ElMessage.success('用户创建成功')
        }

        showAddDialog.value = false
        editingUser.value = null
        resetUserForm()
        loadUserList()
      } catch (error) {
        console.error('保存用户失败:', error)
        ElMessage.error('保存失败')
      } finally {
        saving.value = false
      }
    }
  })
}

// 重置表单
const resetUserForm = () => {
  Object.assign(userForm, {
    username: '',
    realName: '',
    phone: '',
    email: '',
    userType: 'CUSTOMER',
    status: 'ACTIVE',
    idCard: '',
    password: '',
  })
  userFormRef.value?.resetFields()
}

// 重置密码
const resetPassword = async (user: any) => {
  try {
    await ElMessageBox.confirm('确定要重置该用户的密码吗？', '重置密码确认', {
      type: 'warning',
    })

    // TODO: 调用重置密码API
    ElMessage.success('密码重置成功，新密码已发送到用户邮箱')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重置密码失败:', error)
      ElMessage.error('重置密码失败')
    }
  }
}

// 锁定用户
const lockUser = async (user: any) => {
  try {
    await ElMessageBox.confirm('确定要锁定该用户吗？', '锁定用户确认', {
      type: 'warning',
    })

    user.status = 'LOCKED'
    // TODO: 调用锁定用户API
    ElMessage.success('用户已锁定')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('锁定用户失败:', error)
      ElMessage.error('锁定失败')
    }
  }
}

// 解锁用户
const unlockUser = async (user: any) => {
  try {
    await ElMessageBox.confirm('确定要解锁该用户吗？', '解锁用户确认')

    user.status = 'ACTIVE'
    // TODO: 调用解锁用户API
    ElMessage.success('用户已解锁')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('解锁用户失败:', error)
      ElMessage.error('解锁失败')
    }
  }
}

// 删除用户
const deleteUser = async (user: any) => {
  try {
    await ElMessageBox.confirm('确定要删除该用户吗？此操作不可恢复！', '删除用户确认', {
      type: 'error',
    })

    // TODO: 调用删除用户API
    ElMessage.success('用户已删除')
    loadUserList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 查看用户订单
const viewUserOrders = (user: any) => {
  router.push(`/admin/order/list?userId=${user.id}`)
}

// 导出用户
const exportUsers = () => {
  ElMessage.info('导出功能开发中...')
}

// 工具函数
const getUserTypeText = (userType: string) => {
  const textMap = {
    CUSTOMER: '普通用户',
    OPERATOR: '操作员',
    COURIER: '配送员',
    ADMIN: '管理员',
  }
  return textMap[userType] || userType
}

const getUserTypeTagType = (userType: string) => {
  const typeMap = {
    CUSTOMER: '',
    OPERATOR: 'success',
    COURIER: 'warning',
    ADMIN: 'danger',
  }
  return typeMap[userType] || ''
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm')
}

// 监听对话框关闭
const handleDialogClose = () => {
  editingUser.value = null
  resetUserForm()
}

onMounted(() => {
  loadUserList()
})
</script>

<style scoped>
.admin-user-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-card {
  margin-bottom: 20px;
}

.text-gray {
  color: #666;
  font-size: 12px;
}

.user-name {
  font-weight: 500;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.danger {
  color: #f56c6c;
}

.user-detail {
  padding: 20px 0;
}

.user-avatar {
  text-align: center;
}

.user-info h3 {
  margin: 0 0 5px 0;
  color: #333;
}

.user-info p {
  margin: 0 0 10px 0;
  color: #666;
}

.detail-item {
  display: flex;
  margin-bottom: 15px;
}

.detail-item label {
  min-width: 80px;
  color: #666;
  font-weight: 500;
}
</style>
