package org.example.logisticsdelivery.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.logisticsdelivery.constants.TaskStatus;
import org.example.logisticsdelivery.entity.Courier;
import org.example.logisticsdelivery.entity.DeliveryTask;
import org.example.logisticsdelivery.mapper.CourierMapper;
import org.example.logisticsdelivery.mapper.DeliveryTaskMapper;
import org.example.logisticsdelivery.service.DeliveryTaskService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 配送任务服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeliveryTaskServiceImpl implements DeliveryTaskService {

    private final DeliveryTaskMapper taskMapper;
    private final CourierMapper courierMapper;

    @Override
    @Transactional
    public DeliveryTask createTask(DeliveryTask task) {
        try {
            // 参数验证
            validateTaskInfo(task);
            
            // 检查订单号是否已存在任务
            if (StringUtils.hasText(task.getOrderNumber())) {
                DeliveryTask existing = taskMapper.findByOrderNumber(task.getOrderNumber());
                if (existing != null) {
                    throw new RuntimeException("该订单已存在配送任务");
                }
            }
            
            // 生成任务编号
            if (!StringUtils.hasText(task.getTaskNumber())) {
                task.setTaskNumber(generateTaskNumber());
            }
            
            // 设置默认值
            task.setTaskStatus(TaskStatus.PENDING.getCode());
            task.setPriority(task.getPriority() != null ? task.getPriority() : 1);
            task.setCreateTime(LocalDateTime.now());
            task.setUpdateTime(LocalDateTime.now());
            
            taskMapper.insert(task);
            log.info("创建配送任务成功，任务编号: {}", task.getTaskNumber());
            return task;
        } catch (Exception e) {
            log.error("创建配送任务失败", e);
            throw new RuntimeException("创建配送任务失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public DeliveryTask updateTask(DeliveryTask task) {
        try {
            if (task.getId() == null) {
                throw new RuntimeException("任务ID不能为空");
            }
            
            DeliveryTask existing = taskMapper.selectById(task.getId());
            if (existing == null) {
                throw new RuntimeException("配送任务不存在");
            }
            
            task.setUpdateTime(LocalDateTime.now());
            taskMapper.updateById(task);
            log.info("更新配送任务成功，ID: {}", task.getId());
            return taskMapper.selectById(task.getId());
        } catch (Exception e) {
            log.error("更新配送任务失败，ID: {}", task.getId(), e);
            throw new RuntimeException("更新配送任务失败: " + e.getMessage());
        }
    }

    @Override
    public DeliveryTask getTaskById(Long id) {
        if (id == null) {
            throw new RuntimeException("任务ID不能为空");
        }
        return taskMapper.selectById(id);
    }

    @Override
    public DeliveryTask getTaskByNumber(String taskNumber) {
        if (!StringUtils.hasText(taskNumber)) {
            throw new RuntimeException("任务编号不能为空");
        }
        return taskMapper.findByTaskNumber(taskNumber);
    }

    @Override
    public DeliveryTask getTaskByOrderNumber(String orderNumber) {
        if (!StringUtils.hasText(orderNumber)) {
            throw new RuntimeException("订单号不能为空");
        }
        return taskMapper.findByOrderNumber(orderNumber);
    }

    @Override
    public List<DeliveryTask> getTasksByCourierId(Long courierId) {
        if (courierId == null) {
            throw new RuntimeException("配送员ID不能为空");
        }
        return taskMapper.findByCourierId(courierId);
    }

    @Override
    public List<DeliveryTask> getTasksByStatus(String taskStatus) {
        if (!StringUtils.hasText(taskStatus)) {
            throw new RuntimeException("任务状态不能为空");
        }
        return taskMapper.findByTaskStatus(taskStatus);
    }

    @Override
    public List<DeliveryTask> getCourierDailyTasks(Long courierId, LocalDate taskDate) {
        if (courierId == null || taskDate == null) {
            throw new RuntimeException("参数不能为空");
        }
        return taskMapper.findCourierDailyTasks(courierId, taskDate);
    }

    @Override
    public List<DeliveryTask> getCourierActiveTasks(Long courierId) {
        if (courierId == null) {
            throw new RuntimeException("配送员ID不能为空");
        }
        return taskMapper.findCourierActiveTasks(courierId);
    }

    @Override
    public List<DeliveryTask> getPendingTasks(String taskType, String workArea) {
        return taskMapper.findPendingTasks(taskType, workArea);
    }

    @Override
    public List<DeliveryTask> getTimeoutTasks(Integer timeoutMinutes) {
        if (timeoutMinutes == null || timeoutMinutes <= 0) {
            timeoutMinutes = 30; // 默认30分钟超时
        }
        return taskMapper.findTimeoutTasks(timeoutMinutes);
    }

    @Override
    @Transactional
    public boolean assignTask(Long taskId, Long courierId) {
        try {
            if (taskId == null || courierId == null) {
                throw new RuntimeException("参数不能为空");
            }
            
            DeliveryTask task = taskMapper.selectById(taskId);
            if (task == null) {
                throw new RuntimeException("配送任务不存在");
            }
            
            if (!TaskStatus.PENDING.getCode().equals(task.getTaskStatus())) {
                throw new RuntimeException("只能分配待分配状态的任务");
            }
            
            Courier courier = courierMapper.selectById(courierId);
            if (courier == null) {
                throw new RuntimeException("配送员不存在");
            }
            
            // 更新任务状态和配送员
            task.setCourierId(courierId);
            task.setTaskStatus(TaskStatus.ASSIGNED.getCode());
            task.setUpdateTime(LocalDateTime.now());
            
            int result = taskMapper.updateById(task);
            if (result > 0) {
                log.info("分配任务成功，任务ID: {}, 配送员ID: {}", taskId, courierId);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("分配任务失败，任务ID: {}, 配送员ID: {}", taskId, courierId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean batchAssignTasks(List<Long> taskIds, Long courierId) {
        try {
            if (taskIds == null || taskIds.isEmpty() || courierId == null) {
                throw new RuntimeException("参数不能为空");
            }
            
            Courier courier = courierMapper.selectById(courierId);
            if (courier == null) {
                throw new RuntimeException("配送员不存在");
            }
            
            // 批量更新任务
            LambdaQueryWrapper<DeliveryTask> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(DeliveryTask::getId, taskIds)
                   .eq(DeliveryTask::getTaskStatus, TaskStatus.PENDING.getCode());
            
            DeliveryTask updateTask = new DeliveryTask();
            updateTask.setCourierId(courierId);
            updateTask.setTaskStatus(TaskStatus.ASSIGNED.getCode());
            updateTask.setUpdateTime(LocalDateTime.now());
            
            int result = taskMapper.update(updateTask, wrapper);
            log.info("批量分配任务成功，数量: {}, 配送员ID: {}", result, courierId);
            return result > 0;
        } catch (Exception e) {
            log.error("批量分配任务失败，配送员ID: {}", courierId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean updateTaskStatus(Long taskId, String taskStatus) {
        try {
            if (taskId == null || !StringUtils.hasText(taskStatus)) {
                throw new RuntimeException("参数不能为空");
            }
            
            DeliveryTask task = taskMapper.selectById(taskId);
            if (task == null) {
                throw new RuntimeException("配送任务不存在");
            }
            
            task.setTaskStatus(taskStatus);
            task.setUpdateTime(LocalDateTime.now());
            
            // 如果是完成状态，设置实际完成时间
            if (TaskStatus.COMPLETED.getCode().equals(taskStatus)) {
                task.setActualTime(LocalDateTime.now());
            }
            
            int result = taskMapper.updateById(task);
            if (result > 0) {
                log.info("更新任务状态成功，任务ID: {}, 状态: {}", taskId, taskStatus);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("更新任务状态失败，任务ID: {}", taskId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean batchUpdateTaskStatus(List<Long> taskIds, String taskStatus) {
        try {
            if (taskIds == null || taskIds.isEmpty() || !StringUtils.hasText(taskStatus)) {
                throw new RuntimeException("参数不能为空");
            }
            
            int result = taskMapper.batchUpdateTaskStatus(taskIds, taskStatus);
            log.info("批量更新任务状态成功，数量: {}, 状态: {}", result, taskStatus);
            return result > 0;
        } catch (Exception e) {
            log.error("批量更新任务状态失败", e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean completeTask(Long taskId) {
        return updateTaskStatus(taskId, TaskStatus.COMPLETED.getCode());
    }

    @Override
    @Transactional
    public boolean cancelTask(Long taskId, String reason) {
        try {
            if (taskId == null) {
                throw new RuntimeException("任务ID不能为空");
            }
            
            DeliveryTask task = taskMapper.selectById(taskId);
            if (task == null) {
                throw new RuntimeException("配送任务不存在");
            }
            
            task.setTaskStatus(TaskStatus.CANCELLED.getCode());
            task.setRemarks(reason);
            task.setUpdateTime(LocalDateTime.now());
            
            int result = taskMapper.updateById(task);
            if (result > 0) {
                log.info("取消任务成功，任务ID: {}, 原因: {}", taskId, reason);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("取消任务失败，任务ID: {}", taskId, e);
            return false;
        }
    }

    @Override
    public IPage<DeliveryTask> getTasksPage(Page<DeliveryTask> page, Long courierId, 
                                           String taskStatus, String taskType,
                                           LocalDateTime startTime, LocalDateTime endTime) {
        return taskMapper.findTasksPage(page, courierId, taskStatus, taskType, startTime, endTime);
    }

    @Override
    public Map<String, Object> getTaskStatistics(Long courierId, LocalDate startDate, LocalDate endDate) {
        return taskMapper.getTaskStatistics(courierId, startDate, endDate);
    }

    @Override
    public Map<String, Long> getStatusStatistics() {
        List<Map<String, Object>> statistics = taskMapper.countByTaskStatus();
        Map<String, Long> result = new HashMap<>();
        
        for (Map<String, Object> stat : statistics) {
            String status = (String) stat.get("task_status");
            Long count = ((Number) stat.get("count")).longValue();
            result.put(status, count);
        }
        
        return result;
    }

    @Override
    @Transactional
    public boolean autoAssignTasks(String workArea) {
        try {
            // 获取待分配的任务
            List<DeliveryTask> pendingTasks = taskMapper.findPendingTasks(null, workArea);
            if (pendingTasks.isEmpty()) {
                return true;
            }
            
            // 获取可用的配送员
            List<Courier> availableCouriers = courierMapper.findAvailableCouriers(workArea);
            if (availableCouriers.isEmpty()) {
                log.warn("没有可用的配送员进行自动分配，工作区域: {}", workArea);
                return false;
            }
            
            // 简单的轮询分配策略
            int courierIndex = 0;
            for (DeliveryTask task : pendingTasks) {
                Courier courier = availableCouriers.get(courierIndex % availableCouriers.size());
                assignTask(task.getId(), courier.getId());
                courierIndex++;
            }
            
            log.info("自动分配任务完成，分配数量: {}, 工作区域: {}", pendingTasks.size(), workArea);
            return true;
        } catch (Exception e) {
            log.error("自动分配任务失败，工作区域: {}", workArea, e);
            return false;
        }
    }

    @Override
    @Transactional
    public int handleTimeoutTasks() {
        try {
            List<DeliveryTask> timeoutTasks = taskMapper.findTimeoutTasks(30);
            if (timeoutTasks.isEmpty()) {
                return 0;
            }
            
            // 处理超时任务
            int handledCount = 0;
            for (DeliveryTask task : timeoutTasks) {
                // 可以发送通知、重新分配等
                log.warn("发现超时任务，任务ID: {}, 任务编号: {}", task.getId(), task.getTaskNumber());
                handledCount++;
            }
            
            return handledCount;
        } catch (Exception e) {
            log.error("处理超时任务失败", e);
            return 0;
        }
    }

    @Override
    @Transactional
    public boolean deleteTask(Long taskId) {
        try {
            if (taskId == null) {
                throw new RuntimeException("任务ID不能为空");
            }
            
            DeliveryTask task = taskMapper.selectById(taskId);
            if (task == null) {
                throw new RuntimeException("配送任务不存在");
            }
            
            // 只能删除待分配或已取消的任务
            if (!TaskStatus.PENDING.getCode().equals(task.getTaskStatus()) && 
                !TaskStatus.CANCELLED.getCode().equals(task.getTaskStatus())) {
                throw new RuntimeException("只能删除待分配或已取消的任务");
            }
            
            int result = taskMapper.deleteById(taskId);
            if (result > 0) {
                log.info("删除配送任务成功，ID: {}", taskId);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("删除配送任务失败，ID: {}", taskId, e);
            return false;
        }
    }

    /**
     * 验证任务信息
     */
    private void validateTaskInfo(DeliveryTask task) {
        if (!StringUtils.hasText(task.getOrderNumber())) {
            throw new RuntimeException("订单号不能为空");
        }
        
        if (!StringUtils.hasText(task.getTaskType())) {
            throw new RuntimeException("任务类型不能为空");
        }
        
        if (!StringUtils.hasText(task.getSenderName()) || !StringUtils.hasText(task.getSenderPhone()) || 
            !StringUtils.hasText(task.getSenderAddress())) {
            throw new RuntimeException("发件人信息不能为空");
        }
        
        if (!StringUtils.hasText(task.getReceiverName()) || !StringUtils.hasText(task.getReceiverPhone()) || 
            !StringUtils.hasText(task.getReceiverAddress())) {
            throw new RuntimeException("收件人信息不能为空");
        }
    }

    /**
     * 生成任务编号
     */
    private String generateTaskNumber() {
        return "TASK" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 4).toUpperCase();
    }
}