package org.example.logisticsgateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 物流网关服务启动类
 * 排除数据库自动配置，网关只负责路由转发
 */
@SpringBootApplication(exclude = {
        DataSourceAutoConfiguration.class,
        HibernateJpaAutoConfiguration.class
})
@EnableDiscoveryClient
public class LogisticsGatewayApplication {

    public static void main(String[] args) {
        SpringApplication.run(LogisticsGatewayApplication.class, args);
        System.out.println("=========================================");
        System.out.println("物流网关服务启动成功！");
        System.out.println("服务端口: 8080");
        System.out.println("健康检查: http://localhost:8080/actuator/health");
        System.out.println("=========================================");
    }
}