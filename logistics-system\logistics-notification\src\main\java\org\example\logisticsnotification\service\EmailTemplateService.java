package org.example.logisticsnotification.service;

import org.example.logisticsnotification.entity.EmailTemplate;
import java.util.List;
import java.util.Map;

/**
 * 邮件模板服务接口
 * 在现有notification模块中增强邮件功能
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
public interface EmailTemplateService {

    /**
     * 根据模板编码获取模板
     */
    EmailTemplate getTemplateByCode(String templateCode);

    /**
     * 获取所有启用的模板
     */
    List<EmailTemplate> getAllEnabledTemplates();

    /**
     * 渲染邮件模板
     * @param templateCode 模板编码
     * @param variables 变量Map
     * @return 渲染后的邮件内容
     */
    String renderTemplate(String templateCode, Map<String, Object> variables);

    /**
     * 发送模板邮件
     * @param to 收件人
     * @param templateCode 模板编码
     * @param variables 变量Map
     * @return 是否发送成功
     */
    boolean sendTemplateEmail(String to, String templateCode, Map<String, Object> variables);

    /**
     * 批量发送模板邮件
     * @param recipients 收件人列表
     * @param templateCode 模板编码
     * @param variables 变量Map
     * @return 发送成功数量
     */
    int sendBatchTemplateEmail(List<String> recipients, String templateCode, Map<String, Object> variables);

    /**
     * 创建或更新模板
     */
    EmailTemplate saveTemplate(EmailTemplate template);

    /**
     * 删除模板
     */
    boolean deleteTemplate(String templateCode);

    /**
     * 启用/禁用模板
     */
    boolean toggleTemplate(String templateCode, boolean enabled);
}
