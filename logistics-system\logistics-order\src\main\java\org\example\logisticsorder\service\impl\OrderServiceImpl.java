package org.example.logisticsorder.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logistics.common.constants.OrderStatus;
import org.example.logisticsorder.service.OrderService;
import org.example.logisticsorder.service.PricingService;
import org.example.logisticsorder.mapper.OrderMapper;
import org.example.logisticsorder.mapper.OrderStatusLogMapper;
import org.example.logisticsorder.entity.Order;
import org.example.logisticsorder.entity.OrderStatusLog;
import org.example.logisticsorder.dto.CreateOrderDTO;
import org.example.logisticsorder.dto.OrderQueryDTO;
import org.example.logisticsorder.dto.OrderStatusUpdateDTO;
import org.example.logisticsorder.vo.OrderVO;
import org.example.logisticsorder.vo.OrderListVO;

import org.example.logisticsorder.constants.PaymentStatus;
import org.example.logisticsorder.constants.ServiceType;
import com.logistics.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 订单服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Slf4j
@Service
public class OrderServiceImpl implements OrderService {

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private OrderStatusLogMapper orderStatusLogMapper;

    @Autowired
    private PricingService pricingService;

    // 订单号生成器
    private static final AtomicLong ORDER_SEQUENCE = new AtomicLong(1);

    @Override
    @Transactional
    public OrderVO createOrder(Long userId, CreateOrderDTO createOrderDTO) {
        log.info("开始创建订单，用户ID: {}", userId);

        // 1. 计算价格
        OrderService.PriceResult priceResult = pricingService.calculateTotalPrice(createOrderDTO);

        // 2. 创建订单实体
        Order order = new Order();
        BeanUtils.copyProperties(createOrderDTO, order);
        order.setUserId(userId);
        order.setOrderNumber(generateOrderNumber());
        order.setShippingFee(priceResult.getShippingFee());
        order.setInsuranceFee(priceResult.getInsuranceFee());
        order.setPackingFee(priceResult.getPackingFee());
        order.setTotalFee(priceResult.getTotalFee());
        order.setPaymentStatus(PaymentStatus.UNPAID.getCode());
        order.setOrderStatus(OrderStatus.PENDING.getCode());
        order.setCreateTime(LocalDateTime.now());
        order.setUpdateTime(LocalDateTime.now());

        // 3. 保存订单
        int result = orderMapper.insert(order);
        if (result <= 0) {
            throw new BusinessException("订单创建失败");
        }

        // 4. 记录状态变更日志
        recordStatusChange(order.getId(), order.getOrderNumber(), null,
                OrderStatus.PENDING.getCode(), null, "系统", "用户下单");

        // 5. 转换为VO返回
        OrderVO orderVO = convertToOrderVO(order);
        log.info("订单创建成功，订单号: {}", order.getOrderNumber());
        return orderVO;
    }

    @Override
    public OrderVO getOrderById(Long orderId) {
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        return convertToOrderVO(order);
    }

    @Override
    public OrderVO getOrderByNumber(String orderNumber) {
        Order order = orderMapper.selectByOrderNumber(orderNumber);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        return convertToOrderVO(order);
    }

    @Override
    public IPage<OrderListVO> getOrderPage(OrderQueryDTO queryDTO) {
        Page<OrderListVO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        return orderMapper.selectOrderPage(page, queryDTO);
    }

    @Override
    public List<OrderListVO> getUserOrders(Long userId, Integer limit) {
        return orderMapper.selectByUserId(userId, limit);
    }

    @Override
    @Transactional
    public boolean updateOrderStatus(OrderStatusUpdateDTO updateDTO) {
        log.info("更新订单状态，订单ID: {}, 新状态: {}", updateDTO.getOrderId(), updateDTO.getNewStatus());

        // 1. 查询订单
        Order order = orderMapper.selectById(updateDTO.getOrderId());
        if (order == null) {
            throw new BusinessException("订单不存在");
        }

        // 2. 验证状态变更是否合理
        validateStatusChange(order.getOrderStatus(), updateDTO.getNewStatus());

        // 3. 更新订单状态
        String oldStatus = order.getOrderStatus();
        order.setOrderStatus(updateDTO.getNewStatus());
        order.setUpdateTime(LocalDateTime.now());

        // 根据状态更新相应时间字段
        updateOrderTimeFields(order, updateDTO.getNewStatus());

        int result = orderMapper.updateById(order);
        if (result <= 0) {
            return false;
        }

        // 4. 记录状态变更日志
        recordStatusChange(order.getId(), order.getOrderNumber(), oldStatus,
                updateDTO.getNewStatus(), updateDTO.getOperatorId(),
                updateDTO.getOperatorName(), updateDTO.getChangeReason());

        log.info("订单状态更新成功，订单号: {}", order.getOrderNumber());
        return true;
    }

    @Override
    @Transactional
    public boolean cancelOrder(Long orderId, Long userId, String reason) {
        log.info("取消订单，订单ID: {}, 用户ID: {}", orderId, userId);

        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }

        // 验证用户权限
        if (!order.getUserId().equals(userId)) {
            throw new BusinessException("无权限操作此订单");
        }

        // 验证是否可以取消
        OrderStatus currentStatus = OrderStatus.fromCode(order.getOrderStatus());
        if (!currentStatus.canCancel()) {
            throw new BusinessException("订单当前状态不允许取消");
        }

        // 更新订单状态为已取消
        OrderStatusUpdateDTO updateDTO = new OrderStatusUpdateDTO();
        updateDTO.setOrderId(orderId);
        updateDTO.setNewStatus(OrderStatus.CANCELLED.getCode());
        updateDTO.setChangeReason(reason);
        updateDTO.setOperatorType("USER");

        return updateOrderStatus(updateDTO);
    }

    @Override
    @Transactional
    public boolean paymentSuccess(String orderNumber, String paymentMethod) {
        log.info("订单支付成功回调，订单号: {}", orderNumber);

        Order order = orderMapper.selectByOrderNumber(orderNumber);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }

        // 更新支付状态
        order.setPaymentStatus(PaymentStatus.PAID.getCode());
        order.setPaymentMethod(paymentMethod);
        order.setOrderStatus(OrderStatus.PAID.getCode());
        order.setUpdateTime(LocalDateTime.now());

        int result = orderMapper.updateById(order);
        if (result <= 0) {
            return false;
        }

        // 记录状态变更
        recordStatusChange(order.getId(), orderNumber, OrderStatus.PENDING.getCode(),
                OrderStatus.PAID.getCode(), null, "系统", "支付成功");

        return true;
    }

    @Override
    public boolean assignPickupCourier(Long orderId, Long courierId) {
        log.info("分配揽件员，订单ID: {}, 配送员ID: {}", orderId, courierId);

        OrderStatusUpdateDTO updateDTO = new OrderStatusUpdateDTO();
        updateDTO.setOrderId(orderId);
        updateDTO.setNewStatus(OrderStatus.PICKUP_ASSIGNED.getCode());
        updateDTO.setOperatorId(courierId);
        updateDTO.setOperatorType("SYSTEM");
        updateDTO.setChangeReason("分配揽件员");

        return updateOrderStatus(updateDTO);
    }

    @Override
    public boolean assignDeliveryCourier(Long orderId, Long courierId) {
        log.info("分配配送员，订单ID: {}, 配送员ID: {}", orderId, courierId);

        OrderStatusUpdateDTO updateDTO = new OrderStatusUpdateDTO();
        updateDTO.setOrderId(orderId);
        updateDTO.setNewStatus(OrderStatus.OUT_FOR_DELIVERY.getCode());
        updateDTO.setOperatorId(courierId);
        updateDTO.setOperatorType("SYSTEM");
        updateDTO.setChangeReason("分配配送员");

        return updateOrderStatus(updateDTO);
    }

    @Override
    @Transactional
    public boolean signOrder(Long orderId, String signProof) {
        log.info("订单签收，订单ID: {}", orderId);

        OrderStatusUpdateDTO updateDTO = new OrderStatusUpdateDTO();
        updateDTO.setOrderId(orderId);
        updateDTO.setNewStatus(OrderStatus.DELIVERED.getCode());
        updateDTO.setOperatorType("COURIER");
        updateDTO.setChangeReason("用户签收");
        updateDTO.setRemarks("签收凭证: " + signProof);

        return updateOrderStatus(updateDTO);
    }

    @Override
    public List<OrderStatusLog> getOrderStatusLog(Long orderId) {
        return orderStatusLogMapper.selectByOrderId(orderId);
    }

    @Override
    public OrderService.PriceResult calculatePrice(CreateOrderDTO createOrderDTO) {
        log.info("开始计算订单价格: {}", createOrderDTO);

        // 使用PricingService进行价格计算
        return pricingService.calculateTotalPrice(createOrderDTO);
    }

    @Override
    @Transactional
    public boolean batchUpdateStatus(List<Long> orderIds, String newStatus, String reason) {
        log.info("批量更新订单状态，订单数量: {}, 新状态: {}", orderIds.size(), newStatus);

        int result = orderMapper.batchUpdateStatus(orderIds, newStatus);

        // 为每个订单记录状态变更日志
        for (Long orderId : orderIds) {
            Order order = orderMapper.selectById(orderId);
            if (order != null) {
                recordStatusChange(orderId, order.getOrderNumber(), order.getOrderStatus(),
                        newStatus, null, "系统", reason);
            }
        }

        return result > 0;
    }

    @Override
    @Transactional
    public int cancelTimeoutOrders(Integer timeoutMinutes) {
        log.info("开始取消超时未支付订单，超时时间: {} 分钟", timeoutMinutes);

        List<Order> timeoutOrders = orderMapper.selectPendingPaymentOrders(timeoutMinutes);
        if (timeoutOrders.isEmpty()) {
            return 0;
        }

        List<Long> orderIds = timeoutOrders.stream()
                .map(Order::getId)
                .collect(java.util.stream.Collectors.toList());

        boolean success = batchUpdateStatus(orderIds, OrderStatus.CANCELLED.getCode(),
                "超时未支付自动取消");

        log.info("取消超时订单完成，处理数量: {}", timeoutOrders.size());
        return success ? timeoutOrders.size() : 0;
    }

    @Override
    public OrderService.OrderStatistics getOrderStatistics(Long userId) {
        OrderService.OrderStatistics statistics = new OrderService.OrderStatistics();

        if (userId != null) {
            // 用户维度统计
            statistics.setTotalOrders(orderMapper.countByUserIdAndStatus(userId, null));
            statistics.setPendingOrders(orderMapper.countByUserIdAndStatus(userId, OrderStatus.PENDING.getCode()));
            statistics.setPaidOrders(orderMapper.countByUserIdAndStatus(userId, OrderStatus.PAID.getCode()));
            statistics.setInTransitOrders(orderMapper.countByUserIdAndStatus(userId, OrderStatus.IN_TRANSIT.getCode()));
            statistics.setDeliveredOrders(orderMapper.countByUserIdAndStatus(userId, OrderStatus.DELIVERED.getCode()));
            statistics.setCancelledOrders(orderMapper.countByUserIdAndStatus(userId, OrderStatus.CANCELLED.getCode()));
        } else {
            // 全局统计
            statistics.setPendingOrders(orderMapper.countByStatus(OrderStatus.PENDING.getCode()));
            statistics.setPaidOrders(orderMapper.countByStatus(OrderStatus.PAID.getCode()));
            statistics.setInTransitOrders(orderMapper.countByStatus(OrderStatus.IN_TRANSIT.getCode()));
            statistics.setDeliveredOrders(orderMapper.countByStatus(OrderStatus.DELIVERED.getCode()));
            statistics.setCancelledOrders(orderMapper.countByStatus(OrderStatus.CANCELLED.getCode()));

            statistics.setTotalOrders(statistics.getPendingOrders() + statistics.getPaidOrders() +
                    statistics.getInTransitOrders() + statistics.getDeliveredOrders() +
                    statistics.getCancelledOrders());
        }

        return statistics;
    }

    // 私有辅助方法
    private String generateOrderNumber() {
        // 订单号格式: LD + yyyyMMddHHmmss + 4位序列号
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        long sequence = ORDER_SEQUENCE.getAndIncrement() % 10000;
        return String.format("LD%s%04d", timestamp, sequence);
    }

    private void recordStatusChange(Long orderId, String orderNumber, String oldStatus,
                                    String newStatus, Long operatorId, String operatorName, String reason) {
        OrderStatusLog log = new OrderStatusLog();
        log.setOrderId(orderId);
        log.setOrderNumber(orderNumber);
        log.setOldStatus(oldStatus);
        log.setNewStatus(newStatus);
        log.setOperatorId(operatorId);
        log.setOperatorName(operatorName);
        log.setChangeReason(reason);
        log.setCreateTime(LocalDateTime.now());
        log.setUpdateTime(LocalDateTime.now());

        orderStatusLogMapper.insert(log);
    }

    private void validateStatusChange(String currentStatus, String newStatus) {
        // 这里可以添加状态变更的业务规则验证
        // 例如：已签收的订单不能改为运输中
        OrderStatus current = OrderStatus.fromCode(currentStatus);
        OrderStatus target = OrderStatus.fromCode(newStatus);

        if (current.isCompleted() && !target.isCompleted()) {
            throw new BusinessException("已完成的订单不能回退状态");
        }
    }

    private void updateOrderTimeFields(Order order, String newStatus) {
        LocalDateTime now = LocalDateTime.now();

        switch (newStatus) {
            case "PICKED_UP":
                order.setPickupTime(now);
                break;
            case "OUT_FOR_DELIVERY":
                order.setDeliveryTime(now);
                break;
            case "DELIVERED":
                order.setSignTime(now);
                break;
            default:
                break;
        }
    }

    private OrderVO convertToOrderVO(Order order) {
        OrderVO vo = new OrderVO();
        BeanUtils.copyProperties(order, vo);

        // 设置描述字段
        try {
            ServiceType serviceType = ServiceType.fromCode(order.getServiceType());
            vo.setServiceTypeDesc(serviceType.getDescription());
        } catch (Exception e) {
            vo.setServiceTypeDesc("标准快递");
        }

        try {
            PaymentStatus paymentStatus = PaymentStatus.fromCode(order.getPaymentStatus());
            vo.setPaymentStatusDesc(paymentStatus.getDescription());
        } catch (Exception e) {
            vo.setPaymentStatusDesc("未知状态");
        }

        try {
            OrderStatus orderStatus = OrderStatus.fromCode(order.getOrderStatus());
            vo.setOrderStatusDesc(orderStatus.getDescription());
            vo.setCanCancel(orderStatus.canCancel());
            vo.setIsCompleted(orderStatus.isCompleted());
        } catch (Exception e) {
            vo.setOrderStatusDesc("未知状态");
            vo.setCanCancel(false);
            vo.setIsCompleted(false);
        }

        // 支付方式描述
        if ("ONLINE".equals(order.getPaymentMethod())) {
            vo.setPaymentMethodDesc("在线支付");
        } else if ("COD".equals(order.getPaymentMethod())) {
            vo.setPaymentMethodDesc("货到付款");
        }

        return vo;
    }

}