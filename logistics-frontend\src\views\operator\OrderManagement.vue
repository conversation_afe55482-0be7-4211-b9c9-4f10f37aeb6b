<template>
  <div class="order-management">
    <div class="page-header">
      <h1>订单管理</h1>
      <div class="header-actions">
        <el-button
          type="primary"
          @click="openBatchAssignDialog"
          :disabled="selectedOrders.length === 0"
        >
          <el-icon><Van /></el-icon>
          批量分配 ({{ selectedOrders.length }})
        </el-button>
        <el-button
          type="danger"
          @click="showEmergencyDialog = true"
          :disabled="selectedOrders.length === 0"
        >
          <el-icon><Warning /></el-icon>
          紧急调度
        </el-button>
      </div>
    </div>

    <!-- 搜索和过滤 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="订单号">
          <el-input
            v-model="searchForm.orderNumber"
            placeholder="输入订单号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="客户姓名">
          <el-input
            v-model="searchForm.customerName"
            placeholder="输入客户姓名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="订单状态">
          <el-select
            v-model="searchForm.status"
            placeholder="选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="待分配" value="pending" />
            <el-option label="已分配" value="assigned" />
            <el-option label="配送中" value="delivering" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="优先级">
          <el-select
            v-model="searchForm.priority"
            placeholder="选择优先级"
            clearable
            style="width: 120px"
          >
            <el-option label="紧急" value="emergency" />
            <el-option label="加急" value="urgent" />
            <el-option label="普通" value="normal" />
          </el-select>
        </el-form-item>
        <el-form-item label="配送员">
          <el-select
            v-model="searchForm.courierId"
            placeholder="选择配送员"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="courier in allCouriers"
              :key="courier.id"
              :label="courier.name"
              :value="courier.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="table-header">
          <span>订单列表 (共 {{ total }} 条)</span>
          <div class="table-actions">
            <el-button size="small" @click="exportOrders">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
            <el-button size="small" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        :data="orderList"
        style="width: 100%"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
        v-loading="loading"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column prop="orderNumber" label="订单号" width="140" fixed="left">
          <template #default="scope">
            <el-link type="primary" @click="viewOrderDetail(scope.row)">
              {{ scope.row.orderNumber }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="customerName" label="客户" width="100" />
        <el-table-column prop="customerPhone" label="联系电话" width="120" />
        <el-table-column prop="pickupAddress" label="取件地址" width="200" show-overflow-tooltip />
        <el-table-column
          prop="deliveryAddress"
          label="配送地址"
          width="200"
          show-overflow-tooltip
        />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" width="80">
          <template #default="scope">
            <el-tag :type="getPriorityType(scope.row.priority)" size="small">
              {{ getPriorityText(scope.row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="courierName" label="配送员" width="100">
          <template #default="scope">
            <span v-if="scope.row.courierName">{{ scope.row.courierName }}</span>
            <el-tag v-else type="info" size="small">未分配</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="weight" label="重量(kg)" width="80" />
        <el-table-column prop="value" label="价值(元)" width="80">
          <template #default="scope"> ¥{{ scope.row.value }} </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="140">
          <template #default="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="estimatedTime" label="预计送达" width="140">
          <template #default="scope">
            {{ formatDateTime(scope.row.estimatedTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button
              v-if="scope.row.status === 'pending'"
              type="primary"
              size="small"
              @click="assignOrder(scope.row)"
            >
              分配
            </el-button>
            <el-button
              v-if="scope.row.status === 'assigned'"
              type="warning"
              size="small"
              @click="reassignOrder(scope.row)"
            >
              重新分配
            </el-button>
            <el-dropdown trigger="click" @command="handleCommand($event, scope.row)">
              <el-button size="small" type="info">
                更多 <el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="detail">查看详情</el-dropdown-item>
                  <el-dropdown-item command="priority">修改优先级</el-dropdown-item>
                  <el-dropdown-item command="notes">添加备注</el-dropdown-item>
                  <el-dropdown-item command="track">物流跟踪</el-dropdown-item>
                  <el-dropdown-item command="cancel" divided>取消订单</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 订单分配对话框 -->
    <el-dialog v-model="showAssignDialog" title="分配订单" width="600px">
      <div v-if="currentOrder">
        <div class="order-summary">
          <h4>订单信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="订单号">{{
              currentOrder.orderNumber
            }}</el-descriptions-item>
            <el-descriptions-item label="客户">{{
              currentOrder.customerName
            }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{
              currentOrder.customerPhone
            }}</el-descriptions-item>
            <el-descriptions-item label="优先级">
              <el-tag :type="getPriorityType(currentOrder.priority)">
                {{ getPriorityText(currentOrder.priority) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="取件地址" :span="2">{{
              currentOrder.pickupAddress
            }}</el-descriptions-item>
            <el-descriptions-item label="配送地址" :span="2">{{
              currentOrder.deliveryAddress
            }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <el-form :model="assignForm" label-width="100px" style="margin-top: 20px">
          <el-form-item label="选择配送员" required>
            <el-select
              v-model="assignForm.courierId"
              placeholder="请选择配送员"
              style="width: 100%"
            >
              <el-option v-for="courier in availableCouriers" :key="courier.id" :value="courier.id">
                <div class="courier-option">
                  <div class="courier-info">
                    <span class="name">{{ courier.name }}</span>
                    <span class="location">{{ courier.location }}</span>
                  </div>
                  <div class="courier-stats">
                    <el-tag :type="getStatusType(courier.status)" size="small">
                      {{ getStatusText(courier.status) }}
                    </el-tag>
                    <span class="orders">{{ courier.currentOrders }}单</span>
                    <el-rate v-model="courier.rating" disabled size="small" />
                  </div>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备注">
            <el-input
              v-model="assignForm.notes"
              type="textarea"
              rows="3"
              placeholder="可选，添加分配备注"
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="showAssignDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmAssign" :loading="assigning"> 确认分配 </el-button>
      </template>
    </el-dialog>

    <!-- 批量分配对话框 -->
    <el-dialog v-model="showBatchAssignDialog" title="批量分配订单" width="900px">
      <div class="batch-assign-container">
        <div class="selected-orders">
          <h4>已选择的订单 ({{ selectedOrders.length }})</h4>
          <el-table :data="selectedOrders" max-height="300">
            <el-table-column prop="orderNumber" label="订单号" width="120" />
            <el-table-column prop="customerName" label="客户" width="100" />
            <el-table-column prop="deliveryAddress" label="配送地址" show-overflow-tooltip />
            <el-table-column prop="priority" label="优先级" width="80">
              <template #default="scope">
                <el-tag :type="getPriorityType(scope.row.priority)" size="small">
                  {{ getPriorityText(scope.row.priority) }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="batch-assignment">
          <h4>配送员分配</h4>
          <div class="assignment-options">
            <el-radio-group v-model="batchAssignMode">
              <el-radio label="auto">智能分配</el-radio>
              <el-radio label="manual">手动分配</el-radio>
              <el-radio label="single">统一分配</el-radio>
            </el-radio-group>
          </div>

          <!-- 智能分配 -->
          <div v-if="batchAssignMode === 'auto'" class="auto-assign">
            <p>系统将根据配送员位置、当前订单数量和评分自动分配订单</p>
            <el-button type="primary" @click="autoAssignOrders" :loading="autoAssigning">
              <el-icon><MagicStick /></el-icon>
              开始智能分配
            </el-button>
          </div>

          <!-- 手动分配 -->
          <div v-if="batchAssignMode === 'manual'" class="manual-assign">
            <div v-for="(order, index) in selectedOrders" :key="order.id" class="assignment-item">
              <div class="order-info">
                <span class="order-number">{{ order.orderNumber }}</span>
                <span class="customer">{{ order.customerName }}</span>
              </div>
              <el-select
                v-model="manualAssignments[index]"
                placeholder="选择配送员"
                style="width: 200px"
              >
                <el-option
                  v-for="courier in availableCouriers"
                  :key="courier.id"
                  :label="courier.name"
                  :value="courier.id"
                />
              </el-select>
            </div>
          </div>

          <!-- 统一分配 -->
          <div v-if="batchAssignMode === 'single'" class="single-assign">
            <el-form-item label="选择配送员">
              <el-select v-model="singleCourierId" placeholder="选择配送员" style="width: 300px">
                <el-option
                  v-for="courier in availableCouriers"
                  :key="courier.id"
                  :label="`${courier.name} (当前: ${courier.currentOrders}单)`"
                  :value="courier.id"
                />
              </el-select>
            </el-form-item>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="showBatchAssignDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmBatchAssign" :loading="batchAssigning">
          确认分配
        </el-button>
      </template>
    </el-dialog>

    <!-- 紧急调度对话框 -->
    <el-dialog v-model="showEmergencyDialog" title="紧急调度" width="600px">
      <el-form :model="emergencyForm" label-width="100px">
        <el-form-item label="调度原因" required>
          <el-input
            v-model="emergencyForm.reason"
            type="textarea"
            rows="4"
            placeholder="请输入紧急调度的原因"
          />
        </el-form-item>
        <el-form-item label="优先级">
          <el-radio-group v-model="emergencyForm.priority">
            <el-radio label="high">高优先级</el-radio>
            <el-radio label="urgent">紧急</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="影响订单">
          <el-tag v-for="order in selectedOrders" :key="order.id" style="margin-right: 10px">
            {{ order.orderNumber }}
          </el-tag>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showEmergencyDialog = false">取消</el-button>
        <el-button type="danger" @click="confirmEmergencyDispatch" :loading="emergencyDispatching">
          确认紧急调度
        </el-button>
      </template>
    </el-dialog>

    <!-- 修改优先级对话框 -->
    <el-dialog v-model="showPriorityDialog" title="修改优先级" width="400px">
      <el-form :model="priorityForm" label-width="100px">
        <el-form-item label="订单号">
          <span>{{ currentOrder?.orderNumber }}</span>
        </el-form-item>
        <el-form-item label="当前优先级">
          <el-tag :type="getPriorityType(currentOrder?.priority || '')">
            {{ getPriorityText(currentOrder?.priority || '') }}
          </el-tag>
        </el-form-item>
        <el-form-item label="新优先级" required>
          <el-select v-model="priorityForm.priority" placeholder="选择新的优先级">
            <el-option label="紧急" value="emergency" />
            <el-option label="加急" value="urgent" />
            <el-option label="普通" value="normal" />
          </el-select>
        </el-form-item>
        <el-form-item label="修改原因">
          <el-input v-model="priorityForm.reason" type="textarea" rows="3" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showPriorityDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmPriorityChange" :loading="updatingPriority">
          确认修改
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Download,
  Van,
  Warning,
  ArrowDown,
  MagicStick,
} from '@element-plus/icons-vue'
import { operatorApi, type OrderDispatch, type CourierInfo } from '@/api/operator'
import { orderApi } from '@/api/order'
import { courierApi } from '@/api/courier'
import { 
  getOrderStatusText, 
  getOrderStatusType, 
  getPriorityType, 
  getPriorityText,
  canCancelOrder,
  isValidStatusTransition,
  OrderStatus 
} from '@/utils/orderStatus'
import http from '@/utils/http'

const router = useRouter()

// 响应式数据
const orderList = ref<OrderDispatch[]>([])
const allCouriers = ref<CourierInfo[]>([])
const availableCouriers = ref<CourierInfo[]>([])
const selectedOrders = ref<OrderDispatch[]>([])
const currentOrder = ref<OrderDispatch | null>(null)

// 搜索表单
const searchForm = reactive({
  orderNumber: '',
  customerName: '',
  status: '',
  priority: '',
  courierId: '',
  dateRange: null as [Date, Date] | null,
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
})

const total = ref(0)
const loading = ref(false)

// 对话框控制
const showAssignDialog = ref(false)
const showBatchAssignDialog = ref(false)
const showEmergencyDialog = ref(false)
const showPriorityDialog = ref(false)

// 表单数据
const assignForm = reactive({
  courierId: '',
  notes: '',
})

const batchAssignMode = ref('auto')
const manualAssignments = ref<number[]>([])
const singleCourierId = ref('')

const emergencyForm = reactive({
  reason: '',
  priority: 'high' as 'high' | 'urgent',
})

const priorityForm = reactive({
  priority: '',
  reason: '',
})

// 加载状态
const assigning = ref(false)
const batchAssigning = ref(false)
const autoAssigning = ref(false)
const emergencyDispatching = ref(false)
const updatingPriority = ref(false)

// 生命周期
onMounted(() => {
  loadData()
  loadCouriers()
})

// 在loadData方法中
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: pagination.page,
      pageSize: pagination.size,
      orderStatus: searchForm.status,
      priority: searchForm.priority,
      orderNumber: searchForm.orderNumber,
      receiverName: searchForm.customerName,
      courierId: searchForm.courierId,
      // 时间范围处理
      startTime: searchForm.dateRange?.[0]?.toISOString(),
      endTime: searchForm.dateRange?.[1]?.toISOString(),
    }

    const response = await orderApi.getOrderPage(params)
    if (response.code === 200) {
      orderList.value = response.data.records.map((order: any) => ({
        id: order.id,
        orderNumber: order.orderNumber,
        customerName: order.receiverName,
        customerPhone: order.receiverPhone,
        pickupAddress: order.senderAddress,
        deliveryAddress: order.receiverAddress,
        status: order.orderStatus,
        priority: order.priority || 'normal',
        estimatedTime: order.estimatedDeliveryTime,
        weight: order.weight,
        value: order.totalAmount,
        courierId: order.courierId,
        courierName: order.courierName,
        createTime: order.createTime,
        updateTime: order.updateTime,
      }))
      total.value = response.data.total
    }
  } catch (error) {
    console.error('加载订单数据失败:', error)
    orderList.value = []
    total.value = 0
    ElMessage.error('加载订单数据失败')
  } finally {
    loading.value = false
  }
}

const loadCouriers = async () => {
  try {
    console.log('开始加载配送员列表')
    
    // 尝试主要接口
    try {
      const response = await operatorApi.getCouriers({
        status: 1, // 在线状态
      })
      
      console.log('主要接口响应:', response)
      
      if (response.code === 200) {
        // 处理不同的响应格式
        let couriers: any[] = []
        if (Array.isArray(response.data)) {
          couriers = response.data
        } else if (response.data && typeof response.data === 'object') {
          const data = response.data as any
          if (Array.isArray(data.list)) {
            couriers = data.list
          } else if (Array.isArray(data.records)) {
            couriers = data.records
          }
        }

        allCouriers.value = couriers.map((courier: any) => ({
          id: courier.id,
          name: courier.courierName || courier.realName || courier.name || '未知',
          phone: courier.phone || '',
          status: courier.status === 1 ? 'online' : 'offline',
          location: courier.workArea || courier.currentAddress || '未知',
          currentOrders: courier.currentOrderCount || 0,
          todayCompleted: courier.todayCompletedCount || 0,
          rating: courier.rating || 5.0,
          workingHours: courier.workingHours || '09:00-18:00',
          lastActiveTime: courier.lastActiveTime || courier.updateTime || '',
        }))
        
        console.log('主要接口加载成功:', allCouriers.value)
        return
      }
    } catch (mainError) {
      console.error('主要接口失败:', mainError)
    }

    // 备选接口1：直接调用配送员API
    try {
      console.log('尝试备选接口1: courierApi.getAllCouriers')
      const backupResponse = await courierApi.getAllCouriers({
        page: 1,
        size: 100,
        status: 1,
      })
      
      console.log('备选接口1响应:', backupResponse)
      
      if (backupResponse.code === 200) {
        const data = backupResponse.data as any
        let couriers: any[] = []
        
        if (data.records) {
          couriers = data.records
        } else if (Array.isArray(data)) {
          couriers = data
        }

        allCouriers.value = couriers.map((courier: any) => ({
          id: courier.id,
          name: courier.courierName || courier.realName || '未知',
          phone: courier.phone || '',
          status: courier.status === 1 ? 'online' : 'offline',
          location: courier.workArea || courier.currentAddress || '未知',
          currentOrders: courier.currentOrderCount || 0,
          todayCompleted: courier.todayCompletedCount || 0,
          rating: courier.rating || 5.0,
          workingHours: courier.workingHours || '09:00-18:00',
          lastActiveTime: courier.lastActiveTime || courier.updateTime || '',
        }))
        
        console.log('备选接口1加载成功:', allCouriers.value)
        return
      }
    } catch (backup1Error) {
      console.error('备选接口1失败:', backup1Error)
    }

    // 备选接口2：获取可用配送员
    try {
      console.log('尝试备选接口2: courierApi.getAvailableCouriers')
      const availableResponse = await courierApi.getAvailableCouriers()
      
      console.log('备选接口2响应:', availableResponse)
      
      if ((availableResponse as any).code === 200) {
        const responseData = (availableResponse as any).data
        if (Array.isArray(responseData)) {
          allCouriers.value = responseData.map((courier: any) => ({
            id: courier.id,
            name: courier.courierName || courier.realName || '未知',
            phone: courier.phone || '',
            status: 'online', // 可用配送员默认在线
            location: courier.workArea || '未知',
            currentOrders: courier.currentOrderCount || 0,
            todayCompleted: courier.todayCompletedCount || 0,
            rating: courier.rating || 5.0,
            workingHours: courier.workingHours || '09:00-18:00',
            lastActiveTime: courier.lastActiveTime || courier.updateTime || '',
          }))
          
          console.log('备选接口2加载成功:', allCouriers.value)
          return
        }
      }
    } catch (backup2Error) {
      console.error('备选接口2失败:', backup2Error)
    }

    // 所有接口都失败，设置空数组并提示
    console.error('所有配送员接口都失败')
    allCouriers.value = []
    ElMessage.warning('无法获取配送员数据，请检查网络连接或联系管理员')
    
  } catch (error) {
    console.error('加载配送员数据时发生未知错误:', error)
    allCouriers.value = []
    ElMessage.error('加载配送员数据失败')
  }
}

const refreshData = () => {
  loadData()
  ElMessage.success('数据已刷新')
}

const handleSearch = () => {
  pagination.page = 1
  loadData()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    orderNumber: '',
    customerName: '',
    status: '',
    priority: '',
    courierId: '',
    dateRange: null,
  })
  handleSearch()
}

const handleSelectionChange = (selection: OrderDispatch[]) => {
  selectedOrders.value = selection
}

const handleRowClick = (row: OrderDispatch) => {
  viewOrderDetail(row)
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadData()
}

// 订单操作
const assignOrder = async (order: OrderDispatch) => {
  currentOrder.value = order
  console.log('分配订单:', order.orderNumber)

  try {
    console.log('获取可用配送员，配送地址:', order.deliveryAddress)
    const response = await operatorApi.getAvailableCouriers(order.deliveryAddress)
    console.log('单个分配-配送员响应:', response)

    if (response.code === 200 || response.data?.code === 200) {
      const data = response.data?.data || response.data || []
      availableCouriers.value = Array.isArray(data) ? data : []
      console.log('单个分配-可用配送员:', availableCouriers.value)
    } else {
      throw new Error('获取配送员失败')
    }
  } catch (error) {
    console.error('获取配送员失败:', error)
    console.log('使用所有在线配送员作为备选')
    availableCouriers.value = allCouriers.value.filter((c) => c.status === 'online')
    console.log('备选配送员:', availableCouriers.value)
  }

  if (availableCouriers.value.length === 0) {
    ElMessage.warning('当前没有可用的配送员')
    return
  }

  showAssignDialog.value = true
}

const reassignOrder = (order: OrderDispatch) => {
  assignOrder(order)
}

const confirmAssign = async () => {
  if (!assignForm.courierId || !currentOrder.value) {
    ElMessage.warning('请选择配送员')
    return
  }

  assigning.value = true
  try {
    const response = await operatorApi.assignOrder(
      currentOrder.value.id,
      Number(assignForm.courierId),
      assignForm.notes,
    )
    
    // 修复：统一响应格式检查
    if ((response as any).code === 200 || (response as any).data?.code === 200) {
      ElMessage.success('订单分配成功')
      showAssignDialog.value = false
      loadData()

      // 重置表单
      assignForm.courierId = ''
      assignForm.notes = ''
    } else {
      throw new Error('分配失败')
    }
  } catch (error) {
    console.error('分配失败:', error)
    ElMessage.error('分配失败，请重试')
  } finally {
    assigning.value = false
  }
}

const confirmBatchAssign = async () => {
  if (!selectedOrders.value.length || !singleCourierId.value) {
    ElMessage.warning('请选择订单和配送员')
    return
  }

  console.log('🚀 开始批量分配')
  console.log('📋 分配详情:', {
    selectedOrders: selectedOrders.value.map(order => ({
      id: order.id,
      orderNumber: order.orderNumber,
      status: order.status
    })),
    courierId: singleCourierId.value,
    courierName: availableCouriers.value.find(c => c.id === singleCourierId.value)?.name
  })

  batchAssigning.value = true
  let successCount = 0
  let failCount = 0
  
  try {
    // 逐个分配订单
    for (const order of selectedOrders.value) {
      try {
        console.log(`📝 正在分配订单 ${order.orderNumber} (ID: ${order.id}) 给配送员 ${singleCourierId.value}`)
        
        // 直接调用HTTP接口，不通过API封装
        const response = await http.put(`/order/${order.id}/assign-delivery`, null, {
          params: { courierId: singleCourierId.value }
        })
        
        console.log(`✅ 订单 ${order.orderNumber} 分配成功:`, response)
        successCount++
        
      } catch (error: any) {
        console.error(`❌ 订单 ${order.orderNumber} 分配失败:`, error)
        console.error('❌ 错误详情:', {
          orderId: order.id,
          courierId: singleCourierId.value,
          status: error.response?.status,
          data: error.response?.data
        })
        failCount++
      }
    }
    
    // 显示结果
    if (successCount > 0) {
      const message = failCount > 0 
        ? `部分分配成功：成功 ${successCount} 个，失败 ${failCount} 个`
        : `批量分配成功：共分配 ${successCount} 个订单`
      
      ElMessage.success(message)
      
      // 刷新数据
      await loadData()
      
      // 关闭对话框
      showBatchAssignDialog.value = false
      selectedOrders.value = []
      singleCourierId.value = null
    } else {
      ElMessage.error('所有订单分配都失败了，请检查网络连接或联系管理员')
    }
    
  } catch (error: any) {
    console.error('❌ 批量分配过程出错:', error)
    ElMessage.error(`批量分配失败: ${error.message || '未知错误'}`)
  } finally {
    batchAssigning.value = false
  }
}

const autoAssignOrders = async () => {
  if (selectedOrders.value.length === 0) {
    ElMessage.warning('请先选择要分配的订单')
    return
  }

  if (availableCouriers.value.length === 0) {
    ElMessage.warning('当前没有可用的配送员')
    return
  }

  autoAssigning.value = true
  try {
    console.log('开始智能分配...')

    // 智能分配算法：按配送员当前订单数量平均分配
    const assignments = selectedOrders.value.map((order, index) => {
      // 找到当前订单数量最少的配送员
      const bestCourier = availableCouriers.value.reduce((prev, current) => {
        const prevOrders = prev.currentOrders || 0
        const currentOrders = current.currentOrders || 0
        return prevOrders <= currentOrders ? prev : current
      })

      // 更新配送员的当前订单数（用于下次选择）
      bestCourier.currentOrders = (bestCourier.currentOrders || 0) + 1

      console.log(`订单 ${order.orderNumber} 分配给配送员 ${bestCourier.name}`)

      return {
        orderId: order.id,
        courierId: bestCourier.id,
      }
    })

    console.log('智能分配方案:', assignments)

    const response = await operatorApi.batchAssignOrders(assignments)

    // 修复：统一响应格式检查
    const responseData = (response as any).data || response
    if (responseData.code === 200) {
      ElMessage.success(responseData.message || '智能分配完成')
      showBatchAssignDialog.value = false

      // 等待一下再刷新数据，确保后端已处理完成
      setTimeout(() => {
        loadData()
      }, 1000)

      selectedOrders.value = []
    } else {
      throw new Error(responseData.message || '智能分配失败')
    }
  } catch (error) {
    console.error('智能分配失败:', error)
    ElMessage.error('智能分配失败，请重试')
  } finally {
    autoAssigning.value = false
  }
}

const confirmEmergencyDispatch = async () => {
  if (!emergencyForm.reason) {
    ElMessage.warning('请输入调度原因')
    return
  }

  emergencyDispatching.value = true
  try {
    await operatorApi.emergencyDispatch({
      orderIds: selectedOrders.value.map((o) => o.id),
      reason: emergencyForm.reason,
      priority: emergencyForm.priority,
    })
    ElMessage.success('紧急调度已启动')
    showEmergencyDialog.value = false
    loadData()
    selectedOrders.value = []
  } catch (error) {
    ElMessage.error('紧急调度失败，请重试')
  } finally {
    emergencyDispatching.value = false
  }
}

const handleCommand = (command: string, row: OrderDispatch) => {
  switch (command) {
    case 'detail':
      viewOrderDetail(row)
      break
    case 'priority':
      currentOrder.value = row
      priorityForm.priority = row.priority
      showPriorityDialog.value = true
      break
    case 'notes':
      // 添加备注逻辑
      break
    case 'track':
      // 物流跟踪逻辑
      break
    case 'cancel':
      cancelOrder(row)
      break
  }
}

const viewOrderDetail = (order: OrderDispatch) => {
  // 跳转到订单详情页面
  router.push(`/operator/orders/${order.id}`)
}

const cancelOrder = async (order: OrderDispatch) => {
  try {
    await ElMessageBox.confirm('确定要取消这个订单吗？', '确认操作', {
      type: 'warning',
    })

    // 调用取消订单API
    ElMessage.success('订单已取消')
    loadData()
  } catch (error) {
    // 用户取消操作
  }
}

const confirmPriorityChange = async () => {
  if (!priorityForm.priority || !currentOrder.value) {
    ElMessage.warning('请选择新的优先级')
    return
  }

  updatingPriority.value = true
  try {
    await operatorApi.updateOrderPriority(currentOrder.value.id, priorityForm.priority)
    ElMessage.success('优先级修改成功')
    showPriorityDialog.value = false
    loadData()
  } catch (error) {
    ElMessage.error('修改失败，请重试')
  } finally {
    updatingPriority.value = false
  }
}

const exportOrders = () => {
  ElMessage.info('导出功能开发中')
}
// 打开批量分配对话框
const openBatchAssignDialog = async () => {
  if (selectedOrders.value.length === 0) {
    ElMessage.warning('请先选择要分配的订单')
    return
  }

  console.log('打开批量分配对话框，选中订单数:', selectedOrders.value.length)

  // 重置表单数据
  batchAssignMode.value = 'auto'
  manualAssignments.value = []
  singleCourierId.value = ''

  // 显示加载状态
  const loadingMessage = ElMessage({
    message: '正在加载配送员数据...',
    type: 'info',
    duration: 0,
  })

  try {
    // 加载可用配送员
    console.log('开始加载可用配送员...')
    const response = await operatorApi.getAvailableCouriers()
    console.log('可用配送员API响应:', response)

    // 修复：统一响应格式检查
    if ((response as any).code === 200 || (response as any).data?.code === 200) {
      const responseData = (response as any).data || response
      const data = responseData.data || responseData
      availableCouriers.value = Array.isArray(data) ? data : []
      console.log('可用配送员加载成功:', availableCouriers.value)
    } else {
      // 使用所有在线配送员作为备选
      console.log('使用所有在线配送员作为备选')
      availableCouriers.value = allCouriers.value.filter((c) => c.status === 'online')
      console.log('备选配送员:', availableCouriers.value)
    }

    loadingMessage.close()

    if (availableCouriers.value.length === 0) {
      ElMessage.warning('当前没有可用的配送员，请稍后再试')
      return
    }

    // 初始化手动分配数组
    manualAssignments.value = new Array(selectedOrders.value.length).fill('')

    ElMessage.success(`加载到 ${availableCouriers.value.length} 个可用配送员`)
    showBatchAssignDialog.value = true
  } catch (error) {
    loadingMessage.close()
    console.error('加载配送员失败:', error)
    ElMessage.error('加载配送员数据失败，请检查网络连接或联系管理员')
  }
}

// 工具方法 - 使用统一的状态工具模块
const getStatusType = getOrderStatusType
const getStatusText = getOrderStatusText

const getPriorityType = (priority: string) => {
  switch (priority) {
    case 'emergency':
      return 'danger'
    case 'urgent':
      return 'warning'
    default:
      return 'info'
  }
}

const getPriorityText = (priority: string) => {
  switch (priority) {
    case 'emergency':
      return '紧急'
    case 'urgent':
      return '加急'
    default:
      return '普通'
  }
}

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 简化的批量分配函数
const confirmBatchAssignSimple = async () => {
  if (!selectedOrders.value.length || !singleCourierId.value) {
    ElMessage.warning('请选择订单和配送员')
    return
  }

  console.log('🚀 开始简化批量分配')
  batchAssigning.value = true
  
  let successCount = 0
  let failCount = 0
  
  try {
    // 导入http模块
    const { default: http } = await import('@/utils/http')
    
    for (const order of selectedOrders.value) {
      try {
        console.log(`📝 分配订单 ${order.orderNumber}`)
        
        const response = await http.put(`/order/${order.id}/assign-delivery`, null, {
          params: { courierId: singleCourierId.value }
        })
        
        console.log(`✅ 订单 ${order.orderNumber} 分配成功`)
        successCount++
        
      } catch (error: any) {
        console.error(`❌ 订单 ${order.orderNumber} 分配失败:`, error)
        failCount++
      }
    }
    
    // 显示结果
    if (successCount > 0) {
      const message = failCount > 0 
        ? `部分分配成功：成功 ${successCount} 个，失败 ${failCount} 个`
        : `批量分配成功：共分配 ${successCount} 个订单`
      
      ElMessage.success(message)
      await loadData()
      showBatchAssignDialog.value = false
      selectedOrders.value = []
      singleCourierId.value = null
    } else {
      ElMessage.error('所有订单分配都失败了')
    }
    
  } catch (error: any) {
    console.error('❌ 批量分配失败:', error)
    ElMessage.error(`批量分配失败: ${error.message}`)
  } finally {
    batchAssigning.value = false
  }
}
</script>

<style scoped>
.order-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: none;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.order-summary {
  margin-bottom: 20px;
}

.order-summary h4 {
  margin: 0 0 15px 0;
  color: #333;
}

.courier-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.courier-info .name {
  font-weight: 500;
  margin-right: 10px;
}

.courier-info .location {
  font-size: 12px;
  color: #999;
}

.courier-stats {
  display: flex;
  align-items: center;
  gap: 10px;
}

.courier-stats .orders {
  font-size: 12px;
  color: #666;
}

.batch-assign-container {
  max-height: 600px;
  overflow-y: auto;
}

.selected-orders h4,
.batch-assignment h4 {
  margin: 0 0 15px 0;
  color: #333;
}

.assignment-options {
  margin-bottom: 20px;
}

.auto-assign {
  text-align: center;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.manual-assign {
  max-height: 300px;
  overflow-y: auto;
}

.assignment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.assignment-item:last-child {
  border-bottom: none;
}

.order-info {
  display: flex;
  flex-direction: column;
}

.order-number {
  font-weight: 500;
  color: #333;
}

.customer {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.single-assign {
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 4px;
}
</style>
