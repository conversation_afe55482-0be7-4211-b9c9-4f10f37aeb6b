/**
 * 订单状态工具模块
 * 与后端OrderStatus枚举保持一致
 */

// 订单状态枚举（与后端保持一致）
export enum OrderStatus {
  PENDING = 'PENDING',           // 待处理
  PAID = 'PAID',                 // 已支付
  PICKUP_ASSIGNED = 'PICKUP_ASSIGNED', // 已分配揽件员
  PICKUP = 'PICKUP',             // 已揽收
  TRANSIT = 'TRANSIT',           // 运输中
  IN_TRANSIT = 'IN_TRANSIT',     // 运输中（别名）
  ARRIVED = 'ARRIVED',           // 到达目的地
  DELIVERING = 'DELIVERING',     // 派送中
  OUT_FOR_DELIVERY = 'OUT_FOR_DELIVERY', // 派送中（别名）
  SIGNED = 'SIGNED',             // 已签收
  DELIVERED = 'DELIVERED',       // 已配送（别名）
  CANCELLED = 'CANCELLED',       // 已取消
  EXCEPTION = 'EXCEPTION',       // 异常
  REJECTED = 'REJECTED',         // 拒收
  RETURNED = 'RETURNED',         // 退货
}

// 支付状态枚举
export enum PaymentStatus {
  UNPAID = 0,    // 未支付
  PAID = 1,      // 已支付
  REFUNDED = 2,  // 已退款
  REFUNDING = 3, // 退款中
}

// 服务类型枚举
export enum ServiceType {
  STANDARD = 'STANDARD',   // 标准快递
  EXPRESS = 'EXPRESS',     // 特快专递
  URGENT = 'URGENT',       // 加急配送
  SAME_DAY = 'SAME_DAY',   // 当日达
}

// 订单状态描述映射
export const ORDER_STATUS_MAP: Record<string, string> = {
  [OrderStatus.PENDING]: '待处理',
  [OrderStatus.PAID]: '已支付',
  [OrderStatus.PICKUP_ASSIGNED]: '已分配揽件员',
  [OrderStatus.PICKUP]: '已揽收',
  [OrderStatus.TRANSIT]: '运输中',
  [OrderStatus.IN_TRANSIT]: '运输中',
  [OrderStatus.ARRIVED]: '到达目的地',
  [OrderStatus.DELIVERING]: '派送中',
  [OrderStatus.OUT_FOR_DELIVERY]: '派送中',
  [OrderStatus.SIGNED]: '已签收',
  [OrderStatus.DELIVERED]: '已配送',
  [OrderStatus.CANCELLED]: '已取消',
  [OrderStatus.EXCEPTION]: '异常',
  [OrderStatus.REJECTED]: '拒收',
  [OrderStatus.RETURNED]: '退货',
}

// 支付状态描述映射
export const PAYMENT_STATUS_MAP: Record<number, string> = {
  [PaymentStatus.UNPAID]: '未支付',
  [PaymentStatus.PAID]: '已支付',
  [PaymentStatus.REFUNDED]: '已退款',
  [PaymentStatus.REFUNDING]: '退款中',
}

// 服务类型描述映射
export const SERVICE_TYPE_MAP: Record<string, string> = {
  [ServiceType.STANDARD]: '标准快递',
  [ServiceType.EXPRESS]: '特快专递',
  [ServiceType.URGENT]: '加急配送',
  [ServiceType.SAME_DAY]: '当日达',
}

// 订单状态类型映射（用于UI显示）
export const ORDER_STATUS_TYPE_MAP: Record<string, string> = {
  [OrderStatus.PENDING]: 'warning',
  [OrderStatus.PAID]: 'primary',
  [OrderStatus.PICKUP_ASSIGNED]: 'primary',
  [OrderStatus.PICKUP]: 'success',
  [OrderStatus.TRANSIT]: 'success',
  [OrderStatus.IN_TRANSIT]: 'success',
  [OrderStatus.ARRIVED]: 'success',
  [OrderStatus.DELIVERING]: 'success',
  [OrderStatus.OUT_FOR_DELIVERY]: 'success',
  [OrderStatus.SIGNED]: 'success',
  [OrderStatus.DELIVERED]: 'success',
  [OrderStatus.CANCELLED]: 'danger',
  [OrderStatus.EXCEPTION]: 'danger',
  [OrderStatus.REJECTED]: 'danger',
  [OrderStatus.RETURNED]: 'warning',
}

/**
 * 获取订单状态描述
 */
export function getOrderStatusText(status: string): string {
  return ORDER_STATUS_MAP[status] || ORDER_STATUS_MAP[status?.toUpperCase()] || '未知状态'
}

/**
 * 获取订单状态类型（用于UI显示）
 */
export function getOrderStatusType(status: string): string {
  return ORDER_STATUS_TYPE_MAP[status] || ORDER_STATUS_TYPE_MAP[status?.toUpperCase()] || 'info'
}

/**
 * 获取支付状态描述
 */
export function getPaymentStatusText(status: number): string {
  return PAYMENT_STATUS_MAP[status] || '未知状态'
}

/**
 * 获取服务类型描述
 */
export function getServiceTypeText(type: string): string {
  return SERVICE_TYPE_MAP[type] || SERVICE_TYPE_MAP[type?.toUpperCase()] || '标准快递'
}

/**
 * 检查订单是否可以取消
 */
export function canCancelOrder(status: string): boolean {
  const orderStatus = status?.toUpperCase()
  return orderStatus === OrderStatus.PENDING || orderStatus === OrderStatus.PAID
}

/**
 * 检查订单是否为最终状态
 */
export function isFinalStatus(status: string): boolean {
  const orderStatus = status?.toUpperCase()
  return [
    OrderStatus.SIGNED,
    OrderStatus.DELIVERED,
    OrderStatus.CANCELLED,
    OrderStatus.REJECTED,
    OrderStatus.RETURNED,
  ].includes(orderStatus as OrderStatus)
}

/**
 * 检查订单是否为异常状态
 */
export function isExceptionStatus(status: string): boolean {
  const orderStatus = status?.toUpperCase()
  return [
    OrderStatus.EXCEPTION,
    OrderStatus.REJECTED,
    OrderStatus.RETURNED,
  ].includes(orderStatus as OrderStatus)
}

/**
 * 检查订单是否已完成
 */
export function isOrderCompleted(status: string): boolean {
  return isFinalStatus(status)
}

/**
 * 获取订单状态流程
 */
export function getOrderStatusFlow(): Array<{ status: OrderStatus; text: string; type: string }> {
  return [
    { status: OrderStatus.PENDING, text: '待处理', type: 'warning' },
    { status: OrderStatus.PAID, text: '已支付', type: 'primary' },
    { status: OrderStatus.PICKUP_ASSIGNED, text: '已分配揽件员', type: 'primary' },
    { status: OrderStatus.PICKUP, text: '已揽收', type: 'success' },
    { status: OrderStatus.IN_TRANSIT, text: '运输中', type: 'success' },
    { status: OrderStatus.ARRIVED, text: '到达目的地', type: 'success' },
    { status: OrderStatus.OUT_FOR_DELIVERY, text: '派送中', type: 'success' },
    { status: OrderStatus.DELIVERED, text: '已配送', type: 'success' },
  ]
}

/**
 * 获取下一个可能的状态
 */
export function getNextPossibleStatuses(currentStatus: string): OrderStatus[] {
  const status = currentStatus?.toUpperCase() as OrderStatus
  
  switch (status) {
    case OrderStatus.PENDING:
      return [OrderStatus.PAID, OrderStatus.CANCELLED]
    
    case OrderStatus.PAID:
      return [OrderStatus.PICKUP_ASSIGNED, OrderStatus.CANCELLED]
    
    case OrderStatus.PICKUP_ASSIGNED:
      return [OrderStatus.PICKUP, OrderStatus.CANCELLED]
    
    case OrderStatus.PICKUP:
      return [OrderStatus.IN_TRANSIT, OrderStatus.EXCEPTION]
    
    case OrderStatus.IN_TRANSIT:
      return [OrderStatus.ARRIVED, OrderStatus.EXCEPTION]
    
    case OrderStatus.ARRIVED:
      return [OrderStatus.OUT_FOR_DELIVERY, OrderStatus.EXCEPTION]
    
    case OrderStatus.OUT_FOR_DELIVERY:
      return [OrderStatus.DELIVERED, OrderStatus.REJECTED, OrderStatus.EXCEPTION]
    
    default:
      return []
  }
}

/**
 * 验证状态转换是否合法
 */
export function isValidStatusTransition(fromStatus: string, toStatus: string): boolean {
  const nextStatuses = getNextPossibleStatuses(fromStatus)
  return nextStatuses.includes(toStatus?.toUpperCase() as OrderStatus)
}

/**
 * 获取订单优先级类型
 */
export function getPriorityType(priority: string): string {
  switch (priority?.toLowerCase()) {
    case 'urgent':
      return 'warning'
    case 'emergency':
      return 'danger'
    default:
      return 'info'
  }
}

/**
 * 获取订单优先级文本
 */
export function getPriorityText(priority: string): string {
  switch (priority?.toLowerCase()) {
    case 'urgent':
      return '加急'
    case 'emergency':
      return '紧急'
    default:
      return '普通'
  }
} 