<template>
  <div class="notification-center">
    <!-- 通知铃铛按钮 -->
    <el-dropdown
      trigger="click"
      @visible-change="handleDropdownVisibleChange"
      placement="bottom-end"
      :max-height="400"
    >
      <div class="notification-trigger">
        <el-badge :value="unreadCount" :max="99" :hidden="unreadCount === 0">
          <el-button text size="large" @click="handleTriggerClick">
            <el-icon size="20">
              <Bell v-if="!hasUrgent" />
              <Warning v-else class="urgent-bell" />
            </el-icon>
          </el-button>
        </el-badge>
      </div>

      <template #dropdown>
        <div class="notification-dropdown">
          <!-- 头部 -->
          <div class="notification-header">
            <div class="header-left">
              <h4>通知中心</h4>
              <span class="count-info">{{ unreadCount }} 条未读</span>
            </div>
            <div class="header-actions">
              <el-button text size="small" @click="markAllAsRead" v-if="unreadCount > 0">
                全部已读
              </el-button>
              <el-button text size="small" @click="showSettings = true">
                <el-icon><Setting /></el-icon>
              </el-button>
            </div>
          </div>

          <!-- 过滤标签 -->
          <div class="notification-filters">
            <el-radio-group v-model="activeFilter" size="small" @change="handleFilterChange">
              <el-radio-button label="all">全部</el-radio-button>
              <el-radio-button label="unread">未读</el-radio-button>
              <el-radio-button label="system">系统</el-radio-button>
              <el-radio-button label="order">订单</el-radio-button>
              <el-radio-button label="delivery">配送</el-radio-button>
            </el-radio-group>
          </div>

          <!-- 通知列表 -->
          <div class="notification-list" v-if="filteredNotifications.length > 0">
            <div
              v-for="notification in filteredNotifications.slice(0, 10)"
              :key="notification.id"
              class="notification-item"
              :class="{
                'unread': !notification.read,
                [`priority-${notification.priority}`]: true
              }"
              @click="handleNotificationClick(notification)"
            >
              <div class="notification-icon">
                <el-icon 
                  :size="16" 
                  :color="getNotificationIconColor(notification.type)"
                >
                  <component :is="getNotificationIcon(notification.type)" />
                </el-icon>
              </div>
              
              <div class="notification-content">
                <div class="notification-title">{{ notification.title }}</div>
                <div class="notification-message">{{ notification.message }}</div>
                <div class="notification-meta">
                  <span class="category">{{ getCategoryText(notification.category) }}</span>
                  <span class="time">{{ formatTime(notification.timestamp) }}</span>
                </div>
              </div>
              
              <div class="notification-actions">
                <el-button 
                  text 
                  size="small" 
                  @click.stop="removeNotification(notification.id)"
                >
                  <el-icon><Close /></el-icon>
                </el-button>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="notification-empty">
            <el-icon size="48" color="#ccc"><Bell /></el-icon>
            <p>暂无通知</p>
          </div>

          <!-- 底部操作 -->
          <div class="notification-footer" v-if="notifications.length > 0">
            <el-button text @click="showAllNotifications">查看全部通知</el-button>
            <el-button text type="danger" @click="clearAllNotifications">清空通知</el-button>
          </div>
        </div>
      </template>
    </el-dropdown>

    <!-- 通知设置对话框 -->
    <el-dialog
      v-model="showSettings"
      title="通知设置"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="settingsForm" label-width="120px">
        <el-form-item label="应用内通知">
          <el-switch v-model="settingsForm.enableInApp" />
          <div class="setting-desc">在应用内显示弹窗通知</div>
        </el-form-item>
        
        <el-form-item label="桌面通知">
          <el-switch v-model="settingsForm.enableDesktop" />
          <div class="setting-desc">显示系统桌面通知</div>
        </el-form-item>
        
        <el-form-item label="声音提醒">
          <el-switch v-model="settingsForm.enableSound" />
          <div class="setting-desc">播放通知提示音</div>
        </el-form-item>
        
        <el-form-item label="自动已读">
          <el-switch v-model="settingsForm.autoMarkRead" />
          <div class="setting-desc">点击通知后自动标记为已读</div>
        </el-form-item>
        
        <el-form-item label="最大通知数">
          <el-input-number 
            v-model="settingsForm.maxNotifications" 
            :min="10" 
            :max="500" 
            :step="10"
          />
          <div class="setting-desc">超出限制时自动删除旧通知</div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showSettings = false">取消</el-button>
        <el-button type="primary" @click="saveSettings">保存设置</el-button>
      </template>
    </el-dialog>

    <!-- 全部通知对话框 -->
    <el-dialog
      v-model="showAllDialog"
      title="全部通知"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="all-notifications">
        <div class="notifications-header">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索通知..."
            prefix-icon="Search"
            clearable
            style="width: 300px"
          />
          <div class="header-actions">
            <el-select v-model="categoryFilter" placeholder="分类" clearable style="width: 120px">
              <el-option label="系统" value="system" />
              <el-option label="订单" value="order" />
              <el-option label="配送" value="delivery" />
              <el-option label="用户" value="user" />
            </el-select>
            <el-select v-model="priorityFilter" placeholder="优先级" clearable style="width: 120px">
              <el-option label="紧急" value="urgent" />
              <el-option label="高" value="high" />
              <el-option label="中" value="medium" />
              <el-option label="低" value="low" />
            </el-select>
          </div>
        </div>
        
        <div class="notifications-content">
          <el-table :data="paginatedNotifications" style="width: 100%">
            <el-table-column width="50">
              <template #default="scope">
                <el-icon 
                  :size="16" 
                  :color="getNotificationIconColor(scope.row.type)"
                >
                  <component :is="getNotificationIcon(scope.row.type)" />
                </el-icon>
              </template>
            </el-table-column>
            <el-table-column prop="title" label="标题" width="150" />
            <el-table-column prop="message" label="内容" show-overflow-tooltip />
            <el-table-column prop="category" label="分类" width="80">
              <template #default="scope">
                <el-tag size="small">{{ getCategoryText(scope.row.category) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="priority" label="优先级" width="80">
              <template #default="scope">
                <el-tag :type="getPriorityType(scope.row.priority)" size="small">
                  {{ getPriorityText(scope.row.priority) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="timestamp" label="时间" width="140">
              <template #default="scope">
                {{ formatDateTime(scope.row.timestamp) }}
              </template>
            </el-table-column>
            <el-table-column prop="read" label="状态" width="80">
              <template #default="scope">
                <el-tag :type="scope.row.read ? 'success' : 'warning'" size="small">
                  {{ scope.row.read ? '已读' : '未读' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template #default="scope">
                <el-button 
                  v-if="!scope.row.read"
                  text 
                  size="small" 
                  @click="markAsRead(scope.row.id)"
                >
                  标记已读
                </el-button>
                <el-button 
                  text 
                  size="small" 
                  type="danger"
                  @click="removeNotification(scope.row.id)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="filteredAllNotifications.length"
              layout="total, sizes, prev, pager, next, jumper"
            />
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Bell, Warning, Setting, Close, InfoFilled, SuccessFilled, 
  WarningFilled, CircleCloseFilled
} from '@element-plus/icons-vue'
import { useNotificationStore } from '@/stores/notification'
import type { Notification } from '@/stores/notification'

const notificationStore = useNotificationStore()

// 响应式数据
const activeFilter = ref('all')
const showSettings = ref(false)
const showAllDialog = ref(false)
const searchKeyword = ref('')
const categoryFilter = ref('')
const priorityFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)

// 设置表单
const settingsForm = reactive({
  enableInApp: true,
  enableDesktop: true,
  enableSound: true,
  autoMarkRead: false,
  maxNotifications: 100
})

// 计算属性
const notifications = computed(() => notificationStore.notifications)
const unreadCount = computed(() => notificationStore.unreadCount)

const hasUrgent = computed(() => 
  notifications.value.some(n => !n.read && n.priority === 'urgent')
)

const filteredNotifications = computed(() => {
  let result = notifications.value

  switch (activeFilter.value) {
    case 'unread':
      result = result.filter(n => !n.read)
      break
    case 'system':
    case 'order':
    case 'delivery':
      result = result.filter(n => n.category === activeFilter.value)
      break
  }

  return result
})

const filteredAllNotifications = computed(() => {
  let result = notifications.value

  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(n => 
      n.title.toLowerCase().includes(keyword) || 
      n.message.toLowerCase().includes(keyword)
    )
  }

  if (categoryFilter.value) {
    result = result.filter(n => n.category === categoryFilter.value)
  }

  if (priorityFilter.value) {
    result = result.filter(n => n.priority === priorityFilter.value)
  }

  return result
})

const paginatedNotifications = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredAllNotifications.value.slice(start, end)
})

// 监听设置变化
watch(() => notificationStore.config, (newConfig) => {
  Object.assign(settingsForm, newConfig)
}, { immediate: true, deep: true })

// 方法
const handleDropdownVisibleChange = (visible: boolean) => {
  if (visible) {
    // 下拉框打开时的逻辑
  }
}

const handleTriggerClick = () => {
  // 点击铃铛按钮的逻辑
}

const handleFilterChange = () => {
  // 过滤器变化的逻辑
}

const handleNotificationClick = (notification: Notification) => {
  if (!notification.read) {
    markAsRead(notification.id)
  }
  
  if (notification.onClick) {
    notification.onClick()
  }
}

const markAsRead = (notificationId: string) => {
  notificationStore.markAsRead(notificationId)
}

const markAllAsRead = () => {
  notificationStore.markAllAsRead()
  ElMessage.success('已全部标记为已读')
}

const removeNotification = (notificationId: string) => {
  notificationStore.removeNotification(notificationId)
}

const clearAllNotifications = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有通知吗？', '确认操作', {
      type: 'warning'
    })
    
    notificationStore.clearNotifications()
    ElMessage.success('已清空所有通知')
  } catch (error) {
    // 用户取消操作
  }
}

const showAllNotifications = () => {
  showAllDialog.value = true
}

const saveSettings = () => {
  notificationStore.updateConfig(settingsForm)
  showSettings.value = false
  ElMessage.success('设置已保存')
}

// 工具方法
const getNotificationIcon = (type: string) => {
  switch (type) {
    case 'success': return SuccessFilled
    case 'warning': return WarningFilled
    case 'error': return CircleCloseFilled
    default: return InfoFilled
  }
}

const getNotificationIconColor = (type: string) => {
  switch (type) {
    case 'success': return '#67C23A'
    case 'warning': return '#E6A23C'
    case 'error': return '#F56C6C'
    default: return '#409EFF'
  }
}

const getCategoryText = (category: string) => {
  const categoryMap: Record<string, string> = {
    system: '系统',
    order: '订单',
    delivery: '配送',
    user: '用户'
  }
  return categoryMap[category] || category
}

const getPriorityType = (priority: string) => {
  switch (priority) {
    case 'urgent': return 'danger'
    case 'high': return 'warning'
    case 'medium': return 'primary'
    case 'low': return 'info'
    default: return 'info'
  }
}

const getPriorityText = (priority: string) => {
  const priorityMap: Record<string, string> = {
    urgent: '紧急',
    high: '高',
    medium: '中',
    low: '低'
  }
  return priorityMap[priority] || priority
}

const formatTime = (timestamp: number) => {
  const now = Date.now()
  const diff = now - timestamp
  
  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 24小时内
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return new Date(timestamp).toLocaleDateString()
  }
}

const formatDateTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}
</script>

<style scoped>
.notification-center {
  position: relative;
}

.notification-trigger {
  cursor: pointer;
}

.urgent-bell {
  animation: shake 0.5s ease-in-out infinite alternate;
  color: #F56C6C !important;
}

@keyframes shake {
  0% { transform: rotate(-15deg); }
  100% { transform: rotate(15deg); }
}

.notification-dropdown {
  width: 400px;
  max-height: 500px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.header-left h4 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.count-info {
  font-size: 12px;
  color: #999;
  margin-left: 8px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.notification-filters {
  padding: 12px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.notification-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 20px;
  border-bottom: 1px solid #f8f8f8;
  cursor: pointer;
  transition: background-color 0.2s;
}

.notification-item:hover {
  background-color: #f5f7fa;
}

.notification-item.unread {
  background-color: #f0f9ff;
  border-left: 3px solid #409EFF;
}

.notification-item.priority-urgent {
  border-left-color: #F56C6C;
}

.notification-item.priority-high {
  border-left-color: #E6A23C;
}

.notification-icon {
  margin-right: 12px;
  margin-top: 2px;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.notification-message {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 6px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.notification-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999;
}

.notification-actions {
  margin-left: 8px;
}

.notification-empty {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.notification-empty p {
  margin: 12px 0 0 0;
}

.notification-footer {
  display: flex;
  justify-content: space-between;
  padding: 12px 20px;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.setting-desc {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.all-notifications {
  height: 600px;
  display: flex;
  flex-direction: column;
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.notifications-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style> 