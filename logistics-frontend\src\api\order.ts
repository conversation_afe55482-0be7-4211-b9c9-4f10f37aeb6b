import http from '@/utils/http'
import type { ApiResponse, PageResponse, PageRequest } from '@/types/api'

// 订单基础信息
export interface Order {
  id: number
  orderNumber: string
  userId: number
  senderName: string
  senderPhone: string
  senderAddress: string
  receiverName: string
  receiverPhone: string
  receiverAddress: string
  goodsType: string
  weight: number
  volume: number
  declaredValue: number
  totalAmount: number
  orderStatus: string
  paymentStatus: string
  paymentMethod?: string
  createTime: string
  updateTime: string
  remark?: string
}

// 订单列表项
export interface OrderListItem {
  id: number
  orderNumber: string
  receiverName: string
  receiverAddress: string
  totalAmount: number
  orderStatus: string
  createTime: string
  senderName: string
  senderPhone: string
  goodsType: string
}

// 创建订单DTO
export interface CreateOrderDTO {
  senderName: string
  senderPhone: string
  senderAddress: string
  receiverName: string
  receiverPhone: string
  receiverAddress: string
  goodsType: string
  weight: number
  volume: number
  declaredValue: number
  remark?: string
}

// 订单查询参数
export interface OrderQueryParams extends PageRequest {
  orderStatus?: string
  startDate?: string
  endDate?: string
  userId?: number
}

// 价格计算结果
export interface PriceResult {
  basePrice: number
  weightPrice: number
  volumePrice: number
  insurancePrice: number
  totalPrice: number
}

// 费用计算请求
export interface CalculateFeeRequest {
  // 寄件人信息
  senderName: string
  senderPhone: string
  senderProvince: string
  senderCity: string
  senderDistrict: string
  senderAddress: string

  // 收件人信息
  receiverName: string
  receiverPhone: string
  receiverProvince: string
  receiverCity: string
  receiverDistrict: string
  receiverAddress: string

  // 物品信息
  itemName: string
  itemType: string
  itemWeight: number
  itemVolume: number
  itemValue: number
  isFragile: boolean

  // 服务信息
  serviceType: string
  paymentMethod: string
}

// 费用计算响应
export interface CalculateFeeResponse {
  shippingFee: number
  insuranceFee: number
  packingFee: number
  totalFee: number
  estimatedDeliveryTime?: string
}

// 订单状态日志
export interface OrderStatusLog {
  id: number
  orderId: number
  oldStatus: string
  newStatus: string
  remark: string
  operatorId: number
  operatorName: string
  createTime: string
}

// 订单统计
export interface OrderStatistics {
  totalOrders: number
  pendingOrders: number
  completedOrders: number
  cancelledOrders: number
  totalAmount: number
  todayOrders: number
}

export const orderApi = {
  // 创建订单
  createOrder(data: any): Promise<ApiResponse<Order>> {
    // 转换数据格式以匹配后端
    const requestData = {
      senderName: data.senderName,
      senderPhone: data.senderPhone,
      senderAddress: `${data.senderProvince || ''}${data.senderCity || ''}${data.senderDistrict || ''}${data.senderAddress}`,
      senderProvince: data.senderProvince,
      senderCity: data.senderCity,
      senderDistrict: data.senderDistrict,

      receiverName: data.receiverName,
      receiverPhone: data.receiverPhone,
      receiverAddress: `${data.receiverProvince || ''}${data.receiverCity || ''}${data.receiverDistrict || ''}${data.receiverAddress}`,
      receiverProvince: data.receiverProvince,
      receiverCity: data.receiverCity,
      receiverDistrict: data.receiverDistrict,

      // 添加缺失的物品信息字段
      itemName: data.itemName || '未命名物品', // 添加这个字段
      itemType: data.itemType || '其他',
      itemWeight: data.itemWeight || 1,
      itemVolume: data.itemVolume || 0.001,
      itemValue: data.itemValue || 100,
      isFragile: data.isFragile || false,

      // 服务信息
      serviceType: data.serviceType || 'STANDARD',
      paymentMethod: data.paymentMethod || 'ONLINE',

      // 其他字段（保持兼容性）
      goodsType: data.itemType || '其他',
      weight: data.itemWeight || 1,
      volume: data.itemVolume || 0.001,
      declaredValue: data.itemValue || 100,
      remark: data.remarks || '',
    }

    console.log('发送到后端的订单创建请求:', requestData)
    return http.post('/order/create', requestData)
  },

  // 计算价格（兼容旧接口）
  calculatePrice(data: CreateOrderDTO): Promise<ApiResponse<PriceResult>> {
    return http.post('/order/calculate-price', data)
  },

  // 计算费用（新接口）
  calculateFee(data: CalculateFeeRequest): Promise<ApiResponse<CalculateFeeResponse>> {
    // 构建完整的地址信息
    const senderFullAddress = [
      data.senderProvince,
      data.senderCity,
      data.senderDistrict,
      data.senderAddress,
    ]
      .filter(Boolean)
      .join('')
    const receiverFullAddress = [
      data.receiverProvince,
      data.receiverCity,
      data.receiverDistrict,
      data.receiverAddress,
    ]
      .filter(Boolean)
      .join('')

    // 直接使用表单的真实数据
    const requestData = {
      // 寄件人信息 - 使用真实数据
      senderName: data.senderName,
      senderPhone: data.senderPhone,
      senderAddress: senderFullAddress,
      senderProvince: data.senderProvince,
      senderCity: data.senderCity,
      senderDistrict: data.senderDistrict,

      // 收件人信息 - 使用真实数据
      receiverName: data.receiverName,
      receiverPhone: data.receiverPhone,
      receiverAddress: receiverFullAddress,
      receiverProvince: data.receiverProvince,
      receiverCity: data.receiverCity,
      receiverDistrict: data.receiverDistrict,

      // 物品信息 - 使用真实数据
      itemName: data.itemName,
      itemType: data.itemType,
      itemWeight: data.itemWeight,
      itemVolume: data.itemVolume,
      itemValue: data.itemValue,
      isFragile: data.isFragile,

      // 服务信息 - 使用真实数据
      serviceType: data.serviceType,
      paymentMethod: data.paymentMethod,
    }

    console.log('发送到后端的费用计算请求:', requestData)

    return http
      .post('/order/calculate-price', requestData)
      .then((response) => {
        console.log('费用计算API响应:', response)
        if (response.code === 200) {
          const priceResult = response.data

          // 转换响应格式为前端期望的格式
          const feeResponse: CalculateFeeResponse = {
            shippingFee: Number(priceResult.shippingFee || 0),
            insuranceFee: Number(priceResult.insuranceFee || 0),
            packingFee: Number(priceResult.packingFee || 0),
            totalFee: Number(priceResult.totalFee || 0),
            estimatedDeliveryTime:
              data.serviceType === 'EXPRESS'
                ? '1-2天'
                : data.serviceType === 'URGENT'
                  ? '当日达'
                  : '3-5天',
          }

          return {
            data: {
              code: 200,
              message: '费用计算成功',
              data: feeResponse,
            },
          } as any
        } else {
          console.error('费用计算失败:', response.message)
          throw new Error(response.message || '费用计算失败')
        }
      })
      .catch((error) => {
        console.error('费用计算请求失败:', error)
        throw new Error('费用计算失败')
      })
  },

  // 获取订单详情
  getOrderById(id: number): Promise<ApiResponse<Order>> {
    return http.get(`/order/${id}`)
  },

  // 根据订单号获取订单
  getOrderByNumber(orderNumber: string): Promise<ApiResponse<Order>> {
    return http.get(`/order/number/${orderNumber}`)
  },

  // 分页查询订单
  getOrderPage(params: OrderQueryParams): Promise<ApiResponse<PageResponse<OrderListItem>>> {
    return http.get('/order/page', { params })
  },

  // 获取订单列表（简化版）
  getOrderList(params: {
    page?: number
    size?: number
    orderStatus?: string
    keyword?: string
    startDate?: string
    endDate?: string
  }): Promise<ApiResponse<PageResponse<OrderListItem>>> {
    return http.get('/order/list', { params })
  },

  // 获取我的订单
  getMyOrders(limit: number = 10): Promise<ApiResponse<OrderListItem[]>> {
    return http.get('/order/my-orders', { params: { limit } })
  },

  // 支付订单
  payOrder(orderId: number, paymentMethod: string = 'ONLINE'): Promise<ApiResponse<boolean>> {
    return http.post(`/order/${orderId}/pay`, { paymentMethod })
  },

  // 确认支付（简化版）
  confirmPayment(orderId: number, paymentData: { paymentMethod: string; paymentAmount: number }): Promise<ApiResponse<boolean>> {
    return http.post(`/order/${orderId}/confirm-payment`, paymentData)
  },

  // 取消订单
  cancelOrder(orderId: number, reason?: string): Promise<ApiResponse<boolean>> {
    return http.put(`/order/${orderId}/cancel`, {}, { params: { reason } })
  },

  // 确认收货
  confirmOrder(orderId: number): Promise<ApiResponse<boolean>> {
    return http.put(`/order/${orderId}/confirm`)
  },

  // 获取订单状态日志
  getOrderStatusLog(orderId: number): Promise<ApiResponse<OrderStatusLog[]>> {
    return http.get(`/order/${orderId}/status-log`)
  },

  // 获取订单统计
  getOrderStatistics(): Promise<ApiResponse<OrderStatistics>> {
    return http.get('/order/statistics')
  },

  // 分配揽件员（管理员专用）
  assignPickupCourier(orderId: number, courierId: number): Promise<ApiResponse<boolean>> {
    return http.put(`/order/${orderId}/assign-pickup`, {}, { params: { courierId } })
  },

  // 分配配送员（管理员专用）
  assignDeliveryCourier(orderId: number, courierId: number): Promise<ApiResponse<boolean>> {
    return http.put(`/order/${orderId}/assign-delivery`, {}, { params: { courierId } })
  },

  // 批量更新订单状态（管理员专用）
  batchUpdateStatus(
    orderIds: number[],
    newStatus: string,
    reason?: string,
  ): Promise<ApiResponse<boolean>> {
    return http.put(
      '/order/batch-status',
      {},
      {
        params: {
          orderIds: orderIds.join(','),
          newStatus,
          reason,
        },
      },
    )
  },

  // 获取待处理订单（操作员专用）
  getPendingOrders(params?: {
    page?: number
    size?: number
    priority?: string
    status?: string
    keyword?: string
  }): Promise<ApiResponse<PageResponse<OrderListItem>>> {
    return http.get('/operator/orders/pending', { params })
  },

  // 根据状态获取订单（配送员专用）
  getOrdersByStatus(statuses: string[]): Promise<ApiResponse<OrderListItem[]>> {
    return http.get('/courier/orders', {
      params: { status: statuses.join(',') },
    })
  },
}
