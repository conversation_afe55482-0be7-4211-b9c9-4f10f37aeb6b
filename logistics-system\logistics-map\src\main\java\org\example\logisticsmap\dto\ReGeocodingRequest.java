package org.example.logisticsmap.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 逆地理编码请求DTO
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
public class ReGeocodingRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 经纬度坐标 (格式: 经度,纬度)
     */
    @NotBlank(message = "坐标信息不能为空")
    private String location;

    /**
     * 返回信息详细程度
     * base: 基本信息
     * all: 详细信息
     */
    private String extensions = "all";

    /**
     * 搜索半径 (米)
     */
    private Integer radius = 1000;

    /**
     * 是否返回道路信息
     */
    private Integer roadlevel = 1;

    /**
     * 输出格式
     */
    private String output = "json";

    /**
     * 回调函数
     */
    private String callback;

    /**
     * 创建请求对象
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @return 请求对象
     */
    public static ReGeocodingRequest of(Double longitude, Double latitude) {
        ReGeocodingRequest request = new ReGeocodingRequest();
        request.setLocation(longitude + "," + latitude);
        return request;
    }

    /**
     * 创建请求对象（带半径）
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @param radius    搜索半径
     * @return 请求对象
     */
    public static ReGeocodingRequest of(Double longitude, Double latitude, Integer radius) {
        ReGeocodingRequest request = new ReGeocodingRequest();
        request.setLocation(longitude + "," + latitude);
        request.setRadius(radius);
        return request;
    }

    /**
     * 设置坐标
     *
     * @param longitude 经度
     * @param latitude  纬度
     */
    public void setCoordinate(Double longitude, Double latitude) {
        this.location = longitude + "," + latitude;
    }
} 