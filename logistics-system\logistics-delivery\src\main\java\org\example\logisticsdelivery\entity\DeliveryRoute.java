package org.example.logisticsdelivery.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.logistics.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 配送路线实体
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("delivery_routes")
public class DeliveryRoute extends BaseEntity {

    /**
     * 路线ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 路线编号
     */
    @TableField("route_number")
    private String routeNumber;

    /**
     * 路线名称
     */
    @TableField("route_name")
    private String routeName;

    /**
     * 配送员ID
     */
    @TableField("courier_id")
    private Long courierId;

    /**
     * 路线日期
     */
    @TableField("route_date")
    private LocalDate routeDate;

    /**
     * 开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 起始地址
     */
    @TableField("start_address")
    private String startAddress;

    /**
     * 结束地址
     */
    @TableField("end_address")
    private String endAddress;

    /**
     * 总距离(km)
     */
    @TableField("total_distance")
    private BigDecimal totalDistance;

    /**
     * 预计用时(分钟)
     */
    @TableField("estimated_duration")
    private Integer estimatedDuration;

    /**
     * 实际用时(分钟)
     */
    @TableField("actual_duration")
    private Integer actualDuration;

    /**
     * 任务数量
     */
    @TableField("task_count")
    private Integer taskCount;

    /**
     * 完成数量
     */
    @TableField("completed_count")
    private Integer completedCount;

    /**
     * 路线状态：PLANNING-规划中，STARTED-已开始，COMPLETED-已完成，CANCELLED-已取消
     */
    @TableField("route_status")
    private String routeStatus;

    /**
     * 路线数据(JSON格式)
     */
    @TableField("route_data")
    private String routeData;

    /**
     * 路线点(JSON格式)
     */
    @TableField("route_points")
    private String routePoints;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;
}