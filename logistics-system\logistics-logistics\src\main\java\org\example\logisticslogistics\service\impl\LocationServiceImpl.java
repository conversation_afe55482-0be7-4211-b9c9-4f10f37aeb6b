package org.example.logisticslogistics.service.impl;

import org.example.logisticslogistics.entity.LocationInfo;
import org.example.logisticslogistics.entity.TrackingInfo;
import org.example.logisticslogistics.repository.TrackingInfoCustomRepository;
import org.example.logisticslogistics.service.LocationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 位置管理服务实现
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Service
public class LocationServiceImpl implements LocationService {

    @Autowired
    private TrackingInfoCustomRepository trackingInfoCustomRepository;

    @Override
    public List<TrackingInfo> findByLocationRange(double minLng, double maxLng, double minLat, double maxLat) {
        // 验证坐标范围
        if (!isValidCoordinate(minLng, minLat) || !isValidCoordinate(maxLng, maxLat)) {
            throw new IllegalArgumentException("无效的坐标范围");
        }
        
        if (minLng >= maxLng || minLat >= maxLat) {
            throw new IllegalArgumentException("坐标范围参数错误");
        }
        
        return trackingInfoCustomRepository.findByLocationRange(minLng, maxLng, minLat, maxLat);
    }

    @Override
    public List<TrackingInfo> findNearby(double longitude, double latitude, double radiusKm) {
        // 验证坐标
        if (!isValidCoordinate(longitude, latitude)) {
            throw new IllegalArgumentException("无效的坐标: " + longitude + ", " + latitude);
        }
        
        // 验证半径
        if (radiusKm <= 0 || radiusKm > 1000) {
            throw new IllegalArgumentException("搜索半径必须在0到1000公里之间");
        }
        
        return trackingInfoCustomRepository.findNearby(longitude, latitude, radiusKm);
    }

    @Override
    public List<TrackingInfo> findByCity(String city) {
        if (!StringUtils.hasText(city)) {
            throw new IllegalArgumentException("城市名称不能为空");
        }
        
        // 使用复杂条件查询，只按城市过滤
        return trackingInfoCustomRepository.findByComplexConditions(
                null, city, null, null, null, null, 
                org.springframework.data.domain.Pageable.unpaged()
        ).getContent();
    }

    @Override
    public Double calculateDistance(LocationInfo from, LocationInfo to) {
        if (from == null || to == null) {
            return 0.0;
        }
        
        if (from.getLongitude() == null || from.getLatitude() == null ||
            to.getLongitude() == null || to.getLatitude() == null) {
            return 0.0;
        }

        double lat1 = from.getLatitude().doubleValue();
        double lon1 = from.getLongitude().doubleValue();
        double lat2 = to.getLatitude().doubleValue();
        double lon2 = to.getLongitude().doubleValue();

        return calculateDistanceByHaversine(lat1, lon1, lat2, lon2);
    }

    @Override
    public boolean isValidCoordinate(double longitude, double latitude) {
        // 中国境内经纬度范围大致：
        // 经度：73°33′E 到 135°05′E
        // 纬度：3°51′N 到 53°33′N
        // 这里放宽一些范围以支持国际物流
        return longitude >= -180 && longitude <= 180 && 
               latitude >= -90 && latitude <= 90;
    }

    @Override
    public LocationInfo parseAddress(String address) {
        if (!StringUtils.hasText(address)) {
            return null;
        }
        
        LocationInfo location = new LocationInfo();
        location.setAddress(address);
        
        // 简单的地址解析逻辑，实际项目中应该调用地图API
        // 这里只做基本的省市区解析
        String[] addressParts = parseAddressParts(address);
        if (addressParts.length >= 3) {
            location.setProvince(addressParts[0]);
            location.setCity(addressParts[1]);
            location.setDistrict(addressParts[2]);
        }
        
        return location;
    }

    @Override
    public String formatAddress(LocationInfo location) {
        if (location == null) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        
        if (StringUtils.hasText(location.getProvince())) {
            sb.append(location.getProvince());
        }
        
        if (StringUtils.hasText(location.getCity())) {
            if (sb.length() > 0) sb.append(" ");
            sb.append(location.getCity());
        }
        
        if (StringUtils.hasText(location.getDistrict())) {
            if (sb.length() > 0) sb.append(" ");
            sb.append(location.getDistrict());
        }
        
        if (StringUtils.hasText(location.getAddress())) {
            if (sb.length() > 0) sb.append(" ");
            sb.append(location.getAddress());
        }
        
        return sb.toString();
    }

    @Override
    public List<String> getAllCities() {
        Map<String, Long> cityStats = trackingInfoCustomRepository.countByCity();
        return cityStats.keySet().stream()
                .filter(StringUtils::hasText)
                .sorted()
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getCitiesByProvince(String province) {
        if (!StringUtils.hasText(province)) {
            return new ArrayList<>();
        }
        
        // 这里应该从数据库或配置中获取省市对应关系
        // 暂时返回空列表，实际项目中需要实现具体逻辑
        return new ArrayList<>();
    }

    @Override
    public List<Map<String, Object>> getLocationHeatMapData(String city) {
        if (!StringUtils.hasText(city)) {
            throw new IllegalArgumentException("城市名称不能为空");
        }
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startOfMonth = now.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
        
        return trackingInfoCustomRepository.getHeatMapData(city, startOfMonth, now);
    }

    // 私有辅助方法

    /**
     * 使用 Haversine 公式计算两点间距离
     */
    private double calculateDistanceByHaversine(double lat1, double lon1, double lat2, double lon2) {
        double earthRadius = 6371; // 地球半径（公里）
        
        double dLat = Math.toRadians(lat2 - lat1);
        double dLon = Math.toRadians(lon2 - lon1);
        
        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                   Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2)) *
                   Math.sin(dLon / 2) * Math.sin(dLon / 2);
        
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return earthRadius * c;
    }

    /**
     * 简单的地址解析
     */
    private String[] parseAddressParts(String address) {
        // 这里是简化的地址解析逻辑
        // 实际项目中应该使用更复杂的地址解析算法或调用第三方API
        
        String[] keywords = {"省", "市", "区", "县"};
        String[] result = new String[3];
        
        // 简单的关键词匹配
        int lastIndex = 0;
        int partIndex = 0;
        
        for (String keyword : keywords) {
            int index = address.indexOf(keyword, lastIndex);
            if (index > lastIndex && partIndex < 3) {
                result[partIndex] = address.substring(lastIndex, index + keyword.length());
                lastIndex = index + keyword.length();
                partIndex++;
            }
        }
        
        return result;
    }
} 