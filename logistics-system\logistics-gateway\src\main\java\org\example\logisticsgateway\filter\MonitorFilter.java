package org.example.logisticsgateway.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicLong;
import java.util.HashMap;
import java.util.Map;

/**
 * API监控统计过滤器
 * 收集请求统计、性能监控、访问日志等数据
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Slf4j
@Component
public class MonitorFilter implements GlobalFilter, Ordered {

    @Autowired
    private ReactiveRedisTemplate<String, String> redisTemplate;

    // 内存统计计数器
    private final AtomicLong totalRequests = new AtomicLong(0);
    private final AtomicLong successRequests = new AtomicLong(0);
    private final AtomicLong errorRequests = new AtomicLong(0);

    // 时间格式化器
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter HOUR_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd-HH");

    // Redis键前缀
    private static final String STATS_PREFIX = "gateway:stats:";
    private static final String DAILY_PREFIX = "gateway:daily:";
    private static final String HOURLY_PREFIX = "gateway:hourly:";
    private static final String API_PREFIX = "gateway:api:";

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getURI().getPath();
        String method = request.getMethod().toString();
        String clientIp = getClientIp(request);
        long startTime = System.currentTimeMillis();

        // 增加总请求计数
        totalRequests.incrementAndGet();

        // 记录请求开始
        logRequestStart(request, clientIp, startTime);

        return chain.filter(exchange).then(
                Mono.fromRunnable(() -> {
                    long endTime = System.currentTimeMillis();
                    long duration = endTime - startTime;
                    ServerHttpResponse response = exchange.getResponse();
                    int statusCode = response.getStatusCode().value();

                    // 记录请求完成
                    logRequestEnd(request, response, clientIp, startTime, endTime, duration);

                    // 更新统计数据
                    updateStatistics(path, method, statusCode, duration, clientIp);

                    // 记录慢请求
                    if (duration > 1000) { // 超过1秒的请求
                        logSlowRequest(request, duration, statusCode);
                    }
                })
        ).doOnError(throwable -> {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            // 记录错误
            errorRequests.incrementAndGet();
            logRequestError(request, throwable, clientIp, startTime, endTime, duration);
            
            // 更新错误统计
            updateErrorStatistics(path, method, throwable.getClass().getSimpleName());
        }).then();
    }

    /**
     * 记录请求开始
     */
    private void logRequestStart(ServerHttpRequest request, String clientIp, long startTime) {
        String path = request.getURI().getPath();
        String method = request.getMethod().toString();
        String userAgent = request.getHeaders().getFirst("User-Agent");
        
        log.info("API请求开始 - Method: {}, Path: {}, IP: {}, UserAgent: {}, StartTime: {}", 
                method, path, clientIp, userAgent, startTime);
    }

    /**
     * 记录请求完成
     */
    private void logRequestEnd(ServerHttpRequest request, ServerHttpResponse response, 
                              String clientIp, long startTime, long endTime, long duration) {
        String path = request.getURI().getPath();
        String method = request.getMethod().toString();
        int statusCode = response.getStatusCode().value();
        
        // 判断请求结果
        if (statusCode >= 200 && statusCode < 300) {
            successRequests.incrementAndGet();
        } else if (statusCode >= 400) {
            errorRequests.incrementAndGet();
        }
        
        log.info("API请求完成 - Method: {}, Path: {}, IP: {}, Status: {}, Duration: {}ms, StartTime: {}, EndTime: {}", 
                method, path, clientIp, statusCode, duration, startTime, endTime);
    }

    /**
     * 记录请求错误
     */
    private void logRequestError(ServerHttpRequest request, Throwable throwable, 
                                String clientIp, long startTime, long endTime, long duration) {
        String path = request.getURI().getPath();
        String method = request.getMethod().toString();
        
        log.error("API请求异常 - Method: {}, Path: {}, IP: {}, Duration: {}ms, Error: {}", 
                method, path, clientIp, duration, throwable.getMessage(), throwable);
    }

    /**
     * 记录慢请求
     */
    private void logSlowRequest(ServerHttpRequest request, long duration, int statusCode) {
        String path = request.getURI().getPath();
        String method = request.getMethod().toString();
        
        log.warn("发现慢请求 - Method: {}, Path: {}, Duration: {}ms, Status: {}", 
                method, path, duration, statusCode);
        
        // 异步记录到Redis
        String slowKey = STATS_PREFIX + "slow:" + LocalDateTime.now().format(DATE_FORMATTER);
        String slowData = String.format("%s|%s|%d|%d|%d", 
                method, path, duration, statusCode, System.currentTimeMillis());
        
        redisTemplate.opsForList().rightPush(slowKey, slowData)
                .doOnSuccess(result -> redisTemplate.expire(slowKey, java.time.Duration.ofDays(7)).subscribe())
                .subscribe();
    }

    /**
     * 更新统计数据
     */
    private void updateStatistics(String path, String method, int statusCode, long duration, String clientIp) {
        String now = LocalDateTime.now().format(DATE_FORMATTER);
        String hour = LocalDateTime.now().format(HOUR_FORMATTER);
        
        // 异步更新统计数据，不影响主流程
        Mono.fromRunnable(() -> {
            try {
                // 1. 更新日统计
                updateDailyStats(now, path, method, statusCode, duration);
                
                // 2. 更新小时统计
                updateHourlyStats(hour, path, method, statusCode, duration);
                
                // 3. 更新API统计
                updateApiStats(path, method, statusCode, duration);
                
                // 4. 更新IP统计
                updateIpStats(clientIp, now);
                
            } catch (Exception e) {
                log.error("更新统计数据异常: {}", e.getMessage());
            }
        }).then().subscribe();
    }

    /**
     * 更新日统计
     */
    private void updateDailyStats(String date, String path, String method, int statusCode, long duration) {
        String dailyKey = DAILY_PREFIX + date;
        
        // 总请求数
        redisTemplate.opsForHash().increment(dailyKey, "total", 1).subscribe();
        
        // 成功请求数
        if (statusCode >= 200 && statusCode < 300) {
            redisTemplate.opsForHash().increment(dailyKey, "success", 1).subscribe();
        }
        
        // 错误请求数
        if (statusCode >= 400) {
            redisTemplate.opsForHash().increment(dailyKey, "error", 1).subscribe();
        }
        
        // 响应时间统计
        redisTemplate.opsForHash().increment(dailyKey, "total_duration", duration).subscribe();
        
        // 设置过期时间（30天）
        redisTemplate.expire(dailyKey, java.time.Duration.ofDays(30)).subscribe();
    }

    /**
     * 更新小时统计
     */
    private void updateHourlyStats(String hour, String path, String method, int statusCode, long duration) {
        String hourlyKey = HOURLY_PREFIX + hour;
        
        redisTemplate.opsForHash().increment(hourlyKey, "total", 1).subscribe();
        
        if (statusCode >= 200 && statusCode < 300) {
            redisTemplate.opsForHash().increment(hourlyKey, "success", 1).subscribe();
        }
        
        if (statusCode >= 400) {
            redisTemplate.opsForHash().increment(hourlyKey, "error", 1).subscribe();
        }
        
        redisTemplate.opsForHash().increment(hourlyKey, "total_duration", duration).subscribe();
        
        // 设置过期时间（7天）
        redisTemplate.expire(hourlyKey, java.time.Duration.ofDays(7)).subscribe();
    }

    /**
     * 更新API统计
     */
    private void updateApiStats(String path, String method, int statusCode, long duration) {
        String apiKey = API_PREFIX + method + ":" + path;
        
        redisTemplate.opsForHash().increment(apiKey, "total", 1).subscribe();
        
        if (statusCode >= 200 && statusCode < 300) {
            redisTemplate.opsForHash().increment(apiKey, "success", 1).subscribe();
        }
        
        if (statusCode >= 400) {
            redisTemplate.opsForHash().increment(apiKey, "error", 1).subscribe();
        }
        
        redisTemplate.opsForHash().increment(apiKey, "total_duration", duration).subscribe();
        
        // 记录最大响应时间
        redisTemplate.opsForHash().get(apiKey, "max_duration")
                .cast(String.class)
                .defaultIfEmpty("0")
                .map(Long::parseLong)
                .filter(maxDuration -> duration > maxDuration)
                .flatMap(maxDuration -> redisTemplate.opsForHash().put(apiKey, "max_duration", String.valueOf(duration)))
                .subscribe();
        
        // 设置过期时间（30天）
        redisTemplate.expire(apiKey, java.time.Duration.ofDays(30)).subscribe();
    }

    /**
     * 更新IP统计
     */
    private void updateIpStats(String clientIp, String date) {
        String ipKey = STATS_PREFIX + "ip:" + date;
        redisTemplate.opsForHash().increment(ipKey, clientIp, 1).subscribe();
        redisTemplate.expire(ipKey, java.time.Duration.ofDays(30)).subscribe();
    }

    /**
     * 更新错误统计
     */
    private void updateErrorStatistics(String path, String method, String errorType) {
        String now = LocalDateTime.now().format(DATE_FORMATTER);
        String errorKey = STATS_PREFIX + "error:" + now;
        String errorField = method + ":" + path + ":" + errorType;
        
        redisTemplate.opsForHash().increment(errorKey, errorField, 1).subscribe();
        redisTemplate.expire(errorKey, java.time.Duration.ofDays(7)).subscribe();
    }

    /**
     * 获取客户端IP
     */
    private String getClientIp(ServerHttpRequest request) {
        String xForwardedFor = request.getHeaders().getFirst("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIP = request.getHeaders().getFirst("X-Real-IP");
        if (xRealIP != null && !xRealIP.isEmpty()) {
            return xRealIP;
        }

        return request.getRemoteAddress() != null ?
                request.getRemoteAddress().getAddress().getHostAddress() : "unknown";
    }

    /**
     * 获取内存统计数据
     */
    public Map<String, Long> getMemoryStats() {
        Map<String, Long> stats = new HashMap<>();
        stats.put("totalRequests", totalRequests.get());
        stats.put("successRequests", successRequests.get());
        stats.put("errorRequests", errorRequests.get());
        return stats;
    }

    @Override
    public int getOrder() {
        return -80; // 在限流过滤器之后执行
    }
} 