<template>
  <div class="courier-dashboard">
    <!-- 顶部状态栏 -->
    <div class="status-bar">
      <div class="courier-info">
        <el-avatar :size="50" :src="courierInfo?.avatar">
          {{ courierInfo?.courierName?.charAt(0) }}
        </el-avatar>
        <div class="info-text">
          <h3>{{ courierInfo?.courierName }}</h3>
          <p>工号：{{ courierInfo?.workNumber }}</p>
        </div>
      </div>

      <div class="status-controls">
        <div class="status-switch">
          <span>工作状态：</span>
          <el-select v-model="currentStatus" @change="updateStatus" size="small">
            <el-option label="离线" :value="0" />
            <el-option label="在线" :value="1" />
            <el-option label="忙碌" :value="2" />
            <el-option label="休息" :value="3" />
          </el-select>
        </div>

        <div class="clock-buttons">
          <el-button v-if="!isClockedIn" type="success" @click="clockIn" :loading="clockLoading">
            签到
          </el-button>
          <el-button v-else type="warning" @click="clockOut" :loading="clockLoading">
            签退
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon today">
              <el-icon size="30"><Calendar /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.todayTasks }}</div>
              <div class="stat-label">今日任务</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon completed">
              <el-icon size="30"><Check /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.todayCompleted }}</div>
              <div class="stat-label">今日完成</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon progress">
              <el-icon size="30"><Clock /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.inProgressTasks }}</div>
              <div class="stat-label">进行中</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon rate">
              <el-icon size="30"><TrendCharts /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.successRate }}%</div>
              <div class="stat-label">完成率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 主要内容区域 -->
    <el-row :gutter="20" class="main-content">
      <!-- 左侧：当前任务 -->
      <el-col :span="14">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>当前任务</span>
              <div class="header-actions">
                <el-button @click="refreshTasks" :icon="Refresh" size="small">刷新</el-button>
                <el-button @click="$router.push('/courier/tasks')" size="small">查看全部</el-button>
              </div>
            </div>
          </template>

          <div v-if="activeTasks.length === 0" class="empty-state">
            <el-empty description="暂无进行中的任务">
              <el-button type="primary" @click="$router.push('/courier/tasks')">
                查看可接任务
              </el-button>
            </el-empty>
          </div>

          <div v-else class="task-list">
            <div
              v-for="task in activeTasks"
              :key="task.id"
              class="task-item"
              @click="viewTaskDetail(task)"
            >
              <div class="task-header">
                <div class="task-info">
                  <span class="task-number">{{ task.taskNumber }}</span>
                  <el-tag :type="getTaskTypeTag(task.taskType)" size="small">
                    {{ getTaskTypeText(task.taskType) }}
                  </el-tag>
                  <el-tag :type="getStatusTag(task.taskStatus)" size="small">
                    {{ getStatusText(task.taskStatus) }}
                  </el-tag>
                </div>
                <div class="task-priority">
                  <el-tag :type="getPriorityTag(task.priority)" size="small">
                    {{ getPriorityText(task.priority) }}
                  </el-tag>
                </div>
              </div>

              <div class="task-addresses">
                <div class="address-item">
                  <el-icon><Position /></el-icon>
                  <span>取件：{{ task.senderAddress }}</span>
                </div>
                <div class="address-item">
                  <el-icon><LocationFilled /></el-icon>
                  <span>送达：{{ task.receiverAddress }}</span>
                </div>
              </div>

              <div class="task-contact">
                <span>联系人：{{ task.receiverName }} {{ task.receiverPhone }}</span>
                <span v-if="task.estimatedTime" class="estimated-time">
                  预计：{{ formatTime(task.estimatedTime) }}
                </span>
              </div>

              <div class="task-actions">
                <el-button
                  v-if="task.taskStatus === 'ASSIGNED'"
                  type="primary"
                  size="small"
                  @click.stop="startTask(task)"
                >
                  开始任务
                </el-button>
                <el-button
                  v-if="task.taskStatus === 'IN_TRANSIT'"
                  type="success"
                  size="small"
                  @click.stop="completeTask(task)"
                >
                  完成任务
                </el-button>
                <el-button size="small" @click.stop="viewTaskDetail(task)"> 查看详情 </el-button>
                <el-button type="danger" size="small" @click.stop="showCancelDialog(task)">
                  取消任务
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：快速操作和通知 -->
      <el-col :span="10">
        <!-- 快速操作 -->
        <el-card class="quick-actions-card">
          <template #header>
            <span>快速操作</span>
          </template>

          <div class="quick-actions">
            <el-button type="primary" class="action-btn" @click="$router.push('/courier/tasks')">
              <el-icon><List /></el-icon>
              任务列表
            </el-button>

            <el-button class="action-btn" @click="$router.push('/courier/route')">
              <el-icon><Guide /></el-icon>
              路线规划
            </el-button>

            <el-button class="action-btn" @click="$router.push('/courier/history')">
              <el-icon><Document /></el-icon>
              配送历史
            </el-button>

            <el-button class="action-btn" @click="$router.push('/courier/profile')">
              <el-icon><User /></el-icon>
              个人信息
            </el-button>
          </div>
        </el-card>

        <!-- 今日概览 -->
        <el-card class="today-overview">
          <template #header>
            <span>今日概览</span>
          </template>

          <div class="overview-content">
            <div class="progress-item">
              <div class="progress-header">
                <span>任务完成进度</span>
                <span>{{ statistics.todayCompleted }}/{{ statistics.todayTasks }}</span>
              </div>
              <el-progress
                :percentage="
                  statistics.todayTasks > 0
                    ? Math.round((statistics.todayCompleted / statistics.todayTasks) * 100)
                    : 0
                "
                :stroke-width="8"
              />
            </div>

            <div class="overview-stats">
              <div class="stat-row">
                <span>总完成任务：</span>
                <span class="stat-value">{{ statistics.completedTasks }}</span>
              </div>
              <div class="stat-row">
                <span>平均评分：</span>
                <span class="stat-value">{{ statistics.avgRating }}/5.0</span>
              </div>
              <div class="stat-row">
                <span>工作区域：</span>
                <span class="stat-value">{{ courierInfo?.workArea }}</span>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 系统通知 -->
        <el-card class="notifications-card">
          <template #header>
            <span>系统通知</span>
          </template>

          <div class="notifications">
            <div class="notification-item">
              <el-icon class="notification-icon"><Bell /></el-icon>
              <div class="notification-content">
                <div class="notification-title">新任务提醒</div>
                <div class="notification-desc">您有新的配送任务待处理</div>
                <div class="notification-time">5分钟前</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 任务完成对话框 -->
    <el-dialog v-model="showCompleteDialog" title="完成任务" width="500px">
      <el-form :model="completeForm" label-width="80px">
        <el-form-item label="任务编号">
          <el-input v-model="completeForm.taskNumber" readonly />
        </el-form-item>

        <el-form-item label="完成凭证">
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :on-change="handleFileChange"
            :show-file-list="false"
            accept="image/*"
          >
            <el-button>选择图片</el-button>
          </el-upload>
          <div v-if="completeForm.proofImage" class="proof-preview">
            <img :src="completeForm.proofImage" alt="完成凭证" />
          </div>
        </el-form-item>

        <el-form-item label="备注">
          <el-input v-model="completeForm.remarks" type="textarea" placeholder="请输入完成备注" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showCompleteDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmComplete" :loading="completing">
          确认完成
        </el-button>
      </template>
    </el-dialog>

    <!-- 取消任务对话框 -->
    <el-dialog v-model="showCancelTaskDialog" title="取消任务" width="400px">
      <el-form label-width="80px">
        <el-form-item label="取消原因" required>
          <el-select v-model="cancelReason" placeholder="请选择取消原因" style="width: 100%">
            <el-option label="客户要求取消" value="customer_cancel" />
            <el-option label="地址有误" value="wrong_address" />
            <el-option label="联系不上客户" value="cannot_contact" />
            <el-option label="货物损坏" value="goods_damaged" />
            <el-option label="其他原因" value="other" />
          </el-select>
        </el-form-item>

        <el-form-item label="详细说明">
          <el-input v-model="cancelDescription" type="textarea" placeholder="请详细说明取消原因" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showCancelTaskDialog = false">取消</el-button>
        <el-button type="danger" @click="confirmCancel" :loading="cancelling"> 确认取消 </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Calendar,
  Check,
  Clock,
  TrendCharts,
  Refresh,
  Position,
  LocationFilled,
  List,
  Guide,
  Document,
  User,
  Bell,
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { courierApi, type DeliveryTask, type CourierInfo, type TaskStatistics } from '@/api/courier'
import dayjs from 'dayjs'

const router = useRouter()
const authStore = useAuthStore()

// 状态
const loading = ref(false)
const clockLoading = ref(false)
const completing = ref(false)
const cancelling = ref(false)
const courierInfo = ref<CourierInfo | null>(null)
const activeTasks = ref<DeliveryTask[]>([])
const statistics = ref<TaskStatistics>({
  totalTasks: 0,
  completedTasks: 0,
  pendingTasks: 0,
  inProgressTasks: 0,
  todayTasks: 0,
  todayCompleted: 0,
  successRate: 0,
  avgRating: 0,
})

// 对话框状态
const showCompleteDialog = ref(false)
const showCancelTaskDialog = ref(false)
const currentTaskForAction = ref<DeliveryTask | null>(null)

// 表单
const completeForm = reactive({
  taskId: 0,
  taskNumber: '',
  proofImage: '',
  remarks: '',
})

const cancelReason = ref('')
const cancelDescription = ref('')

// 工作状态
const currentStatus = ref(1) // 默认在线
const isClockedIn = ref(false)

// 计算属性
const userInfo = computed(() => authStore.userInfo)

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    console.log('配送员工作台开始加载数据...')
    
    // 并行加载统计数据和任务数据
    await Promise.all([
      loadStatistics(),
      loadTodayTasks(),
      loadActiveTasks()
    ])
    
    console.log('配送员工作台数据加载完成')
  } catch (error) {
    console.error('加载配送员工作台数据失败:', error)
    ElMessage.error('加载数据失败，请刷新重试')
  } finally {
    loading.value = false
  }
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    console.log('开始加载配送员统计数据...')
    const response = await courierApi.getTaskStatistics()
    
    if (response.code === 200) {
      statistics.value = response.data
      console.log('配送员统计数据加载成功:', statistics.value)
    } else {
      throw new Error(response.message || '获取统计数据失败')
    }
  } catch (error) {
    console.error('获取配送员统计数据失败:', error)
    // 设置默认统计数据
    statistics.value = {
      todayTasks: 0,
      completedTasks: 0,
      pendingTasks: 0,
      todayCompleted: 0,
      avgRating: 5.0,
      todayEarnings: 0,
      totalEarnings: 0,
      onlineHours: 0,
      thisMonthTasks: 0,
      thisMonthEarnings: 0,
    }
    ElMessage.warning('获取统计数据失败，显示默认数据')
  }
}

// 加载今日任务
const loadTodayTasks = async () => {
  try {
    console.log('开始加载今日任务...')
    const response = await courierApi.getTodayTasks()
    
    if (response.code === 200) {
      todayTasks.value = response.data || []
      console.log('今日任务加载成功:', todayTasks.value.length, '个任务')
    } else {
      throw new Error(response.message || '获取今日任务失败')
    }
  } catch (error) {
    console.error('获取今日任务失败:', error)
    todayTasks.value = []
    ElMessage.warning('获取今日任务失败')
  }
}

// 加载进行中的任务
const loadActiveTasks = async () => {
  try {
    console.log('开始加载进行中任务...')
    const response = await courierApi.getActiveTasks()
    
    if (response.code === 200) {
      activeTasks.value = response.data || []
      console.log('进行中任务加载成功:', activeTasks.value.length, '个任务')
    } else {
      throw new Error(response.message || '获取进行中任务失败')
    }
  } catch (error) {
    console.error('获取进行中任务失败:', error)
    activeTasks.value = []
    ElMessage.warning('获取进行中任务失败')
  }
}

// 接受任务
const acceptTask = async (task: any) => {
  try {
    console.log('接受任务:', task.id)
    
    const response = await courierApi.acceptTask(task.id)
    if (response.code === 200) {
      ElMessage.success('任务接受成功')
      
      // 使用订单生命周期管理器
      await OrderLifecycleManager.acceptTaskWithTracking(task.id, task.orderNumber)
      
      // 刷新数据
      await loadData()
    } else {
      throw new Error(response.message || '接受任务失败')
    }
  } catch (error) {
    console.error('接受任务失败:', error)
    ElMessage.error('接受任务失败：' + error.message)
  }
}

// 开始任务
const startTask = async (task: any) => {
  try {
    console.log('开始任务:', task.id)
    
    const response = await courierApi.startTask(task.id)
    if (response.code === 200) {
      ElMessage.success('任务已开始')
      
      // 使用订单生命周期管理器
      await OrderLifecycleManager.startDeliveryWithTracking(task.id, task.orderNumber)
      
      // 刷新数据
      await loadData()
    } else {
      throw new Error(response.message || '开始任务失败')
    }
  } catch (error) {
    console.error('开始任务失败:', error)
    ElMessage.error('开始任务失败：' + error.message)
  }
}

// 更新工作状态
const updateStatus = async (status: number) => {
  try {
    await courierApi.updateStatus(status)
    ElMessage.success('状态更新成功')
  } catch (error) {
    console.error('更新状态失败:', error)
    ElMessage.error('状态更新失败')
    // 恢复原状态
    currentStatus.value = courierInfo.value?.status || 1
  }
}

// 签到
const clockIn = async () => {
  clockLoading.value = true
  try {
    // 获取位置信息
    const position = await getCurrentPosition()
    await courierApi.clockInOut('IN', {
      longitude: position.longitude,
      latitude: position.latitude,
    })

    isClockedIn.value = true
    ElMessage.success('签到成功')
  } catch (error) {
    console.error('签到失败:', error)
    ElMessage.error('签到失败')
  } finally {
    clockLoading.value = false
  }
}

// 签退
const clockOut = async () => {
  clockLoading.value = true
  try {
    const position = await getCurrentPosition()
    await courierApi.clockInOut('OUT', {
      longitude: position.longitude,
      latitude: position.latitude,
    })

    isClockedIn.value = false
    ElMessage.success('签退成功')
  } catch (error) {
    console.error('签退失败:', error)
    ElMessage.error('签退失败')
  } finally {
    clockLoading.value = false
  }
}

// 获取当前位置
const getCurrentPosition = (): Promise<{ longitude: number; latitude: number }> => {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error('浏览器不支持地理位置'))
      return
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        resolve({
          longitude: position.coords.longitude,
          latitude: position.coords.latitude,
        })
      },
      (error) => {
        reject(error)
      },
    )
  })
}

// 完成任务
const completeTask = (task: DeliveryTask) => {
  currentTaskForAction.value = task
  completeForm.taskId = task.id
  completeForm.taskNumber = task.taskNumber
  completeForm.proofImage = ''
  completeForm.remarks = ''
  showCompleteDialog.value = true
}

// 确认完成任务
const confirmComplete = async () => {
  if (!completeForm.proofImage) {
    ElMessage.warning('请上传完成凭证')
    return
  }

  completing.value = true
  try {
    await courierApi.completeTask({
      taskId: completeForm.taskId,
      completionProof: completeForm.proofImage,
      remarks: completeForm.remarks,
    })

    ElMessage.success('任务完成成功')
    showCompleteDialog.value = false
    refreshTasks()
    loadStatistics()
  } catch (error) {
    console.error('完成任务失败:', error)
    ElMessage.error('完成任务失败')
  } finally {
    completing.value = false
  }
}

// 显示取消对话框
const showCancelDialog = (task: DeliveryTask) => {
  currentTaskForAction.value = task
  cancelReason.value = ''
  cancelDescription.value = ''
  showCancelTaskDialog.value = true
}

// 确认取消任务
const confirmCancel = async () => {
  if (!cancelReason.value) {
    ElMessage.warning('请选择取消原因')
    return
  }

  cancelling.value = true
  try {
    const reason = `${cancelReason.value}: ${cancelDescription.value}`
    await courierApi.cancelTask(currentTaskForAction.value!.id, reason)

    ElMessage.success('任务已取消')
    showCancelTaskDialog.value = false
    refreshTasks()
    loadStatistics()
  } catch (error) {
    console.error('取消任务失败:', error)
    ElMessage.error('取消任务失败')
  } finally {
    cancelling.value = false
  }
}

// 处理文件上传
const handleFileChange = (file: any) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    completeForm.proofImage = e.target?.result as string
  }
  reader.readAsDataURL(file.raw)
}

// 查看任务详情
const viewTaskDetail = (task: DeliveryTask) => {
  router.push(`/courier/task/detail/${task.id}`)
}

// 刷新任务
const refreshTasks = () => {
  loadActiveTasks()
}

// 状态转换工具方法
const CourierStatusUtils = {
  // 数字状态转文本
  getStatusText(status: number | string): string {
    const statusMap: Record<string | number, string> = {
      0: '离线',
      1: '在线',
      2: '忙碌',
      3: '休息',
      offline: '离线',
      online: '在线',
      busy: '忙碌',
      rest: '休息',
    }
    return statusMap[status] || '未知'
  },

  // 状态对应的标签类型
  getStatusType(status: number | string): string {
    const typeMap: Record<string | number, string> = {
      0: 'info',
      1: 'success',
      2: 'warning',
      3: 'info',
      offline: 'info',
      online: 'success',
      busy: 'warning',
      rest: 'info',
    }
    return typeMap[status] || 'info'
  },

  // 文本状态转数字
  getStatusValue(status: string): number {
    const valueMap: Record<string, number> = {
      offline: 0,
      online: 1,
      busy: 2,
      rest: 3,
    }
    return valueMap[status] ?? 0
  },

  // 数字状态转文本状态
  getStatusKey(status: number): string {
    const keyMap: Record<number, string> = {
      0: 'offline',
      1: 'online',
      2: 'busy',
      3: 'rest',
    }
    return keyMap[status] || 'offline'
  },
}

// 任务状态工具方法
const TaskStatusUtils = {
  getStatusText(status: string): string {
    const statusMap: Record<string, string> = {
      PENDING: '待分配',
      ASSIGNED: '已分配',
      ACCEPTED: '已接受',
      PICKED_UP: '已揽收',
      IN_TRANSIT: '运输中',
      OUT_FOR_DELIVERY: '派送中',
      DELIVERED: '已送达',
      COMPLETED: '已完成',
      CANCELLED: '已取消',
      FAILED: '失败',
    }
    return statusMap[status] || status
  },

  getStatusType(status: string): string {
    const typeMap: Record<string, string> = {
      PENDING: 'info',
      ASSIGNED: 'warning',
      ACCEPTED: 'primary',
      PICKED_UP: 'primary',
      IN_TRANSIT: 'warning',
      OUT_FOR_DELIVERY: 'warning',
      DELIVERED: 'success',
      COMPLETED: 'success',
      CANCELLED: 'danger',
      FAILED: 'danger',
    }
    return typeMap[status] || 'info'
  },
}

const formatTime = (time: string) => {
  return dayjs(time).format('MM-DD HH:mm')
}

onMounted(() => {
  loadCourierInfo()
  loadActiveTasks()
  loadStatistics()

  // 模拟签到状态
  isClockedIn.value = true
})
</script>

<style scoped>
.courier-dashboard {
  padding: 20px;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.courier-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.info-text h3 {
  margin: 0 0 5px 0;
  color: #333;
}

.info-text p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.status-controls {
  display: flex;
  align-items: center;
  gap: 20px;
}

.status-switch {
  display: flex;
  align-items: center;
  gap: 10px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  height: 100px;
}

.stat-item {
  display: flex;
  align-items: center;
  height: 100%;
  gap: 15px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.today {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.progress {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.rate {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.main-content {
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.task-list {
  max-height: 600px;
  overflow-y: auto;
}

.task-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s;
}

.task-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.task-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-number {
  font-weight: bold;
  color: #333;
}

.task-addresses {
  margin-bottom: 12px;
}

.address-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 14px;
  color: #666;
}

.task-contact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
  color: #666;
}

.estimated-time {
  color: #f56c6c;
}

.task-actions {
  display: flex;
  gap: 8px;
}

.quick-actions-card {
  margin-bottom: 20px;
}

.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.action-btn {
  width: 100%;
  height: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.today-overview {
  margin-bottom: 20px;
}

.overview-content {
  padding: 10px 0;
}

.progress-item {
  margin-bottom: 20px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}

.overview-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.stat-value {
  font-weight: bold;
  color: #333;
}

.notifications-card {
  margin-bottom: 20px;
}

.notifications {
  max-height: 200px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-icon {
  color: #409eff;
  margin-top: 2px;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.notification-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.notification-time {
  font-size: 12px;
  color: #999;
}

.proof-preview {
  margin-top: 10px;
}

.proof-preview img {
  max-width: 200px;
  max-height: 200px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}
</style>
