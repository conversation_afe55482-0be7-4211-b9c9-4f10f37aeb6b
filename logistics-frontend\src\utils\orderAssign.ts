import http from '@/utils/http'

/**
 * 完整的订单分配流程
 * 确保分配后配送员能收到任务
 */
export async function assignOrderToCourier(orderId: number, courierId: number): Promise<boolean> {
  console.log(`🚀 开始完整的订单分配流程`)
  console.log(`📋 分配参数:`, { orderId, courierId })
  
  try {
    // 1. 分配订单给配送员
    console.log('📝 第1步：分配订单给配送员')
    const assignResponse = await http.put(`/order/${orderId}/assign-delivery`, null, {
      params: { courierId }
    })
    
    console.log('✅ 分配响应:', assignResponse)
    
    if (!assignResponse || (assignResponse.code !== 200 && assignResponse.status !== 200)) {
      throw new Error('订单分配失败')
    }
    
    // 2. 创建配送任务（如果后端支持）
    console.log('📝 第2步：创建配送任务')
    try {
      await http.post('/delivery/task/create-from-order', {
        orderId,
        courierId,
        taskType: 'DELIVERY'
      })
      console.log('✅ 配送任务创建成功')
    } catch (taskError) {
      console.warn('⚠️ 配送任务创建失败，但订单分配成功:', taskError)
      // 不抛出错误，因为主要的分配已经成功
    }
    
    // 3. 更新轨迹状态（如果后端支持）
    console.log('📝 第3步：更新轨迹状态')
    try {
      await http.post('/logistics/tracking/update-status', {
        orderId,
        status: 'PICKUP_ASSIGNED',
        description: `订单已分配给配送员，配送员ID: ${courierId}`,
        operatorName: '系统操作员'
      })
      console.log('✅ 轨迹状态更新成功')
    } catch (trackingError) {
      console.warn('⚠️ 轨迹状态更新失败，但订单分配成功:', trackingError)
      // 不抛出错误，因为主要的分配已经成功
    }
    
    console.log('🎉 订单分配流程完成')
    return true
    
  } catch (error: any) {
    console.error('❌ 订单分配流程失败:', error)
    console.error('❌ 错误详情:', {
      orderId,
      courierId,
      error: error.message,
      response: error.response?.data
    })
    throw error
  }
}

/**
 * 批量订单分配
 */
export async function batchAssignOrders(assignments: Array<{ orderId: number; courierId: number }>): Promise<{
  successCount: number
  failCount: number
  results: Array<{ success: boolean; orderId: number; error?: any }>
}> {
  console.log('🚀 开始批量订单分配:', assignments)
  
  const results = []
  let successCount = 0
  let failCount = 0
  
  for (const assignment of assignments) {
    try {
      console.log(`📝 分配订单 ${assignment.orderId} 给配送员 ${assignment.courierId}`)
      await assignOrderToCourier(assignment.orderId, assignment.courierId)
      
      results.push({ success: true, orderId: assignment.orderId })
      successCount++
      console.log(`✅ 订单 ${assignment.orderId} 分配成功`)
      
    } catch (error) {
      results.push({ success: false, orderId: assignment.orderId, error })
      failCount++
      console.error(`❌ 订单 ${assignment.orderId} 分配失败:`, error)
    }
  }
  
  console.log(`🎯 批量分配完成: 成功 ${successCount}，失败 ${failCount}`)
  
  return { successCount, failCount, results }
} 