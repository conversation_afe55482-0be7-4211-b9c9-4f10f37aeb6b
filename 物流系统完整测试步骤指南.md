# 物流系统完整测试步骤指南

## 测试前准备

### 1. 确保后端服务运行正常
```bash
# 检查后端服务状态
curl http://localhost:8080/health
curl http://localhost:8081/health  # gateway
curl http://localhost:8082/health  # order service
curl http://localhost:8083/health  # delivery service
curl http://localhost:8084/health  # logistics service
```

### 2. 准备测试账号
确保数据库中有以下测试账号：

**客户账号**:
- 用户名: <EMAIL>
- 密码: 123456
- 角色: CUSTOMER

**操作员账号**:
- 用户名: <EMAIL>  
- 密码: 123456
- 角色: OPERATOR

**配送员账号**:
- 用户名: <EMAIL>
- 密码: 123456
- 角色: COURIER

### 3. 检查前端配置
确保前端API地址配置正确：
```typescript
// logistics-frontend/src/utils/http.ts
const baseURL = 'http://localhost:8081'  // gateway地址
```

## 完整业务流程测试

### 阶段一：客户下单和支付 (10分钟)

#### 1.1 客户注册登录
1. 打开浏览器访问: `http://localhost:5173`
2. 点击"注册"，创建新客户账号
3. 填写注册信息并提交
4. 使用新账号登录系统

#### 1.2 创建订单
1. 登录后点击"创建订单"
2. 填写寄件人信息：
   ```
   姓名: 张三
   电话: 13800138001
   地址: 北京市朝阳区建国路1号
   ```
3. 填写收件人信息：
   ```
   姓名: 李四
   电话: 13800138002
   地址: 上海市浦东新区陆家嘴1号
   ```
4. 填写物品信息：
   ```
   物品名称: 测试包裹
   物品类型: 其他
   重量: 2kg
   尺寸: 20x15x10cm
   价值: 500元
   ```
5. 选择服务类型: "标准快递"
6. 选择支付方式: "在线支付"
7. 点击"创建订单"

**预期结果**: 
- ✅ 订单创建成功，获得订单号（如：LD202506300103290003）
- ✅ 自动跳转到订单详情页面
- ✅ 在浏览器控制台看到"物流轨迹创建成功"日志

#### 1.3 支付订单
1. 在订单详情页面点击"立即支付"
2. 选择支付方式（支付宝/微信/货到付款）
3. 点击"立即支付"或"确认订单"

**预期结果**:
- ✅ 支付成功提示
- ✅ 订单状态变为"已支付"
- ✅ 在浏览器控制台看到轨迹更新日志

#### 1.4 查看物流轨迹
1. 在订单详情页面点击"查看物流"
2. 或者在"物流追踪"页面输入订单号查询

**预期结果**:
- ✅ 能够正常显示物流轨迹
- ✅ 轨迹包含：订单创建、支付成功等节点
- ✅ 不再出现"未找到轨迹信息"错误

### 阶段二：操作员分配配送员 (5分钟)

#### 2.1 操作员登录
1. 退出客户账号
2. 使用操作员账号登录: <EMAIL> / 123456
3. 进入操作员工作台

#### 2.2 查看待分配订单
1. 点击"订单管理"
2. 查看订单列表，找到刚创建的订单
3. 确认订单状态为"已支付"

#### 2.3 分配配送员
1. 点击订单操作中的"分配"按钮
2. 选择可用的配送员
3. 添加分配备注（可选）
4. 点击"确认分配"

**预期结果**:
- ✅ 分配成功提示
- ✅ 订单状态变为"已分配配送员"
- ✅ 订单列表中显示配送员姓名
- ✅ 在浏览器控制台看到"创建配送任务成功"和"更新轨迹成功"日志

#### 2.4 查看订单详情
1. 点击订单号进入订单详情
2. 确认配送员信息已正确显示

### 阶段三：配送员处理任务 (10分钟)

#### 3.1 配送员登录
1. 退出操作员账号
2. 使用配送员账号登录: <EMAIL> / 123456
3. 进入配送员工作台

#### 3.2 查看分配的任务
1. 在配送员工作台查看"今日任务"
2. 确认能看到刚分配的订单

**预期结果**:
- ✅ 任务列表中显示分配的订单
- ✅ 任务状态为"已分配"
- ✅ 显示正确的订单信息

#### 3.3 接受任务
1. 点击任务的"接受"按钮
2. 确认接受任务

**预期结果**:
- ✅ 接受成功提示
- ✅ 任务状态变为"已接受"
- ✅ 轨迹更新为"配送员已接受任务"

#### 3.4 开始配送
1. 点击任务的"开始配送"按钮
2. 确认开始配送

**预期结果**:
- ✅ 开始配送成功
- ✅ 任务状态变为"配送中"
- ✅ 轨迹更新为"配送员已开始配送"

#### 3.5 完成配送
1. 点击任务的"完成配送"按钮
2. 上传签收凭证（拍照或选择图片）
3. 填写配送备注
4. 确认完成

**预期结果**:
- ✅ 配送完成成功
- ✅ 任务状态变为"已完成"
- ✅ 轨迹更新为"配送完成，客户已签收"

### 阶段四：客户查看配送状态 (5分钟)

#### 4.1 客户查看订单状态
1. 切换回客户账号登录
2. 进入"我的订单"
3. 查看订单状态更新

**预期结果**:
- ✅ 订单状态显示为"已送达"
- ✅ 显示配送员信息和联系方式

#### 4.2 实时追踪功能
1. 点击"实时追踪"
2. 查看配送路径和状态

**预期结果**:
- ✅ 地图正常显示
- ✅ 显示配送员位置（如果有GPS数据）
- ✅ 显示完整的物流轨迹时间线

#### 4.3 确认收货
1. 点击"确认收货"按钮
2. 对配送服务进行评价

**预期结果**:
- ✅ 确认收货成功
- ✅ 订单状态变为"已完成"

## 测试检查点

### 关键检查点
在每个阶段完成后，检查以下内容：

1. **数据库检查**:
   ```sql
   -- 检查订单表
   SELECT * FROM orders WHERE order_number = 'LD202506300103290003';
   
   -- 检查配送任务表
   SELECT * FROM delivery_tasks WHERE order_number = 'LD202506300103290003';
   
   -- 检查轨迹表（MongoDB）
   db.tracking_info.find({"order_number": "LD202506300103290003"});
   ```

2. **前端控制台日志**:
   - 订单创建：看到"物流轨迹创建成功"
   - 支付：看到"支付轨迹更新成功"
   - 分配：看到"创建配送任务成功"
   - 配送：看到各阶段轨迹更新日志

3. **API响应检查**:
   ```bash
   # 检查订单API
   curl http://localhost:8081/order/LD202506300103290003
   
   # 检查轨迹API
   curl http://localhost:8081/logistics/tracking/LD202506300103290003
   
   # 检查配送任务API
   curl http://localhost:8081/delivery/task/courier/1/active
   ```

## 常见问题排查

### 问题1: "未找到轨迹信息"
**原因**: 订单创建时没有同时创建轨迹
**解决**: 
1. 检查OrderLifecycleManager是否正确引入
2. 检查轨迹创建API是否正常
3. 检查MongoDB连接

### 问题2: 配送员看不到任务
**原因**: 订单分配时没有创建配送任务
**解决**:
1. 检查分配接口是否调用了创建任务API
2. 检查配送员ID是否正确
3. 检查任务状态是否正确

### 问题3: 前端API调用失败
**原因**: 网关路由配置或服务不可用
**解决**:
1. 检查网关配置
2. 检查各微服务状态
3. 检查数据库连接

## 成功标准

完整流程测试成功的标准：

1. ✅ 客户能够成功创建订单并支付
2. ✅ 物流轨迹能够正常查询，显示完整信息
3. ✅ 操作员能够成功分配订单给配送员
4. ✅ 配送员能够看到分配的任务并进行处理
5. ✅ 客户能够实时查看配送状态和轨迹
6. ✅ 整个流程中所有状态变更都有轨迹记录
7. ✅ 前端界面响应正常，无报错信息

## 测试数据记录

请在测试过程中记录以下信息：

| 测试项目 | 预期结果 | 实际结果 | 是否通过 | 备注 |
|---------|---------|---------|---------|------|
| 订单创建 | 成功创建并生成轨迹 |  |  |  |
| 订单支付 | 支付成功并更新轨迹 |  |  |  |
| 轨迹查询 | 能正常显示轨迹信息 |  |  |  |
| 订单分配 | 成功分配并创建任务 |  |  |  |
| 配送员任务 | 能看到并处理任务 |  |  |  |
| 实时追踪 | 地图和轨迹正常显示 |  |  |  |

完成测试后，请告诉我哪个步骤出现了问题，我会进一步协助解决。 