package org.example.logisticsdelivery.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logistics.common.result.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.logisticsdelivery.entity.DeliveryTask;
import org.example.logisticsdelivery.service.DeliveryTaskService;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 配送任务管理控制器
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Slf4j
@RestController
@RequestMapping("/delivery/task")
@RequiredArgsConstructor
public class DeliveryTaskController {

    private final DeliveryTaskService taskService;

    /**
     * 创建配送任务
     */
    @PostMapping
    public Result<DeliveryTask> createTask(@RequestBody DeliveryTask task) {
        try {
            DeliveryTask result = taskService.createTask(task);
            return Result.success(result);
        } catch (Exception e) {
            log.error("创建配送任务失败", e);
            return Result.error("创建配送任务失败: " + e.getMessage());
        }
    }

    /**
     * 更新配送任务
     */
    @PutMapping("/{id}")
    public Result<DeliveryTask> updateTask(@PathVariable Long id, @RequestBody DeliveryTask task) {
        try {
            task.setId(id);
            DeliveryTask result = taskService.updateTask(task);
            return Result.success(result);
        } catch (Exception e) {
            log.error("更新配送任务失败，ID: {}", id, e);
            return Result.error("更新配送任务失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询任务
     */
    @GetMapping("/{id}")
    public Result<DeliveryTask> getTaskById(@PathVariable Long id) {
        try {
            DeliveryTask task = taskService.getTaskById(id);
            if (task != null) {
                return Result.success(task);
            } else {
                return Result.error("配送任务不存在");
            }
        } catch (Exception e) {
            log.error("查询配送任务失败，ID: {}", id, e);
            return Result.error("查询配送任务失败: " + e.getMessage());
        }
    }

    /**
     * 根据任务编号查询
     */
    @GetMapping("/number/{taskNumber}")
    public Result<DeliveryTask> getTaskByNumber(@PathVariable String taskNumber) {
        try {
            DeliveryTask task = taskService.getTaskByNumber(taskNumber);
            if (task != null) {
                return Result.success(task);
            } else {
                return Result.error("配送任务不存在");
            }
        } catch (Exception e) {
            log.error("查询配送任务失败，任务编号: {}", taskNumber, e);
            return Result.error("查询配送任务失败: " + e.getMessage());
        }
    }

    /**
     * 根据订单号查询任务
     */
    @GetMapping("/order/{orderNumber}")
    public Result<DeliveryTask> getTaskByOrderNumber(@PathVariable String orderNumber) {
        try {
            DeliveryTask task = taskService.getTaskByOrderNumber(orderNumber);
            if (task != null) {
                return Result.success(task);
            } else {
                return Result.error("该订单没有配送任务");
            }
        } catch (Exception e) {
            log.error("查询配送任务失败，订单号: {}", orderNumber, e);
            return Result.error("查询配送任务失败: " + e.getMessage());
        }
    }

    /**
     * 根据配送员ID查询任务列表
     */
    @GetMapping("/courier/{courierId}")
    public Result<List<DeliveryTask>> getTasksByCourierId(@PathVariable Long courierId) {
        try {
            List<DeliveryTask> tasks = taskService.getTasksByCourierId(courierId);
            return Result.success(tasks);
        } catch (Exception e) {
            log.error("查询配送员任务列表失败，配送员ID: {}", courierId, e);
            return Result.error("查询配送员任务列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据任务状态查询任务列表
     */
    @GetMapping("/status/{taskStatus}")
    public Result<List<DeliveryTask>> getTasksByStatus(@PathVariable String taskStatus) {
        try {
            List<DeliveryTask> tasks = taskService.getTasksByStatus(taskStatus);
            return Result.success(tasks);
        } catch (Exception e) {
            log.error("查询任务列表失败，状态: {}", taskStatus, e);
            return Result.error("查询任务列表失败: " + e.getMessage());
        }
    }

    /**
     * 查询配送员当日任务
     */
    @GetMapping("/courier/{courierId}/daily")
    public Result<List<DeliveryTask>> getCourierDailyTasks(
            @PathVariable Long courierId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate taskDate) {
        try {
            List<DeliveryTask> tasks = taskService.getCourierDailyTasks(courierId, taskDate);
            return Result.success(tasks);
        } catch (Exception e) {
            log.error("查询配送员当日任务失败，配送员ID: {}, 日期: {}", courierId, taskDate, e);
            return Result.error("查询配送员当日任务失败: " + e.getMessage());
        }
    }

    /**
     * 查询配送员进行中的任务
     */
    @GetMapping("/courier/{courierId}/active")
    public Result<List<DeliveryTask>> getCourierActiveTasks(@PathVariable Long courierId) {
        try {
            List<DeliveryTask> tasks = taskService.getCourierActiveTasks(courierId);
            return Result.success(tasks);
        } catch (Exception e) {
            log.error("查询配送员进行中任务失败，配送员ID: {}", courierId, e);
            return Result.error("查询配送员进行中任务失败: " + e.getMessage());
        }
    }

    /**
     * 查询待分配的任务
     */
    @GetMapping("/pending")
    public Result<List<DeliveryTask>> getPendingTasks(
            @RequestParam(required = false) String taskType,
            @RequestParam(required = false) String workArea) {
        try {
            List<DeliveryTask> tasks = taskService.getPendingTasks(taskType, workArea);
            return Result.success(tasks);
        } catch (Exception e) {
            log.error("查询待分配任务失败", e);
            return Result.error("查询待分配任务失败: " + e.getMessage());
        }
    }

    /**
     * 查询超时的任务
     */
    @GetMapping("/timeout")
    public Result<List<DeliveryTask>> getTimeoutTasks(@RequestParam(defaultValue = "30") Integer timeoutMinutes) {
        try {
            List<DeliveryTask> tasks = taskService.getTimeoutTasks(timeoutMinutes);
            return Result.success(tasks);
        } catch (Exception e) {
            log.error("查询超时任务失败", e);
            return Result.error("查询超时任务失败: " + e.getMessage());
        }
    }

    /**
     * 分配任务给配送员
     */
    @PostMapping("/{taskId}/assign/{courierId}")
    public Result<String> assignTask(@PathVariable Long taskId, @PathVariable Long courierId) {
        try {
            boolean success = taskService.assignTask(taskId, courierId);
            if (success) {
                return Result.success("任务分配成功");
            } else {
                return Result.error("任务分配失败");
            }
        } catch (Exception e) {
            log.error("分配任务失败，任务ID: {}, 配送员ID: {}", taskId, courierId, e);
            return Result.error("分配任务失败: " + e.getMessage());
        }
    }

    /**
     * 批量分配任务
     */
    @PostMapping("/batch/assign")
    public Result<String> batchAssignTasks(@RequestBody Map<String, Object> params) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> taskIds = (List<Long>) params.get("taskIds");
            Long courierId = Long.valueOf(params.get("courierId").toString());

            boolean success = taskService.batchAssignTasks(taskIds, courierId);
            if (success) {
                return Result.success("批量分配任务成功");
            } else {
                return Result.error("批量分配任务失败");
            }
        } catch (Exception e) {
            log.error("批量分配任务失败", e);
            return Result.error("批量分配任务失败: " + e.getMessage());
        }
    }

    /**
     * 更新任务状态
     */
    @PutMapping("/{taskId}/status")
    public Result<String> updateTaskStatus(@PathVariable Long taskId, @RequestParam String taskStatus) {
        try {
            boolean success = taskService.updateTaskStatus(taskId, taskStatus);
            if (success) {
                return Result.success("任务状态更新成功");
            } else {
                return Result.error("任务状态更新失败");
            }
        } catch (Exception e) {
            log.error("更新任务状态失败，任务ID: {}", taskId, e);
            return Result.error("更新任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新任务状态
     */
    @PutMapping("/batch/status")
    public Result<String> batchUpdateTaskStatus(@RequestBody Map<String, Object> params) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> taskIds = (List<Long>) params.get("taskIds");
            String taskStatus = params.get("taskStatus").toString();

            boolean success = taskService.batchUpdateTaskStatus(taskIds, taskStatus);
            if (success) {
                return Result.success("批量更新任务状态成功");
            } else {
                return Result.error("批量更新任务状态失败");
            }
        } catch (Exception e) {
            log.error("批量更新任务状态失败", e);
            return Result.error("批量更新任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 完成任务
     */
    @PutMapping("/{taskId}/complete")
    public Result<String> completeTask(@PathVariable Long taskId) {
        try {
            boolean success = taskService.completeTask(taskId);
            if (success) {
                return Result.success("任务完成成功");
            } else {
                return Result.error("任务完成失败");
            }
        } catch (Exception e) {
            log.error("完成任务失败，任务ID: {}", taskId, e);
            return Result.error("完成任务失败: " + e.getMessage());
        }
    }

    /**
     * 取消任务
     */
    @PutMapping("/{taskId}/cancel")
    public Result<String> cancelTask(@PathVariable Long taskId, @RequestParam(required = false) String reason) {
        try {
            boolean success = taskService.cancelTask(taskId, reason);
            if (success) {
                return Result.success("任务取消成功");
            } else {
                return Result.error("任务取消失败");
            }
        } catch (Exception e) {
            log.error("取消任务失败，任务ID: {}", taskId, e);
            return Result.error("取消任务失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询任务
     */
    @GetMapping("/page")
    public Result<IPage<DeliveryTask>> getTasksPage(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) Long courierId,
            @RequestParam(required = false) String taskStatus,
            @RequestParam(required = false) String taskType,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            Page<DeliveryTask> page = new Page<>(pageNum, pageSize);
            IPage<DeliveryTask> result = taskService.getTasksPage(page, courierId, taskStatus, taskType, startTime, endTime);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询任务失败", e);
            return Result.error("分页查询任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务统计数据
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getTaskStatistics(
            @RequestParam(required = false) Long courierId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            Map<String, Object> statistics = taskService.getTaskStatistics(courierId, startDate, endDate);
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取任务统计数据失败", e);
            return Result.error("获取任务统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 统计各状态任务数量
     */
    @GetMapping("/statistics/status")
    public Result<Map<String, Long>> getStatusStatistics() {
        try {
            Map<String, Long> statistics = taskService.getStatusStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取任务状态统计失败", e);
            return Result.error("获取任务状态统计失败: " + e.getMessage());
        }
    }

    /**
     * 自动分配任务
     */
    @PostMapping("/auto-assign")
    public Result<String> autoAssignTasks(@RequestParam String workArea) {
        try {
            boolean success = taskService.autoAssignTasks(workArea);
            if (success) {
                return Result.success("自动分配任务成功");
            } else {
                return Result.error("自动分配任务失败");
            }
        } catch (Exception e) {
            log.error("自动分配任务失败，工作区域: {}", workArea, e);
            return Result.error("自动分配任务失败: " + e.getMessage());
        }
    }

    /**
     * 检查并处理超时任务
     */
    @PostMapping("/handle-timeout")
    public Result<String> handleTimeoutTasks() {
        try {
            int handledCount = taskService.handleTimeoutTasks();
            return Result.success("处理超时任务完成，处理数量: " + handledCount);
        } catch (Exception e) {
            log.error("处理超时任务失败", e);
            return Result.error("处理超时任务失败: " + e.getMessage());
        }
    }

    /**
     * 删除任务
     */
    @DeleteMapping("/{taskId}")
    public Result<String> deleteTask(@PathVariable Long taskId) {
        try {
            boolean success = taskService.deleteTask(taskId);
            if (success) {
                return Result.success("删除任务成功");
            } else {
                return Result.error("删除任务失败");
            }
        } catch (Exception e) {
            log.error("删除任务失败，任务ID: {}", taskId, e);
            return Result.error("删除任务失败: " + e.getMessage());
        }
    }
}