import http, { type ApiResponse } from '@/utils/http'
import type { TrackingResponse, TrackingQueryRequest } from '@/types/tracking'

export const trackingApi = {
  // 查询物流轨迹（修复路径）
  getTrackingInfo(data: TrackingQueryRequest): Promise<ApiResponse<TrackingResponse>> {
    return http.post('/logistics/tracking/query', data)
  },

  // 根据订单号查询物流轨迹（修复路径，与后端保持一致）
  getTrackingByOrderNumber(orderNumber: string): Promise<ApiResponse<TrackingResponse>> {
    return http.get(`/logistics/tracking/${orderNumber}`)
  },

  // 创建轨迹信息（当订单创建时调用）
  createTracking(orderNumber: string, orderId: number): Promise<ApiResponse<any>> {
    return http.post('/logistics/tracking/create', null, {
      params: { orderNumber, orderId }
    })
  },

  // 更新轨迹状态
  updateTrackingStatus(trackingId: string, data: {
    status: string
    description?: string
    location?: any
    operatorId?: number
    operatorName?: string
  }): Promise<ApiResponse<any>> {
    return http.put(`/logistics/tracking/${trackingId}/status`, data.location, {
      params: {
        status: data.status,
        description: data.description,
        operatorId: data.operatorId,
        operatorName: data.operatorName
      }
    })
  },

  // 添加轨迹节点
  addTrackingNode(trackingId: string, node: any): Promise<ApiResponse<any>> {
    return http.post(`/logistics/tracking/${trackingId}/nodes`, node)
  },

  // 订阅物流通知
  subscribeTracking(
    orderNumber: string,
    phone: string,
    email?: string,
  ): Promise<ApiResponse<void>> {
    return http.post('/logistics/tracking/subscribe', { orderNumber, phone, email })
  },
}
