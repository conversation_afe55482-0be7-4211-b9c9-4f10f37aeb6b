package org.example.logisticslogistics.entity;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 物流轨迹节点实体
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
public class TrackingNode {

    /**
     * 节点ID
     */
    private String nodeId;

    /**
     * 轨迹状态
     */
    private String status;

    /**
     * 节点标题
     */
    private String title;

    /**
     * 详细描述
     */
    private String description;

    /**
     * 位置信息
     */
    private LocationInfo location;

    /**
     * 操作员姓名
     */
    private String operatorName;

    /**
     * 操作员ID
     */
    private Long operatorId;

    /**
     * 操作员类型
     */
    private String operatorType;

    /**
     * 时间戳
     */
    private LocalDateTime timestamp;

    /**
     * 相关图片URL列表
     */
    private List<String> photos;

    /**
     * 备注信息
     */
    private String remarks;
}