package org.example.logisticsuser.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.logisticsuser.entity.User;
import java.util.List;

@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据用户名查询用户
     */
    User selectByUsername(@Param("username") String username);

    /**
     * 根据手机号查询用户
     */
    User selectByPhone(@Param("phone") String phone);

    /**
     * 根据邮箱查询用户
     */
    User selectByEmail(@Param("email") String email);

    /**
     * 根据身份证号查询用户
     */
    User selectByIdCard(@Param("idCard") String idCard);

    /**
     * 根据用户ID查询用户角色
     */
    List<String> selectRolesByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID查询用户权限
     */
    List<String> selectPermissionsByUserId(@Param("userId") Long userId);

    /**
     * 更新用户最后登录信息
     */
    int updateLastLoginInfo(@Param("userId") Long userId,
                            @Param("loginTime") java.time.LocalDateTime loginTime,
                            @Param("loginIp") String loginIp);

    /**
     * 根据用户类型查询用户列表
     */
    List<User> selectByUserType(@Param("userType") String userType);
}
