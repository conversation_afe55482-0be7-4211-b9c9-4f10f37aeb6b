package org.example.logisticsdelivery.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 配送任务状态枚举
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Getter
@AllArgsConstructor
public enum TaskStatus {

    PENDING("PENDING", "待分配", "任务待分配状态"),
    ASSIGNED("ASSIGNED", "已分配", "任务已分配给配送员"),
    PICKED_UP("PICKED_UP", "已揽收", "配送员已揽收货物"),
    IN_TRANSIT("IN_TRANSIT", "运输中", "货物运输中"),
    COMPLETED("COMPLETED", "已完成", "任务已完成"),
    CANCELLED("CANCELLED", "已取消", "任务已取消"),
    FAILED("FAILED", "失败", "任务执行失败");

    private final String code;
    private final String name;
    private final String description;

    /**
     * 根据状态码获取枚举
     */
    public static TaskStatus fromCode(String code) {
        for (TaskStatus status : TaskStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的任务状态: " + code);
    }

    /**
     * 判断是否为最终状态
     */
    public boolean isFinalStatus() {
        return this == COMPLETED || this == CANCELLED || this == FAILED;
    }
}