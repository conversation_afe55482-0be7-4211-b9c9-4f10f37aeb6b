package org.example.logisticsuser.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.logistics.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("users")
public class User extends BaseEntity {

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 用户类型: CUSTOMER, OPERATOR, COURIER, ADMIN
     */
    private String userType;

    /**
     * 用户状态: ACTIVE, INACTIVE, LOCKED
     */
    private String status;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 最后登录时间
     */
    private java.time.LocalDateTime lastLoginTime;

    /**
     * 登录IP
     */
    private String lastLoginIp;

    /**
     * 性别：1-男，2-女
     */
    private Integer gender;

    /**
     * 生日
     */
    private LocalDate birthday;
}
