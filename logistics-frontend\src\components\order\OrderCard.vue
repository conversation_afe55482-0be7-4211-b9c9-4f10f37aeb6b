<template>
  <div class="order-card" :class="{ 'urgent': order.priority === 'URGENT' }">
    <!-- 卡片头部 -->
    <div class="card-header">
      <div class="header-left">
        <div class="order-number">
          <el-icon class="order-icon">
            <Box />
          </el-icon>
          <span class="number-text">{{ order.orderNumber }}</span>
        </div>
        <div class="order-meta">
          <span class="create-time">{{ formatTime(order.createTime) }}</span>
          <el-divider direction="vertical" />
          <span class="service-type">{{ getServiceTypeText(order.serviceType) }}</span>
        </div>
      </div>
      
      <div class="header-right">
        <el-tag 
          :type="getStatusTagType(order.orderStatus)" 
          class="status-tag"
          effect="light"
        >
          <el-icon class="status-icon">
            <component :is="getStatusIcon(order.orderStatus)" />
          </el-icon>
          {{ getStatusText(order.orderStatus) }}
        </el-tag>
        
        <el-dropdown @command="handleAction" trigger="click">
          <el-button circle size="small" class="action-btn">
            <el-icon><MoreFilled /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="detail">
                <el-icon><View /></el-icon>查看详情
              </el-dropdown-item>
              <el-dropdown-item command="track">
                <el-icon><Location /></el-icon>物流追踪
              </el-dropdown-item>
              <el-dropdown-item 
                v-if="canCancel" 
                command="cancel" 
                divided
              >
                <el-icon><Close /></el-icon>取消订单
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="card-content">
      <!-- 地址信息 -->
      <div class="address-section">
        <div class="address-item sender">
          <div class="address-label">
            <el-icon class="label-icon" color="#52c41a">
              <LocationFilled />
            </el-icon>
            <span>寄件</span>
          </div>
          <div class="address-details">
            <div class="contact-info">
              <span class="name">{{ order.senderName }}</span>
              <span class="phone">{{ order.senderPhone }}</span>
            </div>
            <div class="address-text">{{ order.senderAddress }}</div>
          </div>
        </div>

        <div class="address-connector">
          <el-icon class="connector-icon" color="#1890ff">
            <Right />
          </el-icon>
        </div>

        <div class="address-item receiver">
          <div class="address-label">
            <el-icon class="label-icon" color="#f5222d">
              <LocationFilled />
            </el-icon>
            <span>收件</span>
          </div>
          <div class="address-details">
            <div class="contact-info">
              <span class="name">{{ order.receiverName }}</span>
              <span class="phone">{{ order.receiverPhone }}</span>
            </div>
            <div class="address-text">{{ order.receiverAddress }}</div>
          </div>
        </div>
      </div>

      <!-- 物品信息 -->
      <div class="item-section">
        <div class="item-info">
          <el-icon class="item-icon">
            <Box />
          </el-icon>
          <div class="item-details">
            <span class="item-name">{{ order.itemName || '包裹' }}</span>
            <span class="item-specs">
              {{ order.itemWeight }}kg · {{ order.itemType || '普通物品' }}
            </span>
          </div>
        </div>
        
        <div class="item-value" v-if="order.itemValue">
          <span class="value-label">声明价值</span>
          <span class="value-amount">¥{{ order.itemValue }}</span>
        </div>
      </div>

      <!-- 进度条 -->
      <div class="progress-section" v-if="showProgress">
        <div class="progress-header">
          <span class="progress-label">配送进度</span>
          <span class="progress-percentage">{{ progressPercentage }}%</span>
        </div>
        <el-progress 
          :percentage="progressPercentage" 
          :color="progressColor"
          :stroke-width="6"
          class="progress-bar"
        />
        <div class="progress-steps">
          <div 
            v-for="(step, index) in progressSteps" 
            :key="index"
            class="step-item"
            :class="{ active: step.active, completed: step.completed }"
          >
            <el-icon class="step-icon">
              <component :is="step.icon" />
            </el-icon>
            <span class="step-text">{{ step.text }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 卡片底部 -->
    <div class="card-footer">
      <div class="footer-left">
        <div class="fee-info">
          <span class="fee-label">运费</span>
          <span class="fee-amount">¥{{ order.totalFee }}</span>
        </div>
        
        <div class="payment-info" v-if="order.paymentStatus !== undefined">
          <el-tag 
            :type="order.paymentStatus === 1 ? 'success' : 'warning'" 
            size="small"
            effect="plain"
          >
            {{ getPaymentStatusText(order.paymentStatus) }}
          </el-tag>
        </div>
      </div>

      <div class="footer-right">
        <el-button 
          v-if="canPay" 
          type="primary" 
          size="small"
          @click="handlePay"
        >
          立即支付
        </el-button>
        
        <el-button 
          v-if="canTrack" 
          type="primary" 
          plain 
          size="small"
          @click="handleTrack"
        >
          查看物流
        </el-button>
        
        <el-button 
          size="small"
          @click="handleDetail"
        >
          查看详情
        </el-button>
      </div>
    </div>

    <!-- 紧急标识 -->
    <div v-if="order.priority === 'URGENT'" class="urgent-badge">
      <el-icon><Warning /></el-icon>
      <span>加急</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Box, MoreFilled, View, Location, Close, LocationFilled, Right,
  Warning, CircleCheck, Van, Truck, House
} from '@element-plus/icons-vue'
import { getOrderStatusText, getOrderStatusType } from '@/utils/orderStatus'
import type { Order } from '@/types/order'
import dayjs from 'dayjs'

// Props
interface Props {
  order: Order
  showProgress?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showProgress: true
})

// Emits
const emit = defineEmits<{
  pay: [order: Order]
  cancel: [order: Order]
  detail: [order: Order]
  track: [order: Order]
}>()

const router = useRouter()

// 计算属性
const canPay = computed(() => {
  return props.order.orderStatus === 'PENDING' && props.order.paymentStatus === 0
})

const canCancel = computed(() => {
  return ['PENDING', 'PAID'].includes(props.order.orderStatus)
})

const canTrack = computed(() => {
  return !['PENDING', 'CANCELLED'].includes(props.order.orderStatus)
})

const progressPercentage = computed(() => {
  const statusProgress = {
    'PENDING': 10,
    'PAID': 20,
    'PICKUP_ASSIGNED': 30,
    'PICKUP': 40,
    'SORTING': 50,
    'DISPATCHING': 60,
    'TRANSFERRING': 70,
    'ARRIVED': 80,
    'DELIVERING': 90,
    'SIGNED': 100
  }
  return statusProgress[props.order.orderStatus] || 0
})

const progressColor = computed(() => {
  const percentage = progressPercentage.value
  if (percentage < 30) return '#faad14'
  if (percentage < 70) return '#1890ff'
  return '#52c41a'
})

const progressSteps = computed(() => {
  const currentStatus = props.order.orderStatus
  const steps = [
    { text: '已下单', icon: 'CircleCheck', status: 'PAID' },
    { text: '已揽收', icon: 'Box', status: 'PICKUP' },
    { text: '运输中', icon: 'Van', status: 'TRANSIT' },
    { text: '派送中', icon: 'Truck', status: 'DELIVERING' },
    { text: '已签收', icon: 'House', status: 'SIGNED' }
  ]
  
  const statusOrder = ['PENDING', 'PAID', 'PICKUP_ASSIGNED', 'PICKUP', 'SORTING', 'DISPATCHING', 'TRANSFERRING', 'TRANSIT', 'ARRIVED', 'DELIVERING', 'SIGNED']
  const currentIndex = statusOrder.indexOf(currentStatus)
  
  return steps.map((step, index) => {
    const stepIndex = statusOrder.indexOf(step.status)
    return {
      ...step,
      completed: stepIndex <= currentIndex,
      active: stepIndex === currentIndex
    }
  })
})

// 方法
const formatTime = (time: string) => {
  return dayjs(time).format('MM-DD HH:mm')
}

const getStatusText = (status: string) => {
  return getOrderStatusText(status)
}

const getStatusTagType = (status: string) => {
  return getOrderStatusType(status)
}

const getStatusIcon = (status: string) => {
  const iconMap = {
    'PENDING': 'Clock',
    'PAID': 'CircleCheck',
    'PICKUP': 'Box',
    'TRANSIT': 'Van',
    'DELIVERING': 'Truck',
    'SIGNED': 'CircleCheck',
    'CANCELLED': 'Close'
  }
  return iconMap[status] || 'Clock'
}

const getServiceTypeText = (type: string) => {
  const typeMap = {
    'STANDARD': '标准快递',
    'EXPRESS': '特快专递',
    'URGENT': '加急服务'
  }
  return typeMap[type] || '标准快递'
}

const getPaymentStatusText = (status: number) => {
  const statusMap = {
    0: '未支付',
    1: '已支付',
    2: '已退款'
  }
  return statusMap[status] || '未知'
}

const handleAction = (command: string) => {
  switch (command) {
    case 'detail':
      handleDetail()
      break
    case 'track':
      handleTrack()
      break
    case 'cancel':
      handleCancel()
      break
  }
}

const handlePay = () => {
  emit('pay', props.order)
}

const handleCancel = () => {
  emit('cancel', props.order)
}

const handleDetail = () => {
  emit('detail', props.order)
}

const handleTrack = () => {
  emit('track', props.order)
}
</script>

<style scoped>
.order-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-secondary);
  box-shadow: var(--shadow-sm);
  transition: all var(--duration-normal);
  position: relative;
  overflow: hidden;
}

.order-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.order-card.urgent {
  border-left: 4px solid var(--error-color);
}

/* ========== 卡片头部 ========== */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-light);
}

.header-left {
  flex: 1;
}

.order-number {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.order-icon {
  color: var(--primary-color);
  font-size: var(--font-lg);
}

.number-text {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.order-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-sm);
  color: var(--text-tertiary);
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.status-tag {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: 500;
  padding: var(--spacing-xs) var(--spacing-md);
}

.status-icon {
  font-size: var(--font-sm);
}

.action-btn {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-secondary);
}

/* ========== 卡片内容 ========== */
.card-content {
  padding: var(--spacing-lg);
}

/* 地址信息 */
.address-section {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.address-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.address-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-sm);
  font-weight: 500;
  color: var(--text-secondary);
}

.label-icon {
  font-size: var(--font-md);
}

.address-details {
  background: var(--bg-tertiary);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

.contact-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xs);
}

.name {
  font-weight: 500;
  color: var(--text-primary);
}

.phone {
  font-size: var(--font-sm);
  color: var(--text-tertiary);
}

.address-text {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  line-height: 1.4;
}

.address-connector {
  display: flex;
  align-items: center;
  margin-top: var(--spacing-xl);
}

.connector-icon {
  font-size: var(--font-xl);
}

/* 物品信息 */
.item-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-lg);
}

.item-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.item-icon {
  color: var(--primary-color);
  font-size: var(--font-xl);
}

.item-details {
  display: flex;
  flex-direction: column;
}

.item-name {
  font-weight: 500;
  color: var(--text-primary);
}

.item-specs {
  font-size: var(--font-sm);
  color: var(--text-tertiary);
}

.item-value {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.value-label {
  font-size: var(--font-xs);
  color: var(--text-tertiary);
}

.value-amount {
  font-weight: 600;
  color: var(--primary-color);
}

/* 进度条 */
.progress-section {
  margin-bottom: var(--spacing-lg);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.progress-label {
  font-size: var(--font-sm);
  font-weight: 500;
  color: var(--text-secondary);
}

.progress-percentage {
  font-size: var(--font-sm);
  font-weight: 600;
  color: var(--primary-color);
}

.progress-bar {
  margin-bottom: var(--spacing-lg);
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  opacity: 0.5;
  transition: opacity var(--duration-normal);
}

.step-item.completed,
.step-item.active {
  opacity: 1;
}

.step-item.active .step-icon {
  color: var(--primary-color);
}

.step-item.completed .step-icon {
  color: var(--success-color);
}

.step-icon {
  font-size: var(--font-lg);
  color: var(--text-tertiary);
}

.step-text {
  font-size: var(--font-xs);
  color: var(--text-tertiary);
  text-align: center;
}

/* ========== 卡片底部 ========== */
.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  background: var(--bg-tertiary);
  border-top: 1px solid var(--border-light);
}

.footer-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.fee-info {
  display: flex;
  flex-direction: column;
}

.fee-label {
  font-size: var(--font-xs);
  color: var(--text-tertiary);
}

.fee-amount {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--primary-color);
}

.footer-right {
  display: flex;
  gap: var(--spacing-sm);
}

/* ========== 紧急标识 ========== */
.urgent-badge {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  background: var(--error-color);
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-xs);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  z-index: 1;
}

/* ========== 响应式设计 ========== */
@media (max-width: 768px) {
  .address-section {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .address-connector {
    align-self: center;
    margin: 0;
    transform: rotate(90deg);
  }

  .card-footer {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }

  .footer-right {
    justify-content: center;
  }

  .progress-steps {
    gap: var(--spacing-xs);
  }

  .step-text {
    display: none;
  }
}

@media (max-width: 480px) {
  .card-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }

  .header-right {
    justify-content: space-between;
  }

  .item-section {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }

  .item-value {
    align-items: flex-start;
  }
}
</style>
