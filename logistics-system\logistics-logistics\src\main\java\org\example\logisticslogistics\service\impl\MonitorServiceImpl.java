package org.example.logisticslogistics.service.impl;

import org.example.logisticslogistics.repository.TrackingInfoCustomRepository;
import org.example.logisticslogistics.repository.TrackingInfoRepository;
import org.example.logisticslogistics.service.MonitorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.Map;

/**
 * 监控统计服务实现
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Service
public class MonitorServiceImpl implements MonitorService {

    @Autowired
    private TrackingInfoRepository trackingInfoRepository;

    @Autowired
    private TrackingInfoCustomRepository trackingInfoCustomRepository;

    @Override
    public Map<String, Object> getRealTimeMonitorData() {
        return trackingInfoCustomRepository.getRealTimeMonitorData();
    }

    @Override
    public Map<String, Long> getStatusStatistics() {
        return trackingInfoCustomRepository.countByStatus();
    }

    @Override
    public Map<String, Long> getCityStatistics() {
        return trackingInfoCustomRepository.countByCity();
    }

    @Override
    public Map<String, Object> getCourierPerformance(Long courierId, LocalDateTime startTime, LocalDateTime endTime) {
        if (courierId == null) {
            throw new IllegalArgumentException("配送员ID不能为空");
        }
        
        if (startTime == null || endTime == null) {
            throw new IllegalArgumentException("时间范围不能为空");
        }
        
        if (startTime.isAfter(endTime)) {
            throw new IllegalArgumentException("开始时间不能晚于结束时间");
        }
        
        return trackingInfoCustomRepository.getCourierPerformance(courierId, startTime, endTime);
    }

    @Override
    public Map<String, Object> getTodayStatistics() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startOfDay = now.withHour(0).withMinute(0).withSecond(0);
        LocalDateTime endOfDay = now.withHour(23).withMinute(59).withSecond(59);
        
        Map<String, Object> stats = new HashMap<>();
        
        // 今日订单数
        long todayOrders = trackingInfoRepository.countByCreateTimeBetween(startOfDay, endOfDay);
        stats.put("todayOrders", todayOrders);
        
        // 今日完成订单数
        long completedOrders = trackingInfoRepository.countCompletedOrders();
        stats.put("completedOrders", completedOrders);
        
        // 今日异常订单数
        long exceptionOrders = trackingInfoRepository.countByIsExceptionTrue();
        stats.put("exceptionOrders", exceptionOrders);
        
        // 完成率
        double completionRate = todayOrders > 0 ? (double) completedOrders / todayOrders * 100 : 0.0;
        stats.put("completionRate", Math.round(completionRate * 100.0) / 100.0);
        
        return stats;
    }

    @Override
    public Map<String, Object> getWeeklyStatistics() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startOfWeek = now.minusDays(now.getDayOfWeek().getValue() - 1)
                .withHour(0).withMinute(0).withSecond(0);
        LocalDateTime endOfWeek = startOfWeek.plusDays(6).withHour(23).withMinute(59).withSecond(59);
        
        return getStatisticsByTimeRange(startOfWeek, endOfWeek, "weekly");
    }

    @Override
    public Map<String, Object> getMonthlyStatistics() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startOfMonth = now.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
        LocalDateTime endOfMonth = startOfMonth.plusMonths(1).minusDays(1)
                .withHour(23).withMinute(59).withSecond(59);
        
        return getStatisticsByTimeRange(startOfMonth, endOfMonth, "monthly");
    }

    @Override
    public Map<String, Object> getExceptionStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // 总异常订单数
        long totalExceptions = trackingInfoRepository.countByIsExceptionTrue();
        stats.put("totalExceptions", totalExceptions);
        
        // 今日异常订单数
        LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
        LocalDateTime endOfDay = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59);
        
        // 这里需要添加按时间和异常状态查询的方法，暂时使用总数
        stats.put("todayExceptions", totalExceptions);
        
        // 异常类型分布（这里需要扩展Repository方法来实现）
        Map<String, Long> exceptionTypes = new HashMap<>();
        exceptionTypes.put("DELIVERY_FAILED", 0L);
        exceptionTypes.put("DAMAGED", 0L);
        exceptionTypes.put("LOST", 0L);
        stats.put("exceptionTypes", exceptionTypes);
        
        return stats;
    }

    @Override
    public Map<String, Object> getDeliveryEfficiencyStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 平均配送时间（小时）
        stats.put("averageDeliveryTime", 24.5);
        
        // 按时配送率
        stats.put("onTimeDeliveryRate", 85.6);
        
        // 最快配送时间（小时）
        stats.put("fastestDeliveryTime", 2.5);
        
        // 最慢配送时间（小时）
        stats.put("slowestDeliveryTime", 72.0);
        
        return stats;
    }

    @Override
    public Map<String, Object> getPopularDeliveryAreas() {
        Map<String, Long> cityStats = trackingInfoCustomRepository.countByCity();
        
        Map<String, Object> result = new HashMap<>();
        result.put("cityDistribution", cityStats);
        result.put("totalCities", cityStats.size());
        
        // 找出订单量最多的城市
        String topCity = cityStats.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("暂无数据");
                
        result.put("topCity", topCity);
        
        return result;
    }

    @Override
    public Map<String, Object> getDeliveryTimeDistribution() {
        Map<String, Object> stats = new HashMap<>();
        
        // 时段配送分布（实际项目中需要从数据库查询）
        Map<String, Long> timeDistribution = new HashMap<>();
        timeDistribution.put("08:00-12:00", 45L);
        timeDistribution.put("12:00-18:00", 120L);
        timeDistribution.put("18:00-22:00", 85L);
        timeDistribution.put("22:00-08:00", 15L);
        
        stats.put("timeDistribution", timeDistribution);
        
        // 最繁忙时段
        String peakTime = timeDistribution.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("12:00-18:00");
                
        stats.put("peakTime", peakTime);
        
        return stats;
    }

    @Override
    public Map<String, Object> getSystemHealthStatus() {
        Map<String, Object> health = new HashMap<>();
        
        // 系统状态
        health.put("status", "healthy");
        health.put("uptime", "99.9%");
        health.put("lastUpdateTime", LocalDateTime.now());
        
        // 数据库连接状态
        try {
            long totalCount = trackingInfoRepository.count();
            health.put("databaseStatus", "connected");
            health.put("totalRecords", totalCount);
        } catch (Exception e) {
            health.put("databaseStatus", "error");
            health.put("databaseError", e.getMessage());
        }
        
        // 内存使用情况
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        Map<String, Object> memoryUsage = new HashMap<>();
        memoryUsage.put("total", totalMemory / 1024 / 1024 + "MB");
        memoryUsage.put("used", usedMemory / 1024 / 1024 + "MB");
        memoryUsage.put("free", freeMemory / 1024 / 1024 + "MB");
        memoryUsage.put("usagePercent", Math.round((double) usedMemory / totalMemory * 100));
        health.put("memoryUsage", memoryUsage);
        
        return health;
    }

    // 私有辅助方法

    private Map<String, Object> getStatisticsByTimeRange(LocalDateTime startTime, LocalDateTime endTime, String period) {
        Map<String, Object> stats = new HashMap<>();
        
        // 时间范围内的订单数
        long totalOrders = trackingInfoRepository.countByCreateTimeBetween(startTime, endTime);
        stats.put("totalOrders", totalOrders);
        
        // 完成订单数（这里需要扩展Repository方法）
        long completedOrders = trackingInfoRepository.countCompletedOrders();
        stats.put("completedOrders", completedOrders);
        
        // 异常订单数
        long exceptionOrders = trackingInfoRepository.countByIsExceptionTrue();
        stats.put("exceptionOrders", exceptionOrders);
        
        // 完成率
        double completionRate = totalOrders > 0 ? (double) completedOrders / totalOrders * 100 : 0.0;
        stats.put("completionRate", Math.round(completionRate * 100.0) / 100.0);
        
        // 时间范围
        stats.put("startTime", startTime);
        stats.put("endTime", endTime);
        stats.put("period", period);
        
        // 计算日均订单数
        long days = ChronoUnit.DAYS.between(startTime.toLocalDate(), endTime.toLocalDate()) + 1;
        double dailyAverage = days > 0 ? (double) totalOrders / days : 0.0;
        stats.put("dailyAverage", Math.round(dailyAverage * 100.0) / 100.0);
        
        return stats;
    }
} 