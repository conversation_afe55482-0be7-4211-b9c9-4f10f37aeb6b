import http from '@/utils/http'

/**
 * 订单分配助手工具
 */
export class OrderAssignHelper {
  /**
   * 单个订单分配
   */
  static async assignSingleOrder(orderId: number, courierId: number): Promise<boolean> {
    console.log(`🚀 开始分配订单 ${orderId} 给配送员 ${courierId}`)
    
    try {
      // 1. 分配订单
      const response = await http.put(`/order/${orderId}/assign-delivery`, null, {
        params: { courierId }
      })
      
      console.log('✅ 订单分配响应:', response)
      
      if (response && (response.code === 200 || response.status === 200)) {
        console.log('✅ 订单分配成功')
        
        // 2. 尝试创建配送任务（可选，如果后端支持）
        try {
          await http.post('/delivery/task', {
            orderId: orderId,
            courierId: courierId,
            taskType: 'DELIVERY',
            priority: 2
          })
          console.log('✅ 配送任务创建成功')
        } catch (taskError) {
          console.warn('⚠️ 配送任务创建失败，但订单分配成功:', taskError)
        }
        
        return true
      } else {
        throw new Error('分配失败：响应格式异常')
      }
    } catch (error: any) {
      console.error('❌ 订单分配失败:', error)
      throw new Error(`分配失败: ${error.message}`)
    }
  }
  
  /**
   * 批量订单分配
   */
  static async assignBatchOrders(assignments: Array<{ orderId: number; courierId: number }>): Promise<{
    successCount: number
    failCount: number
    message: string
    details: Array<{ orderId: number; success: boolean; error?: string }>
  }> {
    console.log('🚀 开始批量分配:', assignments)
    
    let successCount = 0
    let failCount = 0
    const details = []
    
    for (const assignment of assignments) {
      try {
        await this.assignSingleOrder(assignment.orderId, assignment.courierId)
        details.push({ orderId: assignment.orderId, success: true })
        successCount++
      } catch (error: any) {
        details.push({ orderId: assignment.orderId, success: false, error: error.message })
        failCount++
      }
    }
    
    const message = failCount > 0 
      ? `部分分配成功：成功 ${successCount} 个，失败 ${failCount} 个`
      : `批量分配成功：共分配 ${successCount} 个订单`
    
    console.log(`🎯 批量分配结果: ${message}`)
    
    return { successCount, failCount, message, details }
  }
} 