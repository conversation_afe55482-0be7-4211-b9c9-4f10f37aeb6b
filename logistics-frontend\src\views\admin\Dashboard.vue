<template>
  <div class="admin-dashboard">
    <div class="dashboard-header">
      <h1>管理员控制台</h1>
      <p>系统管理与监控</p>
    </div>

    <div class="dashboard-content">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon">
                <el-icon size="40" color="#409EFF"><User /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">1,234</div>
                <div class="stat-label">总用户数</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon">
                <el-icon size="40" color="#67C23A"><Box /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">5,678</div>
                <div class="stat-label">总订单数</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon">
                <el-icon size="40" color="#E6A23C"><van /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">89</div>
                <div class="stat-label">活跃配送员</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon">
                <el-icon size="40" color="#F56C6C"><TrendCharts /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">98.5%</div>
                <div class="stat-label">系统可用性</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px">
        <el-col :span="16">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>系统监控</span>
              </div>
            </template>
            <div class="system-monitor">
              <div class="monitor-item">
                <span>CPU使用率</span>
                <el-progress :percentage="45" />
              </div>
              <div class="monitor-item">
                <span>内存使用率</span>
                <el-progress :percentage="68" status="warning" />
              </div>
              <div class="monitor-item">
                <span>磁盘使用率</span>
                <el-progress :percentage="32" />
              </div>
              <div class="monitor-item">
                <span>网络带宽</span>
                <el-progress :percentage="78" status="success" />
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>快速操作</span>
              </div>
            </template>
            <div class="quick-actions">
              <el-button type="primary" size="large" style="width: 100%; margin-bottom: 10px" @click="$router.push('/admin/user/list')">
                <el-icon><User /></el-icon>
                用户管理
              </el-button>
              <el-button size="large" style="width: 100%; margin-bottom: 10px" @click="$router.push('/admin/order/list')">
                <el-icon><Box /></el-icon>
                订单管理
              </el-button>
              <el-button size="large" style="width: 100%; margin-bottom: 10px">
                <el-icon><Setting /></el-icon>
                系统设置
              </el-button>
              <el-button size="large" style="width: 100%">
                <el-icon><DataAnalysis /></el-icon>
                数据分析
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { User, Box, Van, TrendCharts, Setting, DataAnalysis } from '@element-plus/icons-vue'
</script>

<style scoped>
.admin-dashboard {
  padding: 20px;
}

.dashboard-header {
  margin-bottom: 30px;
}

.dashboard-header h1 {
  margin: 0 0 10px 0;
  color: #333;
}

.dashboard-header p {
  margin: 0;
  color: #666;
}

.stat-card {
  height: 120px;
}

.stat-item {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 15px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.system-monitor {
  padding: 10px 0;
}

.monitor-item {
  margin-bottom: 20px;
}

.monitor-item:last-child {
  margin-bottom: 0;
}

.monitor-item span {
  display: block;
  margin-bottom: 8px;
  color: #333;
  font-weight: 500;
}

.quick-actions {
  padding: 10px 0;
}
</style>
