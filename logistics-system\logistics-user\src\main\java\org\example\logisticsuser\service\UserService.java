package org.example.logisticsuser.service;

import org.example.logisticsuser.dto.UpdateUserInfoDTO;
import org.example.logisticsuser.dto.UserLoginDTO;
import org.example.logisticsuser.dto.UserRegisterDTO;
import org.example.logisticsuser.vo.LoginResponseVO;
import org.example.logisticsuser.vo.UserInfoVO;
import org.example.logisticsuser.entity.User;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

public interface UserService {

    /**
     * 用户注册
     */
    LoginResponseVO register(UserRegisterDTO registerDTO);

    /**
     * 用户登录
     */
    LoginResponseVO login(UserLoginDTO loginDTO);

    /**
     * 根据token获取用户信息
     */
    UserInfoVO getCurrentUserInfo(String token);

    /**
     * 根据用户ID获取用户信息
     */
    UserInfoVO getUserInfoById(Long userId);

    /**
     * 更新用户信息
     */
    UserInfoVO updateUserInfo(Long userId, UserInfoVO userInfo);

    /**
     * 修改密码
     */
    boolean changePassword(Long userId, String oldPassword, String newPassword);

    /**
     * 重置密码
     */
    boolean resetPassword(String phone, String newPassword, String verificationCode);

    /**
     * 用户分页查询
     */
    IPage<UserInfoVO> getUserPage(Page<User> page, String userType, String status, String keyword);

    /**
     * 锁定/解锁用户
     */
    boolean lockUser(Long userId, boolean lock);

    /**
     * 检查用户名是否存在
     */
    boolean checkUsernameExists(String username);

    /**
     * 检查手机号是否存在
     */
    boolean checkPhoneExists(String phone);

    /**
     * 检查邮箱是否存在
     */
    boolean checkEmailExists(String email);

    /**
     * 为用户分配角色
     */
    boolean assignRoles(Long userId, Long[] roleIds);

    /**
     * 获取用户角色
     */
    java.util.List<String> getUserRoles(Long userId);

    /**
     * 获取用户权限
     */
    java.util.List<String> getUserPermissions(Long userId);

    /**
     * 更新用户信息
     */
    UserInfoVO updateUserInfo(Long userId, UpdateUserInfoDTO updateDTO);

    /**
     * 获取用户编辑信息（未脱敏）
     */
    UpdateUserInfoDTO getUserInfoForEdit(String token);
}
