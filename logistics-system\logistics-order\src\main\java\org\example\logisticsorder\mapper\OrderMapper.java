package org.example.logisticsorder.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.logisticsorder.entity.Order;
import org.example.logisticsorder.dto.OrderQueryDTO;
import org.example.logisticsorder.vo.OrderListVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单Mapper接口
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Mapper
public interface OrderMapper extends BaseMapper<Order> {

    /**
     * 根据订单号查询订单
     */
    Order selectByOrderNumber(@Param("orderNumber") String orderNumber);

    /**
     * 分页查询订单列表
     */
    IPage<OrderListVO> selectOrderPage(Page<OrderListVO> page, @Param("query") OrderQueryDTO query);

    /**
     * 根据用户ID查询订单列表
     */
    List<OrderListVO> selectByUserId(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 根据收件人电话查询订单
     */
    List<Order> selectByReceiverPhone(@Param("receiverPhone") String receiverPhone);

    /**
     * 根据寄件人电话查询订单
     */
    List<Order> selectBySenderPhone(@Param("senderPhone") String senderPhone);

    /**
     * 根据订单状态查询订单数量
     */
    Long countByStatus(@Param("orderStatus") String orderStatus);

    /**
     * 根据用户ID和状态查询订单数量
     */
    Long countByUserIdAndStatus(@Param("userId") Long userId, @Param("orderStatus") String orderStatus);

    /**
     * 查询待支付订单（超时自动取消)
     */
    List<Order> selectPendingPaymentOrders(@Param("timeoutMinutes") Integer timeoutMinutes);

    /**
     * 批量更新订单状态
     */
    int batchUpdateStatus(@Param("orderIds") List<Long> orderIds, @Param("newStatus") String newStatus);

    /**
     * 更新订单支付状态
     */
    int updatePaymentStatus(@Param("orderId") Long orderId, @Param("paymentStatus") Integer paymentStatus);

    /**
     * 更新订单时间字段
     */
    int updateOrderTime(@Param("orderId") Long orderId,
                        @Param("timeType") String timeType,
                        @Param("timeValue") java.time.LocalDateTime timeValue);

    /**
     * 根据状态和时间范围统计订单数量
     */
    Long countByStatusAndTimeRange(@Param("status") String status,
                                   @Param("startTime") LocalDateTime startTime,
                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 根据时间范围统计订单金额
     */
    BigDecimal sumAmountByTimeRange(@Param("startTime") LocalDateTime startTime,
                                    @Param("endTime") LocalDateTime endTime);
}

