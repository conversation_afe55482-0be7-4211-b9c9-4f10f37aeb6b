package org.example.logisticsnotification.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logistics.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.example.logisticsnotification.entity.NotificationTemplate;
import org.example.logisticsnotification.service.NotificationTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 通知模板管理控制器
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Slf4j
@RestController
@RequestMapping("/api/notification-template")
public class NotificationTemplateController {

    @Autowired
    private NotificationTemplateService notificationTemplateService;

    /**
     * 创建模板
     */
    @PostMapping
    public Result<NotificationTemplate> createTemplate(@RequestBody NotificationTemplate template) {
        try {
            NotificationTemplate created = notificationTemplateService.createTemplate(template);
            return created != null ? Result.success(created) : Result.error("创建模板失败");
        } catch (Exception e) {
            log.error("创建模板异常", e);
            return Result.error("创建模板异常：" + e.getMessage());
        }
    }

    /**
     * 更新模板
     */
    @PutMapping("/{id}")
    public Result<Boolean> updateTemplate(@PathVariable Long id, @RequestBody NotificationTemplate template) {
        try {
            template.setId(id);
            boolean success = notificationTemplateService.updateTemplate(template);
            return Result.success(success);
        } catch (Exception e) {
            log.error("更新模板异常: {}", id, e);
            return Result.error("更新模板异常：" + e.getMessage());
        }
    }

    /**
     * 删除模板
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteTemplate(@PathVariable Long id) {
        try {
            boolean success = notificationTemplateService.deleteTemplate(id);
            return Result.success(success);
        } catch (Exception e) {
            log.error("删除模板异常: {}", id, e);
            return Result.error("删除模板异常：" + e.getMessage());
        }
    }

    /**
     * 批量删除模板
     */
    @DeleteMapping("/batch")
    public Result<Boolean> batchDeleteTemplates(@RequestBody List<Long> templateIds) {
        try {
            boolean success = notificationTemplateService.batchDeleteTemplates(templateIds);
            return Result.success(success);
        } catch (Exception e) {
            log.error("批量删除模板异常", e);
            return Result.error("批量删除模板异常：" + e.getMessage());
        }
    }

    /**
     * 启用/禁用模板
     */
    @PutMapping("/{id}/toggle-status")
    public Result<Boolean> toggleTemplateStatus(@PathVariable Long id, @RequestParam Integer isEnabled) {
        try {
            boolean success = notificationTemplateService.toggleTemplateStatus(id, isEnabled);
            return Result.success(success);
        } catch (Exception e) {
            log.error("切换模板状态异常: {}", id, e);
            return Result.error("切换模板状态异常：" + e.getMessage());
        }
    }

    /**
     * 批量更新模板状态
     */
    @PutMapping("/batch-status")
    public Result<Integer> batchUpdateTemplateStatus(
            @RequestParam List<Long> templateIds,
            @RequestParam Integer isEnabled) {
        try {
            int updateCount = notificationTemplateService.batchUpdateTemplateStatus(templateIds, isEnabled);
            return Result.success(updateCount);
        } catch (Exception e) {
            log.error("批量更新模板状态异常", e);
            return Result.error("批量更新模板状态异常：" + e.getMessage());
        }
    }

    /**
     * 根据ID查询模板
     */
    @GetMapping("/{id}")
    public Result<NotificationTemplate> getTemplateById(@PathVariable Long id) {
        try {
            NotificationTemplate template = notificationTemplateService.getTemplateById(id);
            return template != null ? Result.success(template) : Result.error("模板不存在");
        } catch (Exception e) {
            log.error("查询模板异常: {}", id, e);
            return Result.error("查询模板异常：" + e.getMessage());
        }
    }

    /**
     * 根据模板编码查询模板
     */
    @GetMapping("/code/{templateCode}")
    public Result<NotificationTemplate> getTemplateByCode(@PathVariable String templateCode) {
        try {
            NotificationTemplate template = notificationTemplateService.getTemplateByCode(templateCode);
            return template != null ? Result.success(template) : Result.error("模板不存在");
        } catch (Exception e) {
            log.error("查询模板异常: {}", templateCode, e);
            return Result.error("查询模板异常：" + e.getMessage());
        }
    }

    /**
     * 根据模板类型查询模板列表
     */
    @GetMapping("/type/{templateType}")
    public Result<List<NotificationTemplate>> getTemplatesByType(@PathVariable String templateType) {
        try {
            List<NotificationTemplate> templates = notificationTemplateService.getTemplatesByType(templateType);
            return Result.success(templates);
        } catch (Exception e) {
            log.error("查询模板类型异常: {}", templateType, e);
            return Result.error("查询模板类型异常：" + e.getMessage());
        }
    }

    /**
     * 根据通知类型查询模板列表
     */
    @GetMapping("/notification-type/{notificationType}")
    public Result<List<NotificationTemplate>> getTemplatesByNotificationType(@PathVariable String notificationType) {
        try {
            List<NotificationTemplate> templates = notificationTemplateService.getTemplatesByNotificationType(notificationType);
            return Result.success(templates);
        } catch (Exception e) {
            log.error("查询通知类型模板异常: {}", notificationType, e);
            return Result.error("查询通知类型模板异常：" + e.getMessage());
        }
    }

    /**
     * 查询启用的模板
     */
    @GetMapping("/enabled")
    public Result<List<NotificationTemplate>> getEnabledTemplates(
            @RequestParam(required = false) String notificationType,
            @RequestParam(required = false) String templateType) {
        try {
            List<NotificationTemplate> templates = notificationTemplateService.getEnabledTemplates(notificationType, templateType);
            return Result.success(templates);
        } catch (Exception e) {
            log.error("查询启用模板异常", e);
            return Result.error("查询启用模板异常：" + e.getMessage());
        }
    }

    /**
     * 分页查询模板
     */
    @GetMapping("/page")
    public Result<IPage<NotificationTemplate>> getTemplatesPage(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String templateType,
            @RequestParam(required = false) String notificationType,
            @RequestParam(required = false) String templateName,
            @RequestParam(required = false) Integer isEnabled) {
        try {
            Page<NotificationTemplate> page = new Page<>(current, size);
            IPage<NotificationTemplate> result = notificationTemplateService.getTemplatesPage(page, templateType, notificationType, templateName, isEnabled);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询模板异常", e);
            return Result.error("分页查询模板异常：" + e.getMessage());
        }
    }

    /**
     * 检查模板编码是否存在
     */
    @GetMapping("/check-code")
    public Result<Boolean> isTemplateCodeExists(
            @RequestParam String templateCode,
            @RequestParam(required = false) Long excludeId) {
        try {
            boolean exists = notificationTemplateService.isTemplateCodeExists(templateCode, excludeId);
            return Result.success(exists);
        } catch (Exception e) {
            log.error("检查模板编码异常: {}", templateCode, e);
            return Result.error("检查模板编码异常：" + e.getMessage());
        }
    }

    /**
     * 渲染模板内容
     */
    @PostMapping("/render-content")
    public Result<String> renderTemplateContent(
            @RequestParam String templateCode,
            @RequestBody(required = false) Map<String, Object> params) {
        try {
            String content = notificationTemplateService.renderTemplateContent(templateCode, params);
            return content != null ? Result.success(content) : Result.error("渲染模板内容失败");
        } catch (Exception e) {
            log.error("渲染模板内容异常: {}", templateCode, e);
            return Result.error("渲染模板内容异常：" + e.getMessage());
        }
    }

    /**
     * 渲染模板标题
     */
    @PostMapping("/render-title")
    public Result<String> renderTemplateTitle(
            @RequestParam String templateCode,
            @RequestBody(required = false) Map<String, Object> params) {
        try {
            String title = notificationTemplateService.renderTemplateTitle(templateCode, params);
            return title != null ? Result.success(title) : Result.error("渲染模板标题失败");
        } catch (Exception e) {
            log.error("渲染模板标题异常: {}", templateCode, e);
            return Result.error("渲染模板标题异常：" + e.getMessage());
        }
    }

    /**
     * 根据模板和参数生成通知内容
     */
    @PostMapping("/generate-content")
    public Result<Map<String, String>> generateNotificationContent(
            @RequestParam String templateCode,
            @RequestBody(required = false) Map<String, Object> params) {
        try {
            Map<String, String> content = notificationTemplateService.generateNotificationContent(templateCode, params);
            return content != null ? Result.success(content) : Result.error("生成通知内容失败");
        } catch (Exception e) {
            log.error("生成通知内容异常: {}", templateCode, e);
            return Result.error("生成通知内容异常：" + e.getMessage());
        }
    }

    /**
     * 验证模板参数
     */
    @PostMapping("/validate-params")
    public Result<Boolean> validateTemplateParams(
            @RequestParam String templateCode,
            @RequestBody(required = false) Map<String, Object> params) {
        try {
            boolean valid = notificationTemplateService.validateTemplateParams(templateCode, params);
            return Result.success(valid);
        } catch (Exception e) {
            log.error("验证模板参数异常: {}", templateCode, e);
            return Result.error("验证模板参数异常：" + e.getMessage());
        }
    }

    /**
     * 获取模板参数描述
     */
    @GetMapping("/{templateCode}/params-desc")
    public Result<Map<String, Object>> getTemplateParamsDesc(@PathVariable String templateCode) {
        try {
            Map<String, Object> paramsDesc = notificationTemplateService.getTemplateParamsDesc(templateCode);
            return Result.success(paramsDesc);
        } catch (Exception e) {
            log.error("获取模板参数描述异常: {}", templateCode, e);
            return Result.error("获取模板参数描述异常：" + e.getMessage());
        }
    }

    /**
     * 复制模板
     */
    @PostMapping("/{id}/copy")
    public Result<NotificationTemplate> copyTemplate(
            @PathVariable Long id,
            @RequestParam String newTemplateCode,
            @RequestParam String newTemplateName) {
        try {
            NotificationTemplate copied = notificationTemplateService.copyTemplate(id, newTemplateCode, newTemplateName);
            return copied != null ? Result.success(copied) : Result.error("复制模板失败");
        } catch (Exception e) {
            log.error("复制模板异常: {}", id, e);
            return Result.error("复制模板异常：" + e.getMessage());
        }
    }

    /**
     * 导入模板
     */
    @PostMapping("/import")
    public Result<Boolean> importTemplates(@RequestBody List<NotificationTemplate> templates) {
        try {
            boolean success = notificationTemplateService.importTemplates(templates);
            return Result.success(success);
        } catch (Exception e) {
            log.error("导入模板异常", e);
            return Result.error("导入模板异常：" + e.getMessage());
        }
    }

    /**
     * 导出模板
     */
    @PostMapping("/export")
    public Result<List<NotificationTemplate>> exportTemplates(@RequestBody List<Long> templateIds) {
        try {
            List<NotificationTemplate> templates = notificationTemplateService.exportTemplates(templateIds);
            return Result.success(templates);
        } catch (Exception e) {
            log.error("导出模板异常", e);
            return Result.error("导出模板异常：" + e.getMessage());
        }
    }

    /**
     * 获取所有模板类型
     */
    @GetMapping("/template-types")
    public Result<List<String>> getAllTemplateTypes() {
        try {
            List<String> templateTypes = notificationTemplateService.getAllTemplateTypes();
            return Result.success(templateTypes);
        } catch (Exception e) {
            log.error("获取模板类型异常", e);
            return Result.error("获取模板类型异常：" + e.getMessage());
        }
    }

    /**
     * 获取所有通知类型
     */
    @GetMapping("/notification-types")
    public Result<List<String>> getAllNotificationTypes() {
        try {
            List<String> notificationTypes = notificationTemplateService.getAllNotificationTypes();
            return Result.success(notificationTypes);
        } catch (Exception e) {
            log.error("获取通知类型异常", e);
            return Result.error("获取通知类型异常：" + e.getMessage());
        }
    }

    /**
     * 统计模板使用情况
     */
    @GetMapping("/usage-statistics")
    public Result<Map<String, Object>> getTemplateUsageStatistics() {
        try {
            Map<String, Object> statistics = notificationTemplateService.getTemplateUsageStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取模板使用统计异常", e);
            return Result.error("获取模板使用统计异常：" + e.getMessage());
        }
    }

    /**
     * 获取热门模板
     */
    @GetMapping("/popular")
    public Result<List<NotificationTemplate>> getPopularTemplates(
            @RequestParam(required = false, defaultValue = "10") Integer limit) {
        try {
            List<NotificationTemplate> templates = notificationTemplateService.getPopularTemplates(limit);
            return Result.success(templates);
        } catch (Exception e) {
            log.error("获取热门模板异常", e);
            return Result.error("获取热门模板异常：" + e.getMessage());
        }
    }
} 