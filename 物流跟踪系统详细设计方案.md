# 物流跟踪系统详细设计方案

## 🎯 系统用户角色和权限设计

### 用户角色定义
```
1. 普通用户 (Customer)
   ├── 寄件人 (Sender)
   └── 收件人 (Receiver)

2. 操作员 (Operator)
   ├── 揽件员 (Pickup Staff)
   ├── 分拣员 (Sorting Staff)
   ├── 中转员 (Transit Staff)
   └── 客服 (Customer Service)

3. 配送员 (Courier)
   ├── 同城配送员
   └── 最后一公里配送员

4. 管理员 (Admin)
   ├── 系统管理员
   └── 业务管理员
```

## 🏗️ 微服务架构设计

### 服务拆分策略
```
┌─────────────────────────────────────────────────────────────────┐
│                        API Gateway (网关层)                     │
│                     Nginx/Spring Cloud Gateway                  │
└─────────────────────┬───────────────────────────────────────────┘
                      │
    ┌─────────────────┼─────────────────┐
    │                 │                 │
┌───▼────┐    ┌──────▼──────┐    ┌────▼─────┐
│用户服务│    │  订单服务   │    │ 物流服务 │
│        │    │             │    │          │
│• 认证  │    │• 下单       │    │• 轨迹    │
│• 权限  │    │• 运费计算   │    │• 状态    │
│• 用户  │    │• 订单管理   │    │• 路径    │
└────────┘    └─────────────┘    └──────────┘

┌─────────┐    ┌─────────────┐    ┌──────────┐
│配送服务 │    │  通知服务   │    │ 地图服务 │
│         │    │             │    │          │
│• 配送员 │    │• 短信       │    │• 路径    │
│• 路线   │    │• 邮件       │    │• 定位    │
│• 绩效   │    │• 推送       │    │• 计算    │
└─────────┘    └─────────────┘    └──────────┘

┌─────────┐    ┌─────────────┐    ┌──────────┐
│文件服务 │    │  支付服务   │    │ 报表服务 │
│         │    │             │    │          │
│• 图片   │    │• 在线支付   │    │• 统计    │
│• 签收   │    │• 费用结算  │    │• 分析    │
│• 证件   │    │• 账单       │    │• 导出    │
└─────────┘    └─────────────┘    └──────────┘
```

## 📊 详细的物流业务流程

### 完整的订单生命周期
```
1. 用户下单阶段
   ├── 用户填写寄件信息
   ├── 系统计算运费
   ├── 用户确认下单
   └── 系统生成订单号

2. 揽件阶段
   ├── 系统分配揽件员
   ├── 揽件员上门取件
   └── 更新订单状态为"已揽件"

3. 分拣中转阶段
   ├── 到达始发网点
   ├── 分拣员扫描分拣
   ├── 装车发往目的地
   ├── 中转站中转
   └── 每个节点更新轨迹

4. 派送阶段
   ├── 到达目的网点
   ├── 分配配送员
   ├── 配送员派送
   ├── 联系收件人
   ├── 完成签收
   └── 订单完成
```

## 🔧 微服务详细设计

### 1. 用户服务 (User Service) - 端口: 8001
```java
主要职责：
├── 用户注册/登录/登出
├── JWT Token管理
├── 权限验证
├── 用户信息管理
└── 角色权限管理

核心API：
├── POST /api/user/register          # 用户注册
├── POST /api/user/login             # 用户登录
├── GET  /api/user/profile           # 获取用户信息
├── PUT  /api/user/profile           # 更新用户信息
├── POST /api/user/logout            # 用户登出
├── GET  /api/user/permissions       # 获取用户权限
└── POST /api/user/change-password   # 修改密码

数据表：
├── users (用户表)
├── roles (角色表)
├── permissions (权限表)
└── user_roles (用户角色关联表)
```

### 2. 订单服务 (Order Service) - 端口: 8002
```java
主要职责：
├── 订单创建和管理
├── 运费计算引擎
├── 订单状态流转
├── 订单查询和统计
└── 订单取消和退款

核心API：
├── POST /api/order/create           # 创建订单
├── GET  /api/order/{id}             # 获取订单详情
├── GET  /api/order/list             # 订单列表查询
├── POST /api/order/calculate-fee    # 计算运费
├── PUT  /api/order/{id}/status      # 更新订单状态
├── DELETE /api/order/{id}           # 取消订单
├── GET  /api/order/track/{number}   # 订单追踪
└── POST /api/order/batch-create     # 批量创建订单

数据表：
├── orders (订单主表)
├── order_items (订单明细)
├── shipping_addresses (收发货地址)
└── order_status_log (状态变更日志)
```

### 3. 物流服务 (Logistics Service) - 端口: 8003
```java
主要职责：
├── 物流轨迹记录和查询
├── 运输路线规划
├── 网点管理
├── 物流状态更新
└── 轨迹数据分析

核心API：
├── POST /api/logistics/trace        # 添加轨迹记录
├── GET  /api/logistics/trace/{orderNumber} # 获取轨迹
├── PUT  /api/logistics/trace/{id}   # 更新轨迹
├── GET  /api/logistics/route        # 获取运输路线
├── POST /api/logistics/scan         # 扫描操作
├── GET  /api/logistics/stations     # 获取网点信息
└── POST /api/logistics/batch-scan   # 批量扫描

数据库 (MongoDB)：
├── logistics_traces (轨迹记录)
├── transport_routes (运输路线)
├── logistics_stations (物流网点)
└── scan_records (扫描记录)
```

### 4. 配送服务 (Delivery Service) - 端口: 8004
```java
主要职责：
├── 配送员管理
├── 配送任务分配
├── 实时位置追踪
├── 配送路线优化
└── 配送绩效统计

核心API：
├── GET  /api/delivery/couriers      # 获取配送员列表
├── POST /api/delivery/assign        # 分配配送任务
├── GET  /api/delivery/tasks         # 获取配送任务
├── POST /api/delivery/location      # 上报位置
├── GET  /api/delivery/route/{id}    # 获取配送路线
├── POST /api/delivery/complete      # 完成配送
├── GET  /api/delivery/performance   # 配送绩效
└── POST /api/delivery/sign          # 签收确认

数据表：
├── couriers (配送员表)
├── delivery_tasks (配送任务)
├── delivery_routes (配送路线)
├── location_records (位置记录)
└── delivery_performance (绩效统计)
```

### 5. 通知服务 (Notification Service) - 端口: 8005
```java
主要职责：
├── 短信通知发送
├── 邮件通知发送
├── 推送通知
├── 通知模板管理
└── 通知历史记录

核心API：
├── POST /api/notification/sms       # 发送短信
├── POST /api/notification/email     # 发送邮件
├── POST /api/notification/push      # 推送通知
├── GET  /api/notification/templates # 获取模板
├── POST /api/notification/batch     # 批量通知
└── GET  /api/notification/history   # 通知历史

集成方案：
├── 短信: 阿里云SMS/腾讯云SMS
├── 邮件: 阿里云邮件/腾讯云邮件
├── 钉钉: Webhook集成
├── 飞书: Webhook集成
└── 消息队列: RabbitMQ异步处理
```

### 6. 地图服务 (Map Service) - 端口: 8006
```java
主要职责：
├── 地理编码服务
├── 路径规划
├── 距离计算
├── 实时位置展示
└── 地图数据缓存

核心API：
├── POST /api/map/geocode            # 地址转坐标
├── POST /api/map/reverse-geocode    # 坐标转地址
├── POST /api/map/route-plan         # 路径规划
├── POST /api/map/distance           # 距离计算
├── GET  /api/map/real-time/{id}     # 实时位置
└── POST /api/map/batch-geocode      # 批量地理编码

第三方集成：
├── 高德地图API
├── 百度地图API (备选)
└── 腾讯地图API (备选)
```

## 🎨 Vue前端架构设计

### 项目结构
```
logistics-vue/
├── src/
│   ├── components/          # 公共组件
│   │   ├── Layout/         # 布局组件
│   │   ├── Map/            # 地图组件
│   │   ├── Order/          # 订单组件
│   │   └── Common/         # 通用组件
│   ├── views/              # 页面视图
│   │   ├── Customer/       # 用户端页面
│   │   ├── Operator/       # 操作员页面
│   │   ├── Courier/        # 配送员页面
│   │   └── Admin/          # 管理员页面
│   ├── router/             # 路由配置
│   ├── store/              # Vuex/Pinia状态管理
│   ├── api/                # API接口封装
│   ├── utils/              # 工具函数
│   └── assets/             # 静态资源
├── public/
└── package.json
```

### 页面规划

#### 1. 用户端 (Customer Portal)
```
├── 登录/注册页面
├── 个人中心
├── 寄件下单页面
│   ├── 寄件人信息填写
│   ├── 收件人信息填写
│   ├── 物品信息录入
│   ├── 运费计算展示
│   └── 下单确认
├── 订单管理页面
│   ├── 订单列表
│   ├── 订单详情
│   └── 订单追踪
├── 物流追踪页面
│   ├── 轨迹时间线
│   ├── 地图路径展示
│   └── 实时位置
├── 地址管理页面
├── 收发货记录
└── 客服咨询页面
```

#### 2. 操作员端 (Operator Portal)
```
├── 登录页面
├── 工作台
├── 订单处理页面
│   ├── 待处理订单
│   ├── 订单扫描
│   ├── 状态更新
│   └── 批量操作
├── 轨迹录入页面
│   ├── 扫描二维码
│   ├── 位置信息录入
│   ├── 操作说明
│   └── 拍照上传
├── 库存管理
├── 设备管理
└── 工作统计
```

#### 3. 配送员端 (Courier Portal)
```
├── 登录页面
├── 任务中心
├── 配送任务页面
│   ├── 待配送订单
│   ├── 配送路线规划
│   ├── 导航集成
│   └── 任务状态更新
├── 实时位置上报
├── 签收管理页面
│   ├── 电子签名
│   ├── 拍照确认
│   ├── 收件人确认
│   └── 异常情况处理
├── 绩效统计
└── 个人设置
```

#### 4. 管理员端 (Admin Portal)
```
├── 系统概览
├── 用户管理
├── 订单管理
├── 配送员管理
├── 网点管理
├── 数据统计
│   ├── 订单统计
│   ├── 收入统计
│   ├── 配送统计
│   └── 用户统计
├── 系统配置
├── 日志查询
└── 权限管理
```

## 📱 技术实现要点

### 1. 运费计算引擎
```java
@Service
public class ShippingFeeCalculator {
    
    public BigDecimal calculateFee(ShippingRequest request) {
        // 基础费用
        BigDecimal baseFee = getBaseFee(request.getServiceType());
        
        // 重量费用 (首重+续重)
        BigDecimal weightFee = calculateWeightFee(request.getWeight());
        
        // 距离费用
        BigDecimal distanceFee = calculateDistanceFee(request.getDistance());
        
        // 体积费用 (泡货)
        BigDecimal volumeFee = calculateVolumeFee(request.getVolume());
        
        // 增值服务费
        BigDecimal serviceFee = calculateServiceFee(request.getServices());
        
        // 折扣和优惠
        BigDecimal discount = calculateDiscount(request);
        
        BigDecimal totalFee = baseFee.add(weightFee)
                                   .add(distanceFee)
                                   .add(volumeFee)
                                   .add(serviceFee)
                                   .subtract(discount);
        
        return totalFee.max(BigDecimal.ZERO);
    }
}
```

### 2. 物流轨迹MongoDB存储
```json
{
  "_id": "ObjectId",
  "orderNumber": "SF202412250001",
  "createTime": "2024-12-25T08:00:00Z",
  "traces": [
    {
      "id": "trace_001",
      "timestamp": "2024-12-25T10:00:00Z",
      "operation": "PICKUP",
      "operationType": "揽件",
      "location": {
        "longitude": 116.397428,
        "latitude": 39.90923,
        "address": "北京市朝阳区建国门外大街1号",
        "city": "北京市",
        "district": "朝阳区"
      },
      "operator": {
        "id": "emp_001",
        "name": "张三",
        "phone": "13888888888",
        "role": "PICKUP_STAFF"
      },
      "description": "快件已从【北京朝阳营业点】揽收",
      "images": ["image1.jpg", "image2.jpg"],
      "scanCode": "SF202412250001",
      "weight": 1.2,
      "nextStation": "北京分拣中心"
    }
  ],
  "currentStatus": "IN_TRANSIT",
  "estimatedDelivery": "2024-12-26T18:00:00Z"
}
```

### 3. 实时位置追踪WebSocket
```javascript
// 前端WebSocket连接
const websocket = new WebSocket('ws://localhost:8004/ws/location');

// 配送员位置上报
function reportLocation(latitude, longitude) {
    const locationData = {
        courierId: getCurrentCourierId(),
        latitude: latitude,
        longitude: longitude,
        timestamp: new Date().toISOString(),
        accuracy: 10
    };
    websocket.send(JSON.stringify(locationData));
}

// 用户端实时订阅
function subscribeOrderLocation(orderNumber) {
    websocket.send(JSON.stringify({
        type: 'SUBSCRIBE',
        orderNumber: orderNumber
    }));
}
```

### 4. RabbitMQ异步消息处理
```java
// 订单状态变更消息
@RabbitListener(queues = "order.status.update")
public void handleOrderStatusUpdate(OrderStatusUpdateMessage message) {
    // 更新订单状态
    orderService.updateStatus(message.getOrderId(), message.getNewStatus());
    
    // 发送通知
    notificationService.sendStatusNotification(message);
    
    // 更新物流轨迹
    logisticsService.addTrace(message.toTraceRecord());
}

// 通知发送队列
@RabbitListener(queues = "notification.send")
public void handleNotificationSend(NotificationMessage message) {
    switch (message.getType()) {
        case SMS:
            smsService.send(message);
            break;
        case EMAIL:
            emailService.send(message);
            break;
        case PUSH:
            pushService.send(message);
            break;
    }
}
```

## 🔄 开发阶段划分

### 第一阶段：微服务基础架构 (2周)
1. 搭建微服务注册中心 (Nacos/Eureka)
2. 配置服务网关 (Spring Cloud Gateway)
3. 搭建配置中心
4. 建立各服务基础框架
5. 数据库设计和建表

### 第二阶段：用户和订单核心功能 (2周)
1. 用户服务完整实现
2. 订单服务核心功能
3. 运费计算引擎
4. 基础权限控制

### 第三阶段：物流和配送服务 (2周)
1. 物流轨迹服务 (MongoDB)
2. 配送员管理服务
3. 地图服务集成
4. 实时位置追踪

### 第四阶段：通知和缓存优化 (1周)
1. 通知服务完整实现
2. RabbitMQ消息队列集成
3. Redis缓存优化
4. AOP日志记录

### 第五阶段：Vue前端开发 (3周)
1. 前端基础架构搭建
2. 用户端页面开发
3. 操作员/配送员端开发
4. 管理员端开发

### 第六阶段：集成测试和优化 (1周)
1. 端到端集成测试
2. 性能优化
3. 安全加固
4. 部署和监控

## 📋 数据库设计

### MySQL表结构设计

#### 用户相关表
```sql
-- 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    real_name VARCHAR(50),
    avatar_url VARCHAR(255),
    status TINYINT DEFAULT 1,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 角色表
CREATE TABLE roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    role_name VARCHAR(50) UNIQUE NOT NULL,
    role_code VARCHAR(50) UNIQUE NOT NULL,
    description VARCHAR(255),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 权限表
CREATE TABLE permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    permission_name VARCHAR(50) NOT NULL,
    permission_code VARCHAR(50) UNIQUE NOT NULL,
    resource_type VARCHAR(20),
    resource_url VARCHAR(255),
    description VARCHAR(255)
);

-- 用户角色关联表
CREATE TABLE user_roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (role_id) REFERENCES roles(id)
);
```

#### 订单相关表
```sql
-- 订单主表
CREATE TABLE orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    user_id BIGINT NOT NULL,
    sender_name VARCHAR(50) NOT NULL,
    sender_phone VARCHAR(20) NOT NULL,
    sender_address TEXT NOT NULL,
    receiver_name VARCHAR(50) NOT NULL,
    receiver_phone VARCHAR(20) NOT NULL,
    receiver_address TEXT NOT NULL,
    item_name VARCHAR(255),
    item_weight DECIMAL(10,2),
    item_volume DECIMAL(10,2),
    shipping_fee DECIMAL(10,2),
    insurance_fee DECIMAL(10,2),
    total_fee DECIMAL(10,2),
    payment_status TINYINT DEFAULT 0,
    order_status VARCHAR(20) DEFAULT 'PENDING',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 订单状态变更日志
CREATE TABLE order_status_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_id BIGINT NOT NULL,
    old_status VARCHAR(20),
    new_status VARCHAR(20) NOT NULL,
    operator_id BIGINT,
    change_reason VARCHAR(255),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id)
);
```

#### 配送相关表
```sql
-- 配送员表
CREATE TABLE couriers (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    courier_code VARCHAR(50) UNIQUE NOT NULL,
    vehicle_type VARCHAR(20),
    vehicle_number VARCHAR(20),
    work_area VARCHAR(255),
    status TINYINT DEFAULT 1,
    current_latitude DECIMAL(10,8),
    current_longitude DECIMAL(11,8),
    last_location_time TIMESTAMP,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 配送任务表
CREATE TABLE delivery_tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_id BIGINT NOT NULL,
    courier_id BIGINT NOT NULL,
    task_type VARCHAR(20) NOT NULL,
    priority TINYINT DEFAULT 1,
    estimated_time TIMESTAMP,
    actual_start_time TIMESTAMP,
    actual_end_time TIMESTAMP,
    task_status VARCHAR(20) DEFAULT 'ASSIGNED',
    notes TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id),
    FOREIGN KEY (courier_id) REFERENCES couriers(id)
);
```

### MongoDB集合设计

#### 物流轨迹集合
```javascript
// logistics_traces 集合
{
  _id: ObjectId,
  orderNumber: String,        // 订单号
  orderId: Long,             // 订单ID
  createTime: Date,          // 创建时间
  updateTime: Date,          // 更新时间
  currentStatus: String,     // 当前状态
  estimatedDelivery: Date,   // 预计送达时间
  traces: [                  // 轨迹数组
    {
      id: String,            // 轨迹ID
      timestamp: Date,       // 时间戳
      operation: String,     // 操作类型
      operationType: String, // 操作类型中文
      location: {
        longitude: Number,   // 经度
        latitude: Number,    // 纬度
        address: String,     // 地址
        city: String,        // 城市
        district: String     // 区域
      },
      operator: {
        id: String,          // 操作员ID
        name: String,        // 操作员姓名
        phone: String,       // 电话
        role: String         // 角色
      },
      description: String,   // 描述
      images: [String],      // 图片URL数组
      scanCode: String,      // 扫描码
      weight: Number,        // 重量
      nextStation: String,   // 下一站
      remarks: String        // 备注
    }
  ]
}
```

#### 位置记录集合
```javascript
// location_records 集合
{
  _id: ObjectId,
  courierId: Long,           // 配送员ID
  orderNumber: String,       // 订单号 (可选)
  latitude: Number,          // 纬度
  longitude: Number,         // 经度
  accuracy: Number,          // 精度
  address: String,           // 地址
  timestamp: Date,           // 时间戳
  recordType: String,        // 记录类型: TRACK, PICKUP, DELIVERY
  batteryLevel: Number,      // 电量
  networkType: String        // 网络类型
}
```

## 🔧 技术栈和依赖

### 后端技术栈
```
├── Spring Boot 2.7.x
├── Spring Cloud 2021.x
├── Spring Security + JWT
├── MyBatis-Plus
├── Spring Data MongoDB
├── Spring Data Redis
├── RabbitMQ
├── MySQL 8.0
├── MongoDB 4.4+
├── Redis 6.0+
├── Nacos (注册中心+配置中心)
├── Spring Cloud Gateway
└── Docker + Docker Compose
```

### 前端技术栈
```
├── Vue 3.x
├── Vue Router 4.x
├── Pinia (状态管理)
├── Element Plus
├── Axios
├── 高德地图 JavaScript API
├── WebSocket
├── Vite
└── TypeScript (可选)
```

---

*最后更新时间: 2024-12-25* 
，

### 价格计算逻辑

后端计算逻辑（PricingServiceImpl）：

1. 基础运费：10元起步价

1. 重量费：超过1公斤免费重量后，每公斤2元

1. 距离费：超过10公里免费距离后，每公里0.5元

1. 保险费：物品价值的1%

1. 包装费：普通5元，易碎品加倍至7.5元

1. 服务类型倍数：

- STANDARD（标准）：1.0倍

- EXPRESS（快递）：1.5倍

- URGENT（加急）：2.0倍

前端处理：

- 体积计算：长×宽×高(cm) ÷ 1,000,000 = 立方米

- 实时费用计算：选择城市、修改重量、体积、价值、服务类型时自动计算

- 费用展示：运费、保险费、包装费、总计分别显示