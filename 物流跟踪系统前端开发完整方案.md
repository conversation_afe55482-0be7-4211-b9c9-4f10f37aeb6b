# 物流跟踪系统前端开发完整方案

## 📋 项目概述

**项目名称：** 物流跟踪系统前端  
**技术栈：** Vue 3.5 + TypeScript + Vite + Element Plus + Pinia  
**开发模式：** 分阶段迭代开发  
**预计工期：** 7-10 天  
**目标用户：** 普通用户、操作员、配送员、管理员  

---

## 🎯 开发阶段规划

### 第一阶段：项目重构与基础架构 (2天)
- 项目初始化和依赖配置
- 基础架构搭建（HTTP封装、类型定义、状态管理）
- 路由配置和权限控制

### 第二阶段：用户认证模块 (1天)
- 登录注册页面开发
- 权限管理和路由守卫
- 基础测试验证

### 第三阶段：客户端功能开发 (3天)
- 用户首页和布局
- 订单创建和管理
- 物流追踪功能
- 个人中心

### 第四阶段：管理端功能开发 (3天)
- 管理员仪表板
- 用户和订单管理
- 配送管理
- 系统配置

### 第五阶段：高级功能与优化 (2天)
- 地图集成
- 实时通信
- 性能优化
- 用户体验提升

---

## 🚀 实施步骤

### 步骤1：项目初始化

```bash
# 1. 删除现有项目
cd /e:/桌面/springBoot/node
rm -rf logistics-frontend

# 2. 创建新项目
npm create vue@latest logistics-frontend
# 选择：TypeScript、Router、Pinia、ESLint、Prettier

# 3. 安装依赖
cd logistics-frontend
npm install
npm install element-plus @element-plus/icons-vue axios dayjs lodash-es
npm install @types/lodash-es unplugin-auto-import unplugin-vue-components -D
```

### 步骤2：配置文件

**vite.config.ts**
```typescript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

export default defineConfig({
  plugins: [
    vue(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
      imports: ['vue', 'vue-router', 'pinia']
    }),
    Components({
      resolvers: [ElementPlusResolver()]
    })
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true
      }
    }
  }
})
```

### 步骤3：创建目录结构

```bash
mkdir -p src/api src/assets/{images,icons,styles}
mkdir -p src/components/{Layout,Common}
mkdir -p src/layouts src/stores src/types src/utils
mkdir -p src/views/{auth,customer/{order,tracking,profile},admin/{user,order},operator,courier,error}
```

### 步骤4：核心代码实现

**src/utils/http.ts** - HTTP请求封装
```typescript
import axios, { type AxiosInstance, type AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import router from '@/router'

export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
}

const http: AxiosInstance = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: { 'Content-Type': 'application/json' }
})

// 请求拦截器
http.interceptors.request.use((config) => {
  const authStore = useAuthStore()
  if (authStore.token) {
    config.headers.Authorization = `Bearer ${authStore.token}`
  }
  return config
})

// 响应拦截器
http.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { data } = response
    if (data.code === 200 || data.success) {
      return data
    } else {
      ElMessage.error(data.message || '请求失败')
      return Promise.reject(new Error(data.message))
    }
  },
  (error) => {
    if (error.response?.status === 401) {
      const authStore = useAuthStore()
      authStore.logout()
      router.push('/login')
      ElMessage.error('登录已过期，请重新登录')
    } else {
      ElMessage.error(error.message || '网络错误')
    }
    return Promise.reject(error)
  }
)

export default http
```

**src/types/user.ts** - 用户类型定义
```typescript
export interface UserInfo {
  id: number
  username: string
  realName: string
  phone: string
  email: string
  userType: 'CUSTOMER' | 'OPERATOR' | 'COURIER' | 'ADMIN'
  status: string
  avatar?: string
  roles: string[]
  permissions: string[]
  createTime: string
}

export interface LoginRequest {
  username: string
  password: string
  loginType?: 'USERNAME' | 'PHONE' | 'EMAIL'
  rememberMe?: boolean
}

export interface LoginResponse {
  token: string
  tokenType: string
  expiresIn: number
  userInfo: UserInfo
  loginTime: string
}

export interface RegisterRequest {
  username: string
  password: string
  confirmPassword?: string
  realName: string
  phone: string
  email: string
  idCard: string
  userType: 'CUSTOMER' | 'OPERATOR' | 'COURIER' | 'ADMIN'
  verificationCode: string
}
```

**src/api/auth.ts** - 认证API
```typescript
import http, { type ApiResponse } from '@/utils/http'
import type { LoginRequest, LoginResponse, RegisterRequest } from '@/types/user'

export const authApi = {
  login(data: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    return http.post('/user/login', data)
  },
  
  register(data: RegisterRequest): Promise<ApiResponse<LoginResponse>> {
    return http.post('/user/register', data)
  },
  
  getUserInfo(): Promise<ApiResponse<any>> {
    return http.get('/user/profile')
  },
  
  checkUsername(username: string): Promise<ApiResponse<boolean>> {
    return http.get('/user/check-username', { params: { username } })
  },
  
  checkPhone(phone: string): Promise<ApiResponse<boolean>> {
    return http.get('/user/check-phone', { params: { phone } })
  },
  
  checkEmail(email: string): Promise<ApiResponse<boolean>> {
    return http.get('/user/check-email', { params: { email } })
  },
  
  logout(): Promise<ApiResponse<void>> {
    return http.post('/user/logout')
  }
}
```

**src/stores/auth.ts** - 认证状态管理
```typescript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authApi } from '@/api/auth'
import type { UserInfo, LoginRequest, RegisterRequest } from '@/types/user'
import { ElMessage } from 'element-plus'
import router from '@/router'

export const useAuthStore = defineStore('auth', () => {
  const token = ref<string>(localStorage.getItem('token') || '')
  const userInfo = ref<UserInfo | null>(null)
  const loading = ref(false)

  const isLoggedIn = computed(() => !!token.value)
  const userRole = computed(() => userInfo.value?.userType || '')

  const login = async (loginData: LoginRequest): Promise<boolean> => {
    try {
      loading.value = true
      const response = await authApi.login(loginData)
      
      if (response.success) {
        token.value = response.data.token
        userInfo.value = response.data.userInfo
        
        localStorage.setItem('token', response.data.token)
        localStorage.setItem('userInfo', JSON.stringify(response.data.userInfo))
        
        ElMessage.success('登录成功')
        
        // 根据用户角色跳转
        const redirectPath = getRedirectPath(response.data.userInfo.userType)
        router.push(redirectPath)
        
        return true
      }
      return false
    } catch (error) {
      console.error('登录失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  const register = async (registerData: RegisterRequest): Promise<boolean> => {
    try {
      loading.value = true
      const response = await authApi.register(registerData)
      
      if (response.success) {
        ElMessage.success('注册成功，请登录')
        router.push('/login')
        return true
      }
      return false
    } catch (error) {
      console.error('注册失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  const logout = (): void => {
    token.value = ''
    userInfo.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    router.push('/login')
    ElMessage.success('已退出登录')
  }

  const initUserInfo = (): void => {
    const savedUserInfo = localStorage.getItem('userInfo')
    if (savedUserInfo) {
      try {
        userInfo.value = JSON.parse(savedUserInfo)
      } catch (error) {
        logout()
      }
    }
  }

  const getRedirectPath = (userType: string): string => {
    const routes = {
      'ADMIN': '/admin/dashboard',
      'OPERATOR': '/operator/dashboard',
      'COURIER': '/courier/dashboard',
      'CUSTOMER': '/customer/dashboard'
    }
    return routes[userType] || '/customer/dashboard'
  }

  return {
    token, userInfo, loading, isLoggedIn, userRole,
    login, register, logout, initUserInfo
  }
})
```

**src/router/index.ts** - 路由配置
```typescript
import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      redirect: '/login'
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/auth/Login.vue'),
      meta: { requiresAuth: false, title: '用户登录' }
    },
    {
      path: '/register',
      name: 'Register',
      component: () => import('@/views/auth/Register.vue'),
      meta: { requiresAuth: false, title: '用户注册' }
    },
    {
      path: '/customer',
      component: () => import('@/layouts/CustomerLayout.vue'),
      meta: { requiresAuth: true, roles: ['CUSTOMER'] },
      children: [
        {
          path: 'dashboard',
          name: 'CustomerDashboard',
          component: () => import('@/views/customer/Dashboard.vue'),
          meta: { title: '客户首页' }
        }
      ]
    },
    {
      path: '/admin',
      component: () => import('@/layouts/AdminLayout.vue'),
      meta: { requiresAuth: true, roles: ['ADMIN'] },
      children: [
        {
          path: 'dashboard',
          name: 'AdminDashboard',
          component: () => import('@/views/admin/Dashboard.vue'),
          meta: { title: '管理后台' }
        }
      ]
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/error/NotFound.vue')
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 物流跟踪系统`
  }
  
  if (to.meta?.requiresAuth && !authStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    next('/login')
    return
  }
  
  if (to.meta?.roles && authStore.isLoggedIn) {
    const userRole = authStore.userRole
    const requiredRoles = to.meta.roles as string[]
    
    if (!requiredRoles.includes(userRole)) {
      ElMessage.error('没有访问权限')
      next('/login')
      return
    }
  }
  
  next()
})

export default router
```

**src/main.ts** - 应用入口
```typescript
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import App from './App.vue'
import router from './router'
import { useAuthStore } from './stores/auth'

const app = createApp(App)

// 注册 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(createPinia())
app.use(router)
app.use(ElementPlus)

// 初始化用户信息
const authStore = useAuthStore()
authStore.initUserInfo()

app.mount('#app')
```

**src/views/auth/Login.vue** - 登录页面
```vue
<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <h1>物流跟踪系统</h1>
        <p>欢迎登录</p>
      </div>
      
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="用户名/手机号/邮箱"
            size="large"
            prefix-icon="User"
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="密码"
            size="large"
            prefix-icon="Lock"
            show-password
            @keyup.enter="handleLogin"
          />
        </el-form-item>
        
        <el-form-item>
          <div class="login-options">
            <el-checkbox v-model="loginForm.rememberMe">记住我</el-checkbox>
            <el-link type="primary">忘记密码？</el-link>
          </div>
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            :loading="authStore.loading"
            @click="handleLogin"
            class="login-button"
          >
            登 录
          </el-button>
        </el-form-item>
        
        <el-form-item>
          <div class="register-link">
            还没有账户？
            <router-link to="/register">立即注册</router-link>
          </div>
        </el-form-item>
      </el-form>
      
      <!-- 测试账号 -->
      <div class="test-accounts">
        <h3>测试账号</h3>
        <div class="account-list">
          <div class="account-item" @click="fillTestAccount('customer01')">
            <span>普通用户</span>
            <small>customer01 / Admin123456</small>
          </div>
          <div class="account-item" @click="fillTestAccount('admin')">
            <span>管理员</span>
            <small>admin / Admin123456</small>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useAuthStore } from '@/stores/auth'
import type { FormInstance, FormRules } from 'element-plus'
import type { LoginRequest } from '@/types/user'

const authStore = useAuthStore()
const loginFormRef = ref<FormInstance>()

const loginForm = reactive<LoginRequest>({
  username: '',
  password: '',
  rememberMe: false
})

const loginRules: FormRules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
}

const handleLogin = async () => {
  if (!loginFormRef.value) return
  await loginFormRef.value.validate(async (valid) => {
    if (valid) {
      await authStore.login(loginForm)
    }
  })
}

const fillTestAccount = (username: string) => {
  loginForm.username = username
  loginForm.password = 'Admin123456'
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h1 {
  color: #303133;
  font-size: 28px;
  margin-bottom: 10px;
  font-weight: 600;
}

.login-header p {
  color: #909399;
  font-size: 16px;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.login-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
}

.register-link {
  text-align: center;
  color: #909399;
  font-size: 14px;
}

.test-accounts {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.test-accounts h3 {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
  text-align: center;
}

.account-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.account-item {
  padding: 8px 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  text-align: center;
}

.account-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.account-item span {
  display: block;
  font-size: 12px;
  font-weight: 500;
  color: #303133;
}

.account-item small {
  font-size: 10px;
  color: #909399;
  margin-top: 2px;
  display: block;
}
</style>
```

### 步骤5：准备测试数据

```sql
-- 在数据库中执行
USE logistics;

DELETE FROM user_roles;
DELETE FROM users;
ALTER TABLE users AUTO_INCREMENT = 1;
ALTER TABLE user_roles AUTO_INCREMENT = 1;

-- 插入测试用户 (密码: Admin123456)
INSERT INTO users (username, password, real_name, phone, email, id_card, user_type, status, create_time, update_time) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyZOgM7rmckABg1T2UlJkNNdwQa', '系统管理员', '***********', '<EMAIL>', '110101199001011001', 'ADMIN', 'ACTIVE', NOW(), NOW()),
('customer01', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyZOgM7rmckABg1T2UlJkNNdwQa', '张三', '***********', '<EMAIL>', '110101199001011301', 'CUSTOMER', 'ACTIVE', NOW(), NOW());

-- 分配用户角色
INSERT INTO user_roles (user_id, role_id, create_time) VALUES
(1, 4, NOW()),  -- admin -> 管理员
(2, 1, NOW());  -- customer01 -> 普通用户
```

### 步骤6：启动项目

```bash
# 1. 确保后端服务运行
# 2. 启动前端项目
npm run dev
# 3. 访问 http://localhost:3000
```

---

## 📋 测试账号

| 角色 | 用户名 | 密码 | 说明 |
|------|--------|------|------|
| 管理员 | admin | Admin123456 | 系统管理员 |
| 普通用户 | customer01 | Admin123456 | 张三 |

---

## 🎯 功能特性

### ✅ 已实现功能
- 用户登录注册
- 权限控制和路由守卫
- 响应式布局
- 状态管理
- API接口封装
- 错误处理

### 🚧 待开发功能
- 订单管理
- 物流追踪
- 地图集成
- 实时通信
- 数据可视化
- 文件上传

---

## 📞 技术支持

### 常见问题
1. **依赖安装失败** - 确保 Node.js >= 16
2. **端口被占用** - 修改 vite.config.ts 中的端口
3. **API 连接失败** - 确保后端服务正常运行
4. **登录失败** - 检查数据库中的测试数据

### 开发建议
1. 按阶段逐步开发，确保每个阶段功能完整
2. 及时测试，发现问题及时解决
3. 保持代码规范，使用 ESLint 和 Prettier
4. 注意用户体验，优化交互细节

这个完整方案提供了从项目初始化到基础功能实现的全部代码和步骤，您可以按照文档逐步实施，构建出功能完整的物流跟踪系统前端应用。 
response: ApiResponse<AvatarUploadResponse>

response: UploadResponse