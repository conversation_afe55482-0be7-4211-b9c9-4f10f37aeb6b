-- =============================================
-- 物流系统完整数据库表结构（与后端实体完全匹配）
-- 创建时间：2024-12-30
-- 说明：此脚本包含所有后端实体类对应的数据库表
-- =============================================

-- 删除数据库（如果存在）
DROP DATABASE IF EXISTS logistics;

-- 创建数据库
CREATE DATABASE logistics DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE logistics;

-- =============================================
-- 1. 用户管理相关表
-- =============================================

-- 用户表（对应User实体）
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` VARCHAR(50) NOT NULL COMMENT '用户名',
  `password` VARCHAR(255) NOT NULL COMMENT '密码',
  `real_name` VARCHAR(50) NOT NULL COMMENT '真实姓名',
  `phone` VARCHAR(20) NOT NULL COMMENT '手机号',
  `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
  `id_card` VARCHAR(18) DEFAULT NULL COMMENT '身份证号',
  `user_type` VARCHAR(20) NOT NULL DEFAULT 'CUSTOMER' COMMENT '用户类型: CUSTOMER, OPERATOR, COURIER, ADMIN',
  `status` VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '用户状态: ACTIVE, INACTIVE, LOCKED',
  `avatar` VARCHAR(255) DEFAULT NULL COMMENT '头像URL',
  `last_login_time` TIMESTAMP NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` VARCHAR(50) DEFAULT NULL COMMENT '登录IP',
  `gender` TINYINT(1) DEFAULT NULL COMMENT '性别：1-男，2-女',
  `birthday` DATE DEFAULT NULL COMMENT '生日',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_phone` (`phone`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 角色表
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` VARCHAR(50) NOT NULL COMMENT '角色名称',
  `role_code` VARCHAR(50) NOT NULL COMMENT '角色编码',
  `description` VARCHAR(255) DEFAULT NULL COMMENT '角色描述',
  `status` VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-启用，INACTIVE-禁用',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_code` (`role_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 权限表
DROP TABLE IF EXISTS `permissions`;
CREATE TABLE `permissions` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `permission_name` VARCHAR(100) NOT NULL COMMENT '权限名称',
  `permission_code` VARCHAR(100) NOT NULL COMMENT '权限编码',
  `resource_type` VARCHAR(50) NOT NULL DEFAULT 'MENU' COMMENT '资源类型：MENU-菜单，BUTTON-按钮，API-接口',
  `resource_url` VARCHAR(255) DEFAULT NULL COMMENT '资源URL',
  `parent_id` BIGINT(20) DEFAULT 0 COMMENT '父权限ID',
  `sort_order` INT(11) DEFAULT 0 COMMENT '排序',
  `status` VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-启用，INACTIVE-禁用',
  `description` VARCHAR(255) DEFAULT NULL COMMENT '权限描述',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_permission_code` (`permission_code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';

-- 用户角色关联表
DROP TABLE IF EXISTS `user_roles`;
CREATE TABLE `user_roles` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` BIGINT(20) NOT NULL COMMENT '用户ID',
  `role_id` BIGINT(20) NOT NULL COMMENT '角色ID',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_role` (`user_id`, `role_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';

-- 角色权限关联表
DROP TABLE IF EXISTS `role_permissions`;
CREATE TABLE `role_permissions` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `role_id` BIGINT(20) NOT NULL COMMENT '角色ID',
  `permission_id` BIGINT(20) NOT NULL COMMENT '权限ID',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_permission` (`role_id`, `permission_id`),
  KEY `idx_role_id` (`role_id`),
  KEY `idx_permission_id` (`permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';

-- =============================================
-- 2. 订单管理相关表
-- =============================================

-- 订单表（对应Order实体）
DROP TABLE IF EXISTS `orders`;
CREATE TABLE `orders` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_number` VARCHAR(32) NOT NULL COMMENT '订单号',
  `user_id` BIGINT(20) NOT NULL COMMENT '下单用户ID',
  
  -- 寄件人信息
  `sender_name` VARCHAR(50) NOT NULL COMMENT '寄件人姓名',
  `sender_phone` VARCHAR(20) NOT NULL COMMENT '寄件人电话',
  `sender_address` VARCHAR(255) NOT NULL COMMENT '寄件人地址',
  `sender_province` VARCHAR(50) DEFAULT NULL COMMENT '寄件人省份',
  `sender_city` VARCHAR(50) DEFAULT NULL COMMENT '寄件人城市',
  `sender_district` VARCHAR(50) DEFAULT NULL COMMENT '寄件人区域',
  `sender_longitude` DECIMAL(10,6) DEFAULT NULL COMMENT '寄件人经度',
  `sender_latitude` DECIMAL(10,6) DEFAULT NULL COMMENT '寄件人纬度',
  
  -- 收件人信息
  `receiver_name` VARCHAR(50) NOT NULL COMMENT '收件人姓名',
  `receiver_phone` VARCHAR(20) NOT NULL COMMENT '收件人电话',
  `receiver_address` VARCHAR(255) NOT NULL COMMENT '收件人地址',
  `receiver_province` VARCHAR(50) DEFAULT NULL COMMENT '收件人省份',
  `receiver_city` VARCHAR(50) DEFAULT NULL COMMENT '收件人城市',
  `receiver_district` VARCHAR(50) DEFAULT NULL COMMENT '收件人区域',
  `receiver_longitude` DECIMAL(10,6) DEFAULT NULL COMMENT '收件人经度',
  `receiver_latitude` DECIMAL(10,6) DEFAULT NULL COMMENT '收件人纬度',
  
  -- 物品信息
  `item_name` VARCHAR(100) NOT NULL COMMENT '物品名称',
  `item_type` VARCHAR(50) DEFAULT NULL COMMENT '物品类型',
  `item_weight` DECIMAL(10,2) NOT NULL COMMENT '物品重量(kg)',
  `item_volume` DECIMAL(10,2) DEFAULT NULL COMMENT '物品体积(立方米)',
  `item_length` DECIMAL(10,2) DEFAULT NULL COMMENT '物品长度(cm)',
  `item_width` DECIMAL(10,2) DEFAULT NULL COMMENT '物品宽度(cm)',
  `item_height` DECIMAL(10,2) DEFAULT NULL COMMENT '物品高度(cm)',
  `item_value` DECIMAL(10,2) DEFAULT NULL COMMENT '物品价值(元)',
  `is_fragile` TINYINT(1) DEFAULT 0 COMMENT '是否易碎：0-否，1-是',
  
  -- 服务和费用
  `service_type` VARCHAR(20) NOT NULL COMMENT '服务类型：STANDARD-标准，EXPRESS-快递，URGENT-加急',
  `shipping_fee` DECIMAL(10,2) DEFAULT NULL COMMENT '运费',
  `insurance_fee` DECIMAL(10,2) DEFAULT NULL COMMENT '保险费',
  `packing_fee` DECIMAL(10,2) DEFAULT NULL COMMENT '包装费',
  `total_fee` DECIMAL(10,2) NOT NULL COMMENT '总费用(元)',
  
  -- 支付信息
  `payment_method` VARCHAR(20) DEFAULT NULL COMMENT '支付方式：ONLINE-在线支付，COD-货到付款',
  `payment_status` INT(11) NOT NULL DEFAULT 0 COMMENT '支付状态：0-未支付，1-已支付，2-已退款',
  
  -- 状态和时间
  `order_status` VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '订单状态',
  `pickup_time` TIMESTAMP NULL DEFAULT NULL COMMENT '揽件时间',
  `delivery_time` TIMESTAMP NULL DEFAULT NULL COMMENT '派送时间',
  `sign_time` TIMESTAMP NULL DEFAULT NULL COMMENT '签收时间',
  `estimated_delivery_time` TIMESTAMP NULL DEFAULT NULL COMMENT '预计送达时间',
  
  -- 备注
  `remarks` TEXT DEFAULT NULL COMMENT '备注',
  
  -- 系统字段
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_number` (`order_number`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_sender_city` (`sender_city`),
  KEY `idx_receiver_city` (`receiver_city`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- =============================================
-- 3. 配送管理相关表
-- =============================================

-- 配送员表（对应Courier实体）
DROP TABLE IF EXISTS `couriers`;
CREATE TABLE `couriers` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '配送员ID',
  `user_id` BIGINT(20) NOT NULL COMMENT '用户ID',
  `courier_code` VARCHAR(50) NOT NULL COMMENT '配送员编号',
  `courier_name` VARCHAR(50) NOT NULL COMMENT '配送员姓名',
  `phone` VARCHAR(20) NOT NULL COMMENT '手机号',
  `id_card` VARCHAR(18) NOT NULL COMMENT '身份证号',
  `work_number` VARCHAR(50) DEFAULT NULL COMMENT '工号',
  `vehicle_type` VARCHAR(20) DEFAULT NULL COMMENT '车辆类型：BIKE-自行车，MOTORCYCLE-摩托车，CAR-汽车',
  `vehicle_number` VARCHAR(20) DEFAULT NULL COMMENT '车牌号',
  `work_area` VARCHAR(100) DEFAULT NULL COMMENT '工作区域',
  `max_weight` DECIMAL(10,2) DEFAULT NULL COMMENT '最大承重(kg)',
  `max_volume` DECIMAL(10,2) DEFAULT NULL COMMENT '最大体积(m³)',
  `status` INT(11) NOT NULL DEFAULT 0 COMMENT '状态：0-离线，1-在线，2-忙碌，3-休息',
  `current_longitude` DECIMAL(10,6) DEFAULT NULL COMMENT '当前经度',
  `current_latitude` DECIMAL(10,6) DEFAULT NULL COMMENT '当前纬度',
  `current_address` VARCHAR(255) DEFAULT NULL COMMENT '当前地址',
  `last_location_update` TIMESTAMP NULL DEFAULT NULL COMMENT '最后位置更新时间',
  `rating` DECIMAL(3,2) DEFAULT 5.00 COMMENT '评分',
  `total_orders` INT(11) DEFAULT 0 COMMENT '总订单数',
  `completed_orders` INT(11) DEFAULT 0 COMMENT '完成订单数',
  `hire_date` DATE DEFAULT NULL COMMENT '入职日期',
  `emergency_contact` VARCHAR(50) DEFAULT NULL COMMENT '紧急联系人',
  `emergency_phone` VARCHAR(20) DEFAULT NULL COMMENT '紧急联系电话',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_courier_code` (`courier_code`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_work_area` (`work_area`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='配送员表';

-- 配送任务表（对应DeliveryTask实体）
DROP TABLE IF EXISTS `delivery_tasks`;
CREATE TABLE `delivery_tasks` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `task_number` VARCHAR(32) NOT NULL COMMENT '任务编号',
  `order_id` BIGINT(20) NOT NULL COMMENT '订单ID',
  `order_number` VARCHAR(32) NOT NULL COMMENT '订单号',
  `courier_id` BIGINT(20) DEFAULT NULL COMMENT '配送员ID',
  `task_type` VARCHAR(20) NOT NULL COMMENT '任务类型：PICKUP-揽件，DELIVERY-派送',
  `priority` INT(11) DEFAULT 2 COMMENT '优先级：1-低，2-中，3-高，4-紧急',
  `weight` DECIMAL(10,2) DEFAULT NULL COMMENT '重量(kg)',
  `volume` DECIMAL(10,2) DEFAULT NULL COMMENT '体积(m³)',
  `goods_description` VARCHAR(255) DEFAULT NULL COMMENT '货物描述',
  `special_requirements` TEXT DEFAULT NULL COMMENT '特殊要求',
  `delivery_fee` DECIMAL(10,2) DEFAULT NULL COMMENT '配送费',
  `estimated_time` TIMESTAMP NULL DEFAULT NULL COMMENT '预计完成时间',
  `actual_start_time` TIMESTAMP NULL DEFAULT NULL COMMENT '实际开始时间',
  `actual_end_time` TIMESTAMP NULL DEFAULT NULL COMMENT '实际完成时间',
  `task_status` VARCHAR(20) NOT NULL DEFAULT 'ASSIGNED' COMMENT '任务状态：ASSIGNED-已分配，ACCEPTED-已接受，IN_PROGRESS-进行中，COMPLETED-已完成，CANCELLED-已取消',
  `pickup_address` VARCHAR(255) DEFAULT NULL COMMENT '取件地址',
  `pickup_contact` VARCHAR(50) DEFAULT NULL COMMENT '取件联系人',
  `pickup_phone` VARCHAR(20) DEFAULT NULL COMMENT '取件电话',
  `delivery_address` VARCHAR(255) DEFAULT NULL COMMENT '送达地址',
  `delivery_contact` VARCHAR(50) DEFAULT NULL COMMENT '送达联系人',
  `delivery_phone` VARCHAR(20) DEFAULT NULL COMMENT '送达电话',
  `route_id` BIGINT(20) DEFAULT NULL COMMENT '路线ID',
  `sequence_number` INT(11) DEFAULT NULL COMMENT '路线中的序号',
  `completion_proof` TEXT DEFAULT NULL COMMENT '完成凭证（照片URL等）',
  `failure_reason` VARCHAR(255) DEFAULT NULL COMMENT '失败原因',
  `remarks` TEXT DEFAULT NULL COMMENT '备注',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_number` (`task_number`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_courier_id` (`courier_id`),
  KEY `idx_task_status` (`task_status`),
  KEY `idx_task_type` (`task_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='配送任务表';

-- 配送路线表（对应DeliveryRoute实体）
DROP TABLE IF EXISTS `delivery_routes`;
CREATE TABLE `delivery_routes` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '路线ID',
  `route_number` VARCHAR(32) NOT NULL COMMENT '路线编号',
  `courier_id` BIGINT(20) NOT NULL COMMENT '配送员ID',
  `route_date` DATE NOT NULL COMMENT '路线日期',
  `start_time` TIMESTAMP NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` TIMESTAMP NULL DEFAULT NULL COMMENT '结束时间',
  `start_longitude` DECIMAL(10,6) DEFAULT NULL COMMENT '起始经度',
  `start_latitude` DECIMAL(10,6) DEFAULT NULL COMMENT '起始纬度',
  `start_address` VARCHAR(255) DEFAULT NULL COMMENT '起始地址',
  `end_longitude` DECIMAL(10,6) DEFAULT NULL COMMENT '结束经度',
  `end_latitude` DECIMAL(10,6) DEFAULT NULL COMMENT '结束纬度',
  `end_address` VARCHAR(255) DEFAULT NULL COMMENT '结束地址',
  `total_distance` DECIMAL(10,2) DEFAULT NULL COMMENT '总距离(km)',
  `estimated_duration` INT(11) DEFAULT NULL COMMENT '预计用时(分钟)',
  `actual_duration` INT(11) DEFAULT NULL COMMENT '实际用时(分钟)',
  `task_count` INT(11) DEFAULT 0 COMMENT '任务数量',
  `completed_count` INT(11) DEFAULT 0 COMMENT '完成数量',
  `route_status` VARCHAR(20) NOT NULL DEFAULT 'PLANNING' COMMENT '路线状态：PLANNING-规划中，STARTED-已开始，COMPLETED-已完成，CANCELLED-已取消',
  `route_data` TEXT DEFAULT NULL COMMENT '路线数据（JSON格式）',
  `remarks` TEXT DEFAULT NULL COMMENT '备注',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_route_number` (`route_number`),
  KEY `idx_courier_id` (`courier_id`),
  KEY `idx_route_date` (`route_date`),
  KEY `idx_route_status` (`route_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='配送路线表';

-- =============================================
-- 4. 通知管理相关表
-- =============================================

-- 通知记录表（对应Notification实体）
DROP TABLE IF EXISTS `notifications`;
CREATE TABLE `notifications` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `user_id` BIGINT(20) DEFAULT NULL COMMENT '用户ID',
  `order_number` VARCHAR(32) DEFAULT NULL COMMENT '订单号',
  `business_id` BIGINT(20) DEFAULT NULL COMMENT '业务ID（订单ID、任务ID等）',
  `business_type` VARCHAR(50) DEFAULT NULL COMMENT '业务类型（ORDER、DELIVERY、SYSTEM等）',
  `notification_type` VARCHAR(20) NOT NULL COMMENT '通知类型：SMS、EMAIL、PUSH、WECHAT',
  `template_code` VARCHAR(50) DEFAULT NULL COMMENT '模板编码',
  `recipient` VARCHAR(255) NOT NULL COMMENT '接收者',
  `title` VARCHAR(255) DEFAULT NULL COMMENT '通知标题',
  `content` TEXT NOT NULL COMMENT '通知内容',
  `template_params` TEXT DEFAULT NULL COMMENT '模板参数（JSON格式）',
  `send_status` INT(11) NOT NULL DEFAULT 0 COMMENT '发送状态：0-待发送，1-发送中，2-发送成功，3-发送失败，4-已取消',
  `send_time` TIMESTAMP NULL DEFAULT NULL COMMENT '发送时间',
  `failure_reason` VARCHAR(255) DEFAULT NULL COMMENT '失败原因',
  `retry_count` INT(11) DEFAULT 0 COMMENT '重试次数',
  `max_retry_count` INT(11) DEFAULT 3 COMMENT '最大重试次数',
  `next_retry_time` TIMESTAMP NULL DEFAULT NULL COMMENT '下次重试时间',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_number` (`order_number`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_notification_type` (`notification_type`),
  KEY `idx_send_status` (`send_status`),
  KEY `idx_send_time` (`send_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知记录表';

-- 邮件模板表（对应EmailTemplate实体）
DROP TABLE IF EXISTS `email_templates`;
CREATE TABLE `email_templates` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `template_code` VARCHAR(50) NOT NULL COMMENT '模板编码（唯一）',
  `template_name` VARCHAR(100) NOT NULL COMMENT '模板名称',
  `subject` VARCHAR(255) NOT NULL COMMENT '邮件主题',
  `content` TEXT NOT NULL COMMENT '邮件内容（HTML格式）',
  `template_type` VARCHAR(20) NOT NULL DEFAULT 'BUSINESS' COMMENT '模板类型',
  `is_enabled` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_code` (`template_code`),
  KEY `idx_template_type` (`template_type`),
  KEY `idx_is_enabled` (`is_enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件模板表';

-- 通知模板表（对应NotificationTemplate实体）
DROP TABLE IF EXISTS `notification_templates`;
CREATE TABLE `notification_templates` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `template_code` VARCHAR(50) NOT NULL COMMENT '模板编码',
  `template_name` VARCHAR(100) NOT NULL COMMENT '模板名称',
  `notification_type` VARCHAR(20) NOT NULL COMMENT '通知类型：SMS、EMAIL、PUSH、WECHAT',
  `title_template` VARCHAR(255) DEFAULT NULL COMMENT '标题模板',
  `content_template` TEXT NOT NULL COMMENT '内容模板',
  `template_params` TEXT DEFAULT NULL COMMENT '模板参数说明（JSON格式）',
  `is_enabled` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_code_type` (`template_code`, `notification_type`),
  KEY `idx_notification_type` (`notification_type`),
  KEY `idx_is_enabled` (`is_enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知模板表';

-- =============================================
-- 5. 网点管理相关表
-- =============================================

-- 网点表
DROP TABLE IF EXISTS `stations`;
CREATE TABLE `stations` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '网点ID',
  `station_code` VARCHAR(50) NOT NULL COMMENT '网点编码',
  `station_name` VARCHAR(100) NOT NULL COMMENT '网点名称',
  `station_type` VARCHAR(20) NOT NULL COMMENT '网点类型：HUB-枢纽，BRANCH-分拣中心，OUTLET-营业网点',
  `province` VARCHAR(50) NOT NULL COMMENT '省份',
  `city` VARCHAR(50) NOT NULL COMMENT '城市',
  `district` VARCHAR(50) DEFAULT NULL COMMENT '区县',
  `address` VARCHAR(255) NOT NULL COMMENT '详细地址',
  `longitude` DECIMAL(10,6) DEFAULT NULL COMMENT '经度',
  `latitude` DECIMAL(10,6) DEFAULT NULL COMMENT '纬度',
  `contact_person` VARCHAR(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` VARCHAR(20) DEFAULT NULL COMMENT '联系电话',
  `manager_id` BIGINT(20) DEFAULT NULL COMMENT '负责人ID',
  `capacity` INT(11) DEFAULT NULL COMMENT '处理能力（件/天）',
  `coverage_area` TEXT DEFAULT NULL COMMENT '覆盖区域（JSON格式）',
  `operating_hours` VARCHAR(100) DEFAULT NULL COMMENT '营业时间',
  `status` VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-营业，INACTIVE-停业，MAINTENANCE-维护',
  `sort_order` INT(11) DEFAULT 0 COMMENT '排序',
  `remarks` TEXT DEFAULT NULL COMMENT '备注',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_station_code` (`station_code`),
  KEY `idx_station_type` (`station_type`),
  KEY `idx_city` (`city`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网点表';

-- =============================================
-- 6. 基础数据插入
-- =============================================

-- 插入角色数据
INSERT INTO `roles` (`id`, `role_name`, `role_code`, `description`, `status`, `create_time`, `update_time`) VALUES
(1, '普通用户', 'CUSTOMER', '普通客户用户', 'ACTIVE', NOW(), NOW()),
(2, '操作员', 'OPERATOR', '物流操作员', 'ACTIVE', NOW(), NOW()),
(3, '配送员', 'COURIER', '配送员', 'ACTIVE', NOW(), NOW()),
(4, '管理员', 'ADMIN', '系统管理员', 'ACTIVE', NOW(), NOW());

-- 插入权限数据
INSERT INTO `permissions` (`id`, `permission_name`, `permission_code`, `resource_type`, `resource_url`, `parent_id`, `sort_order`, `status`, `description`, `create_time`, `update_time`) VALUES
-- 系统管理
(1, '系统管理', 'SYSTEM_MANAGE', 'MENU', '/system', 0, 1, 'ACTIVE', '系统管理菜单', NOW(), NOW()),
(2, '用户管理', 'USER_MANAGE', 'MENU', '/system/user', 1, 1, 'ACTIVE', '用户管理', NOW(), NOW()),
(3, '角色管理', 'ROLE_MANAGE', 'MENU', '/system/role', 1, 2, 'ACTIVE', '角色管理', NOW(), NOW()),

-- 订单管理
(10, '订单管理', 'ORDER_MANAGE', 'MENU', '/order', 0, 2, 'ACTIVE', '订单管理菜单', NOW(), NOW()),
(11, '订单查看', 'ORDER_VIEW', 'BUTTON', '/order/view', 10, 1, 'ACTIVE', '查看订单', NOW(), NOW()),
(12, '订单创建', 'ORDER_CREATE', 'BUTTON', '/order/create', 10, 2, 'ACTIVE', '创建订单', NOW(), NOW()),
(13, '订单编辑', 'ORDER_EDIT', 'BUTTON', '/order/edit', 10, 3, 'ACTIVE', '编辑订单', NOW(), NOW()),
(14, '订单删除', 'ORDER_DELETE', 'BUTTON', '/order/delete', 10, 4, 'ACTIVE', '删除订单', NOW(), NOW()),

-- 配送管理
(20, '配送管理', 'DELIVERY_MANAGE', 'MENU', '/delivery', 0, 3, 'ACTIVE', '配送管理菜单', NOW(), NOW()),
(21, '配送任务查看', 'DELIVERY_VIEW', 'BUTTON', '/delivery/view', 20, 1, 'ACTIVE', '查看配送任务', NOW(), NOW()),
(22, '配送任务分配', 'DELIVERY_ASSIGN', 'BUTTON', '/delivery/assign', 20, 2, 'ACTIVE', '分配配送任务', NOW(), NOW()),
(23, '配送状态更新', 'DELIVERY_UPDATE', 'BUTTON', '/delivery/update', 20, 3, 'ACTIVE', '更新配送状态', NOW(), NOW()),

-- 物流跟踪
(30, '物流跟踪', 'LOGISTICS_TRACK', 'MENU', '/logistics', 0, 4, 'ACTIVE', '物流跟踪菜单', NOW(), NOW()),
(31, '轨迹查看', 'TRACK_VIEW', 'BUTTON', '/logistics/track', 30, 1, 'ACTIVE', '查看物流轨迹', NOW(), NOW()),
(32, '轨迹更新', 'TRACK_UPDATE', 'BUTTON', '/logistics/update', 30, 2, 'ACTIVE', '更新物流轨迹', NOW(), NOW());

-- 分配角色权限
INSERT INTO `role_permissions` (`role_id`, `permission_id`, `create_time`) VALUES
-- 管理员权限（所有权限）
(4, 1, NOW()), (4, 2, NOW()), (4, 3, NOW()),
(4, 10, NOW()), (4, 11, NOW()), (4, 12, NOW()), (4, 13, NOW()), (4, 14, NOW()),
(4, 20, NOW()), (4, 21, NOW()), (4, 22, NOW()), (4, 23, NOW()),
(4, 30, NOW()), (4, 31, NOW()), (4, 32, NOW()),

-- 操作员权限
(2, 10, NOW()), (2, 11, NOW()), (2, 12, NOW()), (2, 13, NOW()),
(2, 20, NOW()), (2, 21, NOW()), (2, 22, NOW()), (2, 23, NOW()),
(2, 30, NOW()), (2, 31, NOW()), (2, 32, NOW()),

-- 配送员权限
(3, 20, NOW()), (3, 21, NOW()), (3, 23, NOW()),
(3, 30, NOW()), (3, 31, NOW()),

-- 客户权限
(1, 10, NOW()), (1, 11, NOW()), (1, 12, NOW()),
(1, 30, NOW()), (1, 31, NOW());

-- 插入用户数据
INSERT INTO `users` (`id`, `username`, `password`, `real_name`, `phone`, `email`, `id_card`, `user_type`, `status`, `gender`, `create_time`, `update_time`) VALUES
(1, 'admin', '$2a$10$7JB720yubVSOfvVWbazBuOWShWvheWjxVYaGYLx2lyAFQmuQufDJa', '系统管理员', '13800000000', '<EMAIL>', '110101199001010001', 'ADMIN', 'ACTIVE', 1, NOW(), NOW()),
(2, 'operator01', '$2a$10$7JB720yubVSOfvVWbazBuOWShWvheWjxVYaGYLx2lyAFQmuQufDJa', '操作员01', '13800000001', '<EMAIL>', '110101199001010002', 'OPERATOR', 'ACTIVE', 1, NOW(), NOW()),
(3, 'courier01', '$2a$10$7JB720yubVSOfvVWbazBuOWShWvheWjxVYaGYLx2lyAFQmuQufDJa', '配送员01', '13800000002', '<EMAIL>', '110101199001010003', 'COURIER', 'ACTIVE', 1, NOW(), NOW()),
(4, 'courier02', '$2a$10$7JB720yubVSOfvVWbazBuOWShWvheWjxVYaGYLx2lyAFQmuQufDJa', '配送员02', '13800000003', '<EMAIL>', '110101199001010004', 'COURIER', 'ACTIVE', 2, NOW(), NOW()),
(5, 'customer01', '$2a$10$7JB720yubVSOfvVWbazBuOWShWvheWjxVYaGYLx2lyAFQmuQufDJa', '客户01', '13800000004', '<EMAIL>', '110101199001010005', 'CUSTOMER', 'ACTIVE', 1, NOW(), NOW()),
(6, 'customer02', '$2a$10$7JB720yubVSOfvVWbazBuOWShWvheWjxVYaGYLx2lyAFQmuQufDJa', '客户02', '13800000005', '<EMAIL>', '110101199001010006', 'CUSTOMER', 'ACTIVE', 2, NOW(), NOW());

-- 分配用户角色
INSERT INTO `user_roles` (`user_id`, `role_id`, `create_time`) VALUES
(1, 4, NOW()),  -- admin -> 管理员
(2, 2, NOW()),  -- operator01 -> 操作员
(3, 3, NOW()),  -- courier01 -> 配送员
(4, 3, NOW()),  -- courier02 -> 配送员
(5, 1, NOW()),  -- customer01 -> 普通用户
(6, 1, NOW());  -- customer02 -> 普通用户

-- 插入配送员数据
INSERT INTO `couriers` (`id`, `user_id`, `courier_code`, `courier_name`, `phone`, `id_card`, `work_number`, `vehicle_type`, `work_area`, `max_weight`, `max_volume`, `status`, `hire_date`, `create_time`, `update_time`) VALUES
(1, 3, 'C001', '配送员01', '13800000002', '110101199001010003', 'W001', 'MOTORCYCLE', '朝阳区', 50.00, 2.00, 1, '2024-01-01', NOW(), NOW()),
(2, 4, 'C002', '配送员02', '13800000003', '110101199001010004', 'W002', 'CAR', '海淀区', 200.00, 5.00, 1, '2024-01-01', NOW(), NOW());

-- 插入邮件模板数据
INSERT INTO `email_templates` (`id`, `template_code`, `template_name`, `subject`, `content`, `template_type`, `is_enabled`, `create_time`, `update_time`) VALUES
(1, 'ORDER_CREATED', '订单创建通知', '【智慧物流】订单创建成功 - {{orderNumber}}',
'<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
    <h2 style="color: #2c5aa0; text-align: center;">订单创建成功</h2>
    <p>尊敬的 <strong>{{customerName}}</strong>，您好！</p>
    <p>您的订单已成功创建，详情如下：</p>
    <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
        <tr><td style="padding: 8px; border: 1px solid #ddd; background: #f9f9f9;"><strong>订单号：</strong></td><td style="padding: 8px; border: 1px solid #ddd;">{{orderNumber}}</td></tr>
        <tr><td style="padding: 8px; border: 1px solid #ddd; background: #f9f9f9;"><strong>总费用：</strong></td><td style="padding: 8px; border: 1px solid #ddd;">¥{{totalFee}}</td></tr>
        <tr><td style="padding: 8px; border: 1px solid #ddd; background: #f9f9f9;"><strong>创建时间：</strong></td><td style="padding: 8px; border: 1px solid #ddd;">{{createTime}}</td></tr>
    </table>
    <p>我们将尽快为您安排配送，请保持手机畅通。</p>
    <p style="text-align: center; color: #666; font-size: 12px;">此邮件由系统自动发送，请勿回复</p>
</div>', 'BUSINESS', 1, NOW(), NOW()),

(2, 'PAYMENT_SUCCESS', '支付成功通知', '【智慧物流】支付成功，订单处理中 - {{orderNumber}}',
'<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
    <h2 style="color: #28a745; text-align: center;">支付成功</h2>
    <p>尊敬的 <strong>{{customerName}}</strong>，您好！</p>
    <p>您的订单支付已成功，我们正在为您处理订单：</p>
    <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
        <tr><td style="padding: 8px; border: 1px solid #ddd; background: #f9f9f9;"><strong>订单号：</strong></td><td style="padding: 8px; border: 1px solid #ddd;">{{orderNumber}}</td></tr>
        <tr><td style="padding: 8px; border: 1px solid #ddd; background: #f9f9f9;"><strong>支付金额：</strong></td><td style="padding: 8px; border: 1px solid #ddd;">¥{{paymentAmount}}</td></tr>
        <tr><td style="padding: 8px; border: 1px solid #ddd; background: #f9f9f9;"><strong>支付时间：</strong></td><td style="padding: 8px; border: 1px solid #ddd;">{{paymentTime}}</td></tr>
    </table>
    <p>订单正在处理中，我们将尽快安排揽件。</p>
    <p style="text-align: center; color: #666; font-size: 12px;">此邮件由系统自动发送，请勿回复</p>
</div>', 'BUSINESS', 1, NOW(), NOW()),

(3, 'ORDER_STATUS_UPDATE', '订单状态更新通知', '【智慧物流】订单状态更新 - {{orderNumber}}',
'<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
    <h2 style="color: #17a2b8; text-align: center;">订单状态更新</h2>
    <p>尊敬的 <strong>{{customerName}}</strong>，您好！</p>
    <p>您的订单状态已更新：</p>
    <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
        <tr><td style="padding: 8px; border: 1px solid #ddd; background: #f9f9f9;"><strong>订单号：</strong></td><td style="padding: 8px; border: 1px solid #ddd;">{{orderNumber}}</td></tr>
        <tr><td style="padding: 8px; border: 1px solid #ddd; background: #f9f9f9;"><strong>当前状态：</strong></td><td style="padding: 8px; border: 1px solid #ddd;">{{statusText}}</td></tr>
        <tr><td style="padding: 8px; border: 1px solid #ddd; background: #f9f9f9;"><strong>状态描述：</strong></td><td style="padding: 8px; border: 1px solid #ddd;">{{description}}</td></tr>
        <tr><td style="padding: 8px; border: 1px solid #ddd; background: #f9f9f9;"><strong>更新时间：</strong></td><td style="padding: 8px; border: 1px solid #ddd;">{{updateTime}}</td></tr>
    </table>
    <p>您可以随时登录系统查看订单详情和物流轨迹。</p>
    <p style="text-align: center; color: #666; font-size: 12px;">此邮件由系统自动发送，请勿回复</p>
</div>', 'BUSINESS', 1, NOW(), NOW()),

(4, 'TEST', '测试邮件模板', '{{subject}}',
'<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
    <h2 style="color: #6c757d; text-align: center;">测试邮件</h2>
    <p>这是一封测试邮件，用于验证邮件服务是否正常工作。</p>
    <p><strong>内容：</strong>{{content}}</p>
    <p><strong>测试时间：</strong>{{testTime}}</p>
    <p style="text-align: center; color: #666; font-size: 12px;">此邮件由系统自动发送，请勿回复</p>
</div>', 'SYSTEM', 1, NOW(), NOW());

-- 插入网点数据
INSERT INTO `stations` (`id`, `station_code`, `station_name`, `station_type`, `province`, `city`, `district`, `address`, `longitude`, `latitude`, `contact_person`, `contact_phone`, `capacity`, `status`, `create_time`, `update_time`) VALUES
(1, 'BJ001', '北京朝阳分拣中心', 'BRANCH', '北京市', '北京市', '朝阳区', '朝阳区建国门外大街1号', 116.434446, 39.902486, '张经理', '010-12345678', 10000, 'ACTIVE', NOW(), NOW()),
(2, 'BJ002', '北京海淀营业网点', 'OUTLET', '北京市', '北京市', '海淀区', '海淀区中关村大街1号', 116.310316, 39.992957, '李经理', '010-87654321', 5000, 'ACTIVE', NOW(), NOW()),
(3, 'SH001', '上海浦东分拣中心', 'BRANCH', '上海市', '上海市', '浦东新区', '浦东新区陆家嘴环路1000号', 121.499763, 31.239853, '王经理', '021-12345678', 15000, 'ACTIVE', NOW(), NOW()),
(4, 'GZ001', '广州天河营业网点', 'OUTLET', '广东省', '广州市', '天河区', '天河区珠江新城花城大道1号', 113.324520, 23.116536, '陈经理', '020-12345678', 8000, 'ACTIVE', NOW(), NOW());

-- 插入测试订单数据
INSERT INTO `orders` (`id`, `order_number`, `user_id`, `sender_name`, `sender_phone`, `sender_address`, `sender_city`, `receiver_name`, `receiver_phone`, `receiver_address`, `receiver_city`, `item_name`, `item_weight`, `service_type`, `total_fee`, `payment_status`, `order_status`, `create_time`, `update_time`) VALUES
(1, 'LO202412300001', 5, '张三', '13800000001', '北京市朝阳区建国门外大街1号', '北京市', '李四', '13800000002', '上海市浦东新区陆家嘴环路1000号', '上海市', '重要文件', 1.50, 'STANDARD', 25.00, 1, 'PICKUP', NOW(), NOW()),
(2, 'LO202412300002', 5, '王五', '13800000003', '广州市天河区珠江新城', '广州市', '赵六', '13800000004', '深圳市南山区科技园', '深圳市', '电子产品', 2.00, 'EXPRESS', 35.00, 0, 'PENDING', NOW(), NOW()),
(3, 'LO202412300003', 6, '钱七', '13800000005', '杭州市西湖区文三路', '杭州市', '孙八', '13800000006', '南京市鼓楼区中山路', '南京市', '书籍资料', 0.80, 'STANDARD', 18.00, 1, 'TRANSIT', NOW(), NOW());

-- =============================================
-- 7. 创建索引优化
-- =============================================

-- 订单表额外索引
CREATE INDEX idx_orders_sender_receiver ON orders(sender_city, receiver_city);
CREATE INDEX idx_orders_service_type ON orders(service_type);
CREATE INDEX idx_orders_estimated_delivery ON orders(estimated_delivery_time);

-- 配送任务表额外索引
CREATE INDEX idx_delivery_tasks_estimated_time ON delivery_tasks(estimated_time);
CREATE INDEX idx_delivery_tasks_route_sequence ON delivery_tasks(route_id, sequence_number);

-- 通知表额外索引
CREATE INDEX idx_notifications_retry ON notifications(send_status, next_retry_time);
CREATE INDEX idx_notifications_business ON notifications(business_type, business_id);

-- =============================================
-- 数据库初始化完成
-- =============================================
