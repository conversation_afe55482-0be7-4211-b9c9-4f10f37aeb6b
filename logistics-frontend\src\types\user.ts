// API响应基础结构
export interface ApiResult<T> {
  code: number
  message: string
  data: T
}

// 用户信息
export interface UserInfo {
  id: number
  username: string
  realName: string
  phone: string
  email: string
  userType: 'CUSTOMER' | 'OPERATOR' | 'COURIER' | 'ADMIN'
  status: string
  avatar?: string
  lastLoginTime?: string
  roles: string[]
  permissions: string[]
  createTime: string
}

// 登录请求 - 与后端UserLoginDTO匹配
export interface LoginRequest {
  username: string
  password: string
  loginType?: 'USERNAME' | 'PHONE' | 'EMAIL'
  rememberMe?: boolean
}

// 登录响应数据 - 与后端LoginResponseVO匹配
export interface LoginResponseData {
  token: string
  expiresIn: number
  userInfo: UserInfo
  loginTime: string
}

// 登录响应 - 包装在标准Result中
export type LoginResponse = LoginResponseData

// 注册请求 - 与后端UserRegisterDTO完全匹配
export interface RegisterRequest {
  username: string
  password: string
  realName: string
  phone: string
  email: string
  idCard: string
  userType: 'CUSTOMER' | 'OPERATOR' | 'COURIER' | 'ADMIN'
  verificationCode: string
}

// 用户信息API响应
export type ApiUserResponse = UserInfo
