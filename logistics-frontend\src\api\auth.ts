import http from '@/utils/http'
import type {
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  ApiUserResponse,
  ApiResult,
} from '@/types/user'

export const authApi = {
  // 用户登录
  login(data: LoginRequest): Promise<ApiResult<LoginResponse>> {
    return http.post('/user/login', data)
  },

  // 用户注册
  register(data: RegisterRequest): Promise<ApiResult<LoginResponse>> {
    return http.post('/user/register', data)
  },

  // 获取用户信息
  getUserInfo(): Promise<ApiResult<ApiUserResponse>> {
    return http.get('/user/profile')
  },

  // 检查用户名是否存在
  checkUsername(username: string): Promise<ApiResult<boolean>> {
    return http.get('/user/check-username', { params: { username } })
  },

  // 检查手机号是否存在
  checkPhone(phone: string): Promise<ApiResult<boolean>> {
    return http.get('/user/check-phone', { params: { phone } })
  },

  // 检查邮箱是否存在
  checkEmail(email: string): Promise<ApiResult<boolean>> {
    return http.get('/user/check-email', { params: { email } })
  },

  // 用户登出
  logout(): Promise<ApiResult<void>> {
    return http.post('/user/logout')
  },
}
