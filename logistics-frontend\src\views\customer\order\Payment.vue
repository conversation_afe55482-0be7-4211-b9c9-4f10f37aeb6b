<template>
  <div class="payment-page">
    <div class="page-header">
      <h2>订单支付</h2>
      <p>请选择支付方式完成订单</p>
    </div>

    <div v-if="orderInfo" class="payment-content">
      <!-- 订单信息 -->
      <el-card class="order-summary">
        <template #header>
          <div class="card-header">
            <el-icon><Document /></el-icon>
            <span>订单信息</span>
          </div>
        </template>

        <div class="order-details">
          <div class="order-number">
            <span class="label">订单号：</span>
            <span class="value">{{ orderInfo.orderNumber }}</span>
          </div>
          
          <div class="address-info">
            <div class="address-item">
              <el-icon><User /></el-icon>
              <span>{{ orderInfo.senderName }} {{ orderInfo.senderPhone }}</span>
              <span class="address">{{ orderInfo.senderAddress }}</span>
            </div>
            <div class="arrow">
              <el-icon><Right /></el-icon>
            </div>
            <div class="address-item">
              <el-icon><LocationInformation /></el-icon>
              <span>{{ orderInfo.receiverName }} {{ orderInfo.receiverPhone }}</span>
              <span class="address">{{ orderInfo.receiverAddress }}</span>
            </div>
          </div>

          <div class="item-info">
            <span class="label">物品：</span>
            <span class="value">{{ orderInfo.itemName }}</span>
            <span class="weight">{{ orderInfo.itemWeight }}kg</span>
          </div>

          <div class="service-info">
            <span class="label">服务类型：</span>
            <span class="value">{{ getServiceTypeText(orderInfo.serviceType) }}</span>
          </div>
        </div>
      </el-card>

      <!-- 费用明细 -->
      <el-card class="fee-summary">
        <template #header>
          <div class="card-header">
            <el-icon><Money /></el-icon>
            <span>费用明细</span>
          </div>
        </template>

        <div class="fee-details">
          <div class="fee-item">
            <span>运费</span>
            <span>¥{{ orderInfo.shippingFee.toFixed(2) }}</span>
          </div>
          <div class="fee-item">
            <span>保险费</span>
            <span>¥{{ orderInfo.insuranceFee.toFixed(2) }}</span>
          </div>
          <div class="fee-item">
            <span>包装费</span>
            <span>¥{{ orderInfo.packingFee.toFixed(2) }}</span>
          </div>
          <div class="fee-item total">
            <span>总计</span>
            <span>¥{{ orderInfo.totalFee.toFixed(2) }}</span>
          </div>
        </div>
      </el-card>

      <!-- 支付方式选择 -->
      <el-card class="payment-methods">
        <template #header>
          <div class="card-header">
            <el-icon><CreditCard /></el-icon>
            <span>选择支付方式</span>
          </div>
        </template>

        <el-radio-group v-model="selectedPaymentMethod" class="payment-options">
          <div class="payment-option">
            <el-radio value="ALIPAY">
              <div class="payment-item">
                <div class="payment-icon alipay">支</div>
                <div class="payment-info">
                  <div class="payment-name">支付宝</div>
                  <div class="payment-desc">推荐使用支付宝快捷支付</div>
                </div>
              </div>
            </el-radio>
          </div>

          <div class="payment-option">
            <el-radio value="WECHAT">
              <div class="payment-item">
                <div class="payment-icon wechat">微</div>
                <div class="payment-info">
                  <div class="payment-name">微信支付</div>
                  <div class="payment-desc">使用微信安全支付</div>
                </div>
              </div>
            </el-radio>
          </div>

          <div class="payment-option">
            <el-radio value="COD">
              <div class="payment-item">
                <div class="payment-icon cod">货</div>
                <div class="payment-info">
                  <div class="payment-name">货到付款</div>
                  <div class="payment-desc">收货时现金支付</div>
                </div>
              </div>
            </el-radio>
          </div>
        </el-radio-group>
      </el-card>

      <!-- 支付操作 -->
      <div class="payment-actions">
        <div class="payment-amount">
          <span>应付金额：</span>
          <span class="amount">¥{{ orderInfo.totalFee.toFixed(2) }}</span>
        </div>
        <div class="payment-buttons">
          <el-button size="large" @click="cancelPayment">取消支付</el-button>
          <el-button 
            type="primary" 
            size="large" 
            :loading="paying"
            @click="confirmPayment"
            :disabled="!selectedPaymentMethod"
          >
            {{ selectedPaymentMethod === 'COD' ? '确认订单' : '立即支付' }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- 支付结果对话框 -->
    <el-dialog v-model="showPaymentResult" title="支付结果" width="400px" :close-on-click-modal="false">
      <div class="payment-result">
        <div v-if="paymentSuccess" class="success-result">
          <el-icon class="result-icon success"><CircleCheck /></el-icon>
          <h3>支付成功</h3>
          <p>您的订单已支付完成，我们将尽快为您安排配送</p>
          <div class="order-info">
            <p>订单号：{{ orderInfo?.orderNumber }}</p>
            <p>支付金额：¥{{ orderInfo?.totalFee.toFixed(2) }}</p>
          </div>
        </div>
        <div v-else class="failed-result">
          <el-icon class="result-icon failed"><CircleClose /></el-icon>
          <h3>支付失败</h3>
          <p>{{ paymentErrorMessage }}</p>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="!paymentSuccess" @click="showPaymentResult = false">重新支付</el-button>
          <el-button v-if="paymentSuccess" type="primary" @click="goToOrderDetail">查看订单</el-button>
          <el-button v-if="paymentSuccess" @click="goToOrderList">我的订单</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Document,
  User,
  LocationInformation,
  Right,
  Money,
  CreditCard,
  CircleCheck,
  CircleClose
} from '@element-plus/icons-vue'
import { orderApi } from '@/api/order'
import type { Order } from '@/types/order'
import { OrderLifecycleManager } from '@/utils/orderLifecycle'

const route = useRoute()
const router = useRouter()

// 状态
const loading = ref(false)
const paying = ref(false)
const showPaymentResult = ref(false)
const paymentSuccess = ref(false)
const paymentErrorMessage = ref('')
const orderInfo = ref<Order | null>(null)
const selectedPaymentMethod = ref('ALIPAY')

// 加载订单信息
const loadOrderInfo = async () => {
  const orderId = route.params.id as string
  if (!orderId) {
    ElMessage.error('订单ID不正确')
    router.back()
    return
  }

  loading.value = true
  try {
    const response = await orderApi.getOrderById(Number(orderId))
    if (response.code === 200) {
      orderInfo.value = response.data
      
      // 检查订单状态
      if (orderInfo.value.orderStatus !== 'PENDING') {
        ElMessage.warning('订单状态不正确，无法支付')
        router.back()
        return
      }
    } else {
      ElMessage.error('订单不存在')
      router.back()
    }
  } catch (error) {
    console.error('加载订单信息失败:', error)
    ElMessage.error('加载订单信息失败')
    router.back()
  } finally {
    loading.value = false
  }
}

// 确认支付（修复：使用订单生命周期管理器）
const confirmPayment = async () => {
  if (!selectedPaymentMethod.value) {
    ElMessage.warning('请选择支付方式')
    return
  }

  paying.value = true
  try {
    console.log('开始支付处理...')
    
    // 使用订单生命周期管理器处理支付
    const response = await OrderLifecycleManager.payOrderWithTracking(
      Number(route.params.id), 
      selectedPaymentMethod.value
    )

    if (response.code === 200) {
      ElMessage.success('支付成功！')
      
      // 更新订单状态
      if (orderInfo.value) {
        orderInfo.value.paymentStatus = 'PAID'
        orderInfo.value.orderStatus = 'PAID'
      }
      
      // 显示支付成功对话框
      showPaymentResult.value = true
      paymentSuccess.value = true
    } else {
      throw new Error(response.message || '支付失败')
    }
  } catch (error: any) {
    console.error('支付失败:', error)
    showPaymentResult.value = true
    paymentSuccess.value = false
    paymentErrorMessage.value = error.message || '支付失败，请重试'
  } finally {
    paying.value = false
  }
}

// 取消支付
const cancelPayment = async () => {
  try {
    await ElMessageBox.confirm('确定要取消支付吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    router.back()
  } catch {
    // 用户取消
  }
}

// 跳转到订单详情
const goToOrderDetail = () => {
  if (orderInfo.value) {
    router.push(`/customer/order/detail/${orderInfo.value.id}`)
  }
}

// 跳转到订单列表
const goToOrderList = () => {
  router.push('/customer/order/list')
}

// 获取服务类型文本
const getServiceTypeText = (serviceType: string) => {
  const typeMap: Record<string, string> = {
    STANDARD: '标准快递',
    EXPRESS: '特快专递',
    URGENT: '加急服务'
  }
  return typeMap[serviceType] || serviceType
}

onMounted(() => {
  loadOrderInfo()
})
</script>

<style scoped>
.payment-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 10px 0;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
}

.payment-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.order-summary .order-details {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.order-number {
  display: flex;
  gap: 10px;
}

.order-number .label {
  color: #666;
}

.order-number .value {
  font-weight: bold;
  color: #333;
}

.address-info {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 8px;
}

.address-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.address-item .address {
  color: #666;
  font-size: 14px;
}

.arrow {
  color: #409eff;
}

.item-info, .service-info {
  display: flex;
  gap: 10px;
  align-items: center;
}

.item-info .label, .service-info .label {
  color: #666;
}

.item-info .weight {
  color: #666;
  font-size: 14px;
}

.fee-details {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.fee-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.fee-item.total {
  border-top: 1px solid #e4e7ed;
  padding-top: 15px;
  font-weight: bold;
  font-size: 16px;
  color: #333;
}

.payment-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.payment-option {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
  transition: all 0.3s;
}

.payment-option:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.payment-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.payment-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 14px;
}

.payment-icon.alipay {
  background: #1677ff;
}

.payment-icon.wechat {
  background: #07c160;
}

.payment-icon.cod {
  background: #fa8c16;
}

.payment-info {
  flex: 1;
}

.payment-name {
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.payment-desc {
  color: #666;
  font-size: 14px;
}

.payment-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #fff;
  border-top: 1px solid #e4e7ed;
  position: sticky;
  bottom: 0;
}

.payment-amount {
  font-size: 18px;
  color: #333;
}

.payment-amount .amount {
  color: #f56c6c;
  font-weight: bold;
  font-size: 24px;
}

.payment-buttons {
  display: flex;
  gap: 15px;
}

.payment-result {
  text-align: center;
  padding: 20px;
}

.result-icon {
  font-size: 64px;
  margin-bottom: 15px;
}

.result-icon.success {
  color: #67c23a;
}

.result-icon.failed {
  color: #f56c6c;
}

.payment-result h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.payment-result p {
  color: #666;
  margin-bottom: 15px;
}

.order-info {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 8px;
  text-align: left;
}

.order-info p {
  margin: 5px 0;
  color: #333;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 15px;
}
</style> 