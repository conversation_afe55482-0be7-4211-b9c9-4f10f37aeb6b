package org.example.logisticsuser.feign;

import com.logistics.common.result.Result;
import org.example.logisticsuser.dto.CreateCourierRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

@FeignClient(name = "logistics-delivery")  // 移除path，直接在PostMapping中指定完整路径
public interface CourierFeignClient {

    @PostMapping("/delivery/courier")  // 直接使用Controller的路径
    Result<Object> createCourier(@RequestBody Map<String, Object> request);
}