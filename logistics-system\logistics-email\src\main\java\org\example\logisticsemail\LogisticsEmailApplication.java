package org.example.logisticsemail;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 物流系统邮件服务启动类
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableAsync
public class LogisticsEmailApplication {

    public static void main(String[] args) {
        SpringApplication.run(LogisticsEmailApplication.class, args);
    }
}
