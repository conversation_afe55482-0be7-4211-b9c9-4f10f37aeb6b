server:
  port: 8006

spring:
  application:
    name: logistics-map
  datasource:
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ****************************************************************************************************************************************************
      username: root
      password: ${DB_PASSWORD:liyuqw8017}
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
  redis:
    host: localhost
    port: 6379
    password: ${REDIS_PASSWORD:redis123}
    time-to-live: 3600000  # 缓存1小时
    cache-null-values: false
    use-key-prefix: true
    key-prefix: "map:"
    database: 0
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 高德地图API配置
amap:
  # 从环境变量读取，避免硬编码
  web-api-key: ${AMAP_WEB_API_KEY:49e4dda6f3977084cce75b8cb92d9dd3}
  js-api-key: ${AMAP_JS_API_KEY:a6a693516b07dc66067eee65e41bbed7}
  base-url: https://restapi.amap.com

  # 各种服务的具体地址
  geocoding:
    url: /v3/geocode/geo
    regeo-url: /v3/geocode/regeo
  direction:
    url: /v3/direction/driving
    walking-url: /v3/direction/walking
  distance:
    url: /v3/distance
  inputtips:
    url: /v3/assistant/inputtips
  place:
    text-url: /v3/place/text
    around-url: /v3/place/around
  weather:
    url: /v3/weather/weatherInfo

  # 默认配置
  default:
    city: "贵阳"
    output: "json"
    extensions: "all"
    radius: 3000
    offset: 20

# 地图服务配置
map:
  cache:
    enabled: true
    ttl: 3600
    max-size: 10000
  rate-limit:
    enabled: true
    requests-per-second: 100
    burst-capacity: 200
  timeout:
    connect: 5000
    read: 10000
  retry:
    enabled: true
    max-attempts: 3
    delay: 1000

# 日志配置
logging:
  level:
    org.example.logisticsmap: debug
    org.apache.http: info

jwt:
  secret: logistics-secret-key-2024-for-microservice-authentication-system
  expiration: 86400