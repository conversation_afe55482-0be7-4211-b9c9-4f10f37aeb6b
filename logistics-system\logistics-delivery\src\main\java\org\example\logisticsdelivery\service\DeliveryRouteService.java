package org.example.logisticsdelivery.service;

import org.example.logisticsdelivery.entity.DeliveryRoute;

import java.time.LocalDate;
import java.util.List;

/**
 * 配送路线服务接口
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
public interface DeliveryRouteService {

    /**
     * 创建配送路线
     */
    DeliveryRoute createRoute(DeliveryRoute route);

    /**
     * 更新配送路线
     */
    DeliveryRoute updateRoute(DeliveryRoute route);

    /**
     * 根据ID查询路线
     */
    DeliveryRoute getRouteById(Long id);

    /**
     * 根据配送员ID查询路线
     */
    List<DeliveryRoute> getRoutesByCourierId(Long courierId);

    /**
     * 根据配送员和日期查询路线
     */
    DeliveryRoute getRouteByCourierAndDate(Long courierId, LocalDate routeDate);

    /**
     * 根据路线状态查询
     */
    List<DeliveryRoute> getRoutesByStatus(String routeStatus);

    /**
     * 查询指定日期的路线
     */
    List<DeliveryRoute> getRoutesByDate(LocalDate routeDate);

    /**
     * 更新路线状态
     */
    boolean updateRouteStatus(Long id, String routeStatus);

    /**
     * 更新路线任务统计
     */
    boolean updateTaskCount(Long id, Integer taskCount, Integer completedCount);

    /**
     * 开始路线
     */
    boolean startRoute(Long routeId);

    /**
     * 完成路线
     */
    boolean completeRoute(Long routeId);

    /**
     * 取消路线
     */
    boolean cancelRoute(Long routeId, String reason);

    /**
     * 删除路线
     */
    boolean deleteRoute(Long routeId);

    /**
     * 为配送员生成当日路线
     */
    DeliveryRoute generateDailyRoute(Long courierId, LocalDate routeDate);

    /**
     * 优化路线
     */
    DeliveryRoute optimizeRoute(Long routeId);
}