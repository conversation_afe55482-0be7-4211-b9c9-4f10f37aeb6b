package org.example.logisticslogistics.service.impl;

import org.example.logisticslogistics.constants.TrackingStatus;
import org.example.logisticslogistics.entity.LocationInfo;
import org.example.logisticslogistics.entity.TrackingInfo;
import org.example.logisticslogistics.entity.TrackingNode;
import org.example.logisticslogistics.repository.TrackingInfoCustomRepository;
import org.example.logisticslogistics.repository.TrackingInfoRepository;
import org.example.logisticslogistics.service.TrackingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 物流轨迹管理服务实现
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Service
public class TrackingServiceImpl implements TrackingService {

    @Autowired
    private TrackingInfoRepository trackingInfoRepository;

    @Autowired
    private TrackingInfoCustomRepository trackingInfoCustomRepository;

    @Override
    public TrackingInfo createTracking(String orderNumber, Long orderId) {
        // 检查是否已存在
        if (trackingInfoRepository.existsByOrderNumber(orderNumber)) {
            throw new RuntimeException("订单号 " + orderNumber + " 的轨迹信息已存在");
        }

        TrackingInfo trackingInfo = new TrackingInfo();
        trackingInfo.setOrderNumber(orderNumber);
        trackingInfo.setOrderId(orderId);
        trackingInfo.setCurrentStatus(TrackingStatus.ORDER_CREATED.getCode());
        trackingInfo.setIsException(false);
        trackingInfo.setTrackingNodes(new ArrayList<>());
        trackingInfo.setCreateTime(LocalDateTime.now());
        trackingInfo.setLastUpdateTime(LocalDateTime.now());

        // 添加初始轨迹节点
        TrackingNode initialNode = createTrackingNode(
                TrackingStatus.ORDER_CREATED.getCode(),
                TrackingStatus.ORDER_CREATED.getTitle(),
                TrackingStatus.ORDER_CREATED.getDescription(),
                null, null, null, "SYSTEM"
        );
        trackingInfo.getTrackingNodes().add(initialNode);

        return trackingInfoRepository.save(trackingInfo);
    }

    @Override
    public TrackingInfo getTrackingByOrderNumber(String orderNumber) {
        return trackingInfoRepository.findByOrderNumber(orderNumber)
                .orElseThrow(() -> new RuntimeException("未找到订单号为 " + orderNumber + " 的轨迹信息"));
    }

    @Override
    public TrackingInfo getTrackingByOrderId(Long orderId) {
        return trackingInfoRepository.findByOrderId(orderId)
                .orElseThrow(() -> new RuntimeException("未找到订单ID为 " + orderId + " 的轨迹信息"));
    }

    @Override
    public TrackingInfo updateTrackingStatus(String trackingId, String status, String description,
                                           LocationInfo location, Long operatorId, String operatorName) {
        // TODO: 实现更新轨迹状态
        return getTrackingById(trackingId);
    }

    @Override
    public TrackingInfo addTrackingNode(String trackingId, TrackingNode node) {
        // TODO: 实现添加轨迹节点
        return getTrackingById(trackingId);
    }

    @Override
    public TrackingInfo addTrackingNodes(String trackingId, List<TrackingNode> nodes) {
        // TODO: 实现批量添加轨迹节点
        return getTrackingById(trackingId);
    }

    @Override
    public TrackingInfo updateCurrentLocation(String trackingId, LocationInfo location) {
        TrackingInfo trackingInfo = getTrackingById(trackingId);
        trackingInfo.setCurrentLocation(location);
        trackingInfo.setLastUpdateTime(LocalDateTime.now());
        return trackingInfoRepository.save(trackingInfo);
    }

    @Override
    public TrackingInfo assignCourier(String trackingId, Long courierId, String courierName, String courierPhone) {
        // TODO: 实现分配配送员
        return getTrackingById(trackingId);
    }

    @Override
    public TrackingInfo updateEstimatedDeliveryTime(String trackingId, LocalDateTime estimatedTime) {
        // TODO: 实现更新预计到达时间
        return getTrackingById(trackingId);
    }

    @Override
    public TrackingInfo markAsException(String trackingId, String exceptionDescription) {
        TrackingInfo trackingInfo = getTrackingById(trackingId);
        trackingInfo.setIsException(true);
        trackingInfo.setExceptionDescription(exceptionDescription);
        trackingInfo.setLastUpdateTime(LocalDateTime.now());
        return trackingInfoRepository.save(trackingInfo);
    }

    @Override
    public TrackingInfo clearException(String trackingId) {
        // TODO: 实现清除异常状态
        return getTrackingById(trackingId);
    }

    @Override
    public TrackingInfo completeDelivery(String trackingId, String completionNotes) {
        // TODO: 实现完成配送
        return getTrackingById(trackingId);
    }

    @Override
    public List<TrackingInfo> getTrackingsByStatus(String status) {
        return trackingInfoRepository.findByCurrentStatus(status);
    }

    @Override
    public Page<TrackingInfo> getTrackingsByCourier(Long courierId, Pageable pageable) {
        return trackingInfoRepository.findByCourierId(courierId, pageable);
    }

    @Override
    public Page<TrackingInfo> getExceptionTrackings(Pageable pageable) {
        return trackingInfoRepository.findByIsExceptionTrue(pageable);
    }

    @Override
    public List<TrackingInfo> getDeliveryInProgress() {
        return trackingInfoCustomRepository.findDeliveryInProgress();
    }

    @Override
    public Page<TrackingInfo> searchTrackings(String status, String city, Long courierId, 
                                             Boolean isException, LocalDateTime startTime, 
                                             LocalDateTime endTime, Pageable pageable) {
        return trackingInfoCustomRepository.findByComplexConditions(
                status, city, courierId, isException, startTime, endTime, pageable);
    }

    @Override
    public List<TrackingInfo> getCourierDailyTasks(Long courierId, LocalDateTime date) {
        // TODO: 实现获取配送员当日任务
        return new ArrayList<>();
    }

    @Override
    public Double calculateTotalDistance(String trackingId) {
        // TODO: 实现计算总里程
        return 0.0;
    }

    @Override
    public List<TrackingNode> getTrackingTimeline(String trackingId) {
        TrackingInfo trackingInfo = getTrackingById(trackingId);
        return trackingInfo.getTrackingNodes() != null ? trackingInfo.getTrackingNodes() : new ArrayList<>();
    }

    @Override
    public void batchUpdateLocations(List<TrackingInfo> trackingInfos) {
        trackingInfoCustomRepository.batchUpdateLocation(trackingInfos);
    }

    @Override
    public long cleanExpiredData(int expireDays) {
        LocalDateTime expireTime = LocalDateTime.now().minusDays(expireDays);
        return trackingInfoCustomRepository.deleteExpiredData(expireTime);
    }

    @Override
    public boolean trackingExists(String orderNumber) {
        return trackingInfoRepository.existsByOrderNumber(orderNumber);
    }

    @Override
    public TrackingInfo getTrackingDetails(String trackingId) {
        return getTrackingById(trackingId);
    }
    
    private TrackingInfo getTrackingById(String trackingId) {
        return trackingInfoRepository.findById(trackingId)
                .orElseThrow(() -> new RuntimeException("未找到ID为 " + trackingId + " 的轨迹信息"));
    }

    private TrackingNode createTrackingNode(String status, String title, String description,
                                          LocationInfo location, Long operatorId, String operatorName,
                                          String operatorType) {
        TrackingNode node = new TrackingNode();
        node.setNodeId(UUID.randomUUID().toString());
        node.setStatus(status);
        node.setTitle(title);
        node.setDescription(description);
        node.setLocation(location);
        node.setOperatorId(operatorId);
        node.setOperatorName(operatorName);
        node.setOperatorType(operatorType);
        node.setTimestamp(LocalDateTime.now());
        return node;
    }
} 