# 后端代码修改指南

## 🔧 必须修改的文件

### 1. 邮件配置修改 (必须)

**文件**: `logistics-notification/src/main/resources/application.yml`

```yaml
# 修改邮件配置
spring:
  mail:
    host: smtp.qq.com
    port: 587
    username: 你的QQ邮箱@qq.com  # 替换为你的QQ邮箱
    password: 你的QQ邮箱授权码    # 替换为QQ邮箱授权码
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
          ssl:
            trust: smtp.qq.com

# 修改通知配置
notification:
  email:
    enabled: true
    from: 你的QQ邮箱@qq.com      # 替换为你的QQ邮箱
    fromName: 物流跟踪系统
```

### 2. 添加邮件服务依赖 (如果缺少)

**文件**: `logistics-notification/pom.xml`

检查是否有以下依赖，如果没有请添加：
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-mail</artifactId>
</dependency>
```

### 3. 添加新的服务类 (已创建，需要放入项目)

**需要添加的文件**:
- `logistics-notification/src/main/java/org/example/logisticsnotification/service/EmailNotificationService.java`
- `logistics-notification/src/main/java/org/example/logisticsnotification/service/impl/EmailNotificationServiceImpl.java`
- `logistics-order/src/main/java/org/example/logisticsorder/service/OrderLifecycleService.java`
- `logistics-order/src/main/java/org/example/logisticsorder/service/impl/OrderLifecycleServiceImpl.java`

### 4. 修改现有的NotificationServiceImpl (已修改)

**文件**: `logistics-notification/src/main/java/org/example/logisticsnotification/service/impl/NotificationServiceImpl.java`

已经修改了sendEmail方法，实现真实的邮件发送功能。

## 🔄 可选修改的文件

### 1. 订单状态枚举 (已修改)

**文件**: `logistics-common/src/main/java/com/logistics/common/constants/OrderStatus.java`

已添加新的状态：SORTING、DISPATCHING、TRANSFERRING

### 2. 添加网点管理实体 (可选)

**文件**: `logistics-logistics/src/main/java/org/example/logisticslogistics/entity/LogisticsStation.java`

已创建网点实体类，可以添加到项目中。

### 3. 添加邮件通知控制器 (推荐)

**新建文件**: `logistics-notification/src/main/java/org/example/logisticsnotification/controller/EmailController.java`

```java
@RestController
@RequestMapping("/notification/email")
@Slf4j
public class EmailController {

    @Autowired
    private EmailNotificationService emailNotificationService;

    @PostMapping("/order-created")
    public Result<Boolean> sendOrderCreatedNotification(
            @RequestParam String orderNumber,
            @RequestParam String customerName,
            @RequestParam String customerEmail,
            @RequestParam String totalFee) {
        
        boolean success = emailNotificationService.sendOrderCreatedNotification(
                customerEmail, customerName, orderNumber, totalFee);
        
        return success ? Result.success(true) : Result.error("邮件发送失败");
    }

    @PostMapping("/payment-success")
    public Result<Boolean> sendPaymentSuccessNotification(
            @RequestParam String orderNumber,
            @RequestParam String customerName,
            @RequestParam String customerEmail,
            @RequestParam String paymentAmount) {
        
        boolean success = emailNotificationService.sendPaymentSuccessNotification(
                customerEmail, customerName, orderNumber, paymentAmount);
        
        return success ? Result.success(true) : Result.error("邮件发送失败");
    }

    @PostMapping("/status-update")
    public Result<Boolean> sendStatusUpdateNotification(
            @RequestParam String orderNumber,
            @RequestParam String customerEmail,
            @RequestParam String customerName,
            @RequestParam String oldStatus,
            @RequestParam String newStatus) {
        
        boolean success = emailNotificationService.sendOrderStatusNotification(
                orderNumber, customerEmail, customerName, oldStatus, newStatus);
        
        return success ? Result.success(true) : Result.error("邮件发送失败");
    }
}
```

## 🚀 集成现有服务

### 1. 在OrderServiceImpl中集成邮件通知

**文件**: `logistics-order/src/main/java/org/example/logisticsorder/service/impl/OrderServiceImpl.java`

在关键方法中添加邮件通知调用：

```java
// 在createOrder方法中添加
@Autowired
private EmailNotificationService emailNotificationService;

// 订单创建成功后
emailNotificationService.sendOrderCreatedNotification(
    order.getSenderPhone(), // 假设用手机号作为邮箱
    order.getSenderName(),
    order.getOrderNumber(),
    order.getTotalFee().toString()
);

// 在paymentSuccess方法中添加
emailNotificationService.sendPaymentSuccessNotification(
    order.getSenderPhone(),
    order.getSenderName(), 
    orderNumber,
    order.getTotalFee().toString()
);
```

### 2. 在DeliveryTaskServiceImpl中集成邮件通知

**文件**: `logistics-delivery/src/main/java/org/example/logisticsdelivery/service/impl/DeliveryTaskServiceImpl.java`

在任务分配时发送邮件：

```java
// 在createDeliveryTask方法中添加
@Autowired
private EmailNotificationService emailNotificationService;

// 任务创建成功后
emailNotificationService.sendCourierTaskNotification(
    courier.getEmail(), // 需要配送员邮箱字段
    courier.getCourierName(),
    task.getOrderNumber(),
    task.getTaskType()
);
```

## 📋 修改步骤总结

### 第一步：配置邮件服务
1. 申请QQ邮箱授权码
2. 修改application.yml配置文件
3. 重启notification服务

### 第二步：添加新服务类
1. 复制EmailNotificationService相关文件到项目
2. 复制OrderLifecycleService相关文件到项目
3. 重新编译项目

### 第三步：集成现有服务
1. 在OrderServiceImpl中添加邮件通知调用
2. 在DeliveryTaskServiceImpl中添加邮件通知调用
3. 测试邮件发送功能

### 第四步：执行数据库脚本
```bash
mysql -u root -p logistics < logistics-system/sql/database_update.sql
```

## ⚠️ 注意事项

1. **QQ邮箱授权码获取**：
   - 登录QQ邮箱 → 设置 → 账户 → 开启SMTP服务 → 获取授权码
   - 授权码不是QQ密码，是专门的16位授权码

2. **Feign客户端配置**：
   - 如果使用OrderLifecycleService，需要确保Feign客户端配置正确
   - 检查服务注册中心(Nacos)是否正常运行

3. **测试建议**：
   - 先测试简单的邮件发送功能
   - 再测试完整的业务流程
   - 检查邮件是否进入垃圾箱

4. **性能考虑**：
   - 邮件发送是异步的，不会阻塞主业务流程
   - 可以考虑使用消息队列进一步优化
