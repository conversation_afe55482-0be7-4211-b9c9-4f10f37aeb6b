/**
 * 真实数据状态管理
 * 替换所有模拟数据，使用真实API接口
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  orderApi, 
  trackingApi, 
  deliveryApi, 
  userApi, 
  stationApi, 
  addressApi, 
  locationApi, 
  analyticsApi 
} from '@/api/realData'
import type { Order, TrackingInfo, DeliveryTask, User, Station, Address } from '@/types'

export const useRealDataStore = defineStore('realData', () => {
  // ========== 订单数据 ==========
  const orders = ref<Order[]>([])
  const currentOrder = ref<Order | null>(null)
  const orderLoading = ref(false)
  const orderStatistics = ref({
    total: 0,
    pending: 0,
    paid: 0,
    inTransit: 0,
    delivered: 0,
    cancelled: 0
  })

  // 获取订单列表
  const fetchOrders = async (params: {
    page?: number
    size?: number
    status?: string
    orderNumber?: string
    startDate?: string
    endDate?: string
  } = {}) => {
    try {
      orderLoading.value = true
      const response = await orderApi.getOrderList(params)
      orders.value = response.data.records || []
      return response.data
    } catch (error) {
      ElMessage.error('获取订单列表失败')
      console.error('获取订单列表失败:', error)
      return { records: [], total: 0 }
    } finally {
      orderLoading.value = false
    }
  }

  // 获取订单详情
  const fetchOrderDetail = async (orderId: string) => {
    try {
      const response = await orderApi.getOrderDetail(orderId)
      currentOrder.value = response.data
      return response.data
    } catch (error) {
      ElMessage.error('获取订单详情失败')
      console.error('获取订单详情失败:', error)
      return null
    }
  }

  // 创建订单
  const createOrder = async (orderData: any) => {
    try {
      const response = await orderApi.createOrder(orderData)
      ElMessage.success('订单创建成功')
      await fetchOrders() // 刷新订单列表
      return response.data
    } catch (error) {
      ElMessage.error('订单创建失败')
      console.error('订单创建失败:', error)
      throw error
    }
  }

  // 支付订单
  const payOrder = async (orderId: string, paymentMethod: string) => {
    try {
      const response = await orderApi.payOrder(orderId, paymentMethod)
      ElMessage.success('支付成功')
      await fetchOrderDetail(orderId) // 刷新订单详情
      return response.data
    } catch (error) {
      ElMessage.error('支付失败')
      console.error('支付失败:', error)
      throw error
    }
  }

  // 获取订单统计
  const fetchOrderStatistics = async (params?: any) => {
    try {
      const response = await orderApi.getOrderStatistics(params)
      orderStatistics.value = response.data
      return response.data
    } catch (error) {
      console.error('获取订单统计失败:', error)
      return orderStatistics.value
    }
  }

  // ========== 物流轨迹数据 ==========
  const trackingInfo = ref<TrackingInfo[]>([])
  const trackingLoading = ref(false)

  // 获取物流轨迹
  const fetchTracking = async (orderNumber: string) => {
    try {
      trackingLoading.value = true
      const response = await trackingApi.getOrderTracking(orderNumber)
      trackingInfo.value = response.data || []
      return response.data
    } catch (error) {
      ElMessage.error('获取物流轨迹失败')
      console.error('获取物流轨迹失败:', error)
      return []
    } finally {
      trackingLoading.value = false
    }
  }

  // 添加轨迹节点
  const addTrackingNode = async (trackingData: any) => {
    try {
      const response = await trackingApi.addTrackingNode(trackingData)
      ElMessage.success('轨迹更新成功')
      await fetchTracking(trackingData.orderNumber) // 刷新轨迹
      return response.data
    } catch (error) {
      ElMessage.error('轨迹更新失败')
      console.error('轨迹更新失败:', error)
      throw error
    }
  }

  // ========== 配送任务数据 ==========
  const deliveryTasks = ref<DeliveryTask[]>([])
  const taskLoading = ref(false)

  // 获取配送任务列表
  const fetchDeliveryTasks = async (params: any = {}) => {
    try {
      taskLoading.value = true
      const response = await deliveryApi.getTaskList(params)
      deliveryTasks.value = response.data.records || []
      return response.data
    } catch (error) {
      ElMessage.error('获取配送任务失败')
      console.error('获取配送任务失败:', error)
      return { records: [], total: 0 }
    } finally {
      taskLoading.value = false
    }
  }

  // 获取配送员任务
  const fetchCourierTasks = async (courierId: string, status?: string) => {
    try {
      const response = await deliveryApi.getCourierTasks(courierId, status)
      return response.data || []
    } catch (error) {
      ElMessage.error('获取配送员任务失败')
      console.error('获取配送员任务失败:', error)
      return []
    }
  }

  // 接受任务
  const acceptTask = async (taskId: string) => {
    try {
      const response = await deliveryApi.acceptTask(taskId)
      ElMessage.success('任务接受成功')
      await fetchDeliveryTasks() // 刷新任务列表
      return response.data
    } catch (error) {
      ElMessage.error('任务接受失败')
      console.error('任务接受失败:', error)
      throw error
    }
  }

  // ========== 用户数据 ==========
  const users = ref<User[]>([])
  const couriers = ref<User[]>([])
  const operators = ref<User[]>([])

  // 获取用户列表
  const fetchUsers = async (params: any = {}) => {
    try {
      const response = await userApi.getUserList(params)
      users.value = response.data.records || []
      return response.data
    } catch (error) {
      ElMessage.error('获取用户列表失败')
      console.error('获取用户列表失败:', error)
      return { records: [], total: 0 }
    }
  }

  // 获取配送员列表
  const fetchCouriers = async (params?: any) => {
    try {
      const response = await userApi.getCourierList(params)
      couriers.value = response.data || []
      return response.data
    } catch (error) {
      ElMessage.error('获取配送员列表失败')
      console.error('获取配送员列表失败:', error)
      return []
    }
  }

  // 获取操作员列表
  const fetchOperators = async (params?: any) => {
    try {
      const response = await userApi.getOperatorList(params)
      operators.value = response.data || []
      return response.data
    } catch (error) {
      ElMessage.error('获取操作员列表失败')
      console.error('获取操作员列表失败:', error)
      return []
    }
  }

  // ========== 网点数据 ==========
  const stations = ref<Station[]>([])

  // 获取网点列表
  const fetchStations = async (params?: any) => {
    try {
      const response = await stationApi.getStationList(params)
      stations.value = response.data || []
      return response.data
    } catch (error) {
      ElMessage.error('获取网点列表失败')
      console.error('获取网点列表失败:', error)
      return []
    }
  }

  // ========== 地址数据 ==========
  const addresses = ref<Address[]>([])

  // 获取用户地址列表
  const fetchAddresses = async (userId?: string) => {
    try {
      const response = await addressApi.getUserAddresses(userId)
      addresses.value = response.data || []
      return response.data
    } catch (error) {
      ElMessage.error('获取地址列表失败')
      console.error('获取地址列表失败:', error)
      return []
    }
  }

  // 添加地址
  const addAddress = async (addressData: any) => {
    try {
      const response = await addressApi.addAddress(addressData)
      ElMessage.success('地址添加成功')
      await fetchAddresses() // 刷新地址列表
      return response.data
    } catch (error) {
      ElMessage.error('地址添加失败')
      console.error('地址添加失败:', error)
      throw error
    }
  }

  // ========== 位置服务数据 ==========
  const searchResults = ref<any[]>([])
  const frequentLocations = ref<any[]>([])

  // 搜索地点
  const searchLocations = async (params: any) => {
    try {
      const response = await locationApi.searchLocations(params)
      searchResults.value = response.data || []
      return response.data
    } catch (error) {
      ElMessage.error('搜索地点失败')
      console.error('搜索地点失败:', error)
      return []
    }
  }

  // 获取常用地点
  const fetchFrequentLocations = async () => {
    try {
      const response = await locationApi.getFrequentLocations()
      frequentLocations.value = response.data || []
      return response.data
    } catch (error) {
      console.error('获取常用地点失败:', error)
      return []
    }
  }

  // ========== 统计分析数据 ==========
  const dashboardData = ref<any>({})
  const orderTrends = ref<any[]>([])

  // 获取仪表板数据
  const fetchDashboardData = async (params?: any) => {
    try {
      const response = await analyticsApi.getDashboardData(params)
      dashboardData.value = response.data || {}
      return response.data
    } catch (error) {
      ElMessage.error('获取仪表板数据失败')
      console.error('获取仪表板数据失败:', error)
      return {}
    }
  }

  // 获取订单趋势
  const fetchOrderTrends = async (params: any) => {
    try {
      const response = await analyticsApi.getOrderTrends(params)
      orderTrends.value = response.data || []
      return response.data
    } catch (error) {
      ElMessage.error('获取订单趋势失败')
      console.error('获取订单趋势失败:', error)
      return []
    }
  }

  // ========== 计算属性 ==========
  const totalOrders = computed(() => orders.value.length)
  const pendingOrders = computed(() => orders.value.filter(order => order.status === 'PENDING').length)
  const completedOrders = computed(() => orders.value.filter(order => order.status === 'SIGNED').length)

  const availableCouriers = computed(() => couriers.value.filter(courier => courier.status === 'AVAILABLE'))
  const busyCouriers = computed(() => couriers.value.filter(courier => courier.status === 'BUSY'))

  const activeStations = computed(() => stations.value.filter(station => station.status === 'ACTIVE'))

  // ========== 初始化数据 ==========
  const initializeData = async () => {
    try {
      // 并行加载基础数据
      await Promise.all([
        fetchOrders({ page: 1, size: 20 }),
        fetchOrderStatistics(),
        fetchCouriers(),
        fetchStations(),
        fetchDashboardData()
      ])
    } catch (error) {
      console.error('初始化数据失败:', error)
    }
  }

  return {
    // 状态
    orders,
    currentOrder,
    orderLoading,
    orderStatistics,
    trackingInfo,
    trackingLoading,
    deliveryTasks,
    taskLoading,
    users,
    couriers,
    operators,
    stations,
    addresses,
    searchResults,
    frequentLocations,
    dashboardData,
    orderTrends,

    // 计算属性
    totalOrders,
    pendingOrders,
    completedOrders,
    availableCouriers,
    busyCouriers,
    activeStations,

    // 方法
    fetchOrders,
    fetchOrderDetail,
    createOrder,
    payOrder,
    fetchOrderStatistics,
    fetchTracking,
    addTrackingNode,
    fetchDeliveryTasks,
    fetchCourierTasks,
    acceptTask,
    fetchUsers,
    fetchCouriers,
    fetchOperators,
    fetchStations,
    fetchAddresses,
    addAddress,
    searchLocations,
    fetchFrequentLocations,
    fetchDashboardData,
    fetchOrderTrends,
    initializeData
  }
})
