# 🚀 **物流系统部署指南**

## 📋 **部署概述**

本指南详细说明如何在生产环境中部署智慧物流订单跟踪系统，包括环境准备、服务配置、部署步骤和运维监控。

## 🏗️ **部署架构**

### **生产环境架构**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   负载均衡器    │    │   Web服务器     │    │   应用服务器    │
│    Nginx        │◄──►│    Nginx        │◄──►│  Spring Boot    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                        ┌───────▼──────┐        ┌──────▼──────┐
                        │   静态资源   │        │   微服务集群 │
                        │   CDN分发    │        │   Docker容器 │
                        └──────────────┘        └─────────────┘
                                                       │
                ┌──────────────────────────────────────┼──────────────────┐
                │                                      │                  │
        ┌───────▼──────┐        ┌──────▼──────┐        ┌─────▼──────┐
        │   数据库集群 │        │   缓存集群  │        │  消息队列  │
        │ MySQL主从    │        │ Redis集群   │        │ RabbitMQ   │
        └──────────────┘        └─────────────┘        └────────────┘
```

## 🔧 **环境准备**

### **服务器配置要求**

#### **应用服务器**
- **CPU**: 4核心以上
- **内存**: 8GB以上
- **存储**: 100GB SSD
- **操作系统**: CentOS 7+ / Ubuntu 18.04+
- **Java**: OpenJDK 11+

#### **数据库服务器**
- **CPU**: 8核心以上
- **内存**: 16GB以上
- **存储**: 500GB SSD（建议RAID 10）
- **MySQL**: 8.0+
- **MongoDB**: 4.4+

#### **缓存和消息队列服务器**
- **CPU**: 4核心以上
- **内存**: 8GB以上
- **存储**: 50GB SSD
- **Redis**: 6.0+
- **RabbitMQ**: 3.9+

### **网络配置**
```bash
# 防火墙端口开放
firewall-cmd --permanent --add-port=80/tcp      # Nginx
firewall-cmd --permanent --add-port=443/tcp     # HTTPS
firewall-cmd --permanent --add-port=8081-8087/tcp # 微服务端口
firewall-cmd --permanent --add-port=3306/tcp    # MySQL
firewall-cmd --permanent --add-port=27017/tcp   # MongoDB
firewall-cmd --permanent --add-port=6379/tcp    # Redis
firewall-cmd --permanent --add-port=5672/tcp    # RabbitMQ
firewall-cmd --reload
```

## 📦 **Docker部署方案**

### **1. 创建Docker网络**
```bash
# 创建自定义网络
docker network create logistics-network
```

### **2. 数据库容器部署**
```bash
# MySQL容器
docker run -d \
  --name logistics-mysql \
  --network logistics-network \
  -p 3306:3306 \
  -e MYSQL_ROOT_PASSWORD=your_password \
  -e MYSQL_DATABASE=logistics \
  -v /data/mysql:/var/lib/mysql \
  mysql:8.0

# MongoDB容器
docker run -d \
  --name logistics-mongodb \
  --network logistics-network \
  -p 27017:27017 \
  -e MONGO_INITDB_ROOT_USERNAME=admin \
  -e MONGO_INITDB_ROOT_PASSWORD=your_password \
  -v /data/mongodb:/data/db \
  mongo:4.4

# Redis容器
docker run -d \
  --name logistics-redis \
  --network logistics-network \
  -p 6379:6379 \
  -v /data/redis:/data \
  redis:6.0 redis-server --appendonly yes

# RabbitMQ容器
docker run -d \
  --name logistics-rabbitmq \
  --network logistics-network \
  -p 5672:5672 \
  -p 15672:15672 \
  -e RABBITMQ_DEFAULT_USER=admin \
  -e RABBITMQ_DEFAULT_PASS=your_password \
  rabbitmq:3.9-management
```

### **3. 应用服务容器部署**
```bash
# 构建应用镜像
cd logistics-system
docker build -t logistics-order:latest ./logistics-order
docker build -t logistics-delivery:latest ./logistics-delivery
docker build -t logistics-logistics:latest ./logistics-logistics
docker build -t logistics-notification:latest ./logistics-notification
docker build -t logistics-user:latest ./logistics-user
docker build -t logistics-payment:latest ./logistics-payment
docker build -t logistics-email:latest ./logistics-email

# 启动微服务容器
docker run -d \
  --name logistics-order \
  --network logistics-network \
  -p 8081:8081 \
  -e SPRING_PROFILES_ACTIVE=prod \
  logistics-order:latest

docker run -d \
  --name logistics-delivery \
  --network logistics-network \
  -p 8082:8082 \
  -e SPRING_PROFILES_ACTIVE=prod \
  logistics-delivery:latest

# ... 其他服务类似
```

### **4. 前端容器部署**
```bash
# 构建前端镜像
cd logistics-frontend
docker build -t logistics-frontend:latest .

# 启动前端容器
docker run -d \
  --name logistics-frontend \
  --network logistics-network \
  -p 80:80 \
  logistics-frontend:latest
```

## 🐳 **Docker Compose部署**

### **docker-compose.yml**
```yaml
version: '3.8'

services:
  # 数据库服务
  mysql:
    image: mysql:8.0
    container_name: logistics-mysql
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: logistics
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    networks:
      - logistics-network

  mongodb:
    image: mongo:4.4
    container_name: logistics-mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_USERNAME}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD}
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    networks:
      - logistics-network

  redis:
    image: redis:6.0
    container_name: logistics-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - logistics-network

  rabbitmq:
    image: rabbitmq:3.9-management
    container_name: logistics-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USERNAME}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD}
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - logistics-network

  # 微服务
  order-service:
    build: ./logistics-order
    container_name: logistics-order
    environment:
      SPRING_PROFILES_ACTIVE: prod
      MYSQL_HOST: mysql
      REDIS_HOST: redis
      RABBITMQ_HOST: rabbitmq
    ports:
      - "8081:8081"
    depends_on:
      - mysql
      - redis
      - rabbitmq
    networks:
      - logistics-network

  delivery-service:
    build: ./logistics-delivery
    container_name: logistics-delivery
    environment:
      SPRING_PROFILES_ACTIVE: prod
      MYSQL_HOST: mysql
      REDIS_HOST: redis
      RABBITMQ_HOST: rabbitmq
    ports:
      - "8082:8082"
    depends_on:
      - mysql
      - redis
      - rabbitmq
    networks:
      - logistics-network

  # ... 其他服务

  # 前端服务
  frontend:
    build: ./logistics-frontend
    container_name: logistics-frontend
    ports:
      - "80:80"
    depends_on:
      - order-service
      - delivery-service
    networks:
      - logistics-network

volumes:
  mysql_data:
  mongodb_data:
  redis_data:
  rabbitmq_data:

networks:
  logistics-network:
    driver: bridge
```

### **启动命令**
```bash
# 创建环境变量文件
cat > .env << EOF
MYSQL_ROOT_PASSWORD=your_mysql_password
MONGO_USERNAME=admin
MONGO_PASSWORD=your_mongo_password
RABBITMQ_USERNAME=admin
RABBITMQ_PASSWORD=your_rabbitmq_password
EOF

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f order-service
```

## 🔧 **生产环境配置**

### **应用配置文件**
```yaml
# application-prod.yml
server:
  port: 8081

spring:
  datasource:
    url: **************************************************************************
    username: ${MYSQL_USERNAME:root}
    password: ${MYSQL_PASSWORD}
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  redis:
    host: ${REDIS_HOST:redis}
    port: 6379
    password: ${REDIS_PASSWORD:}
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5

  rabbitmq:
    host: ${RABBITMQ_HOST:rabbitmq}
    port: 5672
    username: ${RABBITMQ_USERNAME:admin}
    password: ${RABBITMQ_PASSWORD}

logging:
  level:
    org.example: INFO
  file:
    name: /var/log/logistics/order-service.log
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
```

### **Nginx配置**
```nginx
# /etc/nginx/conf.d/logistics.conf
upstream logistics_backend {
    server 127.0.0.1:8081 weight=1 max_fails=2 fail_timeout=30s;
    server 127.0.0.1:8082 weight=1 max_fails=2 fail_timeout=30s;
    server 127.0.0.1:8083 weight=1 max_fails=2 fail_timeout=30s;
}

server {
    listen 80;
    server_name logistics.example.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name logistics.example.com;
    
    # SSL证书配置
    ssl_certificate /etc/nginx/ssl/logistics.crt;
    ssl_certificate_key /etc/nginx/ssl/logistics.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    
    # 前端静态资源
    location / {
        root /var/www/logistics-frontend;
        index index.html;
        try_files $uri $uri/ /index.html;
        
        # 缓存配置
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API代理
    location /api/ {
        proxy_pass http://logistics_backend/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # WebSocket支持
    location /ws/ {
        proxy_pass http://logistics_backend/ws/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

## 📊 **监控和日志**

### **应用监控**
```yaml
# 添加到application-prod.yml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
```

### **日志收集**
```yaml
# logback-spring.xml
<configuration>
    <springProfile name="prod">
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>/var/log/logistics/application.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>/var/log/logistics/application.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <maxFileSize>100MB</maxFileSize>
                <maxHistory>30</maxHistory>
                <totalSizeCap>3GB</totalSizeCap>
            </rollingPolicy>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>
        
        <root level="INFO">
            <appender-ref ref="FILE"/>
        </root>
    </springProfile>
</configuration>
```

## 🔒 **安全配置**

### **数据库安全**
```sql
-- 创建专用数据库用户
CREATE USER 'logistics_app'@'%' IDENTIFIED BY 'strong_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON logistics.* TO 'logistics_app'@'%';
FLUSH PRIVILEGES;

-- 禁用root远程登录
UPDATE mysql.user SET Host='localhost' WHERE User='root' AND Host='%';
FLUSH PRIVILEGES;
```

### **应用安全**
```yaml
# 安全配置
spring:
  security:
    jwt:
      secret: ${JWT_SECRET:your_jwt_secret_key}
      expiration: 86400000 # 24小时
    
server:
  ssl:
    enabled: true
    key-store: classpath:keystore.p12
    key-store-password: ${SSL_KEYSTORE_PASSWORD}
    key-store-type: PKCS12
```

## 🚀 **部署脚本**

### **自动化部署脚本**
```bash
#!/bin/bash
# deploy.sh

set -e

echo "🚀 开始部署物流系统..."

# 1. 拉取最新代码
git pull origin main

# 2. 构建后端服务
echo "📦 构建后端服务..."
mvn clean package -DskipTests

# 3. 构建前端应用
echo "🎨 构建前端应用..."
cd logistics-frontend
npm install
npm run build
cd ..

# 4. 停止旧服务
echo "⏹️ 停止旧服务..."
docker-compose down

# 5. 启动新服务
echo "▶️ 启动新服务..."
docker-compose up -d

# 6. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 7. 健康检查
echo "🔍 执行健康检查..."
./testing/automated-test-script.sh

echo "✅ 部署完成！"
```

## 📈 **性能优化**

### **JVM参数优化**
```bash
# 生产环境JVM参数
JAVA_OPTS="-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/var/log/logistics/"
```

### **数据库优化**
```sql
-- MySQL配置优化
[mysqld]
innodb_buffer_pool_size = 4G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
query_cache_size = 256M
max_connections = 500
```

## 🔄 **备份和恢复**

### **数据备份脚本**
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backup/logistics/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# MySQL备份
mysqldump -u root -p logistics > $BACKUP_DIR/mysql_backup.sql

# MongoDB备份
mongodump --host localhost --port 27017 --out $BACKUP_DIR/mongodb_backup

# Redis备份
cp /var/lib/redis/dump.rdb $BACKUP_DIR/redis_backup.rdb

echo "✅ 备份完成: $BACKUP_DIR"
```

## 📞 **运维支持**

### **常用运维命令**
```bash
# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f service_name

# 重启服务
docker-compose restart service_name

# 扩容服务
docker-compose up -d --scale order-service=3

# 查看资源使用
docker stats

# 清理无用镜像
docker system prune -a
```

### **故障排查**
1. **服务无法启动**: 检查端口占用和配置文件
2. **数据库连接失败**: 检查网络和认证信息
3. **内存不足**: 调整JVM参数和容器资源限制
4. **响应缓慢**: 检查数据库性能和缓存命中率

---

**部署完成后，系统即可投入生产使用！** 🎉
