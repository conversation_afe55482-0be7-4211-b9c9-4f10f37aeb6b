<template>
  <div class="courier-management">
    <div class="page-header">
      <h1>配送员管理</h1>
      <div class="header-actions">
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          添加配送员
        </el-button>
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="配送员姓名">
          <el-input v-model="searchForm.name" placeholder="请输入配送员姓名" clearable />
        </el-form-item>
        <el-form-item label="手机号">
          <el-input v-model="searchForm.phone" placeholder="请输入手机号" clearable />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="在线" value="online" />
            <el-option label="忙碌" value="busy" />
            <el-option label="离线" value="offline" />
            <el-option label="休息" value="rest" />
          </el-select>
        </el-form-item>
        <el-form-item label="工作区域">
          <el-input v-model="searchForm.workArea" placeholder="请输入工作区域" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 配送员列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="table-header">
          <span>配送员列表</span>
          <div class="table-actions">
            <el-button type="primary" size="small" @click="exportData">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        :data="courierList"
        v-loading="loading"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="姓名" width="120" />
        <el-table-column prop="phone" label="手机号" width="130" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="location" label="当前位置" show-overflow-tooltip />
        <el-table-column prop="currentOrders" label="当前订单" width="100" />
        <el-table-column prop="todayCompleted" label="今日完成" width="100" />
        <el-table-column prop="rating" label="评分" width="100">
          <template #default="scope">
            <el-rate v-model="scope.row.rating" disabled size="small" />
          </template>
        </el-table-column>
        <el-table-column prop="lastActiveTime" label="最后活跃" width="150">
          <template #default="scope">
            {{ formatDateTime(scope.row.lastActiveTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="viewDetail(scope.row)"> 详情 </el-button>
            <el-button type="warning" size="small" @click="editCourier(scope.row)">
              编辑
            </el-button>
            <el-dropdown @command="(command) => handleCommand(command, scope.row)">
              <el-button size="small">
                更多<el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="status">修改状态</el-dropdown-item>
                  <el-dropdown-item command="location">更新位置</el-dropdown-item>
                  <el-dropdown-item command="orders">查看订单</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑配送员对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingCourier ? '编辑配送员' : '添加配送员'"
      width="600px"
    >
      <el-form :model="courierForm" :rules="courierRules" ref="courierFormRef" label-width="100px">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="courierForm.name" placeholder="请输入配送员姓名" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="courierForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="工作区域" prop="workArea">
          <el-input v-model="courierForm.workArea" placeholder="请输入工作区域" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="courierForm.status" placeholder="请选择状态">
            <el-option label="在线" value="online" />
            <el-option label="离线" value="offline" />
            <el-option label="休息" value="rest" />
          </el-select>
        </el-form-item>
        <el-form-item label="工作时间" prop="workingHours">
          <el-input v-model="courierForm.workingHours" placeholder="例如：09:00-18:00" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveCourier" :loading="saving">
          {{ editingCourier ? '更新' : '添加' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus, Edit, View, Delete } from '@element-plus/icons-vue'
import { courierApi, type CourierInfo } from '@/api/courier'
import { operatorApi } from '@/api/operator'
import { 
  getCourierStatusText, 
  getCourierStatusType, 
  isCourierAvailable,
  isCourierOnline,
  getAllStatusOptions,
  stringToStatusCode,
  statusCodeToString 
} from '@/utils/courierStatus'

// 响应式数据
const courierList = ref<CourierInfo[]>([])
const selectedCouriers = ref<CourierInfo[]>([])
const editingCourier = ref<CourierInfo | null>(null)

// 搜索表单
const searchForm = reactive({
  name: '',
  phone: '',
  status: '',
  workArea: '',
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
})

const total = ref(0)
const loading = ref(false)
const saving = ref(false)

// 对话框控制
const showAddDialog = ref(false)

// 表单数据
const courierForm = reactive({
  name: '',
  phone: '',
  workArea: '',
  status: 'offline',
  workingHours: '09:00-18:00',
})

// 表单验证规则
const courierRules = {
  name: [{ required: true, message: '请输入配送员姓名', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
  ],
  workArea: [{ required: true, message: '请输入工作区域', trigger: 'blur' }],
}

const courierFormRef = ref()

// 生命周期
onMounted(() => {
  loadData()
})

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: pagination.page,
      pageSize: pagination.size,
      status: searchForm.status ? getStatusValue(searchForm.status) : undefined,
      workArea: searchForm.workArea || undefined,
      courierName: searchForm.name || undefined,
      phone: searchForm.phone || undefined,
    }

    // 清理空值参数
    Object.keys(params).forEach((key) => {
      if (params[key] === undefined || params[key] === '') {
        delete params[key]
      }
    })

    console.log('请求配送员列表，参数:', params)
    const response = await courierApi.getAllCouriers(params)
    console.log('配送员列表响应:', response)

    if (response.code === 200) {
      const data = response.data

      // 转换数据格式
      const convertedList = (data.records || data.list || []).map((courier: any) => ({
        id: courier.id,
        name: courier.courierName || courier.realName || '未知',
        phone: courier.phone || '',
        status: getStatusFromValue(courier.status),
        location: courier.workArea || courier.currentAddress || '未知',
        currentOrders: courier.currentOrderCount || 0,
        todayCompleted: courier.todayCompletedCount || 0,
        rating: courier.rating || 5.0,
        workingHours: courier.workingHours || '09:00-18:00',
        lastActiveTime: courier.lastActiveTime || courier.updateTime || '',
        // 保留原始数据以便编辑
        _raw: courier,
      }))

      courierList.value = convertedList
      total.value = data.total || 0

      console.log('转换后的配送员列表:', courierList.value)

      if (courierList.value.length === 0) {
        ElMessage.info('暂无配送员数据')
      }
    } else {
      throw new Error(response.message || '获取配送员列表失败')
    }
  } catch (error) {
    console.error('加载配送员数据失败:', error)
    ElMessage.error('加载配送员数据失败，请检查网络连接')
    courierList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadData()
  ElMessage.success('数据已刷新')
}

const handleSearch = () => {
  pagination.page = 1
  loadData()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    name: '',
    phone: '',
    status: '',
    workArea: '',
  })
  handleSearch()
}

const handleSelectionChange = (selection: CourierInfo[]) => {
  selectedCouriers.value = selection
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadData()
}

const viewDetail = (courier: CourierInfo) => {
  ElMessage.info('查看配送员详情功能开发中')
}

const editCourier = (courier: CourierInfo) => {
  editingCourier.value = courier
  Object.assign(courierForm, {
    name: courier.name,
    phone: courier.phone,
    workArea: courier.location,
    status: courier.status,
    workingHours: courier.workingHours,
  })
  showAddDialog.value = true
}

const saveCourier = async () => {
  if (!courierFormRef.value) return

  try {
    await courierFormRef.value.validate()

    saving.value = true

    // 准备提交数据
    const submitData = {
      courierName: courierForm.name,
      phone: courierForm.phone,
      workArea: courierForm.workArea,
      status: getStatusValue(courierForm.status),
      workingHours: courierForm.workingHours,
    }

    if (editingCourier.value) {
      // 更新配送员
      console.log('更新配送员数据:', submitData)
      const response = await courierApi.updateCourier(editingCourier.value.id, submitData)

      if (response.code === 200) {
        ElMessage.success('配送员信息更新成功')
      } else {
        throw new Error(response.message || '更新失败')
      }
    } else {
      // 添加配送员
      console.log('创建配送员数据:', submitData)
      const response = await courierApi.createCourier(submitData)

      if (response.code === 200) {
        ElMessage.success('配送员添加成功')
      } else {
        throw new Error(response.message || '添加失败')
      }
    }

    showAddDialog.value = false
    loadData()
    resetForm()
  } catch (error) {
    console.error('保存配送员失败:', error)
    ElMessage.error(`操作失败：${error.message || '请重试'}`)
  } finally {
    saving.value = false
  }
}

const resetForm = () => {
  editingCourier.value = null
  Object.assign(courierForm, {
    name: '',
    phone: '',
    workArea: '',
    status: 'offline',
    workingHours: '09:00-18:00',
  })
  courierFormRef.value?.resetFields()
}

const handleCommand = (command: string, courier: CourierInfo) => {
  switch (command) {
    case 'status':
      updateCourierStatus(courier)
      break
    case 'location':
      updateCourierLocation(courier)
      break
    case 'orders':
      viewCourierOrders(courier)
      break
    case 'delete':
      deleteCourier(courier)
      break
  }
}

const updateCourierStatus = async (courier: CourierInfo) => {
  try {
    const { value: statusText } = await ElMessageBox.prompt('请选择新状态', '修改状态', {
      inputType: 'select',
      inputOptions: [
        { key: 'online', value: '在线' },
        { key: 'offline', value: '离线' },
        { key: 'busy', value: '忙碌' },
        { key: 'rest', value: '休息' },
      ],
    })

    const statusValue = getStatusValue(statusText)
    console.log(`更新配送员 ${courier.id} 状态为: ${statusText} (${statusValue})`)

    const response = await courierApi.updateCourier(courier.id, { status: statusValue })

    if (response.code === 200) {
      ElMessage.success('状态更新成功')
      loadData()
    } else {
      throw new Error(response.message || '状态更新失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('更新状态失败:', error)
      ElMessage.error(`状态更新失败：${error.message || '请重试'}`)
    }
  }
}

const updateCourierLocation = (courier: CourierInfo) => {
  ElMessage.info('更新位置功能开发中')
}

const viewCourierOrders = (courier: CourierInfo) => {
  ElMessage.info('查看配送员订单功能开发中')
}

const deleteCourier = async (courier: CourierInfo) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除配送员"${courier.name}"吗？删除后将无法恢复！`,
      '确认删除',
      {
        type: 'warning',
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
      },
    )

    console.log(`删除配送员: ${courier.id}`)
    const response = await courierApi.deleteCourier(courier.id)

    if (response.code === 200) {
      ElMessage.success('配送员删除成功')
      loadData()
    } else {
      throw new Error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除配送员失败:', error)
      ElMessage.error(`删除失败：${error.message || '请重试'}`)
    }
  }
}

const exportData = () => {
  ElMessage.info('导出功能开发中')
}

// 工具方法
const getStatusType = (status: string) => {
  switch (status) {
    case 'online':
      return 'success'
    case 'busy':
      return 'warning'
    case 'rest':
      return 'info'
    case 'offline':
    default:
      return 'info'
  }
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    online: '在线',
    busy: '忙碌',
    offline: '离线',
    rest: '休息',
  }
  return statusMap[status] || '未知'
}

// 状态转换方法
const getStatusValue = (status: string): number => {
  const statusMap: Record<string, number> = {
    offline: 0,
    online: 1,
    busy: 2,
    rest: 3,
  }
  return statusMap[status] ?? 0
}

const getStatusFromValue = (value: number): string => {
  const statusMap: Record<number, string> = {
    0: 'offline',
    1: 'online',
    2: 'busy',
    3: 'rest',
  }
  return statusMap[value] ?? 'offline'
}

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '暂无数据'
  try {
    return new Date(dateTime).toLocaleString('zh-CN')
  } catch (error) {
    return '格式错误'
  }
}
</script>

<style scoped>
.courier-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: none;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
