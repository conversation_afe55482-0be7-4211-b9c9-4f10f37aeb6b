package org.example.logisticsdelivery.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.logisticsdelivery.entity.Courier;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 配送员数据访问�?
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Mapper
public interface CourierMapper extends BaseMapper<Courier> {

    /**
     * 根据状态查询配送员列表
     */
    List<Courier> findByStatus(@Param("status") Integer status);

    /**
     * 根据工作区域查询配送员
     */
    List<Courier> findByWorkArea(@Param("workArea") String workArea);

    /**
     * 查询指定范围内的配送员
     */
    List<Courier> findByLocationRange(@Param("minLng") BigDecimal minLng,
                                      @Param("maxLng") BigDecimal maxLng,
                                      @Param("minLat") BigDecimal minLat,
                                      @Param("maxLat") BigDecimal maxLat);

    /**
     * 查询可用的配送员（在线且不忙碌）
     */
    List<Courier> findAvailableCouriers(@Param("workArea") String workArea);

    /**
     * 根据配送员编号查询
     */
    Courier findByCourierCode(@Param("courierCode") String courierCode);

    /**
     * 根据用户ID查询配送员
     */
    Courier findByUserId(@Param("userId") Long userId);

    /**
     * 更新配送员位置
     */
    int updateLocation(@Param("id") Long id,
                       @Param("longitude") BigDecimal longitude,
                       @Param("latitude") BigDecimal latitude,
                       @Param("address") String address);

    /**
     * 更新配送员状态
     */
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 分页查询配送员
     */
    IPage<Courier> findCouriersPage(Page<Courier> page,
                                    @Param("status") Integer status,
                                    @Param("workArea") String workArea,
                                    @Param("courierName") String courierName);

    /**
     * 统计各状态配送员数量
     */
    List<Map<String, Object>> countByStatus();

    /**
     * 分页查询配送员（联表查询）
     */
    IPage<Courier> selectCourierPage(Page<Courier> page,
                                     @Param("status") Integer status,
                                     @Param("workArea") String workArea,
                                     @Param("name") String name,
                                     @Param("phone") String phone);
}
