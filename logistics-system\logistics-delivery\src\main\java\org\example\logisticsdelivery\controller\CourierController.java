package org.example.logisticsdelivery.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logistics.common.result.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.logisticsdelivery.entity.Courier;
import org.example.logisticsdelivery.mapper.CourierMapper;
import org.example.logisticsdelivery.service.CourierService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 配送员管理控制器
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Slf4j
@RestController
@RequestMapping("/delivery/courier")
@RequiredArgsConstructor
public class CourierController {

    private final CourierService courierService;

    @Autowired
    CourierMapper courierMapper;

    /**
     * 创建配送员
     */
    @PostMapping
    public Result<Courier> createCourier(@RequestBody Courier courier) {
        try {
            Courier result = courierService.createCourier(courier);
            return Result.success(result);
        } catch (Exception e) {
            log.error("创建配送员失败", e);
            return Result.error("创建配送员失败: " + e.getMessage());
        }
    }

    /**
     * 更新配送员信息
     */
    @PutMapping("/{id}")
    public Result<Courier> updateCourier(@PathVariable Long id, @RequestBody Courier courier) {
        try {
            courier.setId(id);
            Courier result = courierService.updateCourier(courier);
            return Result.success(result);
        } catch (Exception e) {
            log.error("更新配送员信息失败，ID: {}", id, e);
            return Result.error("更新配送员信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询配送员
     */
    @GetMapping("/{id}")
    public Result<Courier> getCourierById(@PathVariable Long id) {
        try {
            Courier courier = courierService.getCourierById(id);
            if (courier != null) {
                return Result.success(courier);
            } else {
                return Result.error("配送员不存在");
            }
        } catch (Exception e) {
            log.error("查询配送员失败，ID: {}", id, e);
            return Result.error("查询配送员失败: " + e.getMessage());
        }
    }

    /**
     * 根据配送员编号查询
     */
    @GetMapping("/code/{courierCode}")
    public Result<Courier> getCourierByCode(@PathVariable String courierCode) {
        try {
            Courier courier = courierService.getCourierByCode(courierCode);
            if (courier != null) {
                return Result.success(courier);
            } else {
                return Result.error("配送员不存在");
            }
        } catch (Exception e) {
            log.error("查询配送员失败，编号: {}", courierCode, e);
            return Result.error("查询配送员失败: " + e.getMessage());
        }
    }

    /**
     * 根据用户ID查询配送员
     */
    @GetMapping("/user/{userId}")
    public Result<Courier> getCourierByUserId(@PathVariable Long userId) {
        try {
            Courier courier = courierService.getCourierByUserId(userId);
            if (courier != null) {
                return Result.success(courier);
            } else {
                return Result.error("该用户未绑定配送员账号");
            }
        } catch (Exception e) {
            log.error("查询配送员失败，用户ID: {}", userId, e);
            return Result.error("查询配送员失败: " + e.getMessage());
        }
    }

    /**
     * 根据状态查询配送员列表
     */
    @GetMapping("/status/{status}")
    public Result<List<Courier>> getCouriersByStatus(@PathVariable Integer status) {
        try {
            List<Courier> couriers = courierService.getCouriersByStatus(status);
            return Result.success(couriers);
        } catch (Exception e) {
            log.error("查询配送员列表失败，状态: {}", status, e);
            return Result.error("查询配送员列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据工作区域查询配送员
     */
    @GetMapping("/work-area/{workArea}")
    public Result<List<Courier>> getCouriersByWorkArea(@PathVariable String workArea) {
        try {
            List<Courier> couriers = courierService.getCouriersByWorkArea(workArea);
            return Result.success(couriers);
        } catch (Exception e) {
            log.error("查询配送员列表失败，工作区域: {}", workArea, e);
            return Result.error("查询配送员列表失败: " + e.getMessage());
        }
    }

    /**
     * 查询指定范围内的配送员
     */
    @GetMapping("/range")
    public Result<List<Courier>> getCouriersInRange(
            @RequestParam BigDecimal minLng,
            @RequestParam BigDecimal maxLng,
            @RequestParam BigDecimal minLat,
            @RequestParam BigDecimal maxLat) {
        try {
            List<Courier> couriers = courierService.getCouriersInRange(minLng, maxLng, minLat, maxLat);
            return Result.success(couriers);
        } catch (Exception e) {
            log.error("查询范围内配送员失败", e);
            return Result.error("查询范围内配送员失败: " + e.getMessage());
        }
    }

    /**
     * 查询可用的配送员
     */
    @GetMapping("/available")
    public Result<List<Courier>> getAvailableCouriers(@RequestParam(required = false) String workArea) {
        try {
            List<Courier> couriers = courierService.getAvailableCouriers(workArea);
            return Result.success(couriers);
        } catch (Exception e) {
            log.error("查询可用配送员失败", e);
            return Result.error("查询可用配送员失败: " + e.getMessage());
        }
    }

    /**
     * 更新配送员位置
     */
    @PutMapping("/{id}/location")
    public Result<String> updateCourierLocation(
            @PathVariable Long id,
            @RequestParam BigDecimal longitude,
            @RequestParam BigDecimal latitude,
            @RequestParam(required = false) String address) {
        try {
            boolean success = courierService.updateCourierLocation(id, longitude, latitude, address);
            if (success) {
                return Result.success("位置更新成功");
            } else {
                return Result.error("位置更新失败");
            }
        } catch (Exception e) {
            log.error("更新配送员位置失败，ID: {}", id, e);
            return Result.error("更新配送员位置失败: " + e.getMessage());
        }
    }

    /**
     * 更新配送员状态
     */
    @PutMapping("/{id}/status")
    public Result<String> updateCourierStatus(@PathVariable Long id, @RequestParam Integer status) {
        try {
            boolean success = courierService.updateCourierStatus(id, status);
            if (success) {
                return Result.success("状态更新成功");
            } else {
                return Result.error("状态更新失败");
            }
        } catch (Exception e) {
            log.error("更新配送员状态失败，ID: {}", id, e);
            return Result.error("更新配送员状态失败: " + e.getMessage());
        }
    }



    /**
     * 统计各状态配送员数量
     */
    @GetMapping("/statistics/status")
    public Result<Map<String, Long>> getStatusStatistics() {
        try {
            Map<String, Long> statistics = courierService.getStatusStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取配送员状态统计失败", e);
            return Result.error("获取配送员状态统计失败: " + e.getMessage());
        }
    }

    /**
     * 自动分配最佳配送员
     */
    @PostMapping("/assign-best")
    public Result<Courier> assignBestCourier(
            @RequestParam String workArea,
            @RequestParam(required = false) BigDecimal longitude,
            @RequestParam(required = false) BigDecimal latitude) {
        try {
            Courier courier = courierService.assignBestCourier(workArea, longitude, latitude);
            if (courier != null) {
                return Result.success(courier);
            } else {
                return Result.error("没有可用的配送员");
            }
        } catch (Exception e) {
            log.error("自动分配配送员失败", e);
            return Result.error("自动分配配送员失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新配送员状态
     */
    @PutMapping("/batch/status")
    public Result<String> batchUpdateStatus(@RequestBody Map<String, Object> params) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> courierIds = (List<Long>) params.get("courierIds");
            Integer status = (Integer) params.get("status");

            boolean success = courierService.batchUpdateStatus(courierIds, status);
            if (success) {
                return Result.success("批量更新状态成功");
            } else {
                return Result.error("批量更新状态失败");
            }
        } catch (Exception e) {
            log.error("批量更新配送员状态失败", e);
            return Result.error("批量更新配送员状态失败: " + e.getMessage());
        }
    }

    /**
     * 删除配送员
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteCourier(@PathVariable Long id) {
        try {
            boolean success = courierService.deleteCourier(id);
            if (success) {
                return Result.success("删除配送员成功");
            } else {
                return Result.error("删除配送员失败");
            }
        } catch (Exception e) {
            log.error("删除配送员失败，ID: {}", id, e);
            return Result.error("删除配送员失败: " + e.getMessage());
        }
    }

    @GetMapping("/page")
    public Result<IPage<Courier>> getCourierPage(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) String workArea,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String phone) {

        try {
            Page<Courier> page = new Page<>(pageNum, pageSize);
            IPage<Courier> result = courierMapper.selectCourierPage(page, status, workArea, name, phone);
            return Result.success("查询成功", result);
        } catch (Exception e) {
            log.error("查询配送员列表失败", e);
            return Result.error("查询配送员列表失败: " + e.getMessage());
        }
    }
}