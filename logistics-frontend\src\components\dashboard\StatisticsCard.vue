<template>
  <div class="statistics-card" :class="cardClass">
    <div class="card-background">
      <div class="background-pattern"></div>
      <div class="background-icon">
        <el-icon :size="80">
          <component :is="icon" />
        </el-icon>
      </div>
    </div>
    
    <div class="card-content">
      <div class="card-header">
        <div class="title-section">
          <h3 class="card-title">{{ title }}</h3>
          <p class="card-subtitle" v-if="subtitle">{{ subtitle }}</p>
        </div>
        
        <div class="icon-section">
          <div class="icon-wrapper" :style="{ backgroundColor: iconBgColor }">
            <el-icon :size="24" :color="iconColor">
              <component :is="icon" />
            </el-icon>
          </div>
        </div>
      </div>
      
      <div class="card-body">
        <div class="main-value">
          <span class="value-number">{{ formattedValue }}</span>
          <span class="value-unit" v-if="unit">{{ unit }}</span>
        </div>
        
        <div class="trend-section" v-if="trend !== undefined">
          <div class="trend-indicator" :class="trendClass">
            <el-icon class="trend-icon">
              <component :is="trendIcon" />
            </el-icon>
            <span class="trend-value">{{ Math.abs(trend) }}%</span>
          </div>
          <span class="trend-text">{{ trendText }}</span>
        </div>
      </div>
      
      <div class="card-footer" v-if="showProgress || actions.length > 0">
        <div class="progress-section" v-if="showProgress">
          <div class="progress-info">
            <span class="progress-label">完成率</span>
            <span class="progress-value">{{ progressPercentage }}%</span>
          </div>
          <el-progress 
            :percentage="progressPercentage" 
            :color="progressColor"
            :stroke-width="4"
            :show-text="false"
            class="progress-bar"
          />
        </div>
        
        <div class="actions-section" v-if="actions.length > 0">
          <el-button
            v-for="action in actions"
            :key="action.key"
            :type="action.type || 'text'"
            :size="action.size || 'small'"
            @click="handleAction(action.key)"
          >
            <el-icon v-if="action.icon">
              <component :is="action.icon" />
            </el-icon>
            {{ action.label }}
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <el-icon class="loading-icon">
        <Loading />
      </el-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ArrowUp, ArrowDown, Loading } from '@element-plus/icons-vue'

// Props
interface Action {
  key: string
  label: string
  icon?: string
  type?: string
  size?: string
}

interface Props {
  title: string
  subtitle?: string
  value: number | string
  unit?: string
  icon: string
  theme?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  trend?: number
  trendText?: string
  progressPercentage?: number
  showProgress?: boolean
  actions?: Action[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  theme: 'primary',
  showProgress: false,
  actions: () => [],
  loading: false
})

// Emits
const emit = defineEmits<{
  action: [key: string]
}>()

// 计算属性
const cardClass = computed(() => {
  return `theme-${props.theme}`
})

const formattedValue = computed(() => {
  if (typeof props.value === 'number') {
    if (props.value >= 10000) {
      return (props.value / 10000).toFixed(1) + 'w'
    }
    if (props.value >= 1000) {
      return (props.value / 1000).toFixed(1) + 'k'
    }
    return props.value.toLocaleString()
  }
  return props.value
})

const trendClass = computed(() => {
  if (props.trend === undefined) return ''
  return props.trend >= 0 ? 'trend-up' : 'trend-down'
})

const trendIcon = computed(() => {
  if (props.trend === undefined) return ''
  return props.trend >= 0 ? ArrowUp : ArrowDown
})

const iconColor = computed(() => {
  const colors = {
    primary: '#1890ff',
    success: '#52c41a',
    warning: '#faad14',
    danger: '#f5222d',
    info: '#13c2c2'
  }
  return colors[props.theme]
})

const iconBgColor = computed(() => {
  const colors = {
    primary: 'rgba(24, 144, 255, 0.1)',
    success: 'rgba(82, 196, 26, 0.1)',
    warning: 'rgba(250, 173, 20, 0.1)',
    danger: 'rgba(245, 34, 45, 0.1)',
    info: 'rgba(19, 194, 194, 0.1)'
  }
  return colors[props.theme]
})

const progressColor = computed(() => {
  const colors = {
    primary: '#1890ff',
    success: '#52c41a',
    warning: '#faad14',
    danger: '#f5222d',
    info: '#13c2c2'
  }
  return colors[props.theme]
})

// 方法
const handleAction = (key: string) => {
  emit('action', key)
}
</script>

<style scoped>
.statistics-card {
  position: relative;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-secondary);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all var(--duration-normal);
  height: 200px;
}

.statistics-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

/* ========== 背景装饰 ========== */
.card-background {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.background-pattern {
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.03) 50%, transparent 70%);
  transform: rotate(45deg);
}

.background-icon {
  position: absolute;
  top: -20px;
  right: -20px;
  opacity: 0.05;
  transform: rotate(15deg);
}

/* ========== 卡片内容 ========== */
.card-content {
  position: relative;
  padding: var(--spacing-xl);
  height: 100%;
  display: flex;
  flex-direction: column;
  z-index: 1;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-lg);
}

.title-section {
  flex: 1;
}

.card-title {
  margin: 0;
  font-size: var(--font-md);
  font-weight: 500;
  color: var(--text-secondary);
  line-height: 1.2;
}

.card-subtitle {
  margin: var(--spacing-xs) 0 0 0;
  font-size: var(--font-sm);
  color: var(--text-tertiary);
}

.icon-section {
  flex-shrink: 0;
}

.icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ========== 主要数值 ========== */
.card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.main-value {
  display: flex;
  align-items: baseline;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-md);
}

.value-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
}

.value-unit {
  font-size: var(--font-lg);
  color: var(--text-tertiary);
  font-weight: 500;
}

/* ========== 趋势指示器 ========== */
.trend-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.trend-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-sm);
  font-weight: 500;
}

.trend-indicator.trend-up {
  background: rgba(82, 196, 26, 0.1);
  color: var(--success-color);
}

.trend-indicator.trend-down {
  background: rgba(245, 34, 45, 0.1);
  color: var(--error-color);
}

.trend-icon {
  font-size: var(--font-sm);
}

.trend-text {
  font-size: var(--font-sm);
  color: var(--text-tertiary);
}

/* ========== 卡片底部 ========== */
.card-footer {
  margin-top: auto;
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-light);
}

.progress-section {
  margin-bottom: var(--spacing-md);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xs);
}

.progress-label {
  font-size: var(--font-sm);
  color: var(--text-tertiary);
}

.progress-value {
  font-size: var(--font-sm);
  font-weight: 500;
  color: var(--text-secondary);
}

.progress-bar {
  margin-bottom: 0;
}

.actions-section {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
}

/* ========== 主题样式 ========== */
.theme-primary {
  border-left: 4px solid var(--primary-color);
}

.theme-success {
  border-left: 4px solid var(--success-color);
}

.theme-warning {
  border-left: 4px solid var(--warning-color);
}

.theme-danger {
  border-left: 4px solid var(--error-color);
}

.theme-info {
  border-left: 4px solid var(--info-color);
}

/* ========== 加载状态 ========== */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-icon {
  font-size: var(--font-2xl);
  color: var(--primary-color);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* ========== 响应式设计 ========== */
@media (max-width: 768px) {
  .statistics-card {
    height: auto;
    min-height: 160px;
  }
  
  .card-content {
    padding: var(--spacing-lg);
  }
  
  .value-number {
    font-size: 2rem;
  }
  
  .card-header {
    margin-bottom: var(--spacing-md);
  }
  
  .icon-wrapper {
    width: 40px;
    height: 40px;
  }
}

@media (max-width: 480px) {
  .card-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }
  
  .icon-section {
    align-self: flex-end;
  }
  
  .value-number {
    font-size: 1.8rem;
  }
  
  .actions-section {
    justify-content: center;
  }
}
</style>
