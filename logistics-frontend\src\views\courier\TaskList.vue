<template>
  <div class="courier-task-list">
    <div class="page-header">
      <h2>我的任务</h2>
      <div class="header-actions">
        <el-button @click="refreshList" :icon="Refresh">刷新</el-button>
        <el-button @click="optimizeRoute" type="primary" :disabled="selectedTasks.length === 0">
          <el-icon><Guide /></el-icon>
          路线优化
        </el-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <el-form :model="filterForm" inline>
        <el-form-item label="任务状态">
          <el-select v-model="filterForm.taskStatus" placeholder="全部状态" clearable>
            <el-option label="待分配" value="PENDING" />
            <el-option label="已分配" value="ASSIGNED" />
            <el-option label="已揽收" value="PICKED_UP" />
            <el-option label="运输中" value="IN_TRANSIT" />
            <el-option label="已完成" value="COMPLETED" />
            <el-option label="已取消" value="CANCELLED" />
          </el-select>
        </el-form-item>

        <el-form-item label="任务类型">
          <el-select v-model="filterForm.taskType" placeholder="全部类型" clearable>
            <el-option label="揽件" value="PICKUP" />
            <el-option label="派送" value="DELIVERY" />
          </el-select>
        </el-form-item>

        <el-form-item label="优先级">
          <el-select v-model="filterForm.priority" placeholder="全部优先级" clearable>
            <el-option label="低" :value="1" />
            <el-option label="中" :value="2" />
            <el-option label="高" :value="3" />
            <el-option label="紧急" :value="4" />
          </el-select>
        </el-form-item>

        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 任务统计 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ taskStats.total }}</div>
            <div class="stat-label">总任务</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ taskStats.assigned }}</div>
            <div class="stat-label">已分配</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ taskStats.inProgress }}</div>
            <div class="stat-label">进行中</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ taskStats.completed }}</div>
            <div class="stat-label">已完成</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 任务列表 -->
    <el-card>
      <div class="list-controls">
        <div class="view-controls">
          <el-radio-group v-model="viewMode">
            <el-radio-button label="list">列表视图</el-radio-button>
            <el-radio-button label="card">卡片视图</el-radio-button>
          </el-radio-group>
        </div>
        
        <div class="sort-controls">
          <span>排序：</span>
          <el-select v-model="sortBy" size="small">
            <el-option label="创建时间" value="createTime" />
            <el-option label="优先级" value="priority" />
            <el-option label="预计时间" value="estimatedTime" />
          </el-select>
          <el-button @click="toggleSortOrder" size="small" :icon="sortOrder === 'asc' ? 'sort-up' : 'sort-down'">
            {{ sortOrder === 'asc' ? '升序' : '降序' }}
          </el-button>
        </div>
      </div>

      <!-- 列表视图 -->
      <el-table
        v-if="viewMode === 'list'"
        :data="taskList"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="taskNumber" label="任务编号" width="150">
          <template #default="{ row }">
            <el-link type="primary" @click="viewTaskDetail(row.id)">
              {{ row.taskNumber }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column label="类型" width="80">
          <template #default="{ row }">
            <el-tag :type="getTaskTypeTag(row.taskType)" size="small">
              {{ getTaskTypeText(row.taskType) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="优先级" width="80">
          <template #default="{ row }">
            <el-tag :type="getPriorityTag(row.priority)" size="small">
              {{ getPriorityText(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="地址信息" min-width="300">
          <template #default="{ row }">
            <div class="address-info">
              <div class="address-item">
                <el-icon><Position /></el-icon>
                <span>{{ row.senderAddress }}</span>
              </div>
              <div class="address-item">
                <el-icon><LocationFilled /></el-icon>
                <span>{{ row.receiverAddress }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="联系信息" width="150">
          <template #default="{ row }">
            <div>
              <div>{{ row.receiverName }}</div>
              <div class="contact-phone">{{ row.receiverPhone }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTag(row.taskStatus)" size="small">
              {{ getStatusText(row.taskStatus) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="预计时间" width="120">
          <template #default="{ row }">
            <span v-if="row.estimatedTime">{{ formatTime(row.estimatedTime) }}</span>
            <span v-else class="text-gray">未设置</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="table-actions">
              <el-button 
                v-if="row.taskStatus === 'ASSIGNED'" 
                type="primary" 
                size="small"
                @click="startTask(row.id)"
              >
                开始
              </el-button>
              <el-button 
                v-if="row.taskStatus === 'IN_TRANSIT'" 
                type="success" 
                size="small"
                @click="completeTask(row)"
              >
                完成
              </el-button>
              <el-button 
                size="small"
                @click="viewTaskDetail(row.id)"
              >
                详情
              </el-button>
              <el-dropdown>
                <el-button size="small">
                  更多<el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="contactCustomer(row)">联系客户</el-dropdown-item>
                    <el-dropdown-item @click="reportProblem(row)">报告问题</el-dropdown-item>
                    <el-dropdown-item @click="cancelTask(row)" divided class="danger">取消任务</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 卡片视图 -->
      <div v-else class="card-view">
        <div v-if="taskList.length === 0" class="empty-state">
          <el-empty description="暂无任务数据" />
        </div>
        
        <div v-else class="task-cards">
          <div 
            v-for="task in taskList" 
            :key="task.id" 
            class="task-card"
            :class="{ selected: selectedTasks.includes(task) }"
            @click="toggleSelection(task)"
          >
            <div class="card-header">
              <div class="task-info">
                <span class="task-number">{{ task.taskNumber }}</span>
                <el-tag :type="getTaskTypeTag(task.taskType)" size="small">
                  {{ getTaskTypeText(task.taskType) }}
                </el-tag>
              </div>
              <div class="task-meta">
                <el-tag :type="getPriorityTag(task.priority)" size="small">
                  {{ getPriorityText(task.priority) }}
                </el-tag>
                <el-tag :type="getStatusTag(task.taskStatus)" size="small">
                  {{ getStatusText(task.taskStatus) }}
                </el-tag>
              </div>
            </div>

            <div class="card-content">
              <div class="address-section">
                <div class="address-item">
                  <el-icon><Position /></el-icon>
                  <span>{{ task.senderAddress }}</span>
                </div>
                <div class="address-item">
                  <el-icon><LocationFilled /></el-icon>
                  <span>{{ task.receiverAddress }}</span>
                </div>
              </div>

              <div class="contact-section">
                <div class="contact-info">
                  <span>{{ task.receiverName }}</span>
                  <span class="phone">{{ task.receiverPhone }}</span>
                </div>
                <div v-if="task.estimatedTime" class="estimated-time">
                  预计：{{ formatTime(task.estimatedTime) }}
                </div>
              </div>

              <div v-if="task.goodsDescription" class="goods-info">
                <span>货物：{{ task.goodsDescription }}</span>
                <span v-if="task.weight">重量：{{ task.weight }}kg</span>
              </div>
            </div>

            <div class="card-actions">
              <el-button 
                v-if="task.taskStatus === 'ASSIGNED'" 
                type="primary" 
                size="small"
                @click.stop="startTask(task.id)"
              >
                开始任务
              </el-button>
              <el-button 
                v-if="task.taskStatus === 'IN_TRANSIT'" 
                type="success" 
                size="small"
                @click.stop="completeTask(task)"
              >
                完成任务
              </el-button>
              <el-button 
                size="small"
                @click.stop="viewTaskDetail(task.id)"
              >
                查看详情
              </el-button>
              <el-button 
                size="small"
                @click.stop="contactCustomer(task)"
              >
                联系客户
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 完成任务对话框 -->
    <el-dialog v-model="showCompleteDialog" title="完成任务" width="500px">
      <el-form :model="completeForm" label-width="80px">
        <el-form-item label="任务编号">
          <el-input v-model="completeForm.taskNumber" readonly />
        </el-form-item>
        
        <el-form-item label="完成凭证">
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :on-change="handleFileChange"
            :show-file-list="false"
            accept="image/*"
          >
            <el-button>选择图片</el-button>
          </el-upload>
          <div v-if="completeForm.proofImage" class="proof-preview">
            <img :src="completeForm.proofImage" alt="完成凭证" />
          </div>
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input 
            v-model="completeForm.remarks" 
            type="textarea" 
            placeholder="请输入完成备注"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCompleteDialog = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="confirmComplete"
          :loading="completing"
        >
          确认完成
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Guide,
  Position,
  LocationFilled,
  ArrowDown,
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { courierApi, type DeliveryTask } from '@/api/courier'
import dayjs from 'dayjs'

const router = useRouter()

// 状态
const loading = ref(false)
const completing = ref(false)
const taskList = ref<DeliveryTask[]>([])
const selectedTasks = ref<DeliveryTask[]>([])
const total = ref(0)
const showCompleteDialog = ref(false)
const currentTaskForComplete = ref<DeliveryTask | null>(null)

// 视图模式
const viewMode = ref('list') // list | card

// 排序
const sortBy = ref('createTime')
const sortOrder = ref<'asc' | 'desc'>('desc')

// 筛选表单
const filterForm = reactive({
  taskStatus: '',
  taskType: '',
  priority: '',
})

// 日期范围
const dateRange = ref<[string, string] | null>(null)

// 分页
const pagination = reactive({
  current: 1,
  size: 20,
})

// 完成表单
const completeForm = reactive({
  taskId: 0,
  taskNumber: '',
  proofImage: '',
  remarks: '',
})

// 任务统计
const taskStats = ref({
  total: 0,
  assigned: 0,
  inProgress: 0,
  completed: 0,
})

// 计算搜索参数
const searchParams = computed(() => {
  const params: any = {
    page: pagination.current,
    size: pagination.size,
  }

  if (filterForm.taskStatus) params.taskStatus = filterForm.taskStatus
  if (filterForm.taskType) params.taskType = filterForm.taskType
  if (filterForm.priority) params.priority = filterForm.priority

  if (dateRange.value) {
    params.startTime = dateRange.value[0] + ' 00:00:00'
    params.endTime = dateRange.value[1] + ' 23:59:59'
  }

  return params
})

// 加载任务列表
const loadTaskList = async () => {
  loading.value = true
  try {
    const response = await courierApi.getDeliveryTasks(searchParams.value)
    if (response.data.code === 200) {
      const data = response.data.data
      taskList.value = data.records
      total.value = data.total
      pagination.current = data.current
      pagination.size = data.size
      
      // 计算统计数据
      updateTaskStats()
    }
  } catch (error) {
    console.error('加载任务列表失败:', error)
    ElMessage.error('加载任务列表失败')
  } finally {
    loading.value = false
  }
}

// 更新任务统计
const updateTaskStats = () => {
  taskStats.value = {
    total: taskList.value.length,
    assigned: taskList.value.filter(t => t.status === 'ASSIGNED').length,
    inProgress: taskList.value.filter(t => ['PICKED_UP', 'IN_TRANSIT'].includes(t.status)).length,
    completed: taskList.value.filter(t => t.status === 'COMPLETED').length,
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadTaskList()
}

// 重置
const handleReset = () => {
  Object.assign(filterForm, {
    taskStatus: '',
    taskType: '',
    priority: '',
  })
  dateRange.value = null
  pagination.current = 1
  loadTaskList()
}

// 刷新列表
const refreshList = () => {
  loadTaskList()
}

// 分页变化
const handleSizeChange = () => {
  pagination.current = 1
  loadTaskList()
}

const handleCurrentChange = () => {
  loadTaskList()
}

// 选择变化
const handleSelectionChange = (selection: DeliveryTask[]) => {
  selectedTasks.value = selection
}

// 切换选择（卡片视图）
const toggleSelection = (task: DeliveryTask) => {
  const index = selectedTasks.value.findIndex(t => t.id === task.id)
  if (index > -1) {
    selectedTasks.value.splice(index, 1)
  } else {
    selectedTasks.value.push(task)
  }
}

// 切换排序顺序
const toggleSortOrder = () => {
  sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  // TODO: 重新排序列表
}

// 开始任务
const startTask = async (taskId: number) => {
  try {
    await courierApi.startTask(taskId)
    ElMessage.success('任务已开始')
    loadTaskList()
  } catch (error) {
    console.error('开始任务失败:', error)
    ElMessage.error('开始任务失败')
  }
}

// 完成任务
const completeTask = (task: DeliveryTask) => {
  currentTaskForComplete.value = task
  completeForm.taskId = task.id
  completeForm.taskNumber = task.orderNumber
  completeForm.proofImage = ''
  completeForm.remarks = ''
  showCompleteDialog.value = true
}

// 确认完成任务
const confirmComplete = async () => {
  if (!completeForm.proofImage) {
    ElMessage.warning('请上传完成凭证')
    return
  }

  completing.value = true
  try {
    await courierApi.completeTask({
      taskId: completeForm.taskId,
      completionProof: completeForm.proofImage,
      remarks: completeForm.remarks,
    })
    
    ElMessage.success('任务完成成功')
    showCompleteDialog.value = false
    loadTaskList()
  } catch (error) {
    console.error('完成任务失败:', error)
    ElMessage.error('完成任务失败')
  } finally {
    completing.value = false
  }
}

// 取消任务
const cancelTask = async (task: DeliveryTask) => {
  try {
    const { value: reason } = await ElMessageBox.prompt('请输入取消原因', '取消任务', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      inputPattern: /.+/,
      inputErrorMessage: '请输入取消原因',
    })
    
    await courierApi.cancelTask(task.id, reason)
    ElMessage.success('任务已取消')
    loadTaskList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消任务失败:', error)
      ElMessage.error('取消任务失败')
    }
  }
}

// 联系客户
const contactCustomer = (task: DeliveryTask) => {
  // 打开拨号界面或显示联系方式
  const phone = task.receiverPhone
  if (phone) {
    window.open(`tel:${phone}`)
  } else {
    ElMessage.warning('客户电话号码不存在')
  }
}

// 报告问题
const reportProblem = (task: DeliveryTask) => {
  router.push(`/courier/task/report/${task.id}`)
}

// 查看任务详情
const viewTaskDetail = (taskId: number) => {
  router.push(`/courier/task/detail/${taskId}`)
}

// 路线优化
const optimizeRoute = async () => {
  if (selectedTasks.value.length < 2) {
    ElMessage.warning('请选择至少2个任务进行路线优化')
    return
  }

  try {
    const taskIds = selectedTasks.value.map(t => t.id)
    const response = await courierApi.optimizeRoute(taskIds)
    
    if (response.data.code === 200) {
      const result = response.data.data
      ElMessage.success(`路线优化完成，总距离：${result.totalDistance}km，预计时间：${result.totalTime}分钟`)
      // TODO: 显示优化后的路线
    }
  } catch (error) {
    console.error('路线优化失败:', error)
    ElMessage.error('路线优化失败')
  }
}

// 处理文件上传
const handleFileChange = (file: any) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    completeForm.proofImage = e.target?.result as string
  }
  reader.readAsDataURL(file.raw)
}

// 工具函数
const getTaskTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    PICKUP: '揽件',
    DELIVERY: '派送',
  }
  return textMap[type] || type
}

const getTaskTypeTag = (type: string) => {
  const tagMap: Record<string, string> = {
    PICKUP: 'warning',
    DELIVERY: 'success',
  }
  return tagMap[type] || ''
}

const getStatusText = (status: string) => {
  const textMap = {
    PENDING: '待分配',
    ASSIGNED: '已分配',
    PICKED_UP: '已揽收',
    IN_TRANSIT: '运输中',
    COMPLETED: '已完成',
    CANCELLED: '已取消',
  }
  return textMap[status] || status
}

const getStatusTag = (status: string) => {
  const tagMap = {
    PENDING: 'info',
    ASSIGNED: 'warning',
    PICKED_UP: 'primary',
    IN_TRANSIT: 'warning',
    COMPLETED: 'success',
    CANCELLED: 'danger',
  }
  return tagMap[status] || ''
}

const getPriorityText = (priority: number) => {
  const textMap = {
    1: '低',
    2: '中',
    3: '高',
    4: '紧急',
  }
  return textMap[priority] || '普通'
}

const getPriorityTag = (priority: number) => {
  const tagMap = {
    1: 'info',
    2: '',
    3: 'warning',
    4: 'danger',
  }
  return tagMap[priority] || ''
}

const formatTime = (time: string) => {
  return dayjs(time).format('MM-DD HH:mm')
}

onMounted(() => {
  loadTaskList()
})
</script>

<style scoped>
.courier-task-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-card {
  margin-bottom: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  padding: 20px 0;
}

.stat-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

.list-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.sort-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.address-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.address-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #666;
}

.contact-phone {
  color: #409eff;
  font-size: 12px;
}

.text-gray {
  color: #999;
}

.table-actions {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.danger {
  color: #f56c6c;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

/* 卡片视图样式 */
.card-view {
  min-height: 400px;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}

.task-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.task-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
}

.task-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.task-card.selected {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.task-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-number {
  font-weight: bold;
  color: #333;
}

.task-meta {
  display: flex;
  gap: 4px;
}

.card-content {
  margin-bottom: 16px;
}

.address-section {
  margin-bottom: 12px;
}

.contact-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.contact-info {
  display: flex;
  gap: 10px;
}

.phone {
  color: #409eff;
}

.estimated-time {
  color: #f56c6c;
  font-size: 12px;
}

.goods-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
}

.card-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.proof-preview {
  margin-top: 10px;
}

.proof-preview img {
  max-width: 200px;
  max-height: 200px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}
</style> 