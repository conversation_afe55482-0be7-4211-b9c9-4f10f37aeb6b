server:
  port: 8004

spring:
  application:
    name: logistics-delivery
  datasource:
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ****************************************************************************************************************************************************
      username: root
      password: liyuqw8017
      # 添加连接池配置
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: true
      test-on-return: false
      # 连接超时配置
      connection-timeout: 30000
      socket-timeout: 60000
      # 防止连接被回收
      keep-alive: true
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
  redis:
    host: localhost
    port: 6379
    password: redis123
    database: 1

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:mapper/*.xml

logging:
  level:
    org.example.logisticsdelivery: debug
# 添加配送服务特有配置
delivery:
  config:
    max-tasks-per-courier: 20        # 每个配送员最大任务数
    default-delivery-radius: 5       # 默认配送半径(公里)
    task-timeout-minutes: 120        # 任务超时时间(分钟)
jwt:
  secret: logistics-secret-key-2024-for-microservice-authentication-system
  expiration: 86400