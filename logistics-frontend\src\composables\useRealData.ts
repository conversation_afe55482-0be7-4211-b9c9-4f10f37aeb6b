/**
 * 真实数据组合式API
 * 提供统一的数据访问接口，替换所有模拟数据
 */

import { ref, computed, onMounted, watch } from 'vue'
import { useRealDataStore } from '@/stores/realDataStore'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

// ========== 订单数据Hook ==========
export function useOrderData() {
  const realDataStore = useRealDataStore()
  const userStore = useUserStore()

  const loading = computed(() => realDataStore.orderLoading)
  const orders = computed(() => realDataStore.orders)
  const currentOrder = computed(() => realDataStore.currentOrder)
  const statistics = computed(() => realDataStore.orderStatistics)

  // 获取订单列表
  const getOrders = async (params: any = {}) => {
    // 根据用户角色过滤数据
    if (userStore.userInfo?.role === 'CUSTOMER') {
      params.customerId = userStore.userInfo.id
    }
    return await realDataStore.fetchOrders(params)
  }

  // 获取订单详情
  const getOrderDetail = async (orderId: string) => {
    return await realDataStore.fetchOrderDetail(orderId)
  }

  // 创建订单
  const createOrder = async (orderData: any) => {
    // 自动添加创建者信息
    orderData.customerId = userStore.userInfo?.id
    orderData.customerName = userStore.userInfo?.realName
    return await realDataStore.createOrder(orderData)
  }

  // 支付订单
  const payOrder = async (orderId: string, paymentMethod: string = 'ALIPAY') => {
    return await realDataStore.payOrder(orderId, paymentMethod)
  }

  // 取消订单
  const cancelOrder = async (orderId: string, reason: string) => {
    try {
      // 调用取消订单API
      const response = await fetch(`/api/order/${orderId}/cancel`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ reason })
      })
      
      if (response.ok) {
        ElMessage.success('订单取消成功')
        await getOrders() // 刷新订单列表
        return true
      } else {
        throw new Error('取消失败')
      }
    } catch (error) {
      ElMessage.error('订单取消失败')
      return false
    }
  }

  return {
    loading,
    orders,
    currentOrder,
    statistics,
    getOrders,
    getOrderDetail,
    createOrder,
    payOrder,
    cancelOrder
  }
}

// ========== 物流轨迹Hook ==========
export function useTrackingData() {
  const realDataStore = useRealDataStore()

  const loading = computed(() => realDataStore.trackingLoading)
  const trackingInfo = computed(() => realDataStore.trackingInfo)

  // 获取物流轨迹
  const getTracking = async (orderNumber: string) => {
    return await realDataStore.fetchTracking(orderNumber)
  }

  // 添加轨迹节点
  const addTrackingNode = async (trackingData: any) => {
    return await realDataStore.addTrackingNode(trackingData)
  }

  // 获取实时位置
  const getRealTimeLocation = async (orderNumber: string) => {
    try {
      const response = await fetch(`/api/logistics/location/${orderNumber}`)
      if (response.ok) {
        return await response.json()
      }
      return null
    } catch (error) {
      console.error('获取实时位置失败:', error)
      return null
    }
  }

  return {
    loading,
    trackingInfo,
    getTracking,
    addTrackingNode,
    getRealTimeLocation
  }
}

// ========== 配送任务Hook ==========
export function useDeliveryData() {
  const realDataStore = useRealDataStore()
  const userStore = useUserStore()

  const loading = computed(() => realDataStore.taskLoading)
  const tasks = computed(() => realDataStore.deliveryTasks)

  // 获取配送任务
  const getTasks = async (params: any = {}) => {
    // 根据用户角色过滤
    if (userStore.userInfo?.role === 'COURIER') {
      params.courierId = userStore.userInfo.id
    }
    return await realDataStore.fetchDeliveryTasks(params)
  }

  // 获取配送员任务
  const getCourierTasks = async (courierId?: string, status?: string) => {
    const id = courierId || userStore.userInfo?.id
    if (id) {
      return await realDataStore.fetchCourierTasks(id, status)
    }
    return []
  }

  // 接受任务
  const acceptTask = async (taskId: string) => {
    return await realDataStore.acceptTask(taskId)
  }

  // 开始配送
  const startDelivery = async (taskId: string, data: any) => {
    try {
      const response = await fetch(`/api/delivery/task/${taskId}/start`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })
      
      if (response.ok) {
        ElMessage.success('配送已开始')
        await getTasks() // 刷新任务列表
        return await response.json()
      } else {
        throw new Error('开始配送失败')
      }
    } catch (error) {
      ElMessage.error('开始配送失败')
      throw error
    }
  }

  // 完成配送
  const completeDelivery = async (taskId: string, data: any) => {
    try {
      const response = await fetch(`/api/delivery/task/${taskId}/complete`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })
      
      if (response.ok) {
        ElMessage.success('配送已完成')
        await getTasks() // 刷新任务列表
        return await response.json()
      } else {
        throw new Error('完成配送失败')
      }
    } catch (error) {
      ElMessage.error('完成配送失败')
      throw error
    }
  }

  return {
    loading,
    tasks,
    getTasks,
    getCourierTasks,
    acceptTask,
    startDelivery,
    completeDelivery
  }
}

// ========== 用户数据Hook ==========
export function useUserData() {
  const realDataStore = useRealDataStore()

  const users = computed(() => realDataStore.users)
  const couriers = computed(() => realDataStore.couriers)
  const operators = computed(() => realDataStore.operators)
  const availableCouriers = computed(() => realDataStore.availableCouriers)

  // 获取用户列表
  const getUsers = async (params: any = {}) => {
    return await realDataStore.fetchUsers(params)
  }

  // 获取配送员列表
  const getCouriers = async (params?: any) => {
    return await realDataStore.fetchCouriers(params)
  }

  // 获取操作员列表
  const getOperators = async (params?: any) => {
    return await realDataStore.fetchOperators(params)
  }

  // 更新配送员位置
  const updateCourierLocation = async (courierId: string, locationData: any) => {
    try {
      const response = await fetch(`/api/delivery/courier/${courierId}/location`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(locationData)
      })
      
      if (response.ok) {
        return await response.json()
      } else {
        throw new Error('更新位置失败')
      }
    } catch (error) {
      ElMessage.error('更新位置失败')
      throw error
    }
  }

  return {
    users,
    couriers,
    operators,
    availableCouriers,
    getUsers,
    getCouriers,
    getOperators,
    updateCourierLocation
  }
}

// ========== 网点数据Hook ==========
export function useStationData() {
  const realDataStore = useRealDataStore()

  const stations = computed(() => realDataStore.stations)
  const activeStations = computed(() => realDataStore.activeStations)

  // 获取网点列表
  const getStations = async (params?: any) => {
    return await realDataStore.fetchStations(params)
  }

  // 获取网点详情
  const getStationDetail = async (stationCode: string) => {
    try {
      const response = await fetch(`/api/station/${stationCode}`)
      if (response.ok) {
        return await response.json()
      }
      return null
    } catch (error) {
      console.error('获取网点详情失败:', error)
      return null
    }
  }

  // 获取网点统计
  const getStationStatistics = async (stationCode: string, params?: any) => {
    try {
      const response = await fetch(`/api/station/${stationCode}/statistics?${new URLSearchParams(params)}`)
      if (response.ok) {
        return await response.json()
      }
      return null
    } catch (error) {
      console.error('获取网点统计失败:', error)
      return null
    }
  }

  return {
    stations,
    activeStations,
    getStations,
    getStationDetail,
    getStationStatistics
  }
}

// ========== 地址数据Hook ==========
export function useAddressData() {
  const realDataStore = useRealDataStore()

  const addresses = computed(() => realDataStore.addresses)

  // 获取地址列表
  const getAddresses = async (userId?: string) => {
    return await realDataStore.fetchAddresses(userId)
  }

  // 添加地址
  const addAddress = async (addressData: any) => {
    return await realDataStore.addAddress(addressData)
  }

  // 更新地址
  const updateAddress = async (addressId: string, addressData: any) => {
    try {
      const response = await fetch(`/api/address/${addressId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(addressData)
      })
      
      if (response.ok) {
        ElMessage.success('地址更新成功')
        await getAddresses() // 刷新地址列表
        return await response.json()
      } else {
        throw new Error('更新失败')
      }
    } catch (error) {
      ElMessage.error('地址更新失败')
      throw error
    }
  }

  // 删除地址
  const deleteAddress = async (addressId: string) => {
    try {
      const response = await fetch(`/api/address/${addressId}`, {
        method: 'DELETE'
      })
      
      if (response.ok) {
        ElMessage.success('地址删除成功')
        await getAddresses() // 刷新地址列表
        return true
      } else {
        throw new Error('删除失败')
      }
    } catch (error) {
      ElMessage.error('地址删除失败')
      return false
    }
  }

  return {
    addresses,
    getAddresses,
    addAddress,
    updateAddress,
    deleteAddress
  }
}

// ========== 统计数据Hook ==========
export function useAnalyticsData() {
  const realDataStore = useRealDataStore()

  const dashboardData = computed(() => realDataStore.dashboardData)
  const orderTrends = computed(() => realDataStore.orderTrends)

  // 获取仪表板数据
  const getDashboardData = async (params?: any) => {
    return await realDataStore.fetchDashboardData(params)
  }

  // 获取订单趋势
  const getOrderTrends = async (params: any) => {
    return await realDataStore.fetchOrderTrends(params)
  }

  // 获取配送员绩效
  const getCourierPerformance = async (params?: any) => {
    try {
      const response = await fetch(`/api/analytics/courier-performance?${new URLSearchParams(params)}`)
      if (response.ok) {
        return await response.json()
      }
      return null
    } catch (error) {
      console.error('获取配送员绩效失败:', error)
      return null
    }
  }

  return {
    dashboardData,
    orderTrends,
    getDashboardData,
    getOrderTrends,
    getCourierPerformance
  }
}

// ========== 通用数据初始化Hook ==========
export function useDataInitialization() {
  const realDataStore = useRealDataStore()
  const userStore = useUserStore()

  const isInitialized = ref(false)
  const initLoading = ref(false)

  // 初始化所有数据
  const initializeAllData = async () => {
    if (isInitialized.value || initLoading.value) return

    try {
      initLoading.value = true
      await realDataStore.initializeData()
      isInitialized.value = true
    } catch (error) {
      console.error('数据初始化失败:', error)
      ElMessage.error('数据加载失败，请刷新页面重试')
    } finally {
      initLoading.value = false
    }
  }

  // 监听用户登录状态，自动初始化数据
  watch(() => userStore.isLoggedIn, (isLoggedIn) => {
    if (isLoggedIn && !isInitialized.value) {
      initializeAllData()
    }
  }, { immediate: true })

  onMounted(() => {
    if (userStore.isLoggedIn && !isInitialized.value) {
      initializeAllData()
    }
  })

  return {
    isInitialized,
    initLoading,
    initializeAllData
  }
}
