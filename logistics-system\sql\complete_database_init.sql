-- =============================================
-- 物流系统完整数据库初始化脚本
-- 包含建表语句和基础数据
-- 密码：Admin123456 (BCrypt加密)
-- 执行时间：2024-12-30
-- =============================================

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 1. 用户相关表
-- =============================================

-- 用户表
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` VARCHAR(50) NOT NULL COMMENT '用户名',
  `password` VARCHAR(255) NOT NULL COMMENT '密码',
  `real_name` VARCHAR(50) NOT NULL COMMENT '真实姓名',
  `phone` VARCHAR(20) NOT NULL COMMENT '手机号',
  `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
  `id_card` VARCHAR(20) DEFAULT NULL COMMENT '身份证号',
  `user_type` VARCHAR(20) NOT NULL COMMENT '用户类型：CUSTOMER-普通用户，OPERATOR-操作员，COURIER-配送员，ADMIN-管理员',
  `status` VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '用户状态：ACTIVE-活跃，INACTIVE-非活跃，LOCKED-锁定',
  `avatar` VARCHAR(255) DEFAULT NULL COMMENT '头像URL',
  `last_login_time` TIMESTAMP NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` VARCHAR(50) DEFAULT NULL COMMENT '最后登录IP',
  `gender` TINYINT(1) DEFAULT NULL COMMENT '性别：1-男，2-女',
  `birthday` DATE DEFAULT NULL COMMENT '生日',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`),
  UNIQUE KEY `uk_phone` (`phone`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 角色表
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` VARCHAR(50) NOT NULL COMMENT '角色名称',
  `role_code` VARCHAR(50) NOT NULL COMMENT '角色编码',
  `description` VARCHAR(255) DEFAULT NULL COMMENT '角色描述',
  `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_code` (`role_code`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 用户角色关联表
DROP TABLE IF EXISTS `user_roles`;
CREATE TABLE `user_roles` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` BIGINT(20) NOT NULL COMMENT '用户ID',
  `role_id` BIGINT(20) NOT NULL COMMENT '角色ID',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_role` (`user_id`, `role_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';

-- =============================================
-- 2. 订单相关表
-- =============================================

-- 订单表
DROP TABLE IF EXISTS `orders`;
CREATE TABLE `orders` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_number` VARCHAR(32) NOT NULL COMMENT '订单号',
  `customer_id` BIGINT(20) NOT NULL COMMENT '客户ID',
  `sender_name` VARCHAR(50) NOT NULL COMMENT '寄件人姓名',
  `sender_phone` VARCHAR(20) NOT NULL COMMENT '寄件人电话',
  `sender_address` VARCHAR(255) NOT NULL COMMENT '寄件人地址',
  `receiver_name` VARCHAR(50) NOT NULL COMMENT '收件人姓名',
  `receiver_phone` VARCHAR(20) NOT NULL COMMENT '收件人电话',
  `receiver_address` VARCHAR(255) NOT NULL COMMENT '收件人地址',
  `item_name` VARCHAR(100) NOT NULL COMMENT '物品名称',
  `item_weight` DECIMAL(10,2) NOT NULL COMMENT '物品重量(kg)',
  `item_value` DECIMAL(10,2) DEFAULT NULL COMMENT '物品价值(元)',
  `service_type` VARCHAR(20) NOT NULL COMMENT '服务类型：STANDARD-标准，EXPRESS-快递',
  `total_fee` DECIMAL(10,2) NOT NULL COMMENT '总费用(元)',
  `payment_status` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '支付状态：0-未支付，1-已支付',
  `order_status` VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '订单状态',
  `remarks` TEXT DEFAULT NULL COMMENT '备注',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_number` (`order_number`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- =============================================
-- 3. 物流相关表
-- =============================================

-- 物流网点表
DROP TABLE IF EXISTS `logistics_stations`;
CREATE TABLE `logistics_stations` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '网点ID',
  `station_code` VARCHAR(20) NOT NULL COMMENT '网点编码',
  `station_name` VARCHAR(100) NOT NULL COMMENT '网点名称',
  `station_type` VARCHAR(20) NOT NULL COMMENT '网点类型：PICKUP-揽件点，TRANSIT-中转站，DELIVERY-派送点',
  `province` VARCHAR(50) NOT NULL COMMENT '省份',
  `city` VARCHAR(50) NOT NULL COMMENT '城市',
  `district` VARCHAR(50) NOT NULL COMMENT '区县',
  `detailed_address` VARCHAR(255) NOT NULL COMMENT '详细地址',
  `longitude` DECIMAL(10,6) DEFAULT NULL COMMENT '经度',
  `latitude` DECIMAL(10,6) DEFAULT NULL COMMENT '纬度',
  `contact_person` VARCHAR(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` VARCHAR(20) DEFAULT NULL COMMENT '联系电话',
  `business_hours` VARCHAR(50) DEFAULT NULL COMMENT '营业时间',
  `service_scope` VARCHAR(255) DEFAULT NULL COMMENT '服务范围',
  `capacity` INT(11) DEFAULT NULL COMMENT '处理能力',
  `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态：1-营业，0-停业',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_station_code` (`station_code`),
  KEY `idx_station_type` (`station_type`),
  KEY `idx_city` (`city`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物流网点表';

-- 物流轨迹表
DROP TABLE IF EXISTS `logistics_tracking`;
CREATE TABLE `logistics_tracking` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '轨迹ID',
  `order_number` VARCHAR(32) NOT NULL COMMENT '订单号',
  `order_id` BIGINT(20) NOT NULL COMMENT '订单ID',
  `tracking_status` VARCHAR(20) NOT NULL COMMENT '轨迹状态',
  `description` VARCHAR(255) NOT NULL COMMENT '状态描述',
  `location` VARCHAR(255) DEFAULT NULL COMMENT '位置信息',
  `longitude` DECIMAL(10,6) DEFAULT NULL COMMENT '经度',
  `latitude` DECIMAL(10,6) DEFAULT NULL COMMENT '纬度',
  `operator_name` VARCHAR(50) DEFAULT NULL COMMENT '操作员姓名',
  `operator_type` VARCHAR(20) DEFAULT NULL COMMENT '操作员类型',
  `tracking_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '轨迹时间',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_number` (`order_number`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_tracking_status` (`tracking_status`),
  KEY `idx_tracking_time` (`tracking_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物流轨迹表';

-- =============================================
-- 4. 配送相关表
-- =============================================

-- 配送员表
DROP TABLE IF EXISTS `couriers`;
CREATE TABLE `couriers` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '配送员ID',
  `user_id` BIGINT(20) NOT NULL COMMENT '用户ID',
  `courier_name` VARCHAR(50) NOT NULL COMMENT '配送员姓名',
  `phone` VARCHAR(20) NOT NULL COMMENT '手机号',
  `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
  `id_card` VARCHAR(20) DEFAULT NULL COMMENT '身份证号',
  `vehicle_type` VARCHAR(20) DEFAULT NULL COMMENT '车辆类型',
  `vehicle_number` VARCHAR(20) DEFAULT NULL COMMENT '车牌号',
  `service_area` VARCHAR(255) DEFAULT NULL COMMENT '服务区域',
  `status` VARCHAR(20) NOT NULL DEFAULT 'AVAILABLE' COMMENT '状态：AVAILABLE-可用，BUSY-忙碌，OFFLINE-离线',
  `rating` DECIMAL(3,2) DEFAULT 5.00 COMMENT '评分',
  `total_orders` INT(11) DEFAULT 0 COMMENT '总订单数',
  `current_location` VARCHAR(255) DEFAULT NULL COMMENT '当前位置',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_service_area` (`service_area`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='配送员表';

-- 配送任务表
DROP TABLE IF EXISTS `delivery_tasks`;
CREATE TABLE `delivery_tasks` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `order_number` VARCHAR(32) NOT NULL COMMENT '订单号',
  `order_id` BIGINT(20) NOT NULL COMMENT '订单ID',
  `courier_id` BIGINT(20) DEFAULT NULL COMMENT '配送员ID',
  `task_type` VARCHAR(20) NOT NULL COMMENT '任务类型：PICKUP-揽件，DELIVERY-派送',
  `pickup_address` VARCHAR(255) DEFAULT NULL COMMENT '揽件地址',
  `delivery_address` VARCHAR(255) DEFAULT NULL COMMENT '派送地址',
  `contact_name` VARCHAR(50) NOT NULL COMMENT '联系人姓名',
  `contact_phone` VARCHAR(20) NOT NULL COMMENT '联系人电话',
  `estimated_time` TIMESTAMP DEFAULT NULL COMMENT '预计时间',
  `actual_start_time` TIMESTAMP DEFAULT NULL COMMENT '实际开始时间',
  `actual_end_time` TIMESTAMP DEFAULT NULL COMMENT '实际结束时间',
  `task_status` VARCHAR(20) NOT NULL DEFAULT 'ASSIGNED' COMMENT '任务状态',
  `priority` TINYINT(1) DEFAULT 1 COMMENT '优先级：1-普通，2-紧急，3-特急',
  `remarks` TEXT DEFAULT NULL COMMENT '备注',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_number` (`order_number`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_courier_id` (`courier_id`),
  KEY `idx_task_status` (`task_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='配送任务表';

-- =============================================
-- 5. 地址相关表
-- =============================================

-- 地址表
DROP TABLE IF EXISTS `addresses`;
CREATE TABLE `addresses` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '地址ID',
  `user_id` BIGINT(20) NOT NULL COMMENT '用户ID',
  `contact_name` VARCHAR(50) NOT NULL COMMENT '联系人姓名',
  `contact_phone` VARCHAR(20) NOT NULL COMMENT '联系人电话',
  `province` VARCHAR(50) NOT NULL COMMENT '省份',
  `city` VARCHAR(50) NOT NULL COMMENT '城市',
  `district` VARCHAR(50) NOT NULL COMMENT '区县',
  `detailed_address` VARCHAR(255) NOT NULL COMMENT '详细地址',
  `postal_code` VARCHAR(10) DEFAULT NULL COMMENT '邮政编码',
  `longitude` DECIMAL(10,6) DEFAULT NULL COMMENT '经度',
  `latitude` DECIMAL(10,6) DEFAULT NULL COMMENT '纬度',
  `is_default` TINYINT(1) DEFAULT 0 COMMENT '是否默认地址：1-是，0-否',
  `address_type` VARCHAR(20) DEFAULT 'HOME' COMMENT '地址类型：HOME-家庭，OFFICE-办公室',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_city` (`city`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='地址表';

-- =============================================
-- 6. 邮件模板表（用于notification模块）
-- =============================================

-- 邮件模板表
DROP TABLE IF EXISTS `email_templates`;
CREATE TABLE `email_templates` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `template_code` varchar(50) NOT NULL COMMENT '模板编码',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `subject` varchar(200) NOT NULL COMMENT '邮件主题',
  `content` text NOT NULL COMMENT '邮件内容',
  `template_type` varchar(20) DEFAULT 'BUSINESS' COMMENT '模板类型',
  `is_enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_code` (`template_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邮件模板表';

-- =============================================
-- 7. 系统配置表
-- =============================================

-- 系统配置表
DROP TABLE IF EXISTS `system_config`;
CREATE TABLE `system_config` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` VARCHAR(100) NOT NULL COMMENT '配置键',
  `config_value` TEXT NOT NULL COMMENT '配置值',
  `config_type` VARCHAR(20) DEFAULT 'STRING' COMMENT '配置类型',
  `description` VARCHAR(255) DEFAULT NULL COMMENT '配置描述',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- =============================================
-- 基础数据插入
-- =============================================

-- 1. 插入角色数据
INSERT INTO `roles` (`id`, `role_name`, `role_code`, `description`, `status`, `create_time`, `update_time`) VALUES
(1, '普通用户', 'CUSTOMER', '普通客户用户', 1, NOW(), NOW()),
(2, '操作员', 'OPERATOR', '物流操作员', 1, NOW(), NOW()),
(3, '配送员', 'COURIER', '配送员', 1, NOW(), NOW()),
(4, '管理员', 'ADMIN', '系统管理员', 1, NOW(), NOW());

-- 2. 插入用户数据（密码：Admin123456，BCrypt加密）
INSERT INTO `users` (`username`, `password`, `real_name`, `phone`, `email`, `id_card`, `user_type`, `status`, `gender`, `create_time`, `update_time`) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyZOgM7rmckABg1T2UlJkNNdwQa', '系统管理员', '13800000001', '<EMAIL>', '110101199001011001', 'ADMIN', 'ACTIVE', 1, NOW(), NOW()),
('operator01', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyZOgM7rmckABg1T2UlJkNNdwQa', '张操作员', '13800000101', '<EMAIL>', '110101199001011101', 'OPERATOR', 'ACTIVE', 1, NOW(), NOW()),
('courier01', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyZOgM7rmckABg1T2UlJkNNdwQa', '赵配送员', '13800000201', '<EMAIL>', '110101199001011201', 'COURIER', 'ACTIVE', 1, NOW(), NOW()),
('courier02', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyZOgM7rmckABg1T2UlJkNNdwQa', '李配送员', '13800000202', '<EMAIL>', '110101199001011202', 'COURIER', 'ACTIVE', 1, NOW(), NOW()),
('customer01', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyZOgM7rmckABg1T2UlJkNNdwQa', '张三', '13800000301', '<EMAIL>', '110101199001011301', 'CUSTOMER', 'ACTIVE', 1, NOW(), NOW()),
('customer02', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyZOgM7rmckABg1T2UlJkNNdwQa', '李四', '13800000302', '<EMAIL>', '110101199001011302', 'CUSTOMER', 'ACTIVE', 2, NOW(), NOW());

-- 3. 分配用户角色
INSERT INTO `user_roles` (`user_id`, `role_id`, `create_time`) VALUES
(1, 4, NOW()),  -- admin -> 管理员
(2, 2, NOW()),  -- operator01 -> 操作员
(3, 3, NOW()),  -- courier01 -> 配送员
(4, 3, NOW()),  -- courier02 -> 配送员
(5, 1, NOW()),  -- customer01 -> 普通用户
(6, 1, NOW());  -- customer02 -> 普通用户

-- 4. 插入物流网点数据
INSERT INTO `logistics_stations` (`station_code`, `station_name`, `station_type`, `province`, `city`, `district`, `detailed_address`, `longitude`, `latitude`, `contact_person`, `contact_phone`, `business_hours`, `service_scope`, `capacity`, `status`, `create_time`, `update_time`) VALUES
-- 北京网点
('BJ001', '北京朝阳分拣中心', 'TRANSIT', '北京市', '北京市', '朝阳区', '朝阳区建国路88号', 116.4074, 39.9042, '张经理', '010-12345678', '06:00-22:00', '朝阳区全区', 5000, 1, NOW(), NOW()),
('BJ002', '北京海淀揽件点', 'PICKUP', '北京市', '北京市', '海淀区', '海淀区中关村大街1号', 116.3017, 39.9656, '李主管', '010-87654321', '08:00-20:00', '海淀区中关村地区', 1000, 1, NOW(), NOW()),
('BJ003', '北京丰台派送点', 'DELIVERY', '北京市', '北京市', '丰台区', '丰台区南三环西路16号', 116.2868, 39.8583, '王队长', '010-11223344', '07:00-21:00', '丰台区全区', 800, 1, NOW(), NOW()),

-- 上海网点
('SH001', '上海浦东分拣中心', 'TRANSIT', '上海市', '上海市', '浦东新区', '浦东新区张江高科技园区', 121.6024, 31.2077, '陈经理', '021-12345678', '06:00-22:00', '浦东新区全区', 6000, 1, NOW(), NOW()),
('SH002', '上海徐汇揽件点', 'PICKUP', '上海市', '上海市', '徐汇区', '徐汇区淮海中路999号', 121.4491, 31.2077, '刘主管', '021-87654321', '08:00-20:00', '徐汇区全区', 1200, 1, NOW(), NOW()),
('SH003', '上海静安派送点', 'DELIVERY', '上海市', '上海市', '静安区', '静安区南京西路1788号', 121.4648, 31.2297, '赵队长', '021-11223344', '07:00-21:00', '静安区全区', 900, 1, NOW(), NOW()),

-- 广州网点
('GZ001', '广州天河分拣中心', 'TRANSIT', '广东省', '广州市', '天河区', '天河区珠江新城花城大道85号', 113.3890, 23.1167, '黄经理', '020-12345678', '06:00-22:00', '天河区全区', 5500, 1, NOW(), NOW()),
('GZ002', '广州越秀揽件点', 'PICKUP', '广东省', '广州市', '越秀区', '越秀区中山五路68号', 113.2644, 23.1291, '林主管', '020-87654321', '08:00-20:00', '越秀区全区', 1100, 1, NOW(), NOW()),

-- 深圳网点
('SZ001', '深圳南山分拣中心', 'TRANSIT', '广东省', '深圳市', '南山区', '南山区科技园南区', 113.9547, 22.5311, '郑经理', '0755-12345678', '06:00-22:00', '南山区全区', 4800, 1, NOW(), NOW()),
('SZ002', '深圳福田揽件点', 'PICKUP', '广东省', '深圳市', '福田区', '福田区华强北路1002号', 114.0579, 22.5455, '何主管', '0755-87654321', '08:00-20:00', '福田区全区', 1000, 1, NOW(), NOW());

-- 5. 插入配送员详细信息
INSERT INTO `couriers` (`user_id`, `courier_name`, `phone`, `email`, `id_card`, `vehicle_type`, `vehicle_number`, `service_area`, `status`, `rating`, `total_orders`, `current_location`, `create_time`, `update_time`) VALUES
-- 北京配送员
(3, '赵配送员', '13800000201', '<EMAIL>', '110101199001011201', 'ELECTRIC_BIKE', '京A12345', '北京市朝阳区', 'AVAILABLE', 4.8, 156, '北京市朝阳区建国路88号', NOW(), NOW()),
(4, '李配送员', '13800000202', '<EMAIL>', '110101199001011202', 'MOTORCYCLE', '京B67890', '北京市海淀区', 'AVAILABLE', 4.9, 203, '北京市海淀区中关村大街1号', NOW(), NOW());

-- 6. 插入用户地址数据
INSERT INTO `addresses` (`user_id`, `contact_name`, `contact_phone`, `province`, `city`, `district`, `detailed_address`, `postal_code`, `longitude`, `latitude`, `is_default`, `address_type`, `create_time`, `update_time`) VALUES
-- 张三的地址
(5, '张三', '13800000301', '北京市', '北京市', '朝阳区', '朝阳区建国路99号国贸大厦A座1001室', '100020', 116.4074, 39.9042, 1, 'HOME', NOW(), NOW()),
(5, '张三', '13800000301', '北京市', '北京市', '海淀区', '海淀区中关村大街27号中关村大厦B座808室', '100080', 116.3017, 39.9656, 0, 'OFFICE', NOW(), NOW()),

-- 李四的地址
(6, '李四', '13800000302', '上海市', '上海市', '浦东新区', '浦东新区陆家嘴环路1000号恒生银行大厦20楼', '200120', 121.6024, 31.2077, 1, 'HOME', NOW(), NOW()),
(6, '李四', '13800000302', '上海市', '上海市', '徐汇区', '徐汇区淮海中路1045号淮海国际广场15楼', '200030', 121.4491, 31.2077, 0, 'OFFICE', NOW(), NOW());

-- 7. 插入邮件模板数据
INSERT INTO `email_templates` (`template_code`, `template_name`, `subject`, `content`, `template_type`, `is_enabled`, `create_time`, `update_time`) VALUES
('ORDER_CREATED', '订单创建通知', '【智慧物流】订单创建成功 - {{orderNumber}}',
'<!DOCTYPE html>
<html>
<head><meta charset="UTF-8"><title>订单创建成功</title></head>
<body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #1890ff, #722ed1); color: white; padding: 20px; border-radius: 8px 8px 0 0;">
        <h1 style="margin: 0; font-size: 24px;">📦 订单创建成功</h1>
        <p style="margin: 5px 0 0 0; opacity: 0.9;">您的包裹即将开始配送之旅</p>
    </div>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px;">
        <h2 style="color: #2c3e50; margin-top: 0;">尊敬的 {{customerName}}，您好！</h2>
        <p>您的订单已成功创建，详情如下：</p>
        <table style="width: 100%; border-collapse: collapse; margin: 15px 0; background: white; border-radius: 6px; overflow: hidden;">
            <tr><td style="padding: 12px; background: #e9ecef; font-weight: bold;">订单号</td><td style="padding: 12px;">{{orderNumber}}</td></tr>
            <tr><td style="padding: 12px; background: #e9ecef; font-weight: bold;">订单金额</td><td style="padding: 12px; color: #e74c3c; font-weight: bold;">¥{{totalFee}}</td></tr>
            <tr><td style="padding: 12px; background: #e9ecef; font-weight: bold;">创建时间</td><td style="padding: 12px;">{{createTime}}</td></tr>
        </table>
        <p style="color: #34495e;">请及时完成支付，我们将在24小时内安排揽件员上门取件。</p>
    </div>
    <div style="text-align: center; margin-top: 20px; color: #95a5a6; font-size: 12px;">
        <p>此邮件由系统自动发送，请勿回复 | 智慧物流系统 © 2024</p>
    </div>
</body>
</html>', 'BUSINESS', 1, NOW(), NOW()),

('PAYMENT_SUCCESS', '支付成功通知', '【智慧物流】支付成功，订单处理中 - {{orderNumber}}',
'<!DOCTYPE html>
<html>
<head><meta charset="UTF-8"><title>支付成功</title></head>
<body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #52c41a, #389e0d); color: white; padding: 20px; border-radius: 8px 8px 0 0;">
        <h1 style="margin: 0; font-size: 24px;">✅ 支付成功</h1>
        <p style="margin: 5px 0 0 0; opacity: 0.9;">您的订单支付已完成，正在安排揽件</p>
    </div>
    <div style="background: #f6ffed; padding: 20px; border-radius: 0 0 8px 8px; border: 1px solid #b7eb8f;">
        <h2 style="color: #2c3e50; margin-top: 0;">尊敬的 {{customerName}}，您好！</h2>
        <p>您的订单支付已成功完成，我们将尽快为您安排揽件服务。</p>
        <table style="width: 100%; border-collapse: collapse; margin: 15px 0; background: white; border-radius: 6px; overflow: hidden;">
            <tr><td style="padding: 12px; background: #e9ecef; font-weight: bold;">订单号</td><td style="padding: 12px;">{{orderNumber}}</td></tr>
            <tr><td style="padding: 12px; background: #e9ecef; font-weight: bold;">支付金额</td><td style="padding: 12px; color: #52c41a; font-weight: bold;">¥{{paymentAmount}}</td></tr>
            <tr><td style="padding: 12px; background: #e9ecef; font-weight: bold;">支付时间</td><td style="padding: 12px;">{{paymentTime}}</td></tr>
        </table>
        <p style="color: #34495e;">我们将在24小时内安排揽件员上门取件，请保持电话畅通。</p>
    </div>
    <div style="text-align: center; margin-top: 20px; color: #95a5a6; font-size: 12px;">
        <p>此邮件由系统自动发送，请勿回复 | 智慧物流系统 © 2024</p>
    </div>
</body>
</html>', 'BUSINESS', 1, NOW(), NOW()),

('ORDER_STATUS_UPDATE', '订单状态更新通知', '【智慧物流】订单状态更新 - {{orderNumber}}',
'<!DOCTYPE html>
<html>
<head><meta charset="UTF-8"><title>订单状态更新</title></head>
<body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #722ed1, #1890ff); color: white; padding: 20px; border-radius: 8px 8px 0 0;">
        <h1 style="margin: 0; font-size: 24px;">🚚 订单状态更新</h1>
        <p style="margin: 5px 0 0 0; opacity: 0.9;">您的包裹状态已更新</p>
    </div>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px;">
        <h2 style="color: #2c3e50; margin-top: 0;">尊敬的 {{customerName}}，您好！</h2>
        <p>您的订单状态已更新：</p>
        <div style="background: white; padding: 15px; border-radius: 6px; margin: 15px 0; border-left: 4px solid #722ed1;">
            <h3 style="margin: 0 0 10px 0; color: #722ed1;">{{statusText}}</h3>
            <p style="margin: 0; color: #666;">{{description}}</p>
            <p style="margin: 10px 0 0 0; font-size: 14px; color: #999;">更新时间：{{updateTime}}</p>
        </div>
    </div>
    <div style="text-align: center; margin-top: 20px; color: #95a5a6; font-size: 12px;">
        <p>此邮件由系统自动发送，请勿回复 | 智慧物流系统 © 2024</p>
    </div>
</body>
</html>', 'BUSINESS', 1, NOW(), NOW()),

('TEST', '测试邮件模板', '{{subject}}',
'<!DOCTYPE html>
<html>
<head><meta charset="UTF-8"><title>测试邮件</title></head>
<body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: #1890ff; color: white; padding: 20px; border-radius: 8px 8px 0 0;">
        <h1 style="margin: 0; font-size: 24px;">🧪 测试邮件</h1>
    </div>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px;">
        <p>{{content}}</p>
        <p style="color: #666; font-size: 14px;">测试时间：{{testTime}}</p>
    </div>
</body>
</html>', 'SYSTEM', 1, NOW(), NOW());

-- 8. 插入示例订单数据
INSERT INTO `orders` (`order_number`, `customer_id`, `sender_name`, `sender_phone`, `sender_address`, `receiver_name`, `receiver_phone`, `receiver_address`, `item_name`, `item_weight`, `item_value`, `service_type`, `total_fee`, `payment_status`, `order_status`, `create_time`, `update_time`) VALUES
('LO202412300001', 5, '张三', '13800000301', '北京市朝阳区建国路99号国贸大厦A座1001室', '李四', '13800000302', '上海市浦东新区陆家嘴环路1000号恒生银行大厦20楼', '重要文件', 0.5, 1000.00, 'EXPRESS', 25.00, 1, 'PAID', NOW() - INTERVAL 2 HOUR, NOW()),
('LO202412300002', 6, '李四', '13800000302', '上海市徐汇区淮海中路1045号淮海国际广场15楼', '张三', '13800000301', '北京市海淀区中关村大街27号中关村大厦B座808室', '电子产品', 2.5, 5000.00, 'STANDARD', 18.00, 1, 'PICKUP_ASSIGNED', NOW() - INTERVAL 1 HOUR, NOW()),
('LO202412300003', 5, '张三', '13800000301', '北京市朝阳区建国路99号国贸大厦A座1001室', '李四', '13800000302', '上海市浦东新区陆家嘴环路1000号恒生银行大厦20楼', '书籍资料', 1.2, 200.00, 'STANDARD', 12.00, 0, 'PENDING', NOW() - INTERVAL 30 MINUTE, NOW());

-- 9. 插入系统配置数据
INSERT INTO `system_config` (`config_key`, `config_value`, `config_type`, `description`, `create_time`, `update_time`) VALUES
('email.enabled', 'true', 'BOOLEAN', '是否启用邮件通知', NOW(), NOW()),
('email.from.address', '<EMAIL>', 'STRING', '邮件发送地址', NOW(), NOW()),
('email.from.name', '智慧物流系统', 'STRING', '邮件发送者名称', NOW(), NOW()),
('sms.enabled', 'true', 'BOOLEAN', '是否启用短信通知', NOW(), NOW()),
('map.api.key', 'YOUR_AMAP_KEY', 'STRING', '高德地图API密钥', NOW(), NOW()),
('delivery.timeout.hours', '24', 'NUMBER', '配送超时时间（小时）', NOW(), NOW()),
('order.auto.cancel.hours', '72', 'NUMBER', '订单自动取消时间（小时）', NOW(), NOW());

-- 查询插入结果
SELECT '=== 用户数据 ===' as info;
SELECT username, real_name, user_type, phone, email FROM users;

SELECT '=== 网点数据 ===' as info;
SELECT station_code, station_name, station_type, city, status FROM logistics_stations;

SELECT '=== 配送员数据 ===' as info;
SELECT c.courier_name, c.phone, c.vehicle_type, c.service_area, c.status FROM couriers c;

SELECT '=== 订单数据 ===' as info;
SELECT order_number, sender_name, receiver_name, order_status, total_fee FROM orders;

SELECT '=== 邮件模板数据 ===' as info;
SELECT template_code, template_name, is_enabled FROM email_templates;

SET FOREIGN_KEY_CHECKS = 1;
