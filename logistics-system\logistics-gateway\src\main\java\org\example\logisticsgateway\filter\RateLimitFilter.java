package org.example.logisticsgateway.filter;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 分布式限流过滤器
 * 基于Redis实现滑动窗口限流算法
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Slf4j
@Component
public class RateLimitFilter implements GlobalFilter, Ordered {

    @Autowired
    private ReactiveRedisTemplate<String, String> redisTemplate;

    private RedisScript<Long> rateLimitScript;

    /**
     * 默认限流配置
     */
    private static final int DEFAULT_REQUESTS_PER_SECOND = 100;
    private static final int DEFAULT_WINDOW_SIZE = 60; // 窗口大小（秒）

    /**
     * 特殊路径限流配置
     */
    private final Map<String, RateLimitConfig> specialLimits = new HashMap<>();

    @PostConstruct
    public void init() {
        // 初始化Lua脚本
        initRateLimitScript();
        
        // 初始化特殊路径限流配置
        initSpecialLimits();
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getURI().getPath();
        String clientIp = getClientIp(request);
        String method = request.getMethod().toString();
        
        // 构建限流键
        String rateLimitKey = buildRateLimitKey(clientIp, path, method);
        
        // 获取限流配置
        RateLimitConfig config = getRateLimitConfig(path);
        
        // 执行限流检查
        return checkRateLimit(rateLimitKey, config)
                .flatMap(allowed -> {
                    if (allowed) {
                        log.debug("限流检查通过 - Key: {}, 配置: {}", rateLimitKey, config);
                        return chain.filter(exchange);
                    } else {
                        log.warn("触发限流 - Key: {}, 配置: {}", rateLimitKey, config);
                        return rateLimitResponse(exchange, "请求过于频繁，请稍后再试");
                    }
                })
                .onErrorResume(ex -> {
                    log.error("限流检查异常: {}", ex.getMessage());
                    // 限流服务异常时，默认放行
                    return chain.filter(exchange);
                });
    }

    /**
     * 初始化限流Lua脚本
     */
    private void initRateLimitScript() {
        String script = 
            "local key = KEYS[1]\n" +
            "local window = tonumber(ARGV[1])\n" +
            "local limit = tonumber(ARGV[2])\n" +
            "local current = tonumber(ARGV[3])\n" +
            "\n" +
            "-- 清除过期数据\n" +
            "redis.call('zremrangebyscore', key, '-inf', current - window * 1000)\n" +
            "\n" +
            "-- 获取当前窗口内的请求数\n" +
            "local count = redis.call('zcard', key)\n" +
            "\n" +
            "if count < limit then\n" +
            "    -- 添加当前请求\n" +
            "    redis.call('zadd', key, current, current)\n" +
            "    -- 设置过期时间\n" +
            "    redis.call('expire', key, window + 1)\n" +
            "    return 1\n" +
            "else\n" +
            "    return 0\n" +
            "end";

        this.rateLimitScript = RedisScript.of(script, Long.class);
    }

    /**
     * 初始化特殊路径限流配置
     */
    private void initSpecialLimits() {
        // 登录接口更严格的限流
        specialLimits.put("/api/user/login", new RateLimitConfig(10, 60));
        // 注册接口限流
        specialLimits.put("/api/user/register", new RateLimitConfig(5, 60));
        // 发送验证码接口限流
        specialLimits.put("/api/user/sendCode", new RateLimitConfig(3, 60));
        // 订单创建接口限流
        specialLimits.put("/api/order/create", new RateLimitConfig(20, 60));
        // 文件上传接口限流
        specialLimits.put("/api/*/upload", new RateLimitConfig(10, 60));
    }

    /**
     * 执行限流检查
     */
    private Mono<Boolean> checkRateLimit(String key, RateLimitConfig config) {
        long currentTimeMillis = System.currentTimeMillis();
        
        return redisTemplate.execute(
                rateLimitScript,
                Arrays.asList(key),
                Arrays.asList(
                    String.valueOf(config.getWindowSize()),
                    String.valueOf(config.getLimit()),
                    String.valueOf(currentTimeMillis)
                )
        ).single().map(result -> result != null && result > 0);
    }

    /**
     * 获取限流配置
     */
    private RateLimitConfig getRateLimitConfig(String path) {
        // 检查特殊路径配置
        for (Map.Entry<String, RateLimitConfig> entry : specialLimits.entrySet()) {
            String pattern = entry.getKey();
            if (pathMatches(path, pattern)) {
                return entry.getValue();
            }
        }
        
        // 返回默认配置
        return new RateLimitConfig(DEFAULT_REQUESTS_PER_SECOND, DEFAULT_WINDOW_SIZE);
    }

    /**
     * 路径匹配检查
     */
    private boolean pathMatches(String path, String pattern) {
        if (pattern.contains("*")) {
            // 简单的通配符匹配
            String regex = pattern.replace("*", ".*");
            return path.matches(regex);
        }
        return path.equals(pattern);
    }

    /**
     * 构建限流键
     */
    private String buildRateLimitKey(String clientIp, String path, String method) {
        // 格式: rate_limit:ip:path:method
        return String.format("rate_limit:%s:%s:%s", 
                clientIp, 
                path.replaceAll("/", "_"), 
                method);
    }

    /**
     * 获取客户端IP
     */
    private String getClientIp(ServerHttpRequest request) {
        String xForwardedFor = request.getHeaders().getFirst("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIP = request.getHeaders().getFirst("X-Real-IP");
        if (xRealIP != null && !xRealIP.isEmpty()) {
            return xRealIP;
        }

        return request.getRemoteAddress() != null ?
                request.getRemoteAddress().getAddress().getHostAddress() : "unknown";
    }

    /**
     * 返回限流响应
     */
    private Mono<Void> rateLimitResponse(ServerWebExchange exchange, String message) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.TOO_MANY_REQUESTS);
        response.getHeaders().add("Content-Type", MediaType.APPLICATION_JSON_VALUE);
        response.getHeaders().add("X-Rate-Limit-Retry-After", "60");

        Map<String, Object> result = new HashMap<>();
        result.put("code", 429);
        result.put("message", message);
        result.put("data", null);
        result.put("timestamp", System.currentTimeMillis());
        result.put("path", exchange.getRequest().getURI().getPath());

        String body = JSON.toJSONString(result);
        DataBuffer buffer = response.bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8));

        return response.writeWith(Mono.just(buffer));
    }

    @Override
    public int getOrder() {
        return -90; // 在认证过滤器之后执行
    }

    /**
     * 限流配置类
     */
    private static class RateLimitConfig {
        private final int limit;        // 限制数量
        private final int windowSize;   // 时间窗口大小（秒）

        public RateLimitConfig(int limit, int windowSize) {
            this.limit = limit;
            this.windowSize = windowSize;
        }

        public int getLimit() {
            return limit;
        }

        public int getWindowSize() {
            return windowSize;
        }

        @Override
        public String toString() {
            return String.format("RateLimitConfig{limit=%d, windowSize=%d}", limit, windowSize);
        }
    }
} 