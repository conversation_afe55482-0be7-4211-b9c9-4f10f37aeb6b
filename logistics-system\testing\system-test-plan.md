# 🧪 **物流系统完整测试计划**

## 📋 **测试概述**

本测试计划涵盖物流系统的完整业务流程，确保所有功能模块正常运行，数据流转正确，用户体验良好。

## 🎯 **测试目标**

- ✅ 验证完整的订单生命周期
- ✅ 确保邮件通知系统正常工作
- ✅ 验证前后端数据交互
- ✅ 测试地点选择功能
- ✅ 确保系统性能和稳定性

## 🔧 **测试环境准备**

### **1. 数据库准备**
```bash
# 执行基础数据初始化
mysql -u root -p logistics < sql/init_base_data.sql

# 验证数据插入
mysql -u root -p logistics -e "
SELECT 'Stations' as Type, COUNT(*) as Count FROM logistics_stations
UNION ALL
SELECT 'Users', COUNT(*) FROM users
UNION ALL
SELECT 'Orders', COUNT(*) FROM orders
UNION ALL
SELECT 'Addresses', COUNT(*) FROM addresses;
"
```

### **2. 服务启动检查**
```bash
# 检查所有微服务状态
curl http://localhost:8081/actuator/health  # order service
curl http://localhost:8082/actuator/health  # delivery service
curl http://localhost:8083/actuator/health  # logistics service
curl http://localhost:8084/actuator/health  # notification service
curl http://localhost:8085/actuator/health  # user service
curl http://localhost:8086/actuator/health  # payment service
curl http://localhost:8087/actuator/health  # email service (new)
```

### **3. 邮件服务配置验证**
```bash
# 测试邮件服务连接
curl -X POST http://localhost:8087/email/test \
  -H "Content-Type: application/json" \
  -d '{"to": "<EMAIL>", "subject": "Test", "content": "Test email"}'
```

## 🧪 **功能测试用例**

### **测试用例 1: 完整订单流程**

#### **1.1 用户注册和登录**
```javascript
// 测试数据
const testUser = {
  username: "test_customer_001",
  password: "123456",
  email: "<EMAIL>",
  phone: "13900000001",
  realName: "测试用户"
}

// 预期结果
- 注册成功，返回用户ID
- 登录成功，获得JWT token
- 用户信息正确存储
```

#### **1.2 创建订单**
```javascript
// 测试数据
const orderData = {
  senderName: "张三",
  senderPhone: "13900001001",
  senderAddress: "北京市朝阳区建国路99号国贸大厦A座1001室",
  receiverName: "李四",
  receiverPhone: "13900001002", 
  receiverAddress: "上海市浦东新区陆家嘴环路1000号恒生银行大厦20楼",
  itemName: "重要文件",
  itemWeight: 0.5,
  itemValue: 1000,
  serviceType: "EXPRESS"
}

// 预期结果
- 订单创建成功，生成订单号
- 运费计算正确
- 发送订单创建邮件
- 订单状态为 PENDING
```

#### **1.3 订单支付**
```javascript
// 测试数据
const paymentData = {
  orderNumber: "LO202412300001",
  paymentMethod: "ALIPAY",
  paymentAmount: 25.00
}

// 预期结果
- 支付成功，订单状态变为 PAID
- 发送支付成功邮件
- 物流轨迹添加支付记录
```

#### **1.4 分配揽件员**
```javascript
// 测试数据
const assignData = {
  orderId: 1,
  courierId: 1,
  operatorName: "张操作员"
}

// 预期结果
- 订单状态变为 PICKUP_ASSIGNED
- 创建配送任务
- 发送配送员任务邮件
- 物流轨迹添加分配记录
```

#### **1.5 揽件完成**
```javascript
// 测试数据
const pickupData = {
  taskId: 1,
  location: "北京市朝阳区建国路99号",
  longitude: 116.4074,
  latitude: 39.9042,
  remarks: "包裹已安全揽收"
}

// 预期结果
- 订单状态变为 PICKUP
- 任务状态变为 COMPLETED
- 物流轨迹添加揽件记录
- 发送揽件成功邮件
```

### **测试用例 2: 邮件通知系统**

#### **2.1 邮件模板测试**
```bash
# 测试所有邮件模板
curl -X POST http://localhost:8087/email/template/test \
  -H "Content-Type: application/json" \
  -d '{
    "templateCode": "ORDER_CREATED",
    "variables": {
      "customerName": "张三",
      "orderNumber": "LO202412300001",
      "totalFee": "25.00"
    },
    "to": "<EMAIL>"
  }'
```

#### **2.2 邮件发送性能测试**
```bash
# 批量发送测试
for i in {1..10}; do
  curl -X POST http://localhost:8087/email/send \
    -H "Content-Type: application/json" \
    -d "{\"to\": \"test$<EMAIL>\", \"subject\": \"Test $i\"}"
done
```

### **测试用例 3: 地点选择功能**

#### **3.1 GPS定位测试**
```javascript
// 模拟GPS定位
const gpsData = {
  longitude: 116.4074,
  latitude: 39.9042
}

// 预期结果
- 正确解析地址信息
- 返回附近地标
- 缓存位置信息
```

#### **3.2 地点搜索测试**
```javascript
// 搜索测试
const searchData = {
  keyword: "国贸大厦",
  city: "北京市",
  limit: 10
}

// 预期结果
- 返回相关地点列表
- 按距离排序
- 包含详细地址信息
```

#### **3.3 常用地点管理**
```javascript
// 添加常用地点
const frequentLocation = {
  locationName: "公司",
  address: "北京市朝阳区建国路99号",
  longitude: 116.4074,
  latitude: 39.9042,
  locationType: "OFFICE"
}

// 预期结果
- 成功保存到数据库
- Redis缓存更新
- 用户地点列表更新
```

### **测试用例 4: 数据一致性测试**

#### **4.1 前后端数据同步**
```javascript
// 前端API调用测试
const apiTests = [
  { api: '/order/list', method: 'GET' },
  { api: '/delivery/tasks', method: 'GET' },
  { api: '/logistics/tracking/LO202412300001', method: 'GET' },
  { api: '/user/couriers', method: 'GET' },
  { api: '/station/list', method: 'GET' }
]

// 预期结果
- 所有API返回真实数据
- 数据结构与前端类型定义一致
- 无模拟数据残留
```

#### **4.2 缓存一致性测试**
```bash
# Redis缓存测试
redis-cli KEYS "logistics:*"
redis-cli GET "logistics:user:1:addresses"
redis-cli GET "logistics:frequent:locations:1"

# 预期结果
- 缓存数据与数据库一致
- 缓存过期时间正确
- 缓存更新及时
```

## 📊 **性能测试**

### **负载测试**
```bash
# 使用Apache Bench进行负载测试
ab -n 1000 -c 10 http://localhost:8081/order/list
ab -n 500 -c 5 http://localhost:8083/logistics/tracking/LO202412300001
ab -n 200 -c 2 http://localhost:8087/email/send
```

### **数据库性能测试**
```sql
-- 查询性能测试
EXPLAIN SELECT * FROM orders WHERE order_status = 'PAID';
EXPLAIN SELECT * FROM logistics_tracking WHERE order_number = 'LO202412300001';
EXPLAIN SELECT * FROM delivery_tasks WHERE courier_id = 1;

-- 索引优化验证
SHOW INDEX FROM orders;
SHOW INDEX FROM logistics_tracking;
```

## 🔍 **集成测试场景**

### **场景 1: 完整业务流程**
```
用户下单 → 支付 → 分配揽件员 → 揽件 → 网点分拣 → 
发车运输 → 到达目的地 → 分配派送员 → 配送 → 签收
```

### **场景 2: 异常处理流程**
```
订单创建 → 支付失败 → 重新支付 → 揽件异常 → 
异常处理 → 重新分配 → 正常配送 → 完成
```

### **场景 3: 并发操作测试**
```
多用户同时下单 → 多配送员同时接单 → 
多操作员同时分配任务 → 系统稳定性验证
```

## ✅ **验收标准**

### **功能验收**
- [ ] 所有API接口正常响应
- [ ] 邮件通知及时发送
- [ ] 订单状态流转正确
- [ ] 地点选择功能完整
- [ ] 数据一致性良好

### **性能验收**
- [ ] 页面响应时间 < 2秒
- [ ] API响应时间 < 500ms
- [ ] 邮件发送成功率 > 95%
- [ ] 系统并发支持 > 100用户

### **用户体验验收**
- [ ] 界面美观专业
- [ ] 操作流程简单
- [ ] 错误提示清晰
- [ ] 响应式设计良好

## 🐛 **缺陷管理**

### **缺陷分类**
- **严重缺陷**: 影响核心业务流程
- **一般缺陷**: 影响用户体验
- **轻微缺陷**: 界面显示问题

### **缺陷跟踪**
```
缺陷ID | 描述 | 严重程度 | 状态 | 负责人 | 修复时间
BUG001 | 邮件发送失败 | 严重 | 已修复 | 开发者 | 2024-12-30
BUG002 | 地图加载慢 | 一般 | 处理中 | 开发者 | 2024-12-30
```

## 📈 **测试报告模板**

### **测试执行总结**
- 测试用例总数: XX
- 通过用例数: XX
- 失败用例数: XX
- 通过率: XX%

### **性能测试结果**
- 平均响应时间: XXms
- 最大并发用户: XX
- 系统稳定性: XX%

### **建议和改进**
1. 优化数据库查询性能
2. 增加缓存策略
3. 完善错误处理机制
4. 提升用户体验

## 🎯 **测试完成标准**

系统测试完成需要满足以下条件：
- ✅ 所有核心功能测试通过
- ✅ 性能指标达到要求
- ✅ 无严重缺陷
- ✅ 用户验收通过
- ✅ 文档完整齐全

测试完成后，系统即可投入生产使用！
