<template>
  <div class="message-center">
    <div class="page-header">
      <h1>消息中心</h1>
      <div class="header-actions">
        <el-button @click="markAllAsRead" :disabled="unreadCount === 0">
          <el-icon><Check /></el-icon>
          全部已读
        </el-button>
        <el-button @click="showSettings = true">
          <el-icon><Setting /></el-icon>
          通知设置
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon total">
                <el-icon size="24"><Bell /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ totalCount }}</div>
                <div class="stat-label">总消息</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon unread">
                <el-icon size="24"><Warning /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ unreadCount }}</div>
                <div class="stat-label">未读消息</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon today">
                <el-icon size="24"><Calendar /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ todayCount }}</div>
                <div class="stat-label">今日消息</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon urgent">
                <el-icon size="24"><AlarmClock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ urgentCount }}</div>
                <div class="stat-label">紧急消息</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 消息内容 -->
    <el-card class="message-content">
      <div class="content-header">
        <div class="filter-section">
          <el-tabs v-model="activeTab" @tab-change="handleTabChange">
            <el-tab-pane label="全部消息" name="all" />
            <el-tab-pane label="未读消息" name="unread" />
            <el-tab-pane label="系统通知" name="system" />
            <el-tab-pane label="订单消息" name="order" />
            <el-tab-pane label="配送消息" name="delivery" />
          </el-tabs>
        </div>
        
        <div class="search-section">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索消息..."
            prefix-icon="Search"
            clearable
            style="width: 300px"
            @input="handleSearch"
          />
          <el-select v-model="priorityFilter" placeholder="优先级" clearable style="width: 120px; margin-left: 10px">
            <el-option label="紧急" value="urgent" />
            <el-option label="高" value="high" />
            <el-option label="中" value="medium" />
            <el-option label="低" value="low" />
          </el-select>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px; margin-left: 10px"
            @change="handleSearch"
          />
        </div>
      </div>

      <!-- 消息列表 -->
      <div class="message-list">
        <div class="list-header">
          <el-checkbox 
            v-model="selectAll" 
            :indeterminate="isIndeterminate"
            @change="handleSelectAll"
          >
            全选
          </el-checkbox>
          <div class="batch-actions" v-if="selectedMessages.length > 0">
            <el-button size="small" @click="batchMarkAsRead">
              标记已读 ({{ selectedMessages.length }})
            </el-button>
            <el-button size="small" type="danger" @click="batchDelete">
              删除 ({{ selectedMessages.length }})
            </el-button>
          </div>
          <div class="sort-section">
            <el-select v-model="sortBy" size="small" style="width: 120px">
              <el-option label="时间排序" value="time" />
              <el-option label="优先级" value="priority" />
              <el-option label="状态" value="status" />
            </el-select>
            <el-button 
              size="small" 
              @click="sortOrder = sortOrder === 'asc' ? 'desc' : 'asc'"
            >
              <el-icon>
                <Sort v-if="sortOrder === 'asc'" />
                <SortDown v-else />
              </el-icon>
            </el-button>
          </div>
        </div>

        <div class="messages" v-loading="loading">
          <div
            v-for="message in paginatedMessages"
            :key="message.id"
            class="message-item"
            :class="{
              'unread': !message.read,
              'selected': selectedMessages.includes(message.id),
              [`priority-${message.priority}`]: true
            }"
            @click="handleMessageClick(message)"
          >
            <div class="message-checkbox">
              <el-checkbox 
                :model-value="selectedMessages.includes(message.id)"
                @change="handleMessageSelect(message.id, $event)"
                @click.stop
              />
            </div>
            
            <div class="message-icon">
              <el-icon 
                :size="20" 
                :color="getMessageIconColor(message.type)"
              >
                <component :is="getMessageIcon(message.type)" />
              </el-icon>
            </div>
            
            <div class="message-content">
              <div class="message-header">
                <h4 class="message-title">{{ message.title }}</h4>
                <div class="message-meta">
                  <el-tag :type="getPriorityType(message.priority)" size="small">
                    {{ getPriorityText(message.priority) }}
                  </el-tag>
                  <el-tag size="small" style="margin-left: 8px">
                    {{ getCategoryText(message.category) }}
                  </el-tag>
                  <span class="message-time">{{ formatTime(message.timestamp) }}</span>
                </div>
              </div>
              <div class="message-body">
                <p class="message-text">{{ message.message }}</p>
                <div class="message-actions" v-if="message.data">
                  <el-button 
                    v-if="message.category === 'order'"
                    size="small" 
                    type="primary"
                    @click.stop="viewOrder(message.data.orderNumber)"
                  >
                    查看订单
                  </el-button>
                  <el-button 
                    v-if="message.category === 'delivery'"
                    size="small" 
                    type="success"
                    @click.stop="trackDelivery(message.data.trackingNumber)"
                  >
                    跟踪配送
                  </el-button>
                </div>
              </div>
            </div>
            
            <div class="message-status">
              <el-tag v-if="!message.read" type="warning" size="small">未读</el-tag>
              <el-button 
                text 
                size="small" 
                @click.stop="toggleMessageRead(message)"
              >
                {{ message.read ? '标记未读' : '标记已读' }}
              </el-button>
              <el-button 
                text 
                size="small" 
                type="danger"
                @click.stop="deleteMessage(message.id)"
              >
                删除
              </el-button>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredMessages.length === 0 && !loading" class="empty-state">
          <el-icon size="64" color="#ccc"><ChatDotSquare /></el-icon>
          <h3>暂无消息</h3>
          <p>当前筛选条件下没有找到消息</p>
        </div>

        <!-- 分页 -->
        <div class="pagination-container" v-if="filteredMessages.length > 0">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="filteredMessages.length"
            layout="total, sizes, prev, pager, next, jumper"
          />
        </div>
      </div>
    </el-card>

    <!-- 通知设置对话框 -->
    <el-dialog
      v-model="showSettings"
      title="通知设置"
      width="600px"
    >
      <el-form :model="settingsForm" label-width="120px">
        <el-form-item label="应用内通知">
          <el-switch v-model="settingsForm.enableInApp" />
          <div class="setting-desc">在应用内显示弹窗通知</div>
        </el-form-item>
        
        <el-form-item label="桌面通知">
          <el-switch v-model="settingsForm.enableDesktop" />
          <div class="setting-desc">显示系统桌面通知</div>
        </el-form-item>
        
        <el-form-item label="声音提醒">
          <el-switch v-model="settingsForm.enableSound" />
          <div class="setting-desc">播放通知提示音</div>
        </el-form-item>
        
        <el-form-item label="邮件通知">
          <el-switch v-model="settingsForm.enableEmail" />
          <div class="setting-desc">重要消息发送邮件提醒</div>
        </el-form-item>
        
        <el-form-item label="短信通知">
          <el-switch v-model="settingsForm.enableSMS" />
          <div class="setting-desc">紧急消息发送短信提醒</div>
        </el-form-item>
        
        <el-form-item label="免打扰时间">
          <el-time-picker
            v-model="settingsForm.quietHours"
            is-range
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="HH:mm"
            value-format="HH:mm"
          />
          <div class="setting-desc">在此时间段内不显示通知</div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showSettings = false">取消</el-button>
        <el-button type="primary" @click="saveSettings">保存设置</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Bell, Warning, Calendar, AlarmClock, Check, Setting, Search,
  Sort, SortDown, ChatDotSquare, InfoFilled, SuccessFilled,
  WarningFilled, CircleCloseFilled
} from '@element-plus/icons-vue'
import { useNotificationStore } from '@/stores/notification'
import type { Notification } from '@/stores/notification'

const notificationStore = useNotificationStore()

// 响应式数据
const activeTab = ref('all')
const searchKeyword = ref('')
const priorityFilter = ref('')
const dateRange = ref<[Date, Date] | null>(null)
const selectedMessages = ref<string[]>([])
const selectAll = ref(false)
const loading = ref(false)
const showSettings = ref(false)

// 排序
const sortBy = ref('time')
const sortOrder = ref<'asc' | 'desc'>('desc')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)

// 设置表单
const settingsForm = reactive({
  enableInApp: true,
  enableDesktop: true,
  enableSound: true,
  enableEmail: false,
  enableSMS: false,
  quietHours: null as [string, string] | null
})

// 计算属性
const messages = computed(() => notificationStore.notifications)
const unreadCount = computed(() => notificationStore.unreadCount)
const totalCount = computed(() => messages.value.length)

const todayCount = computed(() => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return messages.value.filter(m => new Date(m.timestamp) >= today).length
})

const urgentCount = computed(() => 
  messages.value.filter(m => m.priority === 'urgent' && !m.read).length
)

const isIndeterminate = computed(() => 
  selectedMessages.value.length > 0 && selectedMessages.value.length < filteredMessages.value.length
)

const filteredMessages = computed(() => {
  let result = messages.value

  // 标签页过滤
  switch (activeTab.value) {
    case 'unread':
      result = result.filter(m => !m.read)
      break
    case 'system':
    case 'order':
    case 'delivery':
      result = result.filter(m => m.category === activeTab.value)
      break
  }

  // 搜索关键词
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(m => 
      m.title.toLowerCase().includes(keyword) || 
      m.message.toLowerCase().includes(keyword)
    )
  }

  // 优先级过滤
  if (priorityFilter.value) {
    result = result.filter(m => m.priority === priorityFilter.value)
  }

  // 日期范围过滤
  if (dateRange.value && dateRange.value.length === 2) {
    const [startDate, endDate] = dateRange.value
    result = result.filter(m => {
      const messageDate = new Date(m.timestamp)
      return messageDate >= startDate && messageDate <= endDate
    })
  }

  // 排序
  result.sort((a, b) => {
    let comparison = 0
    
    switch (sortBy.value) {
      case 'time':
        comparison = a.timestamp - b.timestamp
        break
      case 'priority':
        const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 }
        comparison = priorityOrder[a.priority as keyof typeof priorityOrder] - 
                    priorityOrder[b.priority as keyof typeof priorityOrder]
        break
      case 'status':
        comparison = Number(a.read) - Number(b.read)
        break
    }
    
    return sortOrder.value === 'asc' ? comparison : -comparison
  })

  return result
})

const paginatedMessages = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredMessages.value.slice(start, end)
})

// 监听器
watch(filteredMessages, () => {
  currentPage.value = 1
  selectedMessages.value = []
  selectAll.value = false
})

// 生命周期
onMounted(() => {
  loadSettings()
})

// 方法
const handleTabChange = () => {
  searchKeyword.value = ''
  priorityFilter.value = ''
  dateRange.value = null
}

const handleSearch = () => {
  currentPage.value = 1
}

const handleMessageClick = (message: Notification) => {
  if (!message.read) {
    notificationStore.markAsRead(message.id)
  }
  
  if (message.onClick) {
    message.onClick()
  }
}

const handleSelectAll = (checked: boolean) => {
  if (checked) {
    selectedMessages.value = paginatedMessages.value.map(m => m.id)
  } else {
    selectedMessages.value = []
  }
}

const handleMessageSelect = (messageId: string, checked: boolean) => {
  if (checked) {
    if (!selectedMessages.value.includes(messageId)) {
      selectedMessages.value.push(messageId)
    }
  } else {
    const index = selectedMessages.value.indexOf(messageId)
    if (index > -1) {
      selectedMessages.value.splice(index, 1)
    }
  }
  
  // 更新全选状态
  selectAll.value = selectedMessages.value.length === paginatedMessages.value.length
}

const toggleMessageRead = (message: Notification) => {
  if (message.read) {
    // 标记为未读（需要API支持）
    message.read = false
  } else {
    notificationStore.markAsRead(message.id)
  }
}

const deleteMessage = async (messageId: string) => {
  try {
    await ElMessageBox.confirm('确定要删除这条消息吗？', '确认删除', {
      type: 'warning'
    })
    
    notificationStore.removeNotification(messageId)
    ElMessage.success('消息已删除')
  } catch (error) {
    // 用户取消操作
  }
}

const markAllAsRead = () => {
  notificationStore.markAllAsRead()
  ElMessage.success('已全部标记为已读')
}

const batchMarkAsRead = () => {
  selectedMessages.value.forEach(messageId => {
    notificationStore.markAsRead(messageId)
  })
  selectedMessages.value = []
  ElMessage.success('已标记选中消息为已读')
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedMessages.value.length} 条消息吗？`, '确认删除', {
      type: 'warning'
    })
    
    selectedMessages.value.forEach(messageId => {
      notificationStore.removeNotification(messageId)
    })
    
    selectedMessages.value = []
    ElMessage.success('已删除选中消息')
  } catch (error) {
    // 用户取消操作
  }
}

const viewOrder = (orderNumber: string) => {
  // 跳转到订单详情
  ElMessage.info(`查看订单: ${orderNumber}`)
}

const trackDelivery = (trackingNumber: string) => {
  // 跳转到配送跟踪
  ElMessage.info(`跟踪配送: ${trackingNumber}`)
}

const loadSettings = () => {
  // 加载用户通知设置
  Object.assign(settingsForm, notificationStore.config)
}

const saveSettings = () => {
  notificationStore.updateConfig(settingsForm)
  showSettings.value = false
  ElMessage.success('设置已保存')
}

// 工具方法
const getMessageIcon = (type: string) => {
  switch (type) {
    case 'success': return SuccessFilled
    case 'warning': return WarningFilled
    case 'error': return CircleCloseFilled
    default: return InfoFilled
  }
}

const getMessageIconColor = (type: string) => {
  switch (type) {
    case 'success': return '#67C23A'
    case 'warning': return '#E6A23C'
    case 'error': return '#F56C6C'
    default: return '#409EFF'
  }
}

const getCategoryText = (category: string) => {
  const categoryMap: Record<string, string> = {
    system: '系统',
    order: '订单',
    delivery: '配送',
    user: '用户'
  }
  return categoryMap[category] || category
}

const getPriorityType = (priority: string) => {
  switch (priority) {
    case 'urgent': return 'danger'
    case 'high': return 'warning'
    case 'medium': return 'primary'
    case 'low': return 'info'
    default: return 'info'
  }
}

const getPriorityText = (priority: string) => {
  const priorityMap: Record<string, string> = {
    urgent: '紧急',
    high: '高',
    medium: '中',
    low: '低'
  }
  return priorityMap[priority] || priority
}

const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}
</script>

<style scoped>
.message-center {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  height: 100px;
}

.stat-item {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 15px;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.stat-icon.total { background-color: #ecf5ff; color: #409EFF; }
.stat-icon.unread { background-color: #fef0f0; color: #F56C6C; }
.stat-icon.today { background-color: #f0f9ff; color: #67C23A; }
.stat-icon.urgent { background-color: #fdf6ec; color: #E6A23C; }

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.message-content {
  min-height: 600px;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-section {
  display: flex;
  align-items: center;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 15px;
}

.batch-actions {
  display: flex;
  gap: 10px;
}

.sort-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.messages {
  min-height: 400px;
}

.message-item {
  display: flex;
  align-items: flex-start;
  padding: 15px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.2s;
}

.message-item:hover {
  background-color: #f5f7fa;
  border-color: #d9ecff;
}

.message-item.unread {
  background-color: #f0f9ff;
  border-left: 4px solid #409EFF;
}

.message-item.selected {
  background-color: #e6f7ff;
  border-color: #91d5ff;
}

.message-item.priority-urgent {
  border-left-color: #F56C6C;
}

.message-item.priority-high {
  border-left-color: #E6A23C;
}

.message-checkbox {
  margin-right: 12px;
  margin-top: 2px;
}

.message-icon {
  margin-right: 12px;
  margin-top: 2px;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.message-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.message-time {
  color: #999;
}

.message-body {
  margin-bottom: 8px;
}

.message-text {
  margin: 0 0 10px 0;
  color: #666;
  line-height: 1.5;
}

.message-actions {
  display: flex;
  gap: 8px;
}

.message-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.empty-state h3 {
  margin: 20px 0 10px 0;
  color: #666;
}

.empty-state p {
  margin: 0;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.setting-desc {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}
</style> 