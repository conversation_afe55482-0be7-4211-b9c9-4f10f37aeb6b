<template>
  <div class="service-tickets">
    <div class="page-header">
      <h1>客服工单</h1>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新建工单
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon open">
                <el-icon size="24"><Clock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.open }}</div>
                <div class="stat-label">待处理</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon processing">
                <el-icon size="24"><Loading /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.processing }}</div>
                <div class="stat-label">处理中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon resolved">
                <el-icon size="24"><Check /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.resolved }}</div>
                <div class="stat-label">已解决</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon total">
                <el-icon size="24"><Document /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.total }}</div>
                <div class="stat-label">总工单</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索过滤 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="工单号">
          <el-input
            v-model="searchForm.ticketNumber"
            placeholder="输入工单号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="订单号">
          <el-input
            v-model="searchForm.orderNumber"
            placeholder="输入订单号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="客户姓名">
          <el-input
            v-model="searchForm.customerName"
            placeholder="输入客户姓名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="工单类型">
          <el-select
            v-model="searchForm.type"
            placeholder="选择类型"
            clearable
            style="width: 120px"
          >
            <el-option label="投诉" value="complaint" />
            <el-option label="咨询" value="inquiry" />
            <el-option label="丢失" value="lost" />
            <el-option label="损坏" value="damage" />
            <el-option label="延误" value="delay" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="待处理" value="open" />
            <el-option label="处理中" value="processing" />
            <el-option label="已解决" value="resolved" />
            <el-option label="已关闭" value="closed" />
          </el-select>
        </el-form-item>
        <el-form-item label="优先级">
          <el-select
            v-model="searchForm.priority"
            placeholder="选择优先级"
            clearable
            style="width: 120px"
          >
            <el-option label="低" value="low" />
            <el-option label="中" value="medium" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="urgent" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 工单列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="table-header">
          <span>工单列表 (共 {{ total }} 条)</span>
          <div class="table-actions">
            <el-button size="small" @click="exportData">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
            <el-button size="small" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <el-table :data="ticketList" style="width: 100%" v-loading="loading">
        <el-table-column prop="ticketNumber" label="工单号" width="120" />
        <el-table-column prop="orderNumber" label="订单号" width="140">
          <template #default="scope">
            <el-link type="primary" @click="viewOrder(scope.row.orderNumber)">
              {{ scope.row.orderNumber }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="customerName" label="客户" width="100" />
        <el-table-column prop="customerPhone" label="联系电话" width="120" />
        <el-table-column prop="type" label="类型" width="80">
          <template #default="scope">
            <el-tag :type="getTypeColor(scope.row.type)" size="small">
              {{ getTypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" width="80">
          <template #default="scope">
            <el-tag :type="getPriorityColor(scope.row.priority)" size="small">
              {{ getPriorityText(scope.row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="getStatusColor(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="问题描述" show-overflow-tooltip />
        <el-table-column prop="assignedTo" label="处理人" width="100" />
        <el-table-column prop="createTime" label="创建时间" width="140">
          <template #default="scope">
            {{ formatTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleTicket(scope.row)">
              处理
            </el-button>
            <el-button type="info" size="small" @click="viewDetail(scope.row)"> 详情 </el-button>
            <el-dropdown @command="handleCommand($event, scope.row)">
              <el-button size="small" type="info">
                更多 <el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="assign">分配处理人</el-dropdown-item>
                  <el-dropdown-item command="priority">修改优先级</el-dropdown-item>
                  <el-dropdown-item command="close" divided>关闭工单</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 工单详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="工单详情" width="800px">
      <div v-if="currentTicket">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="工单号">{{
            currentTicket.ticketNumber
          }}</el-descriptions-item>
          <el-descriptions-item label="订单号">{{
            currentTicket.orderNumber
          }}</el-descriptions-item>
          <el-descriptions-item label="客户姓名">{{
            currentTicket.customerName
          }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{
            currentTicket.customerPhone
          }}</el-descriptions-item>
          <el-descriptions-item label="工单类型">
            <el-tag :type="getTypeColor(currentTicket.type)">
              {{ getTypeText(currentTicket.type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="优先级">
            <el-tag :type="getPriorityColor(currentTicket.priority)">
              {{ getPriorityText(currentTicket.priority) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusColor(currentTicket.status)">
              {{ getStatusText(currentTicket.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="处理人">{{
            currentTicket.assignedTo || '未分配'
          }}</el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">{{
            formatTime(currentTicket.createTime)
          }}</el-descriptions-item>
          <el-descriptions-item label="问题描述" :span="2">{{
            currentTicket.description
          }}</el-descriptions-item>
          <el-descriptions-item v-if="currentTicket.response" label="处理回复" :span="2">{{
            currentTicket.response
          }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 处理工单对话框 -->
    <el-dialog v-model="showHandleDialog" title="处理工单" width="600px">
      <div v-if="currentTicket">
        <div class="ticket-info">
          <h4>工单信息</h4>
          <p><strong>工单号：</strong>{{ currentTicket.ticketNumber }}</p>
          <p><strong>客户：</strong>{{ currentTicket.customerName }}</p>
          <p><strong>问题：</strong>{{ currentTicket.description }}</p>
        </div>

        <el-form :model="handleForm" label-width="100px">
          <el-form-item label="处理状态" required>
            <el-select v-model="handleForm.status" placeholder="选择状态">
              <el-option label="处理中" value="processing" />
              <el-option label="已解决" value="resolved" />
              <el-option label="已关闭" value="closed" />
            </el-select>
          </el-form-item>
          <el-form-item label="处理回复" required>
            <el-input
              v-model="handleForm.response"
              type="textarea"
              rows="4"
              placeholder="请输入处理回复"
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="showHandleDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmHandle" :loading="handling">确认处理</el-button>
      </template>
    </el-dialog>

    <!-- 新建工单对话框 -->
    <el-dialog v-model="showCreateDialog" title="新建工单" width="600px">
      <el-form :model="createForm" label-width="100px">
        <el-form-item label="订单号" required>
          <el-input v-model="createForm.orderNumber" placeholder="请输入订单号" />
        </el-form-item>
        <el-form-item label="客户姓名" required>
          <el-input v-model="createForm.customerName" placeholder="请输入客户姓名" />
        </el-form-item>
        <el-form-item label="联系电话" required>
          <el-input v-model="createForm.customerPhone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="工单类型" required>
          <el-select v-model="createForm.type" placeholder="选择工单类型">
            <el-option label="投诉" value="complaint" />
            <el-option label="咨询" value="inquiry" />
            <el-option label="丢失" value="lost" />
            <el-option label="损坏" value="damage" />
            <el-option label="延误" value="delay" />
          </el-select>
        </el-form-item>
        <el-form-item label="优先级" required>
          <el-select v-model="createForm.priority" placeholder="选择优先级">
            <el-option label="低" value="low" />
            <el-option label="中" value="medium" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="urgent" />
          </el-select>
        </el-form-item>
        <el-form-item label="问题描述" required>
          <el-input
            v-model="createForm.description"
            type="textarea"
            rows="4"
            placeholder="请详细描述问题"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmCreate" :loading="creating">确认创建</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Plus,
  Search,
  Refresh,
  Download,
  ArrowDown,
  Clock,
  Loading,
  Check,
  Document,
} from '@element-plus/icons-vue'
import { operatorApi, type ServiceTicket } from '@/api/operator'

// 响应式数据
const ticketList = ref<ServiceTicket[]>([])
const currentTicket = ref<ServiceTicket | null>(null)

// 统计数据
const stats = ref({
  open: 0,
  processing: 0,
  resolved: 0,
  total: 0,
})

// 搜索表单
const searchForm = reactive({
  ticketNumber: '',
  orderNumber: '',
  customerName: '',
  type: '',
  status: '',
  priority: '',
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
})

const total = ref(0)
const loading = ref(false)

// 对话框控制
const showDetailDialog = ref(false)
const showHandleDialog = ref(false)
const showCreateDialog = ref(false)

// 表单数据
const handleForm = reactive({
  status: '',
  response: '',
})

const createForm = reactive({
  orderNumber: '',
  customerName: '',
  customerPhone: '',
  type: '',
  priority: '',
  description: '',
})

// 加载状态
const handling = ref(false)
const creating = ref(false)

// 生命周期
onMounted(() => {
  loadData()
  loadStats()
})

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm,
    }

    const response = await operatorApi.getServiceTickets(params)
    if (response.code === 200) {
      ticketList.value = response.data.list
      total.value = response.data.total
    }
  } catch (error) {
    console.error('加载工单数据失败:', error)
    ticketList.value = []
    total.value = 0
    ElMessage.error('加载工单数据失败')
  } finally {
    loading.value = false
  }
}

const confirmHandle = async () => {
  if (!handleForm.status || !handleForm.response) {
    ElMessage.warning('请填写完整信息')
    return
  }

  handling.value = true
  try {
    await operatorApi.handleServiceTicket(currentTicket.value!.id, handleForm)
    ElMessage.success('工单处理成功')
    showHandleDialog.value = false
    await loadData()
  } catch (error) {
    console.error('处理工单失败:', error)
    ElMessage.error('处理工单失败')
  } finally {
    handling.value = false
  }
}

const confirmCreate = async () => {
  if (!createForm.orderNumber || !createForm.customerName || !createForm.type) {
    ElMessage.warning('请填写必填信息')
    return
  }

  creating.value = true
  try {
    await operatorApi.createServiceTicket(createForm)
    ElMessage.success('工单创建成功')
    showCreateDialog.value = false
    // 重置表单
    Object.assign(createForm, {
      orderNumber: '',
      customerName: '',
      customerPhone: '',
      type: '',
      priority: '',
      description: '',
    })
    await loadData()
  } catch (error) {
    console.error('创建工单失败:', error)
    ElMessage.error('创建工单失败')
  } finally {
    creating.value = false
  }
}

const loadStats = async () => {
  try {
    // 这里应该调用统计API
    stats.value = {
      open: 15,
      processing: 8,
      resolved: 45,
      total: 68,
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const refreshData = () => {
  loadData()
  loadStats()
  ElMessage.success('数据已刷新')
}

const handleSearch = () => {
  pagination.page = 1
  loadData()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    ticketNumber: '',
    orderNumber: '',
    customerName: '',
    type: '',
    status: '',
    priority: '',
  })
  handleSearch()
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadData()
}

const viewDetail = (ticket: ServiceTicket) => {
  currentTicket.value = ticket
  showDetailDialog.value = true
}

const handleTicket = (ticket: ServiceTicket) => {
  currentTicket.value = ticket
  handleForm.status = ticket.status
  handleForm.response = ticket.response || ''
  showHandleDialog.value = true
}

const handleCommand = (command: string, ticket: ServiceTicket) => {
  switch (command) {
    case 'assign':
      ElMessage.info('分配处理人功能开发中')
      break
    case 'priority':
      ElMessage.info('修改优先级功能开发中')
      break
    case 'close':
      ElMessage.info('关闭工单功能开发中')
      break
  }
}

const viewOrder = (orderNumber: string) => {
  ElMessage.info(`查看订单 ${orderNumber}`)
}

const exportData = () => {
  ElMessage.info('导出功能开发中')
}

// 工具方法
const getTypeColor = (type: string) => {
  switch (type) {
    case 'complaint':
      return 'danger'
    case 'lost':
      return 'danger'
    case 'damage':
      return 'warning'
    case 'delay':
      return 'warning'
    case 'inquiry':
      return 'info'
    default:
      return 'info'
  }
}

const getTypeText = (type: string) => {
  const types: Record<string, string> = {
    complaint: '投诉',
    inquiry: '咨询',
    lost: '丢失',
    damage: '损坏',
    delay: '延误',
  }
  return types[type] || type
}

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'urgent':
      return 'danger'
    case 'high':
      return 'warning'
    case 'medium':
      return 'primary'
    case 'low':
      return 'info'
    default:
      return 'info'
  }
}

const getPriorityText = (priority: string) => {
  const priorities: Record<string, string> = {
    urgent: '紧急',
    high: '高',
    medium: '中',
    low: '低',
  }
  return priorities[priority] || priority
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'open':
      return 'danger'
    case 'processing':
      return 'warning'
    case 'resolved':
      return 'success'
    case 'closed':
      return 'info'
    default:
      return 'info'
  }
}

const getStatusText = (status: string) => {
  const statuses: Record<string, string> = {
    open: '待处理',
    processing: '处理中',
    resolved: '已解决',
    closed: '已关闭',
  }
  return statuses[status] || status
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString('zh-CN')
}
</script>

<style scoped>
.service-tickets {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  height: 100px;
}

.stat-item {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 10px;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.stat-icon.open {
  background-color: #fef0f0;
  color: #f56c6c;
}
.stat-icon.processing {
  background-color: #fdf6ec;
  color: #e6a23c;
}
.stat-icon.resolved {
  background-color: #f0f9ff;
  color: #67c23a;
}
.stat-icon.total {
  background-color: #ecf5ff;
  color: #409eff;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: none;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.ticket-info {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.ticket-info h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.ticket-info p {
  margin: 5px 0;
  font-size: 14px;
}
</style>
