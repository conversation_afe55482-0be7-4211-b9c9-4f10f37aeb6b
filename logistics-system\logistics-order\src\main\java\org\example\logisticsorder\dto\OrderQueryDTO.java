package org.example.logisticsorder.dto;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 订单查询DTO
 */
@Data
public class OrderQueryDTO {

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    private Integer pageSize = 10;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 支付状态
     */
    private Integer paymentStatus;

    /**
     * 寄件人电话
     */
    private String senderPhone;

    /**
     * 收件人电话
     */
    private String receiverPhone;

    /**
     * 收件人城市
     */
    private String receiverCity;

    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;
}