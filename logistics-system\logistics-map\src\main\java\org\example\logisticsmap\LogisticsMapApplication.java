package org.example.logisticsmap;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 地图服务启动类
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableCaching
@EnableConfigurationProperties
public class LogisticsMapApplication {

    public static void main(String[] args) {
        SpringApplication.run(LogisticsMapApplication.class, args);
        System.out.println("=========================================");
        System.out.println("地图服务启动成功！");
        System.out.println("服务端口: 8006");
        System.out.println("API文档: http://localhost:8006/test/hello");
        System.out.println("=========================================");
    }

}
