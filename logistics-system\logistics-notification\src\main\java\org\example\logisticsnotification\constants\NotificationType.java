package org.example.logisticsnotification.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通知类型枚举
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Getter
@AllArgsConstructor
public enum NotificationType {

    SMS("SMS", "短信通知", "通过短信发送通知"),
    EMAIL("EMAIL", "邮件通知", "通过邮件发送通知"),
    PUSH("PUSH", "推送通知", "通过APP推送发送通知"),
    WECHAT("WECHAT", "微信通知", "通过微信发送通知");

    private final String code;
    private final String name;
    private final String description;

    /**
     * 根据编码获取枚举
     */
    public static NotificationType fromCode(String code) {
        for (NotificationType type : NotificationType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的通知类型: " + code);
    }
}
