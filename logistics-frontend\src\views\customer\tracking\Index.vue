<template>
  <div class="tracking-index">
    <div class="page-header">
      <h2>物流追踪</h2>
      <p>输入订单号查询物流信息</p>
    </div>

    <el-card class="search-card">
      <el-form :model="searchForm" @submit.prevent="handleSearch">
        <el-row :gutter="20">
          <el-col :span="16">
            <el-form-item>
              <el-input
                v-model="searchForm.orderNumber"
                placeholder="请输入订单号"
                size="large"
                :prefix-icon="Search"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-button
                type="primary"
                size="large"
                style="width: 100%"
                @click="handleSearch"
                :loading="searching"
              >
                查询
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 最近查询 -->
    <el-card v-if="recentQueries.length > 0" class="recent-card">
      <template #header>
        <div class="card-header">
          <el-icon><Clock /></el-icon>
          <span>最近查询</span>
          <el-button type="text" @click="clearRecentQueries">清空</el-button>
        </div>
      </template>

      <div class="recent-list">
        <el-tag
          v-for="orderNumber in recentQueries"
          :key="orderNumber"
          class="recent-tag"
          @click="quickSearch(orderNumber)"
        >
          {{ orderNumber }}
        </el-tag>
      </div>
    </el-card>

    <!-- 我的订单快速入口 -->
    <el-card class="quick-entry">
      <template #header>
        <div class="card-header">
          <el-icon><Box /></el-icon>
          <span>我的订单</span>
        </div>
      </template>

      <div class="order-status-grid">
        <div
          v-for="status in orderStatusList"
          :key="status.value"
          class="status-item"
          @click="viewOrdersByStatus(status.value)"
        >
          <div class="status-icon">
            <el-icon size="32" :color="status.color">
              <component :is="status.icon" />
            </el-icon>
          </div>
          <div class="status-text">
            <div class="status-name">{{ status.label }}</div>
            <div class="status-count">{{ status.count }}件</div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 常用功能 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <el-icon><Setting /></el-icon>
          <span>常用功能</span>
        </div>
      </template>

      <div class="function-grid">
        <div class="function-item" @click="$router.push('/customer/order/create')">
          <el-icon size="24" color="#409EFF"><Plus /></el-icon>
          <span>创建订单</span>
        </div>
        <div class="function-item" @click="$router.push('/customer/order/list')">
          <el-icon size="24" color="#67C23A"><Document /></el-icon>
          <span>订单管理</span>
        </div>
        <div class="function-item" @click="$router.push('/customer/profile/address')">
          <el-icon size="24" color="#E6A23C"><LocationInformation /></el-icon>
          <span>地址管理</span>
        </div>
        <div class="function-item" @click="$router.push('/customer/profile')">
          <el-icon size="24" color="#F56C6C"><User /></el-icon>
          <span>个人中心</span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Search,
  Clock,
  Box,
  Setting,
  Plus,
  Document,
  LocationInformation,
  User,
  Timer,
  Van,
  CircleCheck,
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 状态
const searching = ref(false)
const recentQueries = ref<string[]>([])

// 搜索表单
const searchForm = reactive({
  orderNumber: '',
})

// 订单状态列表
const orderStatusList = ref([
  {
    label: '待发货',
    value: 'PENDING',
    icon: Timer,
    color: '#909399',
    count: 0,
  },
  {
    label: '运输中',
    value: 'IN_TRANSIT',
    icon: Van,
    color: '#E6A23C',
    count: 0,
  },
  {
    label: '派送中',
    value: 'OUT_FOR_DELIVERY',
    icon: Van,
    color: '#E6A23C',
    count: 0,
  },
  {
    label: '已送达',
    value: 'DELIVERED',
    icon: CircleCheck,
    color: '#67C23A',
    count: 0,
  },
])

// 查询物流
const handleSearch = () => {
  if (!searchForm.orderNumber.trim()) {
    ElMessage.warning('请输入订单号')
    return
  }

  // 添加到最近查询
  addToRecentQueries(searchForm.orderNumber)

  // 跳转到详情页
  router.push(`/customer/tracking/detail?orderNumber=${searchForm.orderNumber}`)
}

// 快速查询
const quickSearch = (orderNumber: string) => {
  searchForm.orderNumber = orderNumber
  handleSearch()
}

// 添加到最近查询
const addToRecentQueries = (orderNumber: string) => {
  const queries = [...recentQueries.value]
  const index = queries.indexOf(orderNumber)

  if (index > -1) {
    queries.splice(index, 1)
  }

  queries.unshift(orderNumber)
  recentQueries.value = queries.slice(0, 5) // 只保留最近5个

  // 保存到本地存储
  localStorage.setItem('recentTrackingQueries', JSON.stringify(recentQueries.value))
}

// 清空最近查询
const clearRecentQueries = () => {
  recentQueries.value = []
  localStorage.removeItem('recentTrackingQueries')
}

// 根据状态查看订单
const viewOrdersByStatus = (status: string) => {
  router.push(`/customer/order/list?status=${status}`)
}

// 加载最近查询
const loadRecentQueries = () => {
  const saved = localStorage.getItem('recentTrackingQueries')
  if (saved) {
    try {
      recentQueries.value = JSON.parse(saved)
    } catch (error) {
      console.error('解析最近查询失败:', error)
    }
  }
}

// 加载订单统计
const loadOrderStats = async () => {
  // TODO: 调用API获取各状态订单数量
  // 这里先用模拟数据
  orderStatusList.value.forEach((status) => {
    status.count = Math.floor(Math.random() * 10)
  })
}

onMounted(() => {
  loadRecentQueries()
  loadOrderStats()
})
</script>

<style scoped>
.tracking-index {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 10px 0;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
}

.search-card {
  margin-bottom: 20px;
}

.recent-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.recent-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.recent-tag {
  cursor: pointer;
  transition: all 0.3s;
}

.recent-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.quick-entry {
  margin-bottom: 20px;
}

.order-status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.status-item:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
  transform: translateY(-2px);
}

.status-text {
  flex: 1;
}

.status-name {
  font-size: 16px;
  color: #333;
  margin-bottom: 5px;
}

.status-count {
  font-size: 14px;
  color: #666;
}

.function-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  padding: 30px 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.function-item:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
  transform: translateY(-2px);
}

.function-item span {
  font-size: 14px;
  color: #333;
}

@media (max-width: 768px) {
  .order-status-grid {
    grid-template-columns: 1fr;
  }

  .function-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .status-item {
    padding: 15px;
  }

  .function-item {
    padding: 20px 15px;
  }
}
</style>
