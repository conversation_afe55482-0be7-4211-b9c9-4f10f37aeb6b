package org.example.logisticsorder.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.example.logisticsorder.dto.CreateOrderDTO;
import org.example.logisticsorder.dto.OrderQueryDTO;
import org.example.logisticsorder.dto.OrderStatusUpdateDTO;
import org.example.logisticsorder.entity.OrderStatusLog;
import org.example.logisticsorder.vo.OrderVO;
import org.example.logisticsorder.vo.OrderListVO;
import java.util.List;

/**
 * 订单服务接口
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
public interface OrderService {

    /**
     * 创建订单
     */
    OrderVO createOrder(Long userId, CreateOrderDTO createOrderDTO);

    /**
     * 根据订单ID获取订单详情
     */
    OrderVO getOrderById(Long orderId);

    /**
     * 根据订单号获取订单详情
     */
    OrderVO getOrderByNumber(String orderNumber);

    /**
     * 分页查询订单列表
     */
    IPage<OrderListVO> getOrderPage(OrderQueryDTO queryDTO);

    /**
     * 根据用户ID查询订单列表
     */
    List<OrderListVO> getUserOrders(Long userId, Integer limit);

    /**
     * 更新订单状态
     */
    boolean updateOrderStatus(OrderStatusUpdateDTO updateDTO);

    /**
     * 取消订单
     */
    boolean cancelOrder(Long orderId, Long userId, String reason);

    /**
     * 订单支付成功回调
     */
    boolean paymentSuccess(String orderNumber, String paymentMethod);

    /**
     * 分配揽件员
     */
    boolean assignPickupCourier(Long orderId, Long courierId);

    /**
     * 分配配送员
     */
    boolean assignDeliveryCourier(Long orderId, Long courierId);

    /**
     * 订单签收
     */
    boolean signOrder(Long orderId, String signProof);

    /**
     * 获取订单状态变更日志
     */
    List<OrderStatusLog> getOrderStatusLog(Long orderId);

    /**
     * 计算订单费用
     */
    PriceResult calculatePrice(CreateOrderDTO createOrderDTO);

    /**
     * 批量更新订单状态
     */
    boolean batchUpdateStatus(List<Long> orderIds, String newStatus, String reason);

    /**
     * 自动取消超时未支付订单
     */
    int cancelTimeoutOrders(Integer timeoutMinutes);

    /**
     * 统计订单数量
     */
    OrderStatistics getOrderStatistics(Long userId);

    /**
     * 内部类：价格计算结果
     */
    class PriceResult {
        private java.math.BigDecimal shippingFee;
        private java.math.BigDecimal insuranceFee;
        private java.math.BigDecimal packingFee;
        private java.math.BigDecimal totalFee;

        public PriceResult() {
        }

        // 构造方法和getter/setter
        public PriceResult(java.math.BigDecimal shippingFee, java.math.BigDecimal insuranceFee,
                           java.math.BigDecimal packingFee, java.math.BigDecimal totalFee) {
            this.shippingFee = shippingFee;
            this.insuranceFee = insuranceFee;
            this.packingFee = packingFee;
            this.totalFee = totalFee;
        }

        // getter methods
        public java.math.BigDecimal getShippingFee() { return shippingFee; }
        public java.math.BigDecimal getInsuranceFee() { return insuranceFee; }
        public java.math.BigDecimal getPackingFee() { return packingFee; }
        public java.math.BigDecimal getTotalFee() { return totalFee; }
    }

    /**
     * 内部类：订单统计
     */
    class OrderStatistics {
        private Long totalOrders;
        private Long pendingOrders;
        private Long paidOrders;
        private Long inTransitOrders;
        private Long deliveredOrders;
        private Long cancelledOrders;

        // 构造方法和getter/setter
        public OrderStatistics() {}

        // getter/setter methods
        public Long getTotalOrders() { return totalOrders; }
        public void setTotalOrders(Long totalOrders) { this.totalOrders = totalOrders; }

        public Long getPendingOrders() { return pendingOrders; }
        public void setPendingOrders(Long pendingOrders) { this.pendingOrders = pendingOrders; }

        public Long getPaidOrders() { return paidOrders; }
        public void setPaidOrders(Long paidOrders) { this.paidOrders = paidOrders; }

        public Long getInTransitOrders() { return inTransitOrders; }
        public void setInTransitOrders(Long inTransitOrders) { this.inTransitOrders = inTransitOrders; }

        public Long getDeliveredOrders() { return deliveredOrders; }
        public void setDeliveredOrders(Long deliveredOrders) { this.deliveredOrders = deliveredOrders; }

        public Long getCancelledOrders() { return cancelledOrders; }
        public void setCancelledOrders(Long cancelledOrders) { this.cancelledOrders = cancelledOrders; }
    }
}
