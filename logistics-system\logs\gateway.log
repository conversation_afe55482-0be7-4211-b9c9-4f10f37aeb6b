2025-06-29 10:58:50 [main] WARN  c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[logistics-gateway] & group[DEFAULT_GROUP]
2025-06-29 10:58:50 [main] WARN  c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[logistics-gateway.properties] & group[DEFAULT_GROUP]
2025-06-29 10:58:50 [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-logistics-gateway.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-logistics-gateway,DEFAULT_GROUP'}]
2025-06-29 10:58:50 [main] INFO  o.e.logisticsgateway.LogisticsGatewayApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-29 10:58:52 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-29 10:58:52 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-29 10:58:52 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-06-29 10:58:52 [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=0fd42190-0a66-3d55-a0e7-3dd72daba566
2025-06-29 10:58:52 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-29 10:58:52 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-29 10:58:52 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-29 10:58:59 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [After]
2025-06-29 10:58:59 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Before]
2025-06-29 10:58:59 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Between]
2025-06-29 10:58:59 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Cookie]
2025-06-29 10:58:59 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Header]
2025-06-29 10:58:59 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Host]
2025-06-29 10:58:59 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Method]
2025-06-29 10:58:59 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Path]
2025-06-29 10:58:59 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Query]
2025-06-29 10:58:59 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [ReadBody]
2025-06-29 10:58:59 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [RemoteAddr]
2025-06-29 10:58:59 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-06-29 10:58:59 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Weight]
2025-06-29 10:58:59 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-29 10:58:59 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-29 10:59:00 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-29 10:59:00 [main] INFO  o.s.boot.web.embedded.netty.NettyWebServer - Netty started on port 8080
2025-06-29 10:59:03 [main] INFO  c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-06-29 10:59:03 [main] INFO  c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-06-29 10:59:03 [main] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - nacos registry, DEFAULT_GROUP logistics-gateway 192.168.32.1:8080 register finished
2025-06-29 10:59:04 [main] INFO  c.a.c.n.discovery.GatewayLocatorHeartBeatPublisher - Start nacos gateway locator heartBeat task scheduler.
2025-06-29 10:59:04 [main] INFO  o.e.logisticsgateway.LogisticsGatewayApplication - Started LogisticsGatewayApplication in 19.976 seconds (JVM running for 21.222)
2025-06-29 10:59:04 [main] INFO  c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=logistics-gateway.properties, group=DEFAULT_GROUP
2025-06-29 10:59:04 [main] INFO  c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=logistics-gateway, group=DEFAULT_GROUP
2025-06-29 11:03:45 [reactor-http-nio-2] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求开始 - 方法: POST, 路径: /api/user/register, 来源: 127.0.0.1
2025-06-29 11:03:45 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 请求路径: /api/user/register, 客户端IP: 127.0.0.1
2025-06-29 11:03:45 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 检查路径 /api/user/register 是否在白名单中，白名单: [/api/user/register, /api/user/login, /api/user/test/**, /api/order/test/**, /api/logistics/test/**, /api/delivery/test/**, /api/notification/test/**, /api/map/test/**, /actuator/**, /favicon.ico]
2025-06-29 11:03:45 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/register 匹配路径 /api/user/register 结果: true
2025-06-29 11:03:45 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 路径 /api/user/register 白名单检查结果: true
2025-06-29 11:03:45 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 白名单路径，直接放行: /api/user/register
2025-06-29 11:03:45 [lettuce-nioEventLoop-5-1] DEBUG o.example.logisticsgateway.filter.RateLimitFilter - 限流检查通过 - Key: rate_limit:127.0.0.1:_api_user_register:POST, 配置: RateLimitConfig{limit=5, windowSize=60}
2025-06-29 11:03:45 [lettuce-nioEventLoop-5-1] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求开始 - Method: POST, Path: /api/user/register, IP: 127.0.0.1, UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********, StartTime: 1751166225903
2025-06-29 11:06:10 [reactor-http-nio-2] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求完成 - Method: POST, Path: /api/user/register, IP: 127.0.0.1, Status: 200, Duration: 144253ms, StartTime: 1751166225903, EndTime: 1751166370156
2025-06-29 11:06:10 [reactor-http-nio-2] WARN  org.example.logisticsgateway.filter.MonitorFilter - 发现慢请求 - Method: POST, Path: /api/user/register, Duration: 144253ms, Status: 200
2025-06-29 11:06:10 [reactor-http-nio-2] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求结束 - 方法: POST, 路径: /api/user/register, 状态码: 200, 耗时: 144341ms
2025-06-29 11:06:40 [reactor-http-nio-5] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求开始 - 方法: POST, 路径: /api/user/login, 来源: 127.0.0.1
2025-06-29 11:06:40 [reactor-http-nio-5] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 请求路径: /api/user/login, 客户端IP: 127.0.0.1
2025-06-29 11:06:40 [reactor-http-nio-5] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 检查路径 /api/user/login 是否在白名单中，白名单: [/api/user/register, /api/user/login, /api/user/test/**, /api/order/test/**, /api/logistics/test/**, /api/delivery/test/**, /api/notification/test/**, /api/map/test/**, /actuator/**, /favicon.ico]
2025-06-29 11:06:40 [reactor-http-nio-5] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/register 匹配路径 /api/user/login 结果: false
2025-06-29 11:06:40 [reactor-http-nio-5] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/login 匹配路径 /api/user/login 结果: true
2025-06-29 11:06:40 [reactor-http-nio-5] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 路径 /api/user/login 白名单检查结果: true
2025-06-29 11:06:40 [reactor-http-nio-5] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 白名单路径，直接放行: /api/user/login
2025-06-29 11:06:40 [lettuce-nioEventLoop-5-1] DEBUG o.example.logisticsgateway.filter.RateLimitFilter - 限流检查通过 - Key: rate_limit:127.0.0.1:_api_user_login:POST, 配置: RateLimitConfig{limit=10, windowSize=60}
2025-06-29 11:06:40 [lettuce-nioEventLoop-5-1] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求开始 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********, StartTime: 1751166400665
2025-06-29 11:06:40 [reactor-http-nio-5] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求完成 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, Status: 200, Duration: 224ms, StartTime: 1751166400665, EndTime: 1751166400889
2025-06-29 11:06:40 [reactor-http-nio-5] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求结束 - 方法: POST, 路径: /api/user/login, 状态码: 200, 耗时: 239ms
2025-06-29 12:08:24 [Thread-7] WARN  com.alibaba.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-29 12:08:24 [Thread-1] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-29 12:08:24 [Thread-7] WARN  com.alibaba.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-06-29 12:08:24 [Thread-1] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-06-29 12:08:24 [SpringApplicationShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-29 12:08:24 [SpringApplicationShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registration finished.
2025-06-29 12:08:41 [main] WARN  c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[logistics-gateway] & group[DEFAULT_GROUP]
2025-06-29 12:08:41 [main] WARN  c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[logistics-gateway.properties] & group[DEFAULT_GROUP]
2025-06-29 12:08:41 [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-logistics-gateway.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-logistics-gateway,DEFAULT_GROUP'}]
2025-06-29 12:08:41 [main] INFO  o.e.logisticsgateway.LogisticsGatewayApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-29 12:08:42 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-29 12:08:42 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-29 12:08:42 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.
2025-06-29 12:08:43 [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=0fd42190-0a66-3d55-a0e7-3dd72daba566
2025-06-29 12:08:43 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-29 12:08:43 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-29 12:08:43 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-29 12:08:53 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [After]
2025-06-29 12:08:53 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Before]
2025-06-29 12:08:53 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Between]
2025-06-29 12:08:53 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Cookie]
2025-06-29 12:08:53 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Header]
2025-06-29 12:08:53 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Host]
2025-06-29 12:08:53 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Method]
2025-06-29 12:08:53 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Path]
2025-06-29 12:08:53 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Query]
2025-06-29 12:08:53 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [ReadBody]
2025-06-29 12:08:53 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [RemoteAddr]
2025-06-29 12:08:53 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-06-29 12:08:53 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Weight]
2025-06-29 12:08:53 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-29 12:08:54 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-29 12:08:55 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-29 12:08:56 [main] INFO  o.s.boot.web.embedded.netty.NettyWebServer - Netty started on port 8080
2025-06-29 12:08:58 [main] INFO  c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-06-29 12:08:58 [main] INFO  c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-06-29 12:08:58 [main] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - nacos registry, DEFAULT_GROUP logistics-gateway 192.168.32.1:8080 register finished
2025-06-29 12:08:59 [main] INFO  c.a.c.n.discovery.GatewayLocatorHeartBeatPublisher - Start nacos gateway locator heartBeat task scheduler.
2025-06-29 12:08:59 [main] INFO  o.e.logisticsgateway.LogisticsGatewayApplication - Started LogisticsGatewayApplication in 24.685 seconds (JVM running for 25.864)
2025-06-29 12:08:59 [main] INFO  c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=logistics-gateway.properties, group=DEFAULT_GROUP
2025-06-29 12:08:59 [main] INFO  c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=logistics-gateway, group=DEFAULT_GROUP
2025-06-29 12:58:03 [reactor-http-nio-2] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求开始 - 方法: GET, 路径: /api/order/list, 来源: 127.0.0.1
2025-06-29 12:58:03 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 请求路径: /api/order/list, 客户端IP: 127.0.0.1
2025-06-29 12:58:03 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 检查路径 /api/order/list 是否在白名单中，白名单: [/api/user/register, /api/user/login, /api/user/test/**, /api/order/test/**, /api/logistics/test/**, /api/delivery/test/**, /api/notification/test/**, /api/map/test/**, /actuator/**, /favicon.ico]
2025-06-29 12:58:03 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/register 匹配路径 /api/order/list 结果: false
2025-06-29 12:58:03 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/login 匹配路径 /api/order/list 结果: false
2025-06-29 12:58:03 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:58:03 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/order/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:58:03 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/logistics/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:58:03 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/delivery/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:58:03 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/notification/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:58:03 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/map/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:58:03 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /actuator/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:58:03 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /favicon.ico 匹配路径 /api/order/list 结果: false
2025-06-29 12:58:03 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 路径 /api/order/list 白名单检查结果: false
2025-06-29 12:58:03 [reactor-http-nio-2] ERROR org.example.logisticsgateway.util.JwtUtil - JWT解析失败: The verification key's size is 296 bits which is not secure enough for the HS512 algorithm.  The JWT JWA Specification (RFC 7518, Section 3.2) states that keys used with HS512 MUST have a size >= 512 bits (the key size must be greater than or equal to the hash output size).  Consider using the io.jsonwebtoken.security.Keys class's 'secretKeyFor(SignatureAlgorithm.HS512)' method to create a key guaranteed to be secure enough for HS512.  See https://tools.ietf.org/html/rfc7518#section-3.2 for more information.
2025-06-29 12:58:03 [reactor-http-nio-2] WARN  org.example.logisticsgateway.util.JwtUtil - JWT token验证失败: Invalid JWT token
2025-06-29 12:58:03 [reactor-http-nio-2] WARN  o.example.logisticsgateway.filter.GlobalAuthFilter - 无效的token, 路径: /api/order/list, IP: 127.0.0.1
2025-06-29 12:58:04 [reactor-http-nio-2] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求结束 - 方法: GET, 路径: /api/order/list, 状态码: 401, 耗时: 576ms
2025-06-29 12:58:58 [reactor-http-nio-3] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求开始 - 方法: POST, 路径: /api/user/login, 来源: 127.0.0.1
2025-06-29 12:58:58 [reactor-http-nio-3] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 请求路径: /api/user/login, 客户端IP: 127.0.0.1
2025-06-29 12:58:58 [reactor-http-nio-3] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 检查路径 /api/user/login 是否在白名单中，白名单: [/api/user/register, /api/user/login, /api/user/test/**, /api/order/test/**, /api/logistics/test/**, /api/delivery/test/**, /api/notification/test/**, /api/map/test/**, /actuator/**, /favicon.ico]
2025-06-29 12:58:58 [reactor-http-nio-3] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/register 匹配路径 /api/user/login 结果: false
2025-06-29 12:58:58 [reactor-http-nio-3] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/login 匹配路径 /api/user/login 结果: true
2025-06-29 12:58:58 [reactor-http-nio-3] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 路径 /api/user/login 白名单检查结果: true
2025-06-29 12:58:58 [reactor-http-nio-3] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 白名单路径，直接放行: /api/user/login
2025-06-29 12:58:58 [lettuce-nioEventLoop-5-1] DEBUG o.example.logisticsgateway.filter.RateLimitFilter - 限流检查通过 - Key: rate_limit:127.0.0.1:_api_user_login:POST, 配置: RateLimitConfig{limit=10, windowSize=60}
2025-06-29 12:58:58 [lettuce-nioEventLoop-5-1] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求开始 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********, StartTime: 1751173138083
2025-06-29 12:59:00 [reactor-http-nio-3] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求完成 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, Status: 200, Duration: 2115ms, StartTime: 1751173138083, EndTime: 1751173140198
2025-06-29 12:59:00 [reactor-http-nio-3] WARN  org.example.logisticsgateway.filter.MonitorFilter - 发现慢请求 - Method: POST, Path: /api/user/login, Duration: 2115ms, Status: 200
2025-06-29 12:59:00 [reactor-http-nio-3] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求结束 - 方法: POST, 路径: /api/user/login, 状态码: 200, 耗时: 2266ms
2025-06-29 12:59:00 [reactor-http-nio-6] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求开始 - 方法: GET, 路径: /api/order/list, 来源: 127.0.0.1
2025-06-29 12:59:00 [reactor-http-nio-6] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 请求路径: /api/order/list, 客户端IP: 127.0.0.1
2025-06-29 12:59:00 [reactor-http-nio-6] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 检查路径 /api/order/list 是否在白名单中，白名单: [/api/user/register, /api/user/login, /api/user/test/**, /api/order/test/**, /api/logistics/test/**, /api/delivery/test/**, /api/notification/test/**, /api/map/test/**, /actuator/**, /favicon.ico]
2025-06-29 12:59:00 [reactor-http-nio-6] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/register 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:00 [reactor-http-nio-6] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/login 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:00 [reactor-http-nio-6] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:00 [reactor-http-nio-6] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/order/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:00 [reactor-http-nio-6] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/logistics/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:00 [reactor-http-nio-6] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/delivery/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:00 [reactor-http-nio-6] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/notification/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:00 [reactor-http-nio-6] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/map/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:00 [reactor-http-nio-6] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /actuator/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:00 [reactor-http-nio-6] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /favicon.ico 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:00 [reactor-http-nio-6] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 路径 /api/order/list 白名单检查结果: false
2025-06-29 12:59:00 [reactor-http-nio-6] ERROR org.example.logisticsgateway.util.JwtUtil - JWT解析失败: The verification key's size is 296 bits which is not secure enough for the HS512 algorithm.  The JWT JWA Specification (RFC 7518, Section 3.2) states that keys used with HS512 MUST have a size >= 512 bits (the key size must be greater than or equal to the hash output size).  Consider using the io.jsonwebtoken.security.Keys class's 'secretKeyFor(SignatureAlgorithm.HS512)' method to create a key guaranteed to be secure enough for HS512.  See https://tools.ietf.org/html/rfc7518#section-3.2 for more information.
2025-06-29 12:59:00 [reactor-http-nio-6] WARN  org.example.logisticsgateway.util.JwtUtil - JWT token验证失败: Invalid JWT token
2025-06-29 12:59:00 [reactor-http-nio-6] WARN  o.example.logisticsgateway.filter.GlobalAuthFilter - 无效的token, 路径: /api/order/list, IP: 127.0.0.1
2025-06-29 12:59:00 [reactor-http-nio-6] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求结束 - 方法: GET, 路径: /api/order/list, 状态码: 401, 耗时: 9ms
2025-06-29 12:59:07 [reactor-http-nio-7] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求开始 - 方法: POST, 路径: /api/user/login, 来源: 127.0.0.1
2025-06-29 12:59:07 [reactor-http-nio-7] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 请求路径: /api/user/login, 客户端IP: 127.0.0.1
2025-06-29 12:59:07 [reactor-http-nio-7] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 检查路径 /api/user/login 是否在白名单中，白名单: [/api/user/register, /api/user/login, /api/user/test/**, /api/order/test/**, /api/logistics/test/**, /api/delivery/test/**, /api/notification/test/**, /api/map/test/**, /actuator/**, /favicon.ico]
2025-06-29 12:59:07 [reactor-http-nio-7] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/register 匹配路径 /api/user/login 结果: false
2025-06-29 12:59:07 [reactor-http-nio-7] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/login 匹配路径 /api/user/login 结果: true
2025-06-29 12:59:07 [reactor-http-nio-7] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 路径 /api/user/login 白名单检查结果: true
2025-06-29 12:59:07 [reactor-http-nio-7] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 白名单路径，直接放行: /api/user/login
2025-06-29 12:59:07 [lettuce-nioEventLoop-5-1] DEBUG o.example.logisticsgateway.filter.RateLimitFilter - 限流检查通过 - Key: rate_limit:127.0.0.1:_api_user_login:POST, 配置: RateLimitConfig{limit=10, windowSize=60}
2025-06-29 12:59:07 [lettuce-nioEventLoop-5-1] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求开始 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********, StartTime: 1751173147510
2025-06-29 12:59:07 [reactor-http-nio-7] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求完成 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, Status: 200, Duration: 247ms, StartTime: 1751173147510, EndTime: 1751173147757
2025-06-29 12:59:07 [reactor-http-nio-7] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求结束 - 方法: POST, 路径: /api/user/login, 状态码: 200, 耗时: 259ms
2025-06-29 12:59:08 [reactor-http-nio-8] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求开始 - 方法: GET, 路径: /api/order/list, 来源: 127.0.0.1
2025-06-29 12:59:08 [reactor-http-nio-8] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 请求路径: /api/order/list, 客户端IP: 127.0.0.1
2025-06-29 12:59:08 [reactor-http-nio-8] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 检查路径 /api/order/list 是否在白名单中，白名单: [/api/user/register, /api/user/login, /api/user/test/**, /api/order/test/**, /api/logistics/test/**, /api/delivery/test/**, /api/notification/test/**, /api/map/test/**, /actuator/**, /favicon.ico]
2025-06-29 12:59:08 [reactor-http-nio-8] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/register 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:08 [reactor-http-nio-8] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/login 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:08 [reactor-http-nio-8] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:08 [reactor-http-nio-8] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/order/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:08 [reactor-http-nio-8] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/logistics/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:08 [reactor-http-nio-8] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/delivery/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:08 [reactor-http-nio-8] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/notification/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:08 [reactor-http-nio-8] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/map/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:08 [reactor-http-nio-8] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /actuator/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:08 [reactor-http-nio-8] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /favicon.ico 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:08 [reactor-http-nio-8] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 路径 /api/order/list 白名单检查结果: false
2025-06-29 12:59:08 [reactor-http-nio-8] ERROR org.example.logisticsgateway.util.JwtUtil - JWT解析失败: The verification key's size is 296 bits which is not secure enough for the HS512 algorithm.  The JWT JWA Specification (RFC 7518, Section 3.2) states that keys used with HS512 MUST have a size >= 512 bits (the key size must be greater than or equal to the hash output size).  Consider using the io.jsonwebtoken.security.Keys class's 'secretKeyFor(SignatureAlgorithm.HS512)' method to create a key guaranteed to be secure enough for HS512.  See https://tools.ietf.org/html/rfc7518#section-3.2 for more information.
2025-06-29 12:59:08 [reactor-http-nio-8] WARN  org.example.logisticsgateway.util.JwtUtil - JWT token验证失败: Invalid JWT token
2025-06-29 12:59:08 [reactor-http-nio-8] WARN  o.example.logisticsgateway.filter.GlobalAuthFilter - 无效的token, 路径: /api/order/list, IP: 127.0.0.1
2025-06-29 12:59:08 [reactor-http-nio-8] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求结束 - 方法: GET, 路径: /api/order/list, 状态码: 401, 耗时: 11ms
2025-06-29 12:59:12 [reactor-http-nio-9] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求开始 - 方法: POST, 路径: /api/user/login, 来源: 127.0.0.1
2025-06-29 12:59:12 [reactor-http-nio-9] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 请求路径: /api/user/login, 客户端IP: 127.0.0.1
2025-06-29 12:59:12 [reactor-http-nio-9] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 检查路径 /api/user/login 是否在白名单中，白名单: [/api/user/register, /api/user/login, /api/user/test/**, /api/order/test/**, /api/logistics/test/**, /api/delivery/test/**, /api/notification/test/**, /api/map/test/**, /actuator/**, /favicon.ico]
2025-06-29 12:59:12 [reactor-http-nio-9] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/register 匹配路径 /api/user/login 结果: false
2025-06-29 12:59:12 [reactor-http-nio-9] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/login 匹配路径 /api/user/login 结果: true
2025-06-29 12:59:12 [reactor-http-nio-9] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 路径 /api/user/login 白名单检查结果: true
2025-06-29 12:59:12 [reactor-http-nio-9] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 白名单路径，直接放行: /api/user/login
2025-06-29 12:59:12 [lettuce-nioEventLoop-5-1] DEBUG o.example.logisticsgateway.filter.RateLimitFilter - 限流检查通过 - Key: rate_limit:127.0.0.1:_api_user_login:POST, 配置: RateLimitConfig{limit=10, windowSize=60}
2025-06-29 12:59:12 [lettuce-nioEventLoop-5-1] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求开始 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********, StartTime: 1751173152232
2025-06-29 12:59:12 [reactor-http-nio-9] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求完成 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, Status: 200, Duration: 239ms, StartTime: 1751173152232, EndTime: 1751173152471
2025-06-29 12:59:12 [reactor-http-nio-9] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求结束 - 方法: POST, 路径: /api/user/login, 状态码: 200, 耗时: 251ms
2025-06-29 12:59:12 [reactor-http-nio-10] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求开始 - 方法: GET, 路径: /api/order/list, 来源: 127.0.0.1
2025-06-29 12:59:12 [reactor-http-nio-10] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 请求路径: /api/order/list, 客户端IP: 127.0.0.1
2025-06-29 12:59:12 [reactor-http-nio-10] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 检查路径 /api/order/list 是否在白名单中，白名单: [/api/user/register, /api/user/login, /api/user/test/**, /api/order/test/**, /api/logistics/test/**, /api/delivery/test/**, /api/notification/test/**, /api/map/test/**, /actuator/**, /favicon.ico]
2025-06-29 12:59:12 [reactor-http-nio-10] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/register 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:12 [reactor-http-nio-10] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/login 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:12 [reactor-http-nio-10] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:12 [reactor-http-nio-10] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/order/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:12 [reactor-http-nio-10] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/logistics/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:12 [reactor-http-nio-10] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/delivery/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:12 [reactor-http-nio-10] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/notification/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:12 [reactor-http-nio-10] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/map/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:12 [reactor-http-nio-10] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /actuator/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:12 [reactor-http-nio-10] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /favicon.ico 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:12 [reactor-http-nio-10] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 路径 /api/order/list 白名单检查结果: false
2025-06-29 12:59:12 [reactor-http-nio-10] ERROR org.example.logisticsgateway.util.JwtUtil - JWT解析失败: The verification key's size is 296 bits which is not secure enough for the HS512 algorithm.  The JWT JWA Specification (RFC 7518, Section 3.2) states that keys used with HS512 MUST have a size >= 512 bits (the key size must be greater than or equal to the hash output size).  Consider using the io.jsonwebtoken.security.Keys class's 'secretKeyFor(SignatureAlgorithm.HS512)' method to create a key guaranteed to be secure enough for HS512.  See https://tools.ietf.org/html/rfc7518#section-3.2 for more information.
2025-06-29 12:59:12 [reactor-http-nio-10] WARN  org.example.logisticsgateway.util.JwtUtil - JWT token验证失败: Invalid JWT token
2025-06-29 12:59:12 [reactor-http-nio-10] WARN  o.example.logisticsgateway.filter.GlobalAuthFilter - 无效的token, 路径: /api/order/list, IP: 127.0.0.1
2025-06-29 12:59:12 [reactor-http-nio-10] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求结束 - 方法: GET, 路径: /api/order/list, 状态码: 401, 耗时: 14ms
2025-06-29 12:59:54 [reactor-http-nio-11] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求开始 - 方法: POST, 路径: /api/user/login, 来源: 127.0.0.1
2025-06-29 12:59:54 [reactor-http-nio-11] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 请求路径: /api/user/login, 客户端IP: 127.0.0.1
2025-06-29 12:59:54 [reactor-http-nio-11] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 检查路径 /api/user/login 是否在白名单中，白名单: [/api/user/register, /api/user/login, /api/user/test/**, /api/order/test/**, /api/logistics/test/**, /api/delivery/test/**, /api/notification/test/**, /api/map/test/**, /actuator/**, /favicon.ico]
2025-06-29 12:59:54 [reactor-http-nio-11] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/register 匹配路径 /api/user/login 结果: false
2025-06-29 12:59:54 [reactor-http-nio-11] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/login 匹配路径 /api/user/login 结果: true
2025-06-29 12:59:54 [reactor-http-nio-11] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 路径 /api/user/login 白名单检查结果: true
2025-06-29 12:59:54 [reactor-http-nio-11] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 白名单路径，直接放行: /api/user/login
2025-06-29 12:59:54 [lettuce-nioEventLoop-5-1] DEBUG o.example.logisticsgateway.filter.RateLimitFilter - 限流检查通过 - Key: rate_limit:127.0.0.1:_api_user_login:POST, 配置: RateLimitConfig{limit=10, windowSize=60}
2025-06-29 12:59:54 [lettuce-nioEventLoop-5-1] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求开始 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********, StartTime: 1751173194775
2025-06-29 12:59:55 [reactor-http-nio-11] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求完成 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, Status: 200, Duration: 241ms, StartTime: 1751173194775, EndTime: 1751173195016
2025-06-29 12:59:55 [reactor-http-nio-11] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求结束 - 方法: POST, 路径: /api/user/login, 状态码: 200, 耗时: 250ms
2025-06-29 12:59:55 [reactor-http-nio-12] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求开始 - 方法: GET, 路径: /api/order/list, 来源: 127.0.0.1
2025-06-29 12:59:55 [reactor-http-nio-12] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 请求路径: /api/order/list, 客户端IP: 127.0.0.1
2025-06-29 12:59:55 [reactor-http-nio-12] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 检查路径 /api/order/list 是否在白名单中，白名单: [/api/user/register, /api/user/login, /api/user/test/**, /api/order/test/**, /api/logistics/test/**, /api/delivery/test/**, /api/notification/test/**, /api/map/test/**, /actuator/**, /favicon.ico]
2025-06-29 12:59:55 [reactor-http-nio-12] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/register 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:55 [reactor-http-nio-12] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/login 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:55 [reactor-http-nio-12] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:55 [reactor-http-nio-12] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/order/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:55 [reactor-http-nio-12] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/logistics/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:55 [reactor-http-nio-12] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/delivery/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:55 [reactor-http-nio-12] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/notification/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:55 [reactor-http-nio-12] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/map/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:55 [reactor-http-nio-12] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /actuator/** 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:55 [reactor-http-nio-12] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /favicon.ico 匹配路径 /api/order/list 结果: false
2025-06-29 12:59:55 [reactor-http-nio-12] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 路径 /api/order/list 白名单检查结果: false
2025-06-29 12:59:55 [reactor-http-nio-12] ERROR org.example.logisticsgateway.util.JwtUtil - JWT解析失败: The verification key's size is 296 bits which is not secure enough for the HS512 algorithm.  The JWT JWA Specification (RFC 7518, Section 3.2) states that keys used with HS512 MUST have a size >= 512 bits (the key size must be greater than or equal to the hash output size).  Consider using the io.jsonwebtoken.security.Keys class's 'secretKeyFor(SignatureAlgorithm.HS512)' method to create a key guaranteed to be secure enough for HS512.  See https://tools.ietf.org/html/rfc7518#section-3.2 for more information.
2025-06-29 12:59:55 [reactor-http-nio-12] WARN  org.example.logisticsgateway.util.JwtUtil - JWT token验证失败: Invalid JWT token
2025-06-29 12:59:55 [reactor-http-nio-12] WARN  o.example.logisticsgateway.filter.GlobalAuthFilter - 无效的token, 路径: /api/order/list, IP: 127.0.0.1
2025-06-29 12:59:55 [reactor-http-nio-12] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求结束 - 方法: GET, 路径: /api/order/list, 状态码: 401, 耗时: 10ms
2025-06-29 13:15:50 [reactor-http-nio-13] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求开始 - 方法: POST, 路径: /api/user/login, 来源: 127.0.0.1
2025-06-29 13:15:50 [reactor-http-nio-13] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 请求路径: /api/user/login, 客户端IP: 127.0.0.1
2025-06-29 13:15:50 [reactor-http-nio-13] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 检查路径 /api/user/login 是否在白名单中，白名单: [/api/user/register, /api/user/login, /api/user/test/**, /api/order/test/**, /api/logistics/test/**, /api/delivery/test/**, /api/notification/test/**, /api/map/test/**, /actuator/**, /favicon.ico]
2025-06-29 13:15:50 [reactor-http-nio-13] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/register 匹配路径 /api/user/login 结果: false
2025-06-29 13:15:50 [reactor-http-nio-13] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/login 匹配路径 /api/user/login 结果: true
2025-06-29 13:15:50 [reactor-http-nio-13] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 路径 /api/user/login 白名单检查结果: true
2025-06-29 13:15:50 [reactor-http-nio-13] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 白名单路径，直接放行: /api/user/login
2025-06-29 13:15:50 [lettuce-nioEventLoop-5-1] DEBUG o.example.logisticsgateway.filter.RateLimitFilter - 限流检查通过 - Key: rate_limit:127.0.0.1:_api_user_login:POST, 配置: RateLimitConfig{limit=10, windowSize=60}
2025-06-29 13:15:50 [lettuce-nioEventLoop-5-1] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求开始 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********, StartTime: 1751174150950
2025-06-29 13:15:51 [reactor-http-nio-13] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求完成 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, Status: 200, Duration: 216ms, StartTime: 1751174150950, EndTime: 1751174151166
2025-06-29 13:15:51 [reactor-http-nio-13] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求结束 - 方法: POST, 路径: /api/user/login, 状态码: 200, 耗时: 222ms
2025-06-29 13:15:51 [reactor-http-nio-15] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求开始 - 方法: GET, 路径: /api/order/list, 来源: 127.0.0.1
2025-06-29 13:15:51 [reactor-http-nio-15] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 请求路径: /api/order/list, 客户端IP: 127.0.0.1
2025-06-29 13:15:51 [reactor-http-nio-15] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 检查路径 /api/order/list 是否在白名单中，白名单: [/api/user/register, /api/user/login, /api/user/test/**, /api/order/test/**, /api/logistics/test/**, /api/delivery/test/**, /api/notification/test/**, /api/map/test/**, /actuator/**, /favicon.ico]
2025-06-29 13:15:51 [reactor-http-nio-15] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/register 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:51 [reactor-http-nio-15] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/login 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:51 [reactor-http-nio-15] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:51 [reactor-http-nio-15] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/order/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:51 [reactor-http-nio-15] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/logistics/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:51 [reactor-http-nio-15] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/delivery/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:51 [reactor-http-nio-15] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/notification/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:51 [reactor-http-nio-15] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/map/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:51 [reactor-http-nio-15] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /actuator/** 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:51 [reactor-http-nio-15] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /favicon.ico 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:51 [reactor-http-nio-15] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 路径 /api/order/list 白名单检查结果: false
2025-06-29 13:15:51 [reactor-http-nio-15] ERROR org.example.logisticsgateway.util.JwtUtil - JWT解析失败: The verification key's size is 296 bits which is not secure enough for the HS512 algorithm.  The JWT JWA Specification (RFC 7518, Section 3.2) states that keys used with HS512 MUST have a size >= 512 bits (the key size must be greater than or equal to the hash output size).  Consider using the io.jsonwebtoken.security.Keys class's 'secretKeyFor(SignatureAlgorithm.HS512)' method to create a key guaranteed to be secure enough for HS512.  See https://tools.ietf.org/html/rfc7518#section-3.2 for more information.
2025-06-29 13:15:51 [reactor-http-nio-15] WARN  org.example.logisticsgateway.util.JwtUtil - JWT token验证失败: Invalid JWT token
2025-06-29 13:15:51 [reactor-http-nio-15] WARN  o.example.logisticsgateway.filter.GlobalAuthFilter - 无效的token, 路径: /api/order/list, IP: 127.0.0.1
2025-06-29 13:15:51 [reactor-http-nio-15] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求结束 - 方法: GET, 路径: /api/order/list, 状态码: 401, 耗时: 7ms
2025-06-29 13:15:53 [reactor-http-nio-16] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求开始 - 方法: POST, 路径: /api/user/login, 来源: 127.0.0.1
2025-06-29 13:15:53 [reactor-http-nio-16] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 请求路径: /api/user/login, 客户端IP: 127.0.0.1
2025-06-29 13:15:53 [reactor-http-nio-16] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 检查路径 /api/user/login 是否在白名单中，白名单: [/api/user/register, /api/user/login, /api/user/test/**, /api/order/test/**, /api/logistics/test/**, /api/delivery/test/**, /api/notification/test/**, /api/map/test/**, /actuator/**, /favicon.ico]
2025-06-29 13:15:53 [reactor-http-nio-16] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/register 匹配路径 /api/user/login 结果: false
2025-06-29 13:15:53 [reactor-http-nio-16] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/login 匹配路径 /api/user/login 结果: true
2025-06-29 13:15:53 [reactor-http-nio-16] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 路径 /api/user/login 白名单检查结果: true
2025-06-29 13:15:53 [reactor-http-nio-16] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 白名单路径，直接放行: /api/user/login
2025-06-29 13:15:53 [lettuce-nioEventLoop-5-1] DEBUG o.example.logisticsgateway.filter.RateLimitFilter - 限流检查通过 - Key: rate_limit:127.0.0.1:_api_user_login:POST, 配置: RateLimitConfig{limit=10, windowSize=60}
2025-06-29 13:15:53 [lettuce-nioEventLoop-5-1] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求开始 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********, StartTime: 1751174153165
2025-06-29 13:15:53 [reactor-http-nio-16] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求完成 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, Status: 200, Duration: 195ms, StartTime: 1751174153165, EndTime: 1751174153360
2025-06-29 13:15:53 [reactor-http-nio-16] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求结束 - 方法: POST, 路径: /api/user/login, 状态码: 200, 耗时: 204ms
2025-06-29 13:15:53 [reactor-http-nio-17] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求开始 - 方法: GET, 路径: /api/order/list, 来源: 127.0.0.1
2025-06-29 13:15:53 [reactor-http-nio-17] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 请求路径: /api/order/list, 客户端IP: 127.0.0.1
2025-06-29 13:15:53 [reactor-http-nio-17] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 检查路径 /api/order/list 是否在白名单中，白名单: [/api/user/register, /api/user/login, /api/user/test/**, /api/order/test/**, /api/logistics/test/**, /api/delivery/test/**, /api/notification/test/**, /api/map/test/**, /actuator/**, /favicon.ico]
2025-06-29 13:15:53 [reactor-http-nio-17] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/register 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:53 [reactor-http-nio-17] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/login 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:53 [reactor-http-nio-17] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:53 [reactor-http-nio-17] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/order/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:53 [reactor-http-nio-17] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/logistics/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:53 [reactor-http-nio-17] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/delivery/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:53 [reactor-http-nio-17] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/notification/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:53 [reactor-http-nio-17] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/map/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:53 [reactor-http-nio-17] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /actuator/** 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:53 [reactor-http-nio-17] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /favicon.ico 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:53 [reactor-http-nio-17] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 路径 /api/order/list 白名单检查结果: false
2025-06-29 13:15:53 [reactor-http-nio-17] ERROR org.example.logisticsgateway.util.JwtUtil - JWT解析失败: The verification key's size is 296 bits which is not secure enough for the HS512 algorithm.  The JWT JWA Specification (RFC 7518, Section 3.2) states that keys used with HS512 MUST have a size >= 512 bits (the key size must be greater than or equal to the hash output size).  Consider using the io.jsonwebtoken.security.Keys class's 'secretKeyFor(SignatureAlgorithm.HS512)' method to create a key guaranteed to be secure enough for HS512.  See https://tools.ietf.org/html/rfc7518#section-3.2 for more information.
2025-06-29 13:15:53 [reactor-http-nio-17] WARN  org.example.logisticsgateway.util.JwtUtil - JWT token验证失败: Invalid JWT token
2025-06-29 13:15:53 [reactor-http-nio-17] WARN  o.example.logisticsgateway.filter.GlobalAuthFilter - 无效的token, 路径: /api/order/list, IP: 127.0.0.1
2025-06-29 13:15:53 [reactor-http-nio-17] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求结束 - 方法: GET, 路径: /api/order/list, 状态码: 401, 耗时: 7ms
2025-06-29 13:15:57 [reactor-http-nio-18] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求开始 - 方法: POST, 路径: /api/user/login, 来源: 127.0.0.1
2025-06-29 13:15:57 [reactor-http-nio-18] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 请求路径: /api/user/login, 客户端IP: 127.0.0.1
2025-06-29 13:15:57 [reactor-http-nio-18] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 检查路径 /api/user/login 是否在白名单中，白名单: [/api/user/register, /api/user/login, /api/user/test/**, /api/order/test/**, /api/logistics/test/**, /api/delivery/test/**, /api/notification/test/**, /api/map/test/**, /actuator/**, /favicon.ico]
2025-06-29 13:15:57 [reactor-http-nio-18] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/register 匹配路径 /api/user/login 结果: false
2025-06-29 13:15:57 [reactor-http-nio-18] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/login 匹配路径 /api/user/login 结果: true
2025-06-29 13:15:57 [reactor-http-nio-18] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 路径 /api/user/login 白名单检查结果: true
2025-06-29 13:15:57 [reactor-http-nio-18] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 白名单路径，直接放行: /api/user/login
2025-06-29 13:15:57 [lettuce-nioEventLoop-5-1] DEBUG o.example.logisticsgateway.filter.RateLimitFilter - 限流检查通过 - Key: rate_limit:127.0.0.1:_api_user_login:POST, 配置: RateLimitConfig{limit=10, windowSize=60}
2025-06-29 13:15:57 [lettuce-nioEventLoop-5-1] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求开始 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********, StartTime: 1751174157527
2025-06-29 13:15:57 [reactor-http-nio-18] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求完成 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, Status: 200, Duration: 237ms, StartTime: 1751174157527, EndTime: 1751174157764
2025-06-29 13:15:57 [reactor-http-nio-18] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求结束 - 方法: POST, 路径: /api/user/login, 状态码: 200, 耗时: 245ms
2025-06-29 13:15:57 [reactor-http-nio-19] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求开始 - 方法: GET, 路径: /api/order/list, 来源: 127.0.0.1
2025-06-29 13:15:57 [reactor-http-nio-19] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 请求路径: /api/order/list, 客户端IP: 127.0.0.1
2025-06-29 13:15:57 [reactor-http-nio-19] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 检查路径 /api/order/list 是否在白名单中，白名单: [/api/user/register, /api/user/login, /api/user/test/**, /api/order/test/**, /api/logistics/test/**, /api/delivery/test/**, /api/notification/test/**, /api/map/test/**, /actuator/**, /favicon.ico]
2025-06-29 13:15:57 [reactor-http-nio-19] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/register 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:57 [reactor-http-nio-19] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/login 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:57 [reactor-http-nio-19] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:57 [reactor-http-nio-19] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/order/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:57 [reactor-http-nio-19] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/logistics/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:57 [reactor-http-nio-19] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/delivery/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:57 [reactor-http-nio-19] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/notification/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:57 [reactor-http-nio-19] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/map/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:57 [reactor-http-nio-19] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /actuator/** 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:57 [reactor-http-nio-19] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /favicon.ico 匹配路径 /api/order/list 结果: false
2025-06-29 13:15:57 [reactor-http-nio-19] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 路径 /api/order/list 白名单检查结果: false
2025-06-29 13:15:57 [reactor-http-nio-19] ERROR org.example.logisticsgateway.util.JwtUtil - JWT解析失败: The verification key's size is 296 bits which is not secure enough for the HS512 algorithm.  The JWT JWA Specification (RFC 7518, Section 3.2) states that keys used with HS512 MUST have a size >= 512 bits (the key size must be greater than or equal to the hash output size).  Consider using the io.jsonwebtoken.security.Keys class's 'secretKeyFor(SignatureAlgorithm.HS512)' method to create a key guaranteed to be secure enough for HS512.  See https://tools.ietf.org/html/rfc7518#section-3.2 for more information.
2025-06-29 13:15:57 [reactor-http-nio-19] WARN  org.example.logisticsgateway.util.JwtUtil - JWT token验证失败: Invalid JWT token
2025-06-29 13:15:57 [reactor-http-nio-19] WARN  o.example.logisticsgateway.filter.GlobalAuthFilter - 无效的token, 路径: /api/order/list, IP: 127.0.0.1
2025-06-29 13:15:57 [reactor-http-nio-19] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求结束 - 方法: GET, 路径: /api/order/list, 状态码: 401, 耗时: 8ms
2025-06-29 13:16:48 [reactor-http-nio-20] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求开始 - 方法: POST, 路径: /api/user/login, 来源: 127.0.0.1
2025-06-29 13:16:48 [reactor-http-nio-20] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 请求路径: /api/user/login, 客户端IP: 127.0.0.1
2025-06-29 13:16:48 [reactor-http-nio-20] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 检查路径 /api/user/login 是否在白名单中，白名单: [/api/user/register, /api/user/login, /api/user/test/**, /api/order/test/**, /api/logistics/test/**, /api/delivery/test/**, /api/notification/test/**, /api/map/test/**, /actuator/**, /favicon.ico]
2025-06-29 13:16:48 [reactor-http-nio-20] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/register 匹配路径 /api/user/login 结果: false
2025-06-29 13:16:48 [reactor-http-nio-20] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/login 匹配路径 /api/user/login 结果: true
2025-06-29 13:16:48 [reactor-http-nio-20] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 路径 /api/user/login 白名单检查结果: true
2025-06-29 13:16:48 [reactor-http-nio-20] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 白名单路径，直接放行: /api/user/login
2025-06-29 13:16:48 [lettuce-nioEventLoop-5-1] DEBUG o.example.logisticsgateway.filter.RateLimitFilter - 限流检查通过 - Key: rate_limit:127.0.0.1:_api_user_login:POST, 配置: RateLimitConfig{limit=10, windowSize=60}
2025-06-29 13:16:48 [lettuce-nioEventLoop-5-1] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求开始 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********, StartTime: 1751174208985
2025-06-29 13:16:49 [reactor-http-nio-20] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求完成 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, Status: 200, Duration: 217ms, StartTime: 1751174208985, EndTime: 1751174209202
2025-06-29 13:16:49 [reactor-http-nio-20] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求结束 - 方法: POST, 路径: /api/user/login, 状态码: 200, 耗时: 225ms
2025-06-29 13:16:49 [reactor-http-nio-1] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求开始 - 方法: GET, 路径: /api/order/list, 来源: 127.0.0.1
2025-06-29 13:16:49 [reactor-http-nio-1] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 请求路径: /api/order/list, 客户端IP: 127.0.0.1
2025-06-29 13:16:49 [reactor-http-nio-1] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 检查路径 /api/order/list 是否在白名单中，白名单: [/api/user/register, /api/user/login, /api/user/test/**, /api/order/test/**, /api/logistics/test/**, /api/delivery/test/**, /api/notification/test/**, /api/map/test/**, /actuator/**, /favicon.ico]
2025-06-29 13:16:49 [reactor-http-nio-1] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/register 匹配路径 /api/order/list 结果: false
2025-06-29 13:16:49 [reactor-http-nio-1] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/login 匹配路径 /api/order/list 结果: false
2025-06-29 13:16:49 [reactor-http-nio-1] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 13:16:49 [reactor-http-nio-1] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/order/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 13:16:49 [reactor-http-nio-1] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/logistics/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 13:16:49 [reactor-http-nio-1] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/delivery/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 13:16:49 [reactor-http-nio-1] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/notification/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 13:16:49 [reactor-http-nio-1] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/map/test/** 匹配路径 /api/order/list 结果: false
2025-06-29 13:16:49 [reactor-http-nio-1] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /actuator/** 匹配路径 /api/order/list 结果: false
2025-06-29 13:16:49 [reactor-http-nio-1] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /favicon.ico 匹配路径 /api/order/list 结果: false
2025-06-29 13:16:49 [reactor-http-nio-1] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 路径 /api/order/list 白名单检查结果: false
2025-06-29 13:16:49 [reactor-http-nio-1] ERROR org.example.logisticsgateway.util.JwtUtil - JWT解析失败: The verification key's size is 296 bits which is not secure enough for the HS512 algorithm.  The JWT JWA Specification (RFC 7518, Section 3.2) states that keys used with HS512 MUST have a size >= 512 bits (the key size must be greater than or equal to the hash output size).  Consider using the io.jsonwebtoken.security.Keys class's 'secretKeyFor(SignatureAlgorithm.HS512)' method to create a key guaranteed to be secure enough for HS512.  See https://tools.ietf.org/html/rfc7518#section-3.2 for more information.
2025-06-29 13:16:49 [reactor-http-nio-1] WARN  org.example.logisticsgateway.util.JwtUtil - JWT token验证失败: Invalid JWT token
2025-06-29 13:16:49 [reactor-http-nio-1] WARN  o.example.logisticsgateway.filter.GlobalAuthFilter - 无效的token, 路径: /api/order/list, IP: 127.0.0.1
2025-06-29 13:16:49 [reactor-http-nio-1] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求结束 - 方法: GET, 路径: /api/order/list, 状态码: 401, 耗时: 9ms
2025-06-29 13:26:28 [Thread-1] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-29 13:26:28 [Thread-7] WARN  com.alibaba.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-29 13:26:28 [Thread-7] WARN  com.alibaba.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-06-29 13:26:28 [Thread-1] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-06-29 13:26:28 [SpringApplicationShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-29 13:26:28 [SpringApplicationShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registration finished.
2025-06-29 13:26:42 [main] WARN  c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[logistics-gateway] & group[DEFAULT_GROUP]
2025-06-29 13:26:42 [main] WARN  c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[logistics-gateway.properties] & group[DEFAULT_GROUP]
2025-06-29 13:26:42 [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-logistics-gateway.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-logistics-gateway,DEFAULT_GROUP'}]
2025-06-29 13:26:42 [main] INFO  o.e.logisticsgateway.LogisticsGatewayApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-29 13:26:44 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-29 13:26:44 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-29 13:26:44 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-06-29 13:26:44 [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=0fd42190-0a66-3d55-a0e7-3dd72daba566
2025-06-29 13:26:44 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-29 13:26:44 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-29 13:26:44 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-29 13:26:49 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [After]
2025-06-29 13:26:49 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Before]
2025-06-29 13:26:49 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Between]
2025-06-29 13:26:49 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Cookie]
2025-06-29 13:26:49 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Header]
2025-06-29 13:26:49 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Host]
2025-06-29 13:26:49 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Method]
2025-06-29 13:26:49 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Path]
2025-06-29 13:26:49 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Query]
2025-06-29 13:26:49 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [ReadBody]
2025-06-29 13:26:49 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [RemoteAddr]
2025-06-29 13:26:49 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-06-29 13:26:49 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Weight]
2025-06-29 13:26:49 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-29 13:26:49 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-29 13:26:49 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-29 13:26:50 [main] INFO  o.s.boot.web.embedded.netty.NettyWebServer - Netty started on port 8080
2025-06-29 13:26:51 [main] INFO  c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-06-29 13:26:51 [main] INFO  c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-06-29 13:26:51 [main] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - nacos registry, DEFAULT_GROUP logistics-gateway 192.168.32.1:8080 register finished
2025-06-29 13:26:51 [main] INFO  c.a.c.n.discovery.GatewayLocatorHeartBeatPublisher - Start nacos gateway locator heartBeat task scheduler.
2025-06-29 13:26:51 [main] INFO  o.e.logisticsgateway.LogisticsGatewayApplication - Started LogisticsGatewayApplication in 14.282 seconds (JVM running for 15.443)
2025-06-29 13:26:51 [main] INFO  c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=logistics-gateway.properties, group=DEFAULT_GROUP
2025-06-29 13:26:51 [main] INFO  c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=logistics-gateway, group=DEFAULT_GROUP
2025-06-29 13:28:41 [reactor-http-nio-2] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求开始 - 方法: POST, 路径: /api/user/login, 来源: 127.0.0.1
2025-06-29 13:28:41 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 请求路径: /api/user/login, 客户端IP: 127.0.0.1
2025-06-29 13:28:41 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 检查路径 /api/user/login 是否在白名单中，白名单: [/api/user/register, /api/user/login, /api/user/test/**, /api/order/test/**, /api/logistics/test/**, /api/delivery/test/**, /api/notification/test/**, /api/map/test/**, /actuator/**, /favicon.ico]
2025-06-29 13:28:41 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/register 匹配路径 /api/user/login 结果: false
2025-06-29 13:28:41 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/login 匹配路径 /api/user/login 结果: true
2025-06-29 13:28:41 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 路径 /api/user/login 白名单检查结果: true
2025-06-29 13:28:41 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 白名单路径，直接放行: /api/user/login
2025-06-29 13:28:41 [lettuce-nioEventLoop-5-1] DEBUG o.example.logisticsgateway.filter.RateLimitFilter - 限流检查通过 - Key: rate_limit:127.0.0.1:_api_user_login:POST, 配置: RateLimitConfig{limit=10, windowSize=60}
2025-06-29 13:28:41 [lettuce-nioEventLoop-5-1] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求开始 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********, StartTime: 1751174921344
2025-06-29 13:28:41 [lettuce-nioEventLoop-5-1] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求完成 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, Status: 403, Duration: 15ms, StartTime: 1751174921344, EndTime: 1751174921359
2025-06-29 13:28:41 [lettuce-nioEventLoop-5-1] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求结束 - 方法: POST, 路径: /api/user/login, 状态码: 403, 耗时: 74ms
2025-06-29 13:29:54 [reactor-http-nio-3] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求开始 - 方法: POST, 路径: /api/user/login, 来源: 127.0.0.1
2025-06-29 13:29:54 [reactor-http-nio-3] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 请求路径: /api/user/login, 客户端IP: 127.0.0.1
2025-06-29 13:29:54 [reactor-http-nio-3] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 检查路径 /api/user/login 是否在白名单中，白名单: [/api/user/register, /api/user/login, /api/user/test/**, /api/order/test/**, /api/logistics/test/**, /api/delivery/test/**, /api/notification/test/**, /api/map/test/**, /actuator/**, /favicon.ico]
2025-06-29 13:29:54 [reactor-http-nio-3] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/register 匹配路径 /api/user/login 结果: false
2025-06-29 13:29:54 [reactor-http-nio-3] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/login 匹配路径 /api/user/login 结果: true
2025-06-29 13:29:54 [reactor-http-nio-3] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 路径 /api/user/login 白名单检查结果: true
2025-06-29 13:29:54 [reactor-http-nio-3] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 白名单路径，直接放行: /api/user/login
2025-06-29 13:29:54 [lettuce-nioEventLoop-5-1] DEBUG o.example.logisticsgateway.filter.RateLimitFilter - 限流检查通过 - Key: rate_limit:127.0.0.1:_api_user_login:POST, 配置: RateLimitConfig{limit=10, windowSize=60}
2025-06-29 13:29:54 [lettuce-nioEventLoop-5-1] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求开始 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********, StartTime: 1751174994663
2025-06-29 13:29:54 [lettuce-nioEventLoop-5-1] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求完成 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, Status: 403, Duration: 1ms, StartTime: 1751174994663, EndTime: 1751174994664
2025-06-29 13:29:54 [lettuce-nioEventLoop-5-1] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求结束 - 方法: POST, 路径: /api/user/login, 状态码: 403, 耗时: 22ms
2025-06-29 13:30:49 [reactor-http-nio-4] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求开始 - 方法: POST, 路径: /api/user/login, 来源: 127.0.0.1
2025-06-29 13:30:49 [reactor-http-nio-4] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 请求路径: /api/user/login, 客户端IP: 127.0.0.1
2025-06-29 13:30:49 [reactor-http-nio-4] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 检查路径 /api/user/login 是否在白名单中，白名单: [/api/user/register, /api/user/login, /api/user/test/**, /api/order/test/**, /api/logistics/test/**, /api/delivery/test/**, /api/notification/test/**, /api/map/test/**, /actuator/**, /favicon.ico]
2025-06-29 13:30:49 [reactor-http-nio-4] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/register 匹配路径 /api/user/login 结果: false
2025-06-29 13:30:49 [reactor-http-nio-4] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/login 匹配路径 /api/user/login 结果: true
2025-06-29 13:30:49 [reactor-http-nio-4] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 路径 /api/user/login 白名单检查结果: true
2025-06-29 13:30:49 [reactor-http-nio-4] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 白名单路径，直接放行: /api/user/login
2025-06-29 13:30:49 [lettuce-nioEventLoop-5-1] DEBUG o.example.logisticsgateway.filter.RateLimitFilter - 限流检查通过 - Key: rate_limit:127.0.0.1:_api_user_login:POST, 配置: RateLimitConfig{limit=10, windowSize=60}
2025-06-29 13:30:49 [lettuce-nioEventLoop-5-1] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求开始 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********, StartTime: 1751175049104
2025-06-29 13:30:49 [lettuce-nioEventLoop-5-1] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求完成 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, Status: 403, Duration: 1ms, StartTime: 1751175049104, EndTime: 1751175049105
2025-06-29 13:30:49 [lettuce-nioEventLoop-5-1] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求结束 - 方法: POST, 路径: /api/user/login, 状态码: 403, 耗时: 13ms
2025-06-29 14:05:49 [Thread-7] WARN  com.alibaba.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-29 14:05:49 [Thread-1] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-29 14:05:49 [Thread-7] WARN  com.alibaba.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-06-29 14:05:49 [Thread-1] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-06-29 14:05:49 [SpringApplicationShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-29 14:05:49 [SpringApplicationShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registration finished.
2025-06-29 14:09:54 [main] WARN  c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[logistics-gateway] & group[DEFAULT_GROUP]
2025-06-29 14:09:54 [main] WARN  c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[logistics-gateway.properties] & group[DEFAULT_GROUP]
2025-06-29 14:09:54 [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-logistics-gateway.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-logistics-gateway,DEFAULT_GROUP'}]
2025-06-29 14:09:54 [main] INFO  o.e.logisticsgateway.LogisticsGatewayApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-29 14:09:56 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-29 14:09:56 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-29 14:09:56 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-06-29 14:09:56 [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=0fd42190-0a66-3d55-a0e7-3dd72daba566
2025-06-29 14:09:56 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-29 14:09:56 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-29 14:09:56 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-29 14:10:02 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [After]
2025-06-29 14:10:02 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Before]
2025-06-29 14:10:02 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Between]
2025-06-29 14:10:02 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Cookie]
2025-06-29 14:10:02 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Header]
2025-06-29 14:10:02 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Host]
2025-06-29 14:10:02 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Method]
2025-06-29 14:10:02 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Path]
2025-06-29 14:10:02 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Query]
2025-06-29 14:10:02 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [ReadBody]
2025-06-29 14:10:02 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [RemoteAddr]
2025-06-29 14:10:02 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-06-29 14:10:02 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Weight]
2025-06-29 14:10:02 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-29 14:10:02 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-29 14:10:02 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-29 14:10:03 [main] INFO  o.s.boot.web.embedded.netty.NettyWebServer - Netty started on port 8080
2025-06-29 14:10:05 [main] INFO  c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-06-29 14:10:05 [main] INFO  c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-06-29 14:10:05 [main] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - nacos registry, DEFAULT_GROUP logistics-gateway 192.168.32.1:8080 register finished
2025-06-29 14:10:05 [main] INFO  c.a.c.n.discovery.GatewayLocatorHeartBeatPublisher - Start nacos gateway locator heartBeat task scheduler.
2025-06-29 14:10:05 [main] INFO  o.e.logisticsgateway.LogisticsGatewayApplication - Started LogisticsGatewayApplication in 16.516 seconds (JVM running for 17.925)
2025-06-29 14:10:05 [main] INFO  c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=logistics-gateway.properties, group=DEFAULT_GROUP
2025-06-29 14:10:05 [main] INFO  c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=logistics-gateway, group=DEFAULT_GROUP
2025-06-29 14:10:55 [reactor-http-nio-2] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求开始 - 方法: POST, 路径: /api/user/login, 来源: 127.0.0.1
2025-06-29 14:10:55 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 请求路径: /api/user/login, 客户端IP: 127.0.0.1
2025-06-29 14:10:55 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 检查路径 /api/user/login 是否在白名单中，白名单: [/user/register, /user/login, /user/test/**, /order/test/**, /logistics/test/**, /delivery/test/**, /notification/test/**, /map/test/**, /actuator/**, /favicon.ico]
2025-06-29 14:10:55 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /user/register 匹配路径 /api/user/login 结果: false
2025-06-29 14:10:55 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /user/login 匹配路径 /api/user/login 结果: false
2025-06-29 14:10:55 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /user/test/** 匹配路径 /api/user/login 结果: false
2025-06-29 14:10:55 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /order/test/** 匹配路径 /api/user/login 结果: false
2025-06-29 14:10:55 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /logistics/test/** 匹配路径 /api/user/login 结果: false
2025-06-29 14:10:55 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /delivery/test/** 匹配路径 /api/user/login 结果: false
2025-06-29 14:10:55 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /notification/test/** 匹配路径 /api/user/login 结果: false
2025-06-29 14:10:55 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /map/test/** 匹配路径 /api/user/login 结果: false
2025-06-29 14:10:55 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /actuator/** 匹配路径 /api/user/login 结果: false
2025-06-29 14:10:55 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /favicon.ico 匹配路径 /api/user/login 结果: false
2025-06-29 14:10:55 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 路径 /api/user/login 白名单检查结果: false
2025-06-29 14:10:55 [reactor-http-nio-2] WARN  o.example.logisticsgateway.filter.GlobalAuthFilter - 未提供token, 路径: /api/user/login, IP: 127.0.0.1
2025-06-29 14:10:55 [reactor-http-nio-2] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求结束 - 方法: POST, 路径: /api/user/login, 状态码: 401, 耗时: 374ms
2025-06-29 14:15:04 [Thread-1] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-29 14:15:04 [Thread-7] WARN  com.alibaba.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-29 14:15:04 [Thread-7] WARN  com.alibaba.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-06-29 14:15:04 [Thread-1] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-06-29 14:15:04 [SpringApplicationShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-29 14:15:04 [SpringApplicationShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registration finished.
2025-06-29 14:15:15 [main] WARN  c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[logistics-gateway] & group[DEFAULT_GROUP]
2025-06-29 14:15:15 [main] WARN  c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[logistics-gateway.properties] & group[DEFAULT_GROUP]
2025-06-29 14:15:15 [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-logistics-gateway.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-logistics-gateway,DEFAULT_GROUP'}]
2025-06-29 14:15:15 [main] INFO  o.e.logisticsgateway.LogisticsGatewayApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-29 14:15:16 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-29 14:15:16 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-29 14:15:16 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.
2025-06-29 14:15:16 [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=0fd42190-0a66-3d55-a0e7-3dd72daba566
2025-06-29 14:15:17 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-29 14:15:17 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-29 14:15:17 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-29 14:15:23 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [After]
2025-06-29 14:15:23 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Before]
2025-06-29 14:15:23 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Between]
2025-06-29 14:15:23 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Cookie]
2025-06-29 14:15:23 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Header]
2025-06-29 14:15:23 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Host]
2025-06-29 14:15:23 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Method]
2025-06-29 14:15:23 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Path]
2025-06-29 14:15:23 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Query]
2025-06-29 14:15:23 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [ReadBody]
2025-06-29 14:15:23 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [RemoteAddr]
2025-06-29 14:15:23 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-06-29 14:15:23 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Weight]
2025-06-29 14:15:23 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-29 14:15:24 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-29 14:15:24 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-29 14:15:25 [main] INFO  o.s.boot.web.embedded.netty.NettyWebServer - Netty started on port 8080
2025-06-29 14:15:27 [main] INFO  c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-06-29 14:15:27 [main] INFO  c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-06-29 14:15:27 [main] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - nacos registry, DEFAULT_GROUP logistics-gateway 192.168.32.1:8080 register finished
2025-06-29 14:15:27 [main] INFO  c.a.c.n.discovery.GatewayLocatorHeartBeatPublisher - Start nacos gateway locator heartBeat task scheduler.
2025-06-29 14:15:28 [main] INFO  o.e.logisticsgateway.LogisticsGatewayApplication - Started LogisticsGatewayApplication in 18.221 seconds (JVM running for 19.461)
2025-06-29 14:15:28 [main] INFO  c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=logistics-gateway.properties, group=DEFAULT_GROUP
2025-06-29 14:15:28 [main] INFO  c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=logistics-gateway, group=DEFAULT_GROUP
2025-06-29 14:15:35 [reactor-http-nio-2] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求开始 - 方法: POST, 路径: /api/user/login, 来源: 127.0.0.1
2025-06-29 14:15:35 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 请求路径: /api/user/login, 客户端IP: 127.0.0.1
2025-06-29 14:15:35 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 检查路径 /api/user/login 是否在白名单中，白名单: [/api/user/register, /api/user/login, /api/user/test/**, /api/order/test/**, /api/logistics/test/**, /api/delivery/test/**, /api/notification/test/**, /api/map/test/**, /actuator/**, /favicon.ico]
2025-06-29 14:15:35 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/register 匹配路径 /api/user/login 结果: false
2025-06-29 14:15:35 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/login 匹配路径 /api/user/login 结果: true
2025-06-29 14:15:35 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 路径 /api/user/login 白名单检查结果: true
2025-06-29 14:15:35 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 白名单路径，直接放行: /api/user/login
2025-06-29 14:15:35 [lettuce-nioEventLoop-5-1] DEBUG o.example.logisticsgateway.filter.RateLimitFilter - 限流检查通过 - Key: rate_limit:127.0.0.1:_api_user_login:POST, 配置: RateLimitConfig{limit=10, windowSize=60}
2025-06-29 14:15:35 [lettuce-nioEventLoop-5-1] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求开始 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********, StartTime: 1751177735769
2025-06-29 14:15:35 [lettuce-nioEventLoop-5-1] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求完成 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, Status: 403, Duration: 29ms, StartTime: 1751177735769, EndTime: 1751177735798
2025-06-29 14:15:35 [lettuce-nioEventLoop-5-1] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求结束 - 方法: POST, 路径: /api/user/login, 状态码: 403, 耗时: 160ms
2025-06-29 14:20:43 [reactor-http-nio-3] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求开始 - 方法: POST, 路径: /api/user/login, 来源: 127.0.0.1
2025-06-29 14:20:43 [reactor-http-nio-3] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 请求路径: /api/user/login, 客户端IP: 127.0.0.1
2025-06-29 14:20:43 [reactor-http-nio-3] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 检查路径 /api/user/login 是否在白名单中，白名单: [/api/user/register, /api/user/login, /api/user/test/**, /api/order/test/**, /api/logistics/test/**, /api/delivery/test/**, /api/notification/test/**, /api/map/test/**, /actuator/**, /favicon.ico]
2025-06-29 14:20:43 [reactor-http-nio-3] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/register 匹配路径 /api/user/login 结果: false
2025-06-29 14:20:43 [reactor-http-nio-3] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/login 匹配路径 /api/user/login 结果: true
2025-06-29 14:20:43 [reactor-http-nio-3] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 路径 /api/user/login 白名单检查结果: true
2025-06-29 14:20:43 [reactor-http-nio-3] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 白名单路径，直接放行: /api/user/login
2025-06-29 14:20:43 [lettuce-nioEventLoop-5-1] DEBUG o.example.logisticsgateway.filter.RateLimitFilter - 限流检查通过 - Key: rate_limit:127.0.0.1:_api_user_login:POST, 配置: RateLimitConfig{limit=10, windowSize=60}
2025-06-29 14:20:43 [lettuce-nioEventLoop-5-1] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求开始 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********, StartTime: 1751178043080
2025-06-29 14:20:43 [lettuce-nioEventLoop-5-1] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求完成 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, Status: 403, Duration: 1ms, StartTime: 1751178043080, EndTime: 1751178043081
2025-06-29 14:20:43 [lettuce-nioEventLoop-5-1] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求结束 - 方法: POST, 路径: /api/user/login, 状态码: 403, 耗时: 14ms
2025-06-29 14:24:50 [Thread-7] WARN  com.alibaba.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-29 14:24:50 [Thread-7] WARN  com.alibaba.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-06-29 14:24:50 [Thread-1] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-29 14:24:50 [Thread-1] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-06-29 14:24:50 [SpringApplicationShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-29 14:24:50 [SpringApplicationShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registration finished.
2025-06-29 14:25:03 [main] WARN  c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[logistics-gateway] & group[DEFAULT_GROUP]
2025-06-29 14:25:03 [main] WARN  c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[logistics-gateway.properties] & group[DEFAULT_GROUP]
2025-06-29 14:25:03 [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-logistics-gateway.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-logistics-gateway,DEFAULT_GROUP'}]
2025-06-29 14:25:03 [main] INFO  o.e.logisticsgateway.LogisticsGatewayApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-29 14:25:04 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-29 14:25:04 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-29 14:25:04 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-06-29 14:25:05 [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=8c2e980b-ee1f-38df-b1f0-1a6d11681a1c
2025-06-29 14:25:05 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-29 14:25:05 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-29 14:25:05 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-29 14:25:10 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [After]
2025-06-29 14:25:10 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Before]
2025-06-29 14:25:10 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Between]
2025-06-29 14:25:10 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Cookie]
2025-06-29 14:25:10 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Header]
2025-06-29 14:25:10 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Host]
2025-06-29 14:25:10 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Method]
2025-06-29 14:25:10 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Path]
2025-06-29 14:25:10 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Query]
2025-06-29 14:25:10 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [ReadBody]
2025-06-29 14:25:10 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [RemoteAddr]
2025-06-29 14:25:10 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-06-29 14:25:10 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Weight]
2025-06-29 14:25:10 [main] INFO  o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-29 14:25:10 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-29 14:25:11 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-29 14:25:11 [main] INFO  o.s.boot.web.embedded.netty.NettyWebServer - Netty started on port 8080
2025-06-29 14:25:13 [main] INFO  c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-06-29 14:25:13 [main] INFO  c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-06-29 14:25:14 [main] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - nacos registry, DEFAULT_GROUP logistics-gateway 192.168.32.1:8080 register finished
2025-06-29 14:25:14 [main] INFO  c.a.c.n.discovery.GatewayLocatorHeartBeatPublisher - Start nacos gateway locator heartBeat task scheduler.
2025-06-29 14:25:14 [main] INFO  o.e.logisticsgateway.LogisticsGatewayApplication - Started LogisticsGatewayApplication in 15.902 seconds (JVM running for 16.965)
2025-06-29 14:25:14 [main] INFO  c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=logistics-gateway.properties, group=DEFAULT_GROUP
2025-06-29 14:25:14 [main] INFO  c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=logistics-gateway, group=DEFAULT_GROUP
2025-06-29 14:25:24 [reactor-http-nio-2] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求开始 - 方法: POST, 路径: /api/user/login, 来源: 127.0.0.1
2025-06-29 14:25:24 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 请求路径: /api/user/login, 客户端IP: 127.0.0.1
2025-06-29 14:25:24 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 检查路径 /api/user/login 是否在白名单中，白名单: [/api/user/register, /api/user/login, /api/user/test/**, /api/order/test/**, /api/logistics/test/**, /api/delivery/test/**, /api/notification/test/**, /api/map/test/**, /actuator/**, /favicon.ico]
2025-06-29 14:25:24 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/register 匹配路径 /api/user/login 结果: false
2025-06-29 14:25:24 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 模式 /api/user/login 匹配路径 /api/user/login 结果: true
2025-06-29 14:25:24 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 路径 /api/user/login 白名单检查结果: true
2025-06-29 14:25:24 [reactor-http-nio-2] DEBUG o.example.logisticsgateway.filter.GlobalAuthFilter - 白名单路径，直接放行: /api/user/login
2025-06-29 14:25:24 [lettuce-nioEventLoop-5-1] DEBUG o.example.logisticsgateway.filter.RateLimitFilter - 限流检查通过 - Key: rate_limit:127.0.0.1:_api_user_login:POST, 配置: RateLimitConfig{limit=10, windowSize=60}
2025-06-29 14:25:24 [lettuce-nioEventLoop-5-1] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求开始 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********, StartTime: 1751178324502
2025-06-29 14:25:24 [lettuce-nioEventLoop-5-1] INFO  org.example.logisticsgateway.filter.MonitorFilter - API请求完成 - Method: POST, Path: /api/user/login, IP: 127.0.0.1, Status: 403, Duration: 16ms, StartTime: 1751178324502, EndTime: 1751178324518
2025-06-29 14:25:24 [lettuce-nioEventLoop-5-1] INFO  org.example.logisticsgateway.filter.LoggingFilter - 请求结束 - 方法: POST, 路径: /api/user/login, 状态码: 403, 耗时: 90ms
2025-06-29 14:40:58 [Thread-7] WARN  com.alibaba.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-06-29 14:40:58 [Thread-1] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-29 14:40:58 [Thread-7] WARN  com.alibaba.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-06-29 14:40:58 [Thread-1] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-06-29 14:40:58 [SpringApplicationShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registering from Nacos Server now...
2025-06-29 14:40:58 [SpringApplicationShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registration finished.
2025-06-29 15:02:00 [main] WARN  c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[logistics-gateway] & group[DEFAULT_GROUP]
2025-06-29 15:02:00 [main] WARN  c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[logistics-gateway.properties] & group[DEFAULT_GROUP]
2025-06-29 15:02:00 [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-logistics-gateway.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-logistics-gateway,DEFAULT_GROUP'}]
2025-06-29 15:02:00 [main] INFO  o.e.logisticsgateway.LogisticsGatewayApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-29 15:02:02 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-29 15:02:02 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-06-29 15:02:02 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 MongoDB repository interfaces.
2025-06-29 15:02:02 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-29 15:02:02 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-29 15:02:02 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 3 ms. Found 0 Redis repository interfaces.
2025-06-29 15:02:02 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[org.example.logisticsgateway]' package. Please check your configuration.
2025-06-29 15:02:03 [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=3dca39e2-f3a5-3ca4-bb1e-7d015d021da0
2025-06-29 15:02:03 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-29 15:02:03 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-29 15:02:03 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-29 15:02:03 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-29 15:02:03 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-29 15:02:03 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-06-29 15:02:04 [main] WARN  o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'gatewayManageController': Unsatisfied dependency expressed through field 'monitorFilter'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'monitorFilter': Unsatisfied dependency expressed through field 'redisTemplate'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveStringRedisTemplate' defined in class path resource [org/springframework/boot/autoconfigure/data/redis/RedisReactiveAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveStringRedisTemplate' parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redisConnectionFactory' defined in class path resource [org/springframework/boot/autoconfigure/data/redis/LettuceConnectionConfiguration.class]: Unsatisfied dependency expressed through method 'redisConnectionFactory' parameter 1; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'lettuceClientResources' defined in class path resource [org/springframework/boot/autoconfigure/data/redis/LettuceConnectionConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [io.lettuce.core.resource.DefaultClientResources]: Factory method 'lettuceClientResources' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'lettuceMetrics' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/redis/LettuceMetricsAutoConfiguration.class]: Unsatisfied dependency expressed through method 'lettuceMetrics' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'prometheusMeterRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/export/prometheus/PrometheusMetricsExportAutoConfiguration.class]: Initialization of bean failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourcePoolMetadataMeterBinder' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/jdbc/DataSourcePoolMetricsAutoConfiguration$DataSourcePoolMetadataMetricsConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourcePoolMetadataMeterBinder' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [org/springframework/boot/autoconfigure/jdbc/DataSourceConfiguration$Hikari.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.zaxxer.hikari.HikariDataSource]: Factory method 'dataSource' threw exception; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
2025-06-29 15:02:04 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-29 15:02:04 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-29 15:02:04 [main] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Failed to configure a DataSource: 'url' attribute is not specified and no embedded datasource could be configured.

Reason: Failed to determine a suitable driver class


Action:

Consider the following:
	If you want an embedded database (H2, HSQL or Derby), please put it on the classpath.
	If you have database settings to be loaded from a particular profile you may need to activate it (no profiles are currently active).

2025-06-29 15:02:04 [Thread-1] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-29 15:02:04 [Thread-7] WARN  com.alibaba.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
