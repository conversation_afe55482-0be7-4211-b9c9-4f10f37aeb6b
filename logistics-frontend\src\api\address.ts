import http from '@/utils/http'
import type { ApiResponse } from '@/types/api'
import type { Address, AddressRequest, AddressType, RegionData } from '@/types/address'
import { mapApi } from '@/api/map'

// 地址类型定义
export interface AddressVO {
  id: number
  userId: number
  contactName: string
  contactPhone: string
  province: string
  city: string
  district: string
  detailedAddress: string
  postalCode?: string
  longitude?: number
  latitude?: number
  addressType: number
  isDefault: boolean
  createTime: string
  updateTime: string
}

export const addressApi = {
  // 获取地址列表
  getAddressList(addressType?: number): Promise<ApiResponse<AddressVO[]>> {
    const params = addressType ? { addressType } : {}
    return http.get('/user/addresses', { params })
  },

  // 获取用户地址列表（兼容方法）
  getUserAddresses(addressType?: number): Promise<ApiResponse<AddressVO[]>> {
    const params = addressType ? { addressType } : {}
    return http.get('/user/addresses', { params })
  },

  // 获取地址详情
  getAddressDetail(addressId: number): Promise<ApiResponse<AddressVO>> {
    return http.get(`/user/addresses/${addressId}`)
  },

  // 创建地址
  createAddress(data: AddressRequest): Promise<ApiResponse<AddressVO>> {
    return http.post('/user/addresses', data)
  },

  // 更新地址
  updateAddress(addressId: number, data: AddressRequest): Promise<ApiResponse<AddressVO>> {
    return http.put(`/user/addresses/${addressId}`, data)
  },

  // 删除地址
  deleteAddress(addressId: number): Promise<ApiResponse<void>> {
    return http.delete(`/user/addresses/${addressId}`)
  },

  // 设置默认地址
  setDefaultAddress(addressId: number): Promise<ApiResponse<void>> {
    return http.post(`/user/addresses/${addressId}/default`)
  },

  // 获取省市区数据 - 使用地图模块的接口
  getRegionData(): Promise<ApiResponse<RegionData[]>> {
    // 调用地图模块的省市区接口
    return mapApi
      .getRegions()
      .then((response) => {
        console.log('地图API响应:')
        if (response.code === 200 && response.success) {
          // 转换数据格式
          const data = response.data
          if (data && data.districts && data.districts.length > 0) {
            // 高德API返回的是中国的行政区划，我们需要提取省份列表
            const chinaData = data.districts[0]
            const provinces = chinaData.districts || []

            // 转换为前端需要的格式
            const formattedData = provinces.map((province: any) => ({
              code: province.adcode,
              name: province.name,
              children: (province.districts || []).map((city: any) => ({
                code: city.adcode,
                name: city.name,
                children: (city.districts || []).map((district: any) => ({
                  code: district.adcode,
                  name: district.name,
                })),
              })),
            }))
            console.log('使用高德API数据（成功）')
            return {
              code: 200,
              message: '获取省市区数据成功',
              data: formattedData,
              success: true,
              timestamp: Date.now(),
            } as ApiResponse<RegionData[]>
          }
        }
      })
      .catch((error) => {
        console.error('获取省市区数据失败', error)
      })
  },
}

// 导出类型
export type { AddressVO as Address }
