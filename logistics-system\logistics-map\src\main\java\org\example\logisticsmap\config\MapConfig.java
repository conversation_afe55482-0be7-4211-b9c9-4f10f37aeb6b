package org.example.logisticsmap.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 地图服务配置类
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
@Component
@ConfigurationProperties(prefix = "map")
public class MapConfig {

    /**
     * 缓存配置
     */
    private Cache cache = new Cache();

    /**
     * 限流配置
     */
    private RateLimit rateLimit = new RateLimit();

    /**
     * 超时配置
     */
    private Timeout timeout = new Timeout();

    /**
     * 重试配置
     */
    private Retry retry = new Retry();

    /**
     * 缓存配置
     */
    @Data
    public static class Cache {
        private boolean enabled = true;
        private long ttl = 3600;
        private int maxSize = 10000;
    }

    /**
     * 限流配置
     */
    @Data
    public static class RateLimit {
        private boolean enabled = true;
        private int requestsPerSecond = 100;
        private int burstCapacity = 200;
    }

    /**
     * 超时配置
     */
    @Data
    public static class Timeout {
        private int connect = 5000;
        private int read = 10000;
    }

    /**
     * 重试配置
     */
    @Data
    public static class Retry {
        private boolean enabled = true;
        private int maxAttempts = 3;
        private long delay = 1000;
    }
} 