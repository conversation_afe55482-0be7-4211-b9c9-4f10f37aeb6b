package org.example.logisticsmap.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 位置信息实体类
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Location implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区县
     */
    private String district;

    /**
     * 街道
     */
    private String street;

    /**
     * 街道号码
     */
    private String streetNumber;

    /**
     * 邮政编码
     */
    private String postcode;

    /**
     * 国家
     */
    private String country;

    /**
     * 地址编码
     */
    private String adcode;

    /**
     * 创建坐标位置
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @return 位置信息
     */
    public static Location of(Double longitude, Double latitude) {
        return new Location(longitude, latitude, null, null, null, null, null, null, null, null, null);
    }

    /**
     * 创建坐标位置（带地址）
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @param address   地址
     * @return 位置信息
     */
    public static Location of(Double longitude, Double latitude, String address) {
        return new Location(longitude, latitude, address, null, null, null, null, null, null, null, null);
    }

    /**
     * 获取坐标字符串 (经度,纬度)
     */
    public String getCoordinateString() {
        if (longitude != null && latitude != null) {
            return longitude + "," + latitude;
        }
        return null;
    }

    /**
     * 获取完整地址
     */
    public String getFullAddress() {
        if (address != null && !address.trim().isEmpty()) {
            return address;
        }

        StringBuilder sb = new StringBuilder();
        if (province != null) sb.append(province);
        if (city != null) sb.append(city);
        if (district != null) sb.append(district);
        if (street != null) sb.append(street);
        if (streetNumber != null) sb.append(streetNumber);

        return sb.toString();
    }

    /**
     * 判断坐标是否有效
     */
    public boolean isValidCoordinate() {
        return longitude != null && latitude != null
                && longitude >= -180 && longitude <= 180
                && latitude >= -90 && latitude <= 90;
    }
} 