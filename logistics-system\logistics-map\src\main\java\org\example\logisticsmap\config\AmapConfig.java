package org.example.logisticsmap.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 高德地图API配置类
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
@Component
@ConfigurationProperties(prefix = "amap")
public class AmapConfig {

    /**
     * Web服务API密钥
     */
    private String webApiKey;

    /**
     * JavaScript API密钥
     */
    private String jsApiKey;

    /**
     * API基础地址
     */
    private String baseUrl = "https://restapi.amap.com";

    /**
     * 地理编码配置
     */
    private Geocoding geocoding = new Geocoding();

    /**
     * 路径规划配置
     */
    private Direction direction = new Direction();

    /**
     * 距离计算配置
     */
    private Distance distance = new Distance();

    /**
     * 搜索提示配置
     */
    private Inputtips inputtips = new Inputtips();

    /**
     * POI搜索配置
     */
    private Place place = new Place();

    /**
     * 天气查询配置
     */
    private Weather weather = new Weather();

    /**
     * 默认配置
     */
    private Default defaultConfig = new Default();

    /**
     * 地理编码配置
     */
    @Data
    public static class Geocoding {
        private String url = "/v3/geocode/geo";
        private String regeoUrl = "/v3/geocode/regeo";
    }

    /**
     * 路径规划配置
     */
    @Data
    public static class Direction {
        private String url = "/v3/direction/driving";
        private String walkingUrl = "/v3/direction/walking";
    }

    /**
     * 距离计算配置
     */
    @Data
    public static class Distance {
        private String url = "/v3/distance";
    }

    /**
     * 搜索提示配置
     */
    @Data
    public static class Inputtips {
        private String url = "/v3/assistant/inputtips";
    }

    /**
     * POI搜索配置
     */
    @Data
    public static class Place {
        private String textUrl = "/v3/place/text";
        private String aroundUrl = "/v3/place/around";
    }

    /**
     * 天气查询配置
     */
    @Data
    public static class Weather {
        private String url = "/v3/weather/weatherInfo";
    }

    /**
     * 默认配置
     */
    @Data
    public static class Default {
        private String city = "北京";
        private String output = "json";
        private String extensions = "all";
        private Integer radius = 3000;
        private Integer offset = 20;
    }
} 