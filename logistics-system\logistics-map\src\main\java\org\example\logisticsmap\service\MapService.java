package org.example.logisticsmap.service;

import org.example.logisticsmap.dto.DirectionRequest;
import org.example.logisticsmap.dto.GeocodingRequest;
import org.example.logisticsmap.dto.ReGeocodingRequest;
import org.example.logisticsmap.entity.Location;

import java.util.List;
import java.util.Map;

/**
 * 地图服务接口
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
public interface MapService {

    /**
     * 地理编码 - 地址转坐标
     *
     * @param request 地理编码请求
     * @return 位置信息列表
     */
    List<Location> geocoding(GeocodingRequest request);

    /**
     * 逆地理编码 - 坐标转地址
     *
     * @param request 逆地理编码请求
     * @return 位置信息
     */
    Location reGeocoding(ReGeocodingRequest request);

    /**
     * 驾车路径规划
     *
     * @param request 路径规划请求
     * @return 路径规划结果
     */
    Map<String, Object> drivingDirection(DirectionRequest request);

    /**
     * 步行路径规划
     *
     * @param request 路径规划请求
     * @return 路径规划结果
     */
    Map<String, Object> walkingDirection(DirectionRequest request);

    /**
     * 计算两点间距离
     *
     * @param origins     起点坐标列表
     * @param destination 终点坐标
     * @param type        计算方式 (1:直线距离 3:驾车导航距离)
     * @return 距离信息
     */
    Map<String, Object> calculateDistance(List<String> origins, String destination, Integer type);

    /**
     * 搜索提示
     *
     * @param keywords 关键字
     * @param city     城市
     * @return 搜索提示结果
     */
    Map<String, Object> inputTips(String keywords, String city);

    /**
     * 关键字搜索POI
     *
     * @param keywords 关键字
     * @param city     城市
     * @param types    类型
     * @return POI搜索结果
     */
    Map<String, Object> searchPOI(String keywords, String city, String types);

    /**
     * 周边搜索POI
     *
     * @param keywords 关键字
     * @param location 中心点坐标
     * @param radius   搜索半径
     * @param types    类型
     * @return POI搜索结果
     */
    Map<String, Object> searchAroundPOI(String keywords, String location, Integer radius, String types);

    /**
     * 获取天气信息
     *
     * @param city         城市
     * @param extensions   天气类型 (base:实况天气 all:预报天气)
     * @return 天气信息
     */
    Map<String, Object> getWeather(String city, String extensions);

    // ===== 便捷方法 =====

    /**
     * 地址转坐标（简化版）
     *
     * @param address 地址
     * @return 位置信息
     */
    Location addressToLocation(String address);

    /**
     * 地址转坐标（带城市）
     *
     * @param address 地址
     * @param city    城市
     * @return 位置信息
     */
    Location addressToLocation(String address, String city);

    /**
     * 坐标转地址（简化版）
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @return 位置信息
     */
    Location locationToAddress(Double longitude, Double latitude);

    /**
     * 计算两点直线距离（米）
     *
     * @param fromLng 起点经度
     * @param fromLat 起点纬度
     * @param toLng   终点经度
     * @param toLat   终点纬度
     * @return 距离（米）
     */
    Double calculateStraightDistance(Double fromLng, Double fromLat, Double toLng, Double toLat);

    /**
     * 计算驾车距离（米）
     *
     * @param fromLng 起点经度
     * @param fromLat 起点纬度
     * @param toLng   终点经度
     * @param toLat   终点纬度
     * @return 距离（米）
     */
    Double calculateDrivingDistance(Double fromLng, Double fromLat, Double toLng, Double toLat);

    /**
     * 获取最优驾车路径
     *
     * @param fromLng 起点经度
     * @param fromLat 起点纬度
     * @param toLng   终点经度
     * @param toLat   终点纬度
     * @return 路径信息
     */
    Map<String, Object> getBestDrivingRoute(Double fromLng, Double fromLat, Double toLng, Double toLat);

    /**
     * 批量地址转坐标
     *
     * @param addresses 地址列表
     * @return 位置信息列表
     */
    List<Location> batchAddressToLocation(List<String> addresses);

    /**
     * 批量坐标转地址
     *
     * @param locations 坐标列表 (格式: 经度,纬度)
     * @return 位置信息列表
     */
    List<Location> batchLocationToAddress(List<String> locations);

    /**
     * 获取行政区划数据
     *
     * @param keywords    查询关键词（省份名称）
     * @param subdistrict 子级行政区级数
     * @return 行政区划数据
     */
    Map<String, Object> getDistrictData(String keywords, String subdistrict);

    /**
     * 获取全国省份列表
     *
     * @return 省份列表
     */
    List<Map<String, Object>> getAllProvinces();
} 