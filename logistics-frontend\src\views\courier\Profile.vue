<template>
  <div class="courier-profile">
    <div class="page-header">
      <h2>个人信息</h2>
      <div class="header-actions">
        <el-button @click="editMode = !editMode" :type="editMode ? 'success' : 'primary'">
          {{ editMode ? '保存' : '编辑' }}
        </el-button>
      </div>
    </div>

    <el-row :gutter="20">
      <!-- 左侧：基本信息 -->
      <el-col :span="16">
        <el-card>
          <template #header>
            <span>基本信息</span>
          </template>

          <el-form :model="profileForm" label-width="120px" :disabled="!editMode">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="配送员编号">
                  <el-input v-model="profileForm.courierCode" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="工号">
                  <el-input v-model="profileForm.workNumber" readonly />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="姓名">
                  <el-input v-model="profileForm.courierName" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="手机号">
                  <el-input v-model="profileForm.phone" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="身份证号">
                  <el-input v-model="profileForm.idCard" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="工作区域">
                  <el-input v-model="profileForm.workArea" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="车辆类型">
                  <el-select v-model="profileForm.vehicleType" style="width: 100%">
                    <el-option label="自行车" value="BIKE" />
                    <el-option label="摩托车" value="MOTORCYCLE" />
                    <el-option label="汽车" value="CAR" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="车牌号">
                  <el-input v-model="profileForm.vehicleNumber" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="最大承重">
                  <el-input v-model="profileForm.maxWeight" type="number">
                    <template #suffix>kg</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="最大体积">
                  <el-input v-model="profileForm.maxVolume" type="number">
                    <template #suffix>m³</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>

        <!-- 工作统计 -->
        <el-card class="stats-card">
          <template #header>
            <span>工作统计</span>
          </template>

          <el-row :gutter="20">
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-number">{{ workStats.totalTasks }}</div>
                <div class="stat-label">总任务数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-number">{{ workStats.completedTasks }}</div>
                <div class="stat-label">已完成</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-number">{{ workStats.successRate }}%</div>
                <div class="stat-label">成功率</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-number">{{ workStats.avgRating }}</div>
                <div class="stat-label">平均评分</div>
              </div>
            </el-col>
          </el-row>

          <el-divider />

          <el-row :gutter="20">
            <el-col :span="8">
              <div class="stat-item">
                <div class="stat-number">{{ workStats.thisMonthTasks }}</div>
                <div class="stat-label">本月任务</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-item">
                <div class="stat-number">¥{{ workStats.thisMonthEarnings }}</div>
                <div class="stat-label">本月收入</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-item">
                <div class="stat-number">{{ workStats.workDays }}</div>
                <div class="stat-label">工作天数</div>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 最近评价 -->
        <el-card class="reviews-card">
          <template #header>
            <span>最近评价</span>
          </template>

          <div class="reviews-list">
            <div v-for="review in recentReviews" :key="review.id" class="review-item">
              <div class="review-header">
                <el-rate v-model="review.rating" disabled size="small" />
                <span class="review-date">{{ formatTime(review.createTime) }}</span>
              </div>
              <div class="review-content">{{ review.comment }}</div>
              <div class="review-task">任务：{{ review.taskNumber }}</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：头像和状态 -->
      <el-col :span="8">
        <el-card class="avatar-card">
          <template #header>
            <span>头像</span>
          </template>

          <div class="avatar-section">
            <el-avatar :size="120" :src="profileForm.avatar">
              {{ profileForm.courierName?.charAt(0) }}
            </el-avatar>
            
            <el-upload
              v-if="editMode"
              :show-file-list="false"
              :before-upload="beforeAvatarUpload"
              :on-success="handleAvatarSuccess"
              action="/api/upload/avatar"
              class="avatar-upload"
            >
              <el-button type="primary" size="small">更换头像</el-button>
            </el-upload>
          </div>
        </el-card>

        <!-- 当前状态 -->
        <el-card class="status-card">
          <template #header>
            <span>当前状态</span>
          </template>

          <div class="status-content">
            <div class="status-item">
              <label>工作状态：</label>
              <el-tag :type="getStatusTag(profileForm.status)" size="large">
                {{ getStatusText(profileForm.status) }}
              </el-tag>
            </div>

            <div class="status-item">
              <label>当前位置：</label>
              <span>{{ profileForm.currentAddress || '未知' }}</span>
            </div>

            <div class="status-item">
              <label>最后更新：</label>
              <span>{{ formatTime(profileForm.updateTime) }}</span>
            </div>

            <div class="status-item">
              <label>入职时间：</label>
              <span>{{ formatTime(profileForm.createTime) }}</span>
            </div>
          </div>
        </el-card>

        <!-- 认证信息 -->
        <el-card class="certification-card">
          <template #header>
            <span>认证信息</span>
          </template>

          <div class="certification-list">
            <div class="cert-item">
              <el-icon class="cert-icon success"><Check /></el-icon>
              <span>身份认证</span>
            </div>
            <div class="cert-item">
              <el-icon class="cert-icon success"><Check /></el-icon>
              <span>手机认证</span>
            </div>
            <div class="cert-item">
              <el-icon class="cert-icon success"><Check /></el-icon>
              <span>车辆认证</span>
            </div>
            <div class="cert-item">
              <el-icon class="cert-icon warning"><Clock /></el-icon>
              <span>安全培训</span>
            </div>
          </div>
        </el-card>

        <!-- 快捷操作 -->
        <el-card class="actions-card">
          <template #header>
            <span>快捷操作</span>
          </template>

          <div class="action-buttons">
            <el-button type="primary" @click="updateLocation">
              <el-icon><LocationFilled /></el-icon>
              更新位置
            </el-button>
            
            <el-button @click="changePassword">
              <el-icon><Lock /></el-icon>
              修改密码
            </el-button>
            
            <el-button @click="contactSupport">
              <el-icon><Phone /></el-icon>
              联系客服
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 修改密码对话框 -->
    <el-dialog v-model="showPasswordDialog" title="修改密码" width="400px">
      <el-form :model="passwordForm" :rules="passwordRules" ref="passwordFormRef" label-width="100px">
        <el-form-item label="当前密码" prop="currentPassword">
          <el-input v-model="passwordForm.currentPassword" type="password" show-password />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="passwordForm.newPassword" type="password" show-password />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="passwordForm.confirmPassword" type="password" show-password />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showPasswordDialog = false">取消</el-button>
        <el-button type="primary" @click="submitPasswordChange" :loading="changingPassword">
          确认修改
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import {
  Check,
  Clock,
  LocationFilled,
  Lock,
  Phone,
} from '@element-plus/icons-vue'
import { courierApi, type CourierInfo } from '@/api/courier'
import dayjs from 'dayjs'

// 状态
const editMode = ref(false)
const showPasswordDialog = ref(false)
const changingPassword = ref(false)
const passwordFormRef = ref<FormInstance>()

// 表单数据
const profileForm = reactive<CourierInfo>({
  id: 0,
  userId: 0,
  courierCode: '',
  courierName: '',
  phone: '',
  idCard: '',
  workNumber: '',
  vehicleType: 'MOTORCYCLE',
  vehicleNumber: '',
  workArea: '',
  maxWeight: 0,
  maxVolume: 0,
  status: 1,
  longitude: 0,
  latitude: 0,
  currentAddress: '',
  rating: 0,
  completedTasks: 0,
  createTime: '',
  updateTime: '',
})

// 密码表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: '',
})

// 密码验证规则
const passwordRules: FormRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' },
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度为6-20个字符', trigger: 'blur' },
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
}

// 工作统计
const workStats = ref({
  totalTasks: 0,
  completedTasks: 0,
  successRate: 0,
  avgRating: 0,
  thisMonthTasks: 0,
  thisMonthEarnings: 0,
  workDays: 0,
})

// 最近评价
const recentReviews = ref([
  {
    id: 1,
    rating: 5,
    comment: '配送及时，服务态度很好！',
    taskNumber: 'T202401010001',
    createTime: '2024-01-01 15:00:00',
  },
  {
    id: 2,
    rating: 4,
    comment: '包装完好，配送员很专业。',
    taskNumber: 'T202401010002',
    createTime: '2024-01-01 16:30:00',
  },
])

// 加载配送员信息
const loadCourierInfo = async () => {
  try {
    const response = await courierApi.getCourierInfo()
    if (response.data.code === 200) {
      Object.assign(profileForm, response.data.data)
    }
  } catch (error) {
    console.error('加载配送员信息失败:', error)
    // 使用模拟数据
    Object.assign(profileForm, {
      id: 1,
      userId: 1,
      courierCode: 'C001',
      courierName: '张三',
      phone: '13800138001',
      idCard: '110101199001011234',
      workNumber: 'W001',
      vehicleType: 'MOTORCYCLE',
      vehicleNumber: '京A12345',
      workArea: '朝阳区',
      maxWeight: 50,
      maxVolume: 2,
      status: 1,
      currentAddress: '北京市朝阳区建国门外大街',
      rating: 4.8,
      completedTasks: 156,
      createTime: '2024-01-01 09:00:00',
      updateTime: '2024-01-01 18:00:00',
    })
  }
}

// 加载工作统计
const loadWorkStats = async () => {
  try {
    const response = await courierApi.getTaskStatistics()
    if (response.data.code === 200) {
      const data = response.data.data
      workStats.value = {
        totalTasks: data.totalTasks,
        completedTasks: data.completedTasks,
        successRate: data.successRate,
        avgRating: data.avgRating,
        thisMonthTasks: data.todayTasks * 30, // 模拟本月数据
        thisMonthEarnings: data.todayTasks * 30 * 15, // 模拟收入
        workDays: 25,
      }
    }
  } catch (error) {
    console.error('加载工作统计失败:', error)
    // 使用模拟数据
    workStats.value = {
      totalTasks: 156,
      completedTasks: 148,
      successRate: 95,
      avgRating: 4.8,
      thisMonthTasks: 45,
      thisMonthEarnings: 675,
      workDays: 25,
    }
  }
}

// 更新位置
const updateLocation = () => {
  if (!navigator.geolocation) {
    ElMessage.error('浏览器不支持地理位置')
    return
  }

  navigator.geolocation.getCurrentPosition(
    async (position) => {
      try {
        await courierApi.updateLocation({
          longitude: position.coords.longitude,
          latitude: position.coords.latitude,
        })
        ElMessage.success('位置更新成功')
        loadCourierInfo()
      } catch (error) {
        console.error('位置更新失败:', error)
        ElMessage.error('位置更新失败')
      }
    },
    (error) => {
      console.error('获取位置失败:', error)
      ElMessage.error('获取位置失败')
    }
  )
}

// 修改密码
const changePassword = () => {
  showPasswordDialog.value = true
  Object.assign(passwordForm, {
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  })
}

// 提交密码修改
const submitPasswordChange = async () => {
  if (!passwordFormRef.value) return

  await passwordFormRef.value.validate(async (valid) => {
    if (valid) {
      changingPassword.value = true
      try {
        // TODO: 调用修改密码API
        ElMessage.success('密码修改成功')
        showPasswordDialog.value = false
      } catch (error) {
        console.error('密码修改失败:', error)
        ElMessage.error('密码修改失败')
      } finally {
        changingPassword.value = false
      }
    }
  })
}

// 联系客服
const contactSupport = () => {
  ElMessage.info('客服电话：400-123-4567')
}

// 头像上传前检查
const beforeAvatarUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 头像上传成功
const handleAvatarSuccess = (response: any) => {
  if (response.code === 200) {
    profileForm.avatar = response.data.url
    ElMessage.success('头像更新成功')
  } else {
    ElMessage.error('头像上传失败')
  }
}

// 工具函数
const getStatusText = (status: number) => {
  const textMap = {
    0: '离线',
    1: '在线',
    2: '忙碌',
    3: '休息',
  }
  return textMap[status] || '未知'
}

const getStatusTag = (status: number) => {
  const tagMap = {
    0: 'info',
    1: 'success',
    2: 'warning',
    3: 'primary',
  }
  return tagMap[status] || ''
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm')
}

onMounted(() => {
  loadCourierInfo()
  loadWorkStats()
})
</script>

<style scoped>
.courier-profile {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-card {
  margin-top: 20px;
}

.stat-item {
  text-align: center;
  padding: 16px 0;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

.reviews-card {
  margin-top: 20px;
}

.reviews-list {
  max-height: 300px;
  overflow-y: auto;
}

.review-item {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.review-item:last-child {
  border-bottom: none;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.review-date {
  color: #999;
  font-size: 12px;
}

.review-content {
  color: #666;
  margin-bottom: 4px;
  line-height: 1.5;
}

.review-task {
  color: #999;
  font-size: 12px;
}

.avatar-card {
  margin-bottom: 20px;
}

.avatar-section {
  text-align: center;
  padding: 20px 0;
}

.avatar-upload {
  margin-top: 16px;
}

.status-card {
  margin-bottom: 20px;
}

.status-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-item label {
  min-width: 80px;
  color: #666;
  font-weight: 500;
}

.certification-card {
  margin-bottom: 20px;
}

.certification-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.cert-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.cert-icon {
  font-size: 16px;
}

.cert-icon.success {
  color: #67c23a;
}

.cert-icon.warning {
  color: #e6a23c;
}

.actions-card {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-buttons .el-button {
  width: 100%;
  justify-content: flex-start;
}
</style> 