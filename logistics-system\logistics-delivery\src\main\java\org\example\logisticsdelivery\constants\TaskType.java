package org.example.logisticsdelivery.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 配送任务类型枚举
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Getter
@AllArgsConstructor
public enum TaskType {

    PICKUP("PIC<PERSON>UP", "揽件", "上门揽件任务"),
    DELIVERY("DELIVERY", "派送", "配送派件任务");

    private final String code;
    private final String name;
    private final String description;

    /**
     * 根据类型码获取枚举
     */
    public static TaskType fromCode(String code) {
        for (TaskType type : TaskType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的任务类型: " + code);
    }
}