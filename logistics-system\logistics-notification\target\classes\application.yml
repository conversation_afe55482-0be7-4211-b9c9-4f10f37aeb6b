server:
  port: 8005

spring:
  application:
    name: logistics-notification
  datasource:
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ****************************************************************************************************************************************************
      username: root
      password: liyuqw8017
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
  redis:
    host: localhost
    port: 6379
    password: redis123
    database: 0
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    virtual-host: /
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: your-email-password
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:mapper/*.xml

# 临时注释API文档配置，避免启动问题
# knife4j:
#   enable: true
#   openapi:
#     title: 物流通知服务API
#     description: 物流系统通知服务，提供短信、邮件、推送等通知功能
#     version: 1.0.0
#     contact:
#       name: logistics团队
#       email: <EMAIL>
#   setting:
#     language: zh_cn

# 自定义通知配置
notification:
  # 短信配置
  sms:
    enabled: true
    provider: ALIYUN  # 短信服务商：ALIYUN, TENCENT
    accessKey: your-access-key
    secretKey: your-secret-key
    signName: 物流系统
  # 邮件配置
  email:
    enabled: true
    from: <EMAIL>
    fromName: 物流系统
  # 推送配置
  push:
    enabled: true
    provider: JPUSH  # 推送服务商：JPUSH, GETUI
    appKey: your-app-key
    masterSecret: your-master-secret
  # 微信配置
  wechat:
    enabled: false
    appId: your-app-id
    appSecret: your-app-secret
  # 重试配置
  retry:
    maxAttempts: 3
    initialDelay: 1000  # 初始延迟（毫秒）
    maxDelay: 60000     # 最大延迟（毫秒）
    multiplier: 2.0     # 延迟倍数
  # 清理配置
  cleanup:
    expireDays: 30      # 过期天数
    batchSize: 1000     # 批量清理大小

logging:
  level:
    org.example.logisticsnotification: debug
    com.baomidou.mybatisplus: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
jwt:
  secret: logistics-secret-key-2024-for-microservice-authentication-system
  expiration: 86400