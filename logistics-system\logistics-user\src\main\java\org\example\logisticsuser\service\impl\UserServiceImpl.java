package org.example.logisticsuser.service.impl;

import com.logistics.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.example.logisticsuser.dto.CreateCourierRequest;
import org.example.logisticsuser.dto.UpdateUserInfoDTO;
import org.example.logisticsuser.feign.CourierFeignClient;
import org.example.logisticsuser.service.UserService;
import org.example.logisticsuser.mapper.UserMapper;
import org.example.logisticsuser.mapper.UserRoleMapper;
import org.example.logisticsuser.entity.User;
import org.example.logisticsuser.entity.UserRole;
import org.example.logisticsuser.dto.UserLoginDTO;
import org.example.logisticsuser.dto.UserRegisterDTO;
import org.example.logisticsuser.vo.LoginResponseVO;
import org.example.logisticsuser.vo.UserInfoVO;
import com.logistics.common.utils.JwtUtils;
import com.logistics.common.exception.BusinessException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private CourierFeignClient courierFeignClient;

    @Override
    @Transactional
    public LoginResponseVO register(UserRegisterDTO registerDTO) {
        // 1. 检查用户名是否已存在
        if (checkUsernameExists(registerDTO.getUsername())) {
            throw new BusinessException("用户名已存在");
        }

        // 2. 检查手机号是否已存在
        if (checkPhoneExists(registerDTO.getPhone())) {
            throw new BusinessException("手机号已被注册");
        }

        // 3. 检查邮箱是否已存在
        if (StringUtils.hasText(registerDTO.getEmail()) && checkEmailExists(registerDTO.getEmail())) {
            throw new BusinessException("邮箱已被注册");
        }

        // 4. 创建用户实体
        User user = new User();
        BeanUtils.copyProperties(registerDTO, user);
        user.setPassword(passwordEncoder.encode(registerDTO.getPassword()));
        user.setStatus("ACTIVE");
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());

        // 5. 保存用户
        userMapper.insert(user);
        if ("COURIER".equals(registerDTO.getUserType())) {
            Map<String, Object> courierData = new HashMap<>();
            courierData.put("courierName", user.getRealName());
            courierData.put("phone", user.getPhone());
            courierData.put("userId", user.getId());
            courierData.put("workArea", "北京市");
            courierData.put("status", 1);
            courierData.put("rating", 5.0);
            courierData.put("deliveryCount", 0);

            try {
                Result<Object> courierResult = courierFeignClient.createCourier(courierData);
                if (courierResult == null || !courierResult.isSuccess()) {
                    log.error("创建配送员记录失败: {}", courierResult != null ? courierResult.getMessage() : "服务调用失败");
                    throw new RuntimeException("创建配送员记录失败");
                }
                log.info("配送员记录创建成功，用户ID: {}", user.getId());
            } catch (Exception e) {
                log.error("调用配送服务失败", e);
                throw new RuntimeException("创建配送员记录失败: " + e.getMessage());
            }
        }

        // 6. 分配默认角色
        assignDefaultRole(user.getId(), registerDTO.getUserType());

        // 7. 生成JWT token
        String token = jwtUtils.generateToken(user.getUsername());

        // 8. 构造返回结果
        LoginResponseVO response = new LoginResponseVO();
        response.setToken(token);
        response.setExpiresIn(86400L); // 24小时，单位：秒
        response.setLoginTime(LocalDateTime.now());
        response.setUserInfo(convertToUserInfoVO(user));

        return response;
    }

    @Override
    public LoginResponseVO login(UserLoginDTO loginDTO) {
        // 1. 根据登录类型查找用户
        User user = findUserByLoginType(loginDTO.getUsername(), loginDTO.getLoginType());

        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 2. 检查用户状态
        if (!"ACTIVE".equals(user.getStatus())) {
            throw new BusinessException("用户已被锁定");
        }

        // 3. 验证密码
        if (!passwordEncoder.matches(loginDTO.getPassword(), user.getPassword())) {
            throw new BusinessException("密码错误");
        }

        // 4. 更新最后登录信息
        User updateUser = new User();
        updateUser.setId(user.getId());
        updateUser.setLastLoginTime(LocalDateTime.now());
        updateUser.setLastLoginIp(getClientIp());
        updateUser.setUpdateTime(LocalDateTime.now());
        userMapper.updateById(updateUser);

        // 5. 生成JWT token
        String token = jwtUtils.generateToken(user.getUsername());

        // 6. 构造返回结果
        LoginResponseVO response = new LoginResponseVO();
        response.setToken(token);
        response.setExpiresIn(86400L); // 24小时，单位：秒
        response.setLoginTime(LocalDateTime.now());
        response.setUserInfo(convertToUserInfoVO(user));

        return response;
    }

        @Override
    public UserInfoVO getCurrentUserInfo(String token) {
        String username = jwtUtils.getUsernameFromToken(token);
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getUsername, username);
        User user = userMapper.selectOne(wrapper);
        
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        return convertToUserInfoVO(user);
    }

    @Override
    public UserInfoVO getUserInfoById(Long userId) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        return convertToUserInfoVO(user);
    }

        @Override
    public boolean checkUsernameExists(String username) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getUsername, username);
        return userMapper.selectCount(wrapper) > 0;
    }
    
    @Override
    public boolean checkPhoneExists(String phone) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getPhone, phone);
        return userMapper.selectCount(wrapper) > 0;
    }
    
    @Override
    public boolean checkEmailExists(String email) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getEmail, email);
        return userMapper.selectCount(wrapper) > 0;
    }

        @Override
        public List<String> getUserRoles(Long userId) {
            return userMapper.selectRolesByUserId(userId);
        }

    @Override
    public List<String> getUserPermissions(Long userId) {
        return userMapper.selectPermissionsByUserId(userId);
    }

    // 私有辅助方法
    private void assignDefaultRole(Long userId, String userType) {
        // 根据用户类型分配默认角色
        Long roleId = getDefaultRoleId(userType);
        if (roleId != null) {
            UserRole userRole = new UserRole();
            userRole.setUserId(userId);
            userRole.setRoleId(roleId);
            userRole.setCreateTime(LocalDateTime.now());
            userRoleMapper.insert(userRole);
        }
    }

    private Long getDefaultRoleId(String userType) {
        // 这里应该从数据库查询，暂时用固定值
        switch (userType) {
            case "CUSTOMER": return 1L;
            case "OPERATOR": return 2L;
            case "COURIER": return 3L;
            case "ADMIN": return 4L;
            default: return 1L;
        }
    }

    private User findUserByLoginType(String loginValue, String loginType) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        switch (loginType) {
            case "PHONE":
                wrapper.eq(User::getPhone, loginValue);
                break;
            case "EMAIL":
                wrapper.eq(User::getEmail, loginValue);
                break;
            default:
                wrapper.eq(User::getUsername, loginValue);
                break;
        }
        return userMapper.selectOne(wrapper);
    }

    private UserInfoVO convertToUserInfoVO(User user) {
        UserInfoVO vo = new UserInfoVO();
        BeanUtils.copyProperties(user, vo);

        // 手机号脱敏处理
        if (user.getPhone() != null) {
            vo.setPhone(maskPhone(user.getPhone()));
        }

        // 邮箱脱敏处理
        if (user.getEmail() != null) {
            vo.setEmail(maskEmail(user.getEmail()));
        }

        // 获取用户角色和权限
        vo.setRoles(userMapper.selectRolesByUserId(user.getId()));
        vo.setPermissions(userMapper.selectPermissionsByUserId(user.getId()));

        return vo;
    }
    /**
     * 手机号脱敏
     */
    private String maskPhone(String phone) {
        if (phone == null || phone.length() != 11) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }

    /**
     * 邮箱脱敏
     */
    private String maskEmail(String email) {
        if (email == null || !email.contains("@")) {
            return email;
        }
        String[] parts = email.split("@");
        String name = parts[0];
        if (name.length() <= 2) {
            return email;
        }
        return name.substring(0, 2) + "***@" + parts[1];
    }

    private String getClientIp() {
        // 这里应该从HttpServletRequest获取，暂时返回固定值
        return "127.0.0.1";
    }

    // 其他方法的实现...
    @Override
    public UserInfoVO updateUserInfo(Long userId, UserInfoVO userInfo) {
        // TODO: 实现更新用户信息
        return null;
    }

    @Override
    public boolean changePassword(Long userId, String oldPassword, String newPassword) {
        // TODO: 实现修改密码
        return false;
    }

    @Override
    public boolean resetPassword(String phone, String newPassword, String verificationCode) {
        // TODO: 实现重置密码
        return false;
    }

    @Override
    public IPage<UserInfoVO> getUserPage(Page<User> page, String userType, String status, String keyword) {
        // TODO: 实现分页查询
        return null;
    }

    @Override
    public boolean lockUser(Long userId, boolean lock) {
        // TODO: 实现锁定用户
        return false;
    }

    @Override
    public boolean assignRoles(Long userId, Long[] roleIds) {
        // TODO: 实现分配角色
        return false;
    }

    @Override
    public UserInfoVO updateUserInfo(Long userId, UpdateUserInfoDTO updateDTO) {
        try {
            // 检查手机号是否已被其他用户使用
            User existingUser = userMapper.selectByPhone(updateDTO.getPhone());
            if (existingUser != null && !existingUser.getId().equals(userId)) {
                throw new BusinessException("手机号已被其他用户使用");
            }

            // 检查邮箱是否已被其他用户使用
            existingUser = userMapper.selectByEmail(updateDTO.getEmail());
            if (existingUser != null && !existingUser.getId().equals(userId)) {
                throw new BusinessException("邮箱已被其他用户使用");
            }

            // 检查身份证号是否已被其他用户使用（如果提供了）
            if (updateDTO.getIdCard() != null && !updateDTO.getIdCard().trim().isEmpty()) {
                existingUser = userMapper.selectByIdCard(updateDTO.getIdCard());
                if (existingUser != null && !existingUser.getId().equals(userId)) {
                    throw new BusinessException("身份证号已被其他用户使用");
                }
            }

            // 获取当前用户信息
            User user = userMapper.selectById(userId);
            if (user == null) {
                throw new BusinessException("用户不存在");
            }

            // 更新用户信息
            user.setRealName(updateDTO.getRealName());
            user.setPhone(updateDTO.getPhone());
            user.setEmail(updateDTO.getEmail());
            user.setIdCard(updateDTO.getIdCard());
            user.setGender(updateDTO.getGender());
            user.setBirthday(updateDTO.getBirthday());
            user.setAvatar(updateDTO.getAvatar());
            user.setUpdateTime(LocalDateTime.now());

            int result = userMapper.updateById(user);
            if (result > 0) {
                return convertToUserInfoVO(user);
            } else {
                throw new BusinessException("更新用户信息失败");
            }
        } catch (Exception e) {
            log.error("更新用户信息失败: userId={}, error={}", userId, e.getMessage(), e);
            throw new BusinessException("更新用户信息失败: " + e.getMessage());
        }
    }

    @Override
    public UpdateUserInfoDTO getUserInfoForEdit(String token) {
        try {
            String username = jwtUtils.getUsernameFromToken(token);
            User user = userMapper.selectByUsername(username);
            if (user == null) {
                throw new BusinessException("用户不存在");
            }

            UpdateUserInfoDTO dto = new UpdateUserInfoDTO();
            dto.setRealName(user.getRealName());
            dto.setPhone(user.getPhone()); // 返回真实手机号，不脱敏
            dto.setEmail(user.getEmail()); // 返回真实邮箱，不脱敏
            dto.setIdCard(user.getIdCard());
            dto.setGender(user.getGender());
            dto.setBirthday(user.getBirthday());
            dto.setAvatar(user.getAvatar());

            return dto;
        } catch (Exception e) {
            log.error("获取用户编辑信息失败: {}", e.getMessage(), e);
            throw new BusinessException("获取用户信息失败");
        }
    }
}