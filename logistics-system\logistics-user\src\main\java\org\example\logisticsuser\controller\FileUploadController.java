package org.example.logisticsuser.controller;

import com.logistics.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 文件上传控制器
 */
@Slf4j
@RestController
@RequestMapping("/user/upload")
@CrossOrigin(origins = "*")
public class FileUploadController {

    @Value("${file.upload.path:/tmp/uploads}")
    private String uploadPath;

    @Value("${file.upload.domain:http://localhost:8001}")
    private String uploadDomain;

    /**
     * 头像上传
     */
    @PostMapping("/avatar")
    public Result<Map<String, String>> uploadAvatar(@RequestParam("file") MultipartFile file) {
        try {
            log.info("开始处理头像上传请求，文件名: {}", file.getOriginalFilename());

            // 验证文件
            if (file.isEmpty()) {
                return Result.error("文件不能为空");
            }

            // 验证文件类型
            String contentType = file.getContentType();
            if (contentType == null || (!contentType.equals("image/jpeg") && !contentType.equals("image/png"))) {
                return Result.error("只支持JPG和PNG格式的图片");
            }

            // 验证文件大小（2MB）
            if (file.getSize() > 2 * 1024 * 1024) {
                return Result.error("文件大小不能超过2MB");
            }

            // 创建上传目录
            Path uploadDir = Paths.get(uploadPath, "avatars");
            if (!Files.exists(uploadDir)) {
                Files.createDirectories(uploadDir);
                log.info("创建上传目录: {}", uploadDir);
            }

            // 生成文件名
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename != null && originalFilename.contains(".")
                    ? originalFilename.substring(originalFilename.lastIndexOf("."))
                    : ".jpg";
            String fileName = UUID.randomUUID().toString() + extension;

            // 保存文件
            Path filePath = uploadDir.resolve(fileName);
            Files.copy(file.getInputStream(), filePath);

            // 返回文件URL
            String fileUrl = uploadDomain + "/uploads/avatars/" + fileName;

            Map<String, String> result = new HashMap<>();
            result.put("url", fileUrl);

            log.info("头像上传成功: {}", fileUrl);
            return Result.success("上传成功", result);

        } catch (IOException e) {
            log.error("头像上传失败: {}", e.getMessage(), e);
            return Result.error("上传失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("头像上传异常: {}", e.getMessage(), e);
            return Result.error("上传异常: " + e.getMessage());
        }
    }
}