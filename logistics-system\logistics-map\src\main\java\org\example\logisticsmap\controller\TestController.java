package org.example.logisticsmap.controller;

import com.logistics.common.result.Result;
import org.example.logisticsmap.config.AmapConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 地图服务健康检查控制器
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@RestController
@RequestMapping("/test")
public class TestController {

    @Autowired
    private AmapConfig amapConfig;

    /**
     * 服务健康检查
     */
    @GetMapping("/hello")
    public Result<String> hello() {
        return Result.success("地图服务启动成功！端口：8006");
    }

    /**
     * 服务状态检查
     */
    @GetMapping("/status")
    public Result<Map<String, Object>> status() {
        Map<String, Object> status = new HashMap<>();
        status.put("service", "logistics-map");
        status.put("port", 8006);
        status.put("status", "running");
        status.put("timestamp", LocalDateTime.now());
        status.put("amapConfigured", amapConfig.getWebApiKey() != null && 
                                   !amapConfig.getWebApiKey().contains("your_amap"));
        
        return Result.success(status);
    }

    /**
     * 配置信息检查
     */
    @GetMapping("/config")
    public Result<Map<String, Object>> config() {
        Map<String, Object> config = new HashMap<>();
        config.put("baseUrl", amapConfig.getBaseUrl());
        config.put("webApiKeyConfigured", amapConfig.getWebApiKey() != null && 
                                        !amapConfig.getWebApiKey().contains("your_amap"));
        config.put("jsApiKeyConfigured", amapConfig.getJsApiKey() != null && 
                                       !amapConfig.getJsApiKey().contains("your_amap"));
        config.put("defaultCity", amapConfig.getDefaultConfig().getCity());
        
        return Result.success(config);
    }
}
