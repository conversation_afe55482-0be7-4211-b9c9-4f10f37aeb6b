<template>
  <div class="delivery-history">
    <div class="page-header">
      <h2>配送历史</h2>
      <div class="header-actions">
        <el-button @click="exportHistory" :icon="Download">导出记录</el-button>
        <el-button @click="refreshHistory" :icon="Refresh">刷新</el-button>
      </div>
    </div>

    <!-- 统计概览 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ statistics.totalDeliveries }}</div>
            <div class="stat-label">总配送数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ statistics.successRate }}%</div>
            <div class="stat-label">成功率</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ statistics.avgRating }}</div>
            <div class="stat-label">平均评分</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">¥{{ statistics.totalEarnings }}</div>
            <div class="stat-label">总收入</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <el-form :model="filterForm" inline>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <el-form-item label="任务状态">
          <el-select v-model="filterForm.status" placeholder="全部状态" clearable>
            <el-option label="已完成" value="COMPLETED" />
            <el-option label="已取消" value="CANCELLED" />
          </el-select>
        </el-form-item>

        <el-form-item label="任务类型">
          <el-select v-model="filterForm.taskType" placeholder="全部类型" clearable>
            <el-option label="揽件" value="PICKUP" />
            <el-option label="派送" value="DELIVERY" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 历史记录列表 -->
    <el-card>
      <el-table :data="historyList" v-loading="loading" stripe style="width: 100%">
        <el-table-column prop="taskNumber" label="任务编号" width="150">
          <template #default="{ row }">
            <el-link type="primary" @click="viewTaskDetail(row.id)">
              {{ row.taskNumber }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column label="类型" width="80">
          <template #default="{ row }">
            <el-tag :type="getTaskTypeTag(row.taskType)" size="small">
              {{ getTaskTypeText(row.taskType) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="地址信息" min-width="300">
          <template #default="{ row }">
            <div class="address-info">
              <div class="address-item">
                <el-icon><Position /></el-icon>
                <span>{{ row.senderAddress }}</span>
              </div>
              <div class="address-item">
                <el-icon><LocationFilled /></el-icon>
                <span>{{ row.receiverAddress }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="联系信息" width="150">
          <template #default="{ row }">
            <div>
              <div>{{ row.receiverName }}</div>
              <div class="contact-phone">{{ row.receiverPhone }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTag(row.taskStatus)" size="small">
              {{ getStatusText(row.taskStatus) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="配送费" width="100">
          <template #default="{ row }">
            <span class="delivery-fee">¥{{ row.deliveryFee.toFixed(2) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="完成时间" width="160">
          <template #default="{ row }">
            <span v-if="row.actualEndTime">{{ formatTime(row.actualEndTime) }}</span>
            <span v-else class="text-gray">未完成</span>
          </template>
        </el-table-column>

        <el-table-column label="评分" width="100">
          <template #default="{ row }">
            <el-rate
              v-model="row.rating"
              disabled
              show-score
              text-color="#ff9900"
              score-template="{value}"
              size="small"
            />
          </template>
        </el-table-column>

        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="text" @click="viewTaskDetail(row.id)">详情</el-button>
            <el-button v-if="row.taskStatus === 'COMPLETED'" type="text" @click="viewProof(row)">
              凭证
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 完成凭证对话框 -->
    <el-dialog v-model="showProofDialog" title="完成凭证" width="600px">
      <div v-if="selectedTask" class="proof-content">
        <div class="task-info">
          <h4>{{ selectedTask.taskNumber }}</h4>
          <p>完成时间：{{ formatTime(selectedTask.actualEndTime) }}</p>
        </div>

        <div v-if="selectedTask.completionProof" class="proof-image">
          <img :src="selectedTask.completionProof" alt="完成凭证" />
        </div>

        <div v-if="selectedTask.remarks" class="proof-remarks">
          <h5>完成备注：</h5>
          <p>{{ selectedTask.remarks }}</p>
        </div>
      </div>
    </el-dialog>

    <!-- 月度统计图表 -->
    <el-card class="chart-card">
      <template #header>
        <span>月度配送统计</span>
      </template>

      <div class="chart-container">
        <div class="chart-placeholder">
          <el-empty description="图表功能开发中..." />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, Refresh, Position, LocationFilled } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { courierApi, type DeliveryTask } from '@/api/courier'
import dayjs from 'dayjs'

const router = useRouter()

// 状态
const loading = ref(false)
const historyList = ref<DeliveryTask[]>([])
const total = ref(0)
const showProofDialog = ref(false)
const selectedTask = ref<DeliveryTask | null>(null)

// 筛选表单
const filterForm = reactive({
  status: '',
  taskType: '',
})

// 日期范围
const dateRange = ref<[string, string] | null>(null)

// 分页
const pagination = reactive({
  current: 1,
  size: 20,
})

// 统计数据
const statistics = ref({
  totalDeliveries: 0,
  successRate: 0,
  avgRating: 0,
  totalEarnings: 0,
})

// 计算搜索参数
const searchParams = computed(() => {
  const params: any = {
    page: pagination.current,
    size: pagination.size,
  }

  if (filterForm.status) params.status = filterForm.status
  if (filterForm.taskType) params.taskType = filterForm.taskType

  if (dateRange.value) {
    params.startDate = dateRange.value[0]
    params.endDate = dateRange.value[1]
  }

  return params
})

// 加载配送历史
const loadDeliveryHistory = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      startDate: searchForm.dateRange?.[0],
      endDate: searchForm.dateRange?.[1],
    }

    const response = await courierApi.getDeliveryHistory(params)

    if (response.code === 200) {
      deliveryList.value = response.data.records || []
      total.value = response.data.total || 0
    } else {
      throw new Error('获取配送历史失败')
    }
  } catch (error) {
    console.error('加载配送历史失败:', error)
    ElMessage.error('加载配送历史失败')
    deliveryList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    const response = await courierApi.getTaskStatistics()

    if (response.code === 200) {
      const data = response.data.data
      statistics.value = {
        totalDeliveries: data.completedTasks || 0,
        successRate: data.successRate || 100,
        avgRating: data.avgRating || 5.0,
        totalEarnings: data.totalEarnings || 0,
      }
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadDeliveryHistory()
}

// 重置
const handleReset = () => {
  Object.assign(filterForm, {
    status: '',
    taskType: '',
  })
  dateRange.value = null
  pagination.current = 1
  loadDeliveryHistory()
}

// 刷新历史
const refreshHistory = () => {
  loadDeliveryHistory()
  loadStatistics()
}

// 分页变化
const handleSizeChange = () => {
  pagination.current = 1
  loadDeliveryHistory()
}

const handleCurrentChange = () => {
  loadDeliveryHistory()
}

// 查看任务详情
const viewTaskDetail = (taskId: number) => {
  router.push(`/courier/task/detail/${taskId}`)
}

// 查看完成凭证
const viewProof = (task: DeliveryTask) => {
  selectedTask.value = task
  showProofDialog.value = true
}

// 导出历史记录
const exportHistory = () => {
  ElMessage.info('导出功能开发中...')
}

// 工具函数
const getTaskTypeText = (type: string) => {
  const textMap = {
    PICKUP: '揽件',
    DELIVERY: '派送',
  }
  return textMap[type] || type
}

const getTaskTypeTag = (type: string) => {
  const tagMap = {
    PICKUP: 'warning',
    DELIVERY: 'success',
  }
  return tagMap[type] || ''
}

const getStatusText = (status: string) => {
  const textMap = {
    COMPLETED: '已完成',
    CANCELLED: '已取消',
  }
  return textMap[status] || status
}

const getStatusTag = (status: string) => {
  const tagMap = {
    COMPLETED: 'success',
    CANCELLED: 'danger',
  }
  return tagMap[status] || ''
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm')
}

onMounted(() => {
  loadDeliveryHistory()
  loadStatistics()
})
</script>

<style scoped>
.delivery-history {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  padding: 20px 0;
}

.stat-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

.filter-card {
  margin-bottom: 20px;
}

.address-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.address-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #666;
}

.contact-phone {
  color: #409eff;
  font-size: 12px;
}

.delivery-fee {
  font-weight: bold;
  color: #f56c6c;
}

.text-gray {
  color: #999;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.proof-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.task-info h4 {
  margin: 0 0 8px 0;
  color: #333;
}

.task-info p {
  margin: 0;
  color: #666;
}

.proof-image {
  text-align: center;
}

.proof-image img {
  max-width: 100%;
  max-height: 400px;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.proof-remarks h5 {
  margin: 0 0 8px 0;
  color: #333;
}

.proof-remarks p {
  margin: 0;
  color: #666;
  line-height: 1.6;
}

.chart-card {
  margin-top: 20px;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
}
</style>
