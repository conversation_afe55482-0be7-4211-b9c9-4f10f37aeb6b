package org.example.logisticsuser.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.example.logisticsuser.entity.ShippingAddress;

/**
 * 收发货地址Mapper
 */
@Mapper
public interface ShippingAddressMapper extends BaseMapper<ShippingAddress> {

    /**
     * 取消用户指定类型的所有默认地址
     */
    @Update("UPDATE shipping_addresses SET is_default = 0 WHERE user_id = #{userId} AND address_type = #{addressType}")
    int cancelDefaultByUserAndType(@Param("userId") Long userId, @Param("addressType") Integer addressType);
}