package org.example.logisticsorder.service;

import org.example.logisticsorder.dto.CreateOrderDTO;
import org.example.logisticsorder.entity.Order;

import java.util.List;

/**
 * 订单生命周期管理服务
 * 负责订单全生命周期的业务流程管理
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
public interface OrderLifecycleService {

    /**
     * 创建订单（包含轨迹初始化和通知）
     */
    Order createOrderWithLifecycle(CreateOrderDTO createOrderDTO);

    /**
     * 支付成功处理（更新状态、轨迹、发送通知）
     */
    boolean processPaymentSuccess(String orderNumber, String paymentMethod);

    /**
     * 分配揽件员（更新状态、创建任务、发送通知）
     */
    boolean assignPickupCourier(Long orderId, Long courierId, String operatorName);

    /**
     * 揽件完成处理（更新状态、轨迹、发送通知）
     */
    boolean processPickupCompleted(Long orderId, Long courierId, String location, String remarks);

    /**
     * 货物到达网点处理（更新状态、轨迹）
     */
    boolean processArriveStation(Long orderId, String stationCode, String operatorName);

    /**
     * 货物离开网点处理（更新状态、轨迹）
     */
    boolean processDepartStation(Long orderId, String stationCode, String nextStation, String operatorName);

    /**
     * 分配派送员（更新状态、创建任务、发送通知）
     */
    boolean assignDeliveryCourier(Long orderId, Long courierId, String operatorName);

    /**
     * 配送完成处理（更新状态、轨迹、发送通知）
     */
    boolean processDeliveryCompleted(Long orderId, Long courierId, String signProof, String remarks);

    /**
     * 异常处理（更新状态、轨迹、发送通知）
     */
    boolean processException(Long orderId, String exceptionType, String description, String operatorName);

    /**
     * 取消订单处理（更新状态、轨迹、发送通知）
     */
    boolean processCancelOrder(Long orderId, String cancelReason, String operatorName);

    /**
     * 获取订单当前可执行的操作
     */
    List<String> getAvailableActions(String orderStatus);

    /**
     * 验证状态流转是否合法
     */
    boolean validateStatusTransition(String currentStatus, String targetStatus);
}
