package com.logistics.common.constants;

/**
 * Redis键常量
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
public class RedisKeys {

    /**
     * 用户Token前缀
     */
    public static final String USER_TOKEN = "user:token:";

    /**
     * 用户信息缓存前缀
     */
    public static final String USER_INFO = "user:info:";

    /**
     * 用户权限缓存前缀
     */
    public static final String USER_PERMISSIONS = "user:permissions:";

    /**
     * 地址信息缓存前缀
     */
    public static final String ADDRESS_INFO = "address:info:";

    /**
     * 订单缓存前缀
     */
    public static final String ORDER_INFO = "order:info:";

    /**
     * 订单状态缓存前缀
     */
    public static final String ORDER_STATUS = "order:status:";

    /**
     * 配送员位置前缀
     */
    public static final String COURIER_LOCATION = "courier:location:";

    /**
     * 配送员状态前缀
     */
    public static final String COURIER_STATUS = "courier:status:";

    /**
     * 验证码前缀
     */
    public static final String VERIFY_CODE = "verify:code:";

    /**
     * 短信发送限制前缀
     */
    public static final String SMS_LIMIT = "sms:limit:";

    /**
     * 邮件发送限制前缀
     */
    public static final String EMAIL_LIMIT = "email:limit:";

    /**
     * 登录失败次数限制前缀
     */
    public static final String LOGIN_FAIL_LIMIT = "login:fail:";

    /**
     * 物流轨迹缓存前缀
     */
    public static final String LOGISTICS_TRACE = "logistics:trace:";

    /**
     * 系统配置缓存前缀
     */
    public static final String SYSTEM_CONFIG = "system:config:";

    /**
     * 获取用户Token的完整键
     */
    public static String getUserTokenKey(String username) {
        return USER_TOKEN + username;
    }

    /**
     * 获取用户信息的完整键
     */
    public static String getUserInfoKey(Long userId) {
        return USER_INFO + userId;
    }

    /**
     * 获取订单信息的完整键
     */
    public static String getOrderInfoKey(String orderNumber) {
        return ORDER_INFO + orderNumber;
    }

    /**
     * 获取配送员位置的完整键
     */
    public static String getCourierLocationKey(Long courierId) {
        return COURIER_LOCATION + courierId;
    }

    /**
     * 获取验证码的完整键
     */
    public static String getVerifyCodeKey(String phone) {
        return VERIFY_CODE + phone;
    }
}
