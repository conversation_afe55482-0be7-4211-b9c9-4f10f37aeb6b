package org.example.logisticsorder.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;

/**
 * 创建订单请求DTO
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
public class CreateOrderDTO {

    // 寄件人信息
    @NotBlank(message = "寄件人姓名不能为空")
    private String senderName;

    @NotBlank(message = "寄件人电话不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "寄件人电话格式不正确")
    private String senderPhone;

    @NotBlank(message = "寄件人地址不能为空")
    private String senderAddress;

    private String senderProvince;
    private String senderCity;
    private String senderDistrict;
    private BigDecimal senderLongitude;
    private BigDecimal senderLatitude;

    // 收件人信息
    @NotBlank(message = "收件人姓名不能为空")
    private String receiverName;

    @NotBlank(message = "收件人电话不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "收件人电话格式不正确")
    private String receiverPhone;

    @NotBlank(message = "收件人地址不能为空")
    private String receiverAddress;

    private String receiverProvince;
    private String receiverCity;
    private String receiverDistrict;
    private BigDecimal receiverLongitude;
    private BigDecimal receiverLatitude;

    // 物品信息
    @NotBlank(message = "物品名称不能为空")
    private String itemName;

    private String itemType;

    @DecimalMin(value = "0.01", message = "物品重量必须大于0")
    private BigDecimal itemWeight;

    private BigDecimal itemVolume;
    private BigDecimal itemLength;
    private BigDecimal itemWidth;
    private BigDecimal itemHeight;
    private BigDecimal itemValue;
    private Boolean isFragile = false;

    // 服务信息
    @NotBlank(message = "服务类型不能为空")
    @Pattern(regexp = "^(STANDARD|EXPRESS|URGENT|SAME_DAY)$", message = "服务类型不正确")
    private String serviceType = "STANDARD";

    @Pattern(regexp = "^(ONLINE|COD)$", message = "支付方式不正确")
    private String paymentMethod = "ONLINE";

    // 其他信息
    private String remarks;
}