<template>
  <div class="operator-dashboard">
    <div class="dashboard-header">
      <div class="header-left">
        <h1>操作员工作台</h1>
        <p>订单调度与物流管理</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button type="danger" @click="showEmergencyDispatch = true">
          <el-icon><Warning /></el-icon>
          紧急调度
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon pending">
                <el-icon size="30"><Clock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.pendingOrders }}</div>
                <div class="stat-label">待处理订单</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon processed">
                <el-icon size="30"><Van /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.todayProcessed }}</div>
                <div class="stat-label">今日处理</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon online">
                <el-icon size="30"><User /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.onlineCouriers }}</div>
                <div class="stat-label">在线配送员</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon exception">
                <el-icon size="30"><Warning /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.exceptionOrders }}</div>
                <div class="stat-label">异常订单</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon revenue">
                <el-icon size="30"><Money /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">¥{{ (stats.todayRevenue || 0).toLocaleString() }}</div>
                <div class="stat-label">今日收入</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon satisfaction">
                <el-icon size="30"><Star /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.customerSatisfaction }}%</div>
                <div class="stat-label">客户满意度</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 系统告警 -->
    <div class="alerts-section" v-if="alerts.length > 0">
      <el-alert
        v-for="alert in alerts"
        :key="alert.id"
        :title="alert.title"
        :type="getAlertType(alert.level)"
        :description="alert.message"
        show-icon
        :closable="true"
        @close="resolveAlert(alert.id)"
        style="margin-bottom: 10px"
      />
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="20">
        <!-- 待处理订单 -->
        <el-col :span="14">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>待处理订单</span>
                <div class="header-actions">
                  <el-select
                    v-model="orderFilter.priority"
                    placeholder="优先级"
                    clearable
                    size="small"
                    style="width: 100px; margin-right: 10px"
                  >
                    <el-option label="紧急" value="emergency" />
                    <el-option label="加急" value="urgent" />
                    <el-option label="普通" value="normal" />
                  </el-select>
                  <el-button type="text" @click="$router.push('/operator/orders')"
                    >查看全部</el-button
                  >
                </div>
              </div>
            </template>

            <el-table
              :data="pendingOrders"
              style="width: 100%"
              @selection-change="handleOrderSelection"
              max-height="400"
            >
              <el-table-column type="selection" width="50" />
              <el-table-column prop="orderNumber" label="订单号" width="120" />
              <el-table-column prop="customerName" label="客户" width="100" />
              <el-table-column prop="deliveryAddress" label="目的地" show-overflow-tooltip />
              <el-table-column prop="priority" label="优先级" width="80">
                <template #default="scope">
                  <el-tag :type="getPriorityType(scope.row.priority)">
                    {{ getPriorityText(scope.row.priority) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="createTime" label="创建时间" width="120">
                <template #default="scope">
                  {{ formatTime(scope.row.createTime) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template #default="scope">
                  <el-button type="primary" size="small" @click="assignOrder(scope.row)">
                    分配
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <div class="table-footer" v-if="selectedOrders.length > 0">
              <span>已选择 {{ selectedOrders.length }} 个订单</span>
              <el-button type="primary" size="small" @click="showBatchAssign = true">
                批量分配
              </el-button>
            </div>
          </el-card>
        </el-col>

        <!-- 配送员状态 -->
        <el-col :span="10">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>配送员状态</span>
                <el-button type="text" @click="$router.push('/operator/couriers')"
                  >管理配送员</el-button
                >
              </div>
            </template>

            <div class="courier-status">
              <div class="status-item" v-for="courier in courierList" :key="courier.id">
                <div class="courier-info">
                  <div class="courier-name">{{ courier.name }}</div>
                  <div class="courier-details">
                    <span class="location">{{ courier.location }}</span>
                    <span class="orders">当前订单: {{ courier.currentOrders }}</span>
                  </div>
                </div>
                <div class="courier-actions">
                  <el-tag :type="getStatusType(courier.status)" size="small">
                    {{ getStatusText(courier.status) }}
                  </el-tag>
                  <el-rate
                    v-model="courier.rating"
                    disabled
                    size="small"
                    style="margin-left: 10px"
                  />
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 第二行 -->
      <el-row :gutter="20" style="margin-top: 20px">
        <!-- 客服工单 -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>客服工单</span>
                <div class="header-actions">
                  <el-button type="primary" size="small" @click="showCreateTicket = true">
                    新建工单
                  </el-button>
                  <el-button type="text" @click="$router.push('/operator/tickets')"
                    >查看全部</el-button
                  >
                </div>
              </div>
            </template>

            <el-table :data="serviceTickets" style="width: 100%" max-height="300">
              <el-table-column prop="ticketNumber" label="工单号" width="100" />
              <el-table-column prop="type" label="类型" width="80">
                <template #default="scope">
                  <el-tag size="small">{{ getTicketTypeText(scope.row.type) }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="customerName" label="客户" width="100" />
              <el-table-column prop="status" label="状态" width="80">
                <template #default="scope">
                  <el-tag :type="getTicketStatusType(scope.row.status)" size="small">
                    {{ getTicketStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100">
                <template #default="scope">
                  <el-button type="text" size="small" @click="handleTicket(scope.row)">
                    处理
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>

        <!-- 实时地图 -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>实时配送地图</span>
                <el-button type="text" @click="$router.push('/operator/map')">全屏查看</el-button>
              </div>
            </template>

            <div class="map-container">
              <div class="map-placeholder">
                <el-icon size="60" color="#ccc"><MapLocation /></el-icon>
                <p>配送地图视图</p>
                <p class="map-stats">
                  配送中:
                  {{ (mapData.couriers || []).filter((c) => c.status === 'busy').length }} 人 |
                  待取件:
                  {{ (mapData.orders || []).filter((o) => o.status === 'pending').length }} 单
                </p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 订单分配对话框 -->
    <el-dialog v-model="showAssignDialog" title="分配订单" width="600px">
      <div v-if="currentOrder">
        <div class="order-info">
          <h4>订单信息</h4>
          <p><strong>订单号:</strong> {{ currentOrder.orderNumber }}</p>
          <p><strong>客户:</strong> {{ currentOrder.customerName }}</p>
          <p><strong>配送地址:</strong> {{ currentOrder.deliveryAddress }}</p>
        </div>

        <el-form :model="assignForm" label-width="100px">
          <el-form-item label="选择配送员">
            <el-select
              v-model="assignForm.courierId"
              placeholder="请选择配送员"
              style="width: 100%"
            >
              <el-option
                v-for="courier in availableCouriers"
                :key="courier.id"
                :label="`${courier.name} (${courier.location}) - 当前订单: ${courier.currentOrders}`"
                :value="courier.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="备注">
            <el-input v-model="assignForm.notes" type="textarea" rows="3" />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="showAssignDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmAssign" :loading="assigning">确认分配</el-button>
      </template>
    </el-dialog>

    <!-- 批量分配对话框 -->
    <el-dialog v-model="showBatchAssign" title="批量分配订单" width="800px">
      <div class="batch-assign-content">
        <div class="selected-orders">
          <h4>选中的订单 ({{ selectedOrders.length }})</h4>
          <el-table :data="selectedOrders" max-height="200">
            <el-table-column prop="orderNumber" label="订单号" />
            <el-table-column prop="customerName" label="客户" />
            <el-table-column prop="deliveryAddress" label="配送地址" />
          </el-table>
        </div>

        <div class="courier-assignment">
          <h4>配送员分配</h4>
          <div v-for="(order, index) in selectedOrders" :key="order.id" class="assignment-item">
            <span class="order-label">{{ order.orderNumber }}</span>
            <el-select
              v-model="batchAssignments[index]"
              placeholder="选择配送员"
              size="small"
              style="width: 200px"
            >
              <el-option
                v-for="courier in availableCouriers"
                :key="courier.id"
                :label="courier.name"
                :value="courier.id"
              />
            </el-select>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="showBatchAssign = false">取消</el-button>
        <el-button type="primary" @click="confirmBatchAssign" :loading="batchAssigning"
          >确认分配</el-button
        >
      </template>
    </el-dialog>

    <!-- 紧急调度对话框 -->
    <el-dialog v-model="showEmergencyDispatch" title="紧急调度" width="600px">
      <el-form :model="emergencyForm" label-width="100px">
        <el-form-item label="调度原因">
          <el-input v-model="emergencyForm.reason" type="textarea" rows="3" />
        </el-form-item>
        <el-form-item label="优先级">
          <el-radio-group v-model="emergencyForm.priority">
            <el-radio label="high">高优先级</el-radio>
            <el-radio label="urgent">紧急</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showEmergencyDispatch = false">取消</el-button>
        <el-button type="danger" @click="confirmEmergencyDispatch">确认调度</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Clock,
  Van,
  User,
  Warning,
  Money,
  Star,
  Refresh,
  MapLocation,
} from '@element-plus/icons-vue'
import {
  operatorApi,
  type OperatorStats,
  type OrderDispatch,
  type CourierInfo,
  type ServiceTicket,
} from '@/api/operator'
import http from '@/utils/http'

// 响应式数据
const stats = ref<OperatorStats>({
  pendingOrders: 0,
  todayProcessed: 0,
  onlineCouriers: 0,
  exceptionOrders: 0,
  todayRevenue: 0,
  customerSatisfaction: 0,
})

const pendingOrders = ref<OrderDispatch[]>([])
const courierList = ref<CourierInfo[]>([])
const serviceTickets = ref<ServiceTicket[]>([])
const alerts = ref<any[]>([])
const mapData = ref<any>({ couriers: [], orders: [] })

// 过滤条件
const orderFilter = reactive({
  priority: '',
  status: '',
  keyword: '',
})

// 选中的订单
const selectedOrders = ref<OrderDispatch[]>([])

// 对话框控制
const showAssignDialog = ref(false)
const showBatchAssign = ref(false)
const showEmergencyDispatch = ref(false)
const showCreateTicket = ref(false)

// 当前操作的订单
const currentOrder = ref<OrderDispatch | null>(null)

// 可用配送员
const availableCouriers = ref<CourierInfo[]>([])

// 表单数据
const assignForm = reactive({
  courierId: '',
  notes: '',
})

const batchAssignments = ref<number[]>([])

const emergencyForm = reactive({
  reason: '',
  priority: 'high' as 'high' | 'urgent',
})

// 加载状态
const assigning = ref(false)
const batchAssigning = ref(false)

// 生命周期
onMounted(() => {
  loadData()
  // 定时刷新数据
  setInterval(loadData, 30000) // 30秒刷新一次
})

// 方法
const loadData = async () => {
  try {
    await Promise.all([
      loadStats(),
      // loadPendingOrders(), // 暂时禁用，等待后端实现
      loadCouriers(),
      // loadServiceTickets(), // 暂时禁用，等待后端实现
      // loadAlerts(), // 暂时禁用，等待后端实现
      // loadMapData(), // 暂时禁用，等待后端实现
    ])
  } catch (error) {
    console.error('加载数据失败:', error)
  }
}

const loadStats = async () => {
  try {
    const response = await operatorApi.getStats()
    if (response.code === 200) {
      stats.value = response.data
    } else {
      throw new Error(response.message || '获取统计数据失败')
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    // 清空数据，显示加载失败状态
    stats.value = {
      pendingOrders: 0,
      todayProcessed: 0,
      onlineCouriers: 0,
      exceptionOrders: 0,
      todayRevenue: 0,
      customerSatisfaction: 0,
    }
  }
}

const loadPendingOrders = async () => {
  try {
    const response = await operatorApi.getPendingOrders({
      page: 1,
      size: 10,
      ...orderFilter,
    })
    if (response.code === 200) {
      pendingOrders.value = Array.isArray(response.data) ? response.data : (response.data.list || [])
    } else {
      throw new Error(response.message || '获取待处理订单失败')
    }
  } catch (error) {
    console.error('获取待处理订单失败:', error)
    pendingOrders.value = []
  }
}

const loadCouriers = async () => {
  try {
    const response = await operatorApi.getCouriers({
      status: 1, // 在线状态
    })
    if (response.code === 200) {
      courierList.value = Array.isArray(response.data) ? response.data : (response.data.list || [])
    } else {
      throw new Error(response.message || '获取配送员列表失败')
    }
  } catch (error) {
    console.error('获取配送员列表失败:', error)
    courierList.value = []
  }
}

const loadServiceTickets = async () => {
  try {
    const response = await operatorApi.getServiceTickets({
      page: 1,
      size: 5,
      status: 'open',
    })
    if (response.code === 200) {
      serviceTickets.value = Array.isArray(response.data) ? response.data : (response.data.list || [])
    } else {
      throw new Error(response.message || '获取服务工单失败')
    }
  } catch (error) {
    console.error('获取服务工单失败:', error)
    serviceTickets.value = []
  }
}

const loadAlerts = async () => {
  try {
    const response = await operatorApi.getSystemAlerts()
    if (response.code === 200) {
      alerts.value = (response.data || []).filter((alert: any) => !alert.resolved)
    } else {
      throw new Error(response.message || '获取系统告警失败')
    }
  } catch (error) {
    console.error('获取系统告警失败:', error)
    alerts.value = []
  }
}

const loadMapData = async () => {
  try {
    const response = await operatorApi.getDeliveryMapData()
    if (response.code === 200) {
      mapData.value = response.data || { couriers: [], orders: [] }
    } else {
      throw new Error(response.message || '获取地图数据失败')
    }
  } catch (error) {
    console.error('获取地图数据失败:', error)
    mapData.value = { couriers: [], orders: [] }
  }
}

const refreshData = () => {
  loadData()
  ElMessage.success('数据已刷新')
}

// 订单相关方法
const handleOrderSelection = (selection: OrderDispatch[]) => {
  selectedOrders.value = selection
  batchAssignments.value = new Array(selection.length).fill('')
}

const assignOrder = async (order: OrderDispatch) => {
  currentOrder.value = order
  try {
    const response = await operatorApi.getAvailableCouriers(order.deliveryAddress)
    availableCouriers.value = response.data
  } catch (error) {
    availableCouriers.value = courierList.value.filter((c) => c.status === 'online')
  }
  showAssignDialog.value = true
}

const confirmAssign = async () => {
  if (!assignForm.courierId || !currentOrder.value) {
    ElMessage.warning('请选择配送员')
    return
  }

  assigning.value = true
  try {
    console.log(`开始分配订单 ${currentOrder.value.id} 给配送员 ${assignForm.courierId}`)

    // 直接调用HTTP接口
    const response = await http.put(`/order/${currentOrder.value.id}/assign-delivery`, null, {
      params: {
        courierId: Number(assignForm.courierId)
      }
    })

    console.log('分配响应:', response)

    if (response && response.code === 200) {
      ElMessage.success('订单分配成功')
      showAssignDialog.value = false

      // 重置表单
      assignForm.courierId = ''
      assignForm.notes = ''

      // 刷新数据
      await loadData()
    } else {
      throw new Error(response?.message || '分配失败')
    }
  } catch (error: any) {
    console.error('分配失败:', error)
    ElMessage.error(error.message || '分配失败，请重试')
  } finally {
    assigning.value = false
  }
}

const confirmBatchAssign = async () => {
  const assignments = selectedOrders.value
    .map((order, index) => ({
      orderId: order.id,
      courierId: batchAssignments.value[index],
    }))
    .filter((a) => a.courierId)

  if (assignments.length === 0) {
    ElMessage.warning('请为订单分配配送员')
    return
  }

  batchAssigning.value = true
  try {
    await operatorApi.batchAssignOrders(assignments)
    ElMessage.success(`成功分配 ${assignments.length} 个订单`)
    showBatchAssign.value = false
    loadPendingOrders()
    selectedOrders.value = []
  } catch (error) {
    ElMessage.error('批量分配失败，请重试')
  } finally {
    batchAssigning.value = false
  }
}

const confirmEmergencyDispatch = async () => {
  if (!emergencyForm.reason) {
    ElMessage.warning('请输入调度原因')
    return
  }

  try {
    await operatorApi.emergencyDispatch({
      orderIds: selectedOrders.value.map((o) => o.id),
      reason: emergencyForm.reason,
      priority: emergencyForm.priority,
    })
    ElMessage.success('紧急调度已启动')
    showEmergencyDispatch.value = false
    loadData()
  } catch (error) {
    ElMessage.error('紧急调度失败，请重试')
  }
}

// 工单处理
const handleTicket = (ticket: ServiceTicket) => {
  // 跳转到工单详情页面
  // this.$router.push(`/operator/tickets/${ticket.id}`)
  ElMessage.info('跳转到工单处理页面')
}

// 告警处理
const resolveAlert = async (alertId: number) => {
  try {
    await operatorApi.resolveAlert(alertId)
    loadAlerts()
  } catch (error) {
    ElMessage.error('解决告警失败')
  }
}

// 工具方法
const getPriorityType = (priority: string) => {
  switch (priority) {
    case 'emergency':
      return 'danger'
    case 'urgent':
      return 'warning'
    default:
      return 'info'
  }
}

const getPriorityText = (priority: string) => {
  switch (priority) {
    case 'emergency':
      return '紧急'
    case 'urgent':
      return '加急'
    default:
      return '普通'
  }
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'online':
      return 'success'
    case 'busy':
      return 'warning'
    case 'rest':
      return 'info'
    default:
      return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'online':
      return '空闲'
    case 'busy':
      return '配送中'
    case 'rest':
      return '休息'
    case 'offline':
      return '离线'
    default:
      return '未知'
  }
}

const getTicketTypeText = (type: string) => {
  const types: Record<string, string> = {
    complaint: '投诉',
    inquiry: '咨询',
    lost: '丢失',
    damage: '损坏',
    delay: '延误',
  }
  return types[type] || type
}

const getTicketStatusType = (status: string) => {
  switch (status) {
    case 'open':
      return 'danger'
    case 'processing':
      return 'warning'
    case 'resolved':
      return 'success'
    case 'closed':
      return 'info'
    default:
      return 'info'
  }
}

const getTicketStatusText = (status: string) => {
  const statuses: Record<string, string> = {
    open: '待处理',
    processing: '处理中',
    resolved: '已解决',
    closed: '已关闭',
  }
  return statuses[status] || status
}

const getAlertType = (level: string) => {
  switch (level) {
    case 'critical':
      return 'error'
    case 'error':
      return 'error'
    case 'warning':
      return 'warning'
    default:
      return 'info'
  }
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

const loadStatistics = async () => {
  try {
    console.log('开始加载统计数据...')
    
    // 并行请求多个统计接口
    const [ordersResponse, couriersResponse] = await Promise.allSettled([
      orderApi.getOrderStatistics(),
      operatorApi.getCourierStatistics()
    ])

    // 处理订单统计数据
    if (ordersResponse.status === 'fulfilled' && ordersResponse.value.code === 200) {
      const orderStats = ordersResponse.value.data
      statistics.value.totalOrders = orderStats.total || 0
      statistics.value.pendingOrders = orderStats.pending || 0
      statistics.value.completedOrders = orderStats.completed || 0
      statistics.value.cancelledOrders = orderStats.cancelled || 0
      statistics.value.todayRevenue = orderStats.todayRevenue || 0
      statistics.value.monthRevenue = orderStats.monthRevenue || 0
      console.log('订单统计数据加载成功:', orderStats)
    } else {
      console.warn('订单统计数据加载失败，使用默认值')
      // 设置默认统计数据
      statistics.value.totalOrders = 0
      statistics.value.pendingOrders = 0
      statistics.value.completedOrders = 0
      statistics.value.cancelledOrders = 0
      statistics.value.todayRevenue = 0
      statistics.value.monthRevenue = 0
    }

    // 处理配送员统计数据
    if (couriersResponse.status === 'fulfilled' && couriersResponse.value.code === 200) {
      const courierStats = couriersResponse.value.data
      statistics.value.totalCouriers = courierStats.total || 0
      statistics.value.onlineCouriers = courierStats.online || 0
      statistics.value.busyCouriers = courierStats.busy || 0
      console.log('配送员统计数据加载成功:', courierStats)
    } else {
      console.warn('配送员统计数据加载失败，使用默认值')
      // 设置默认统计数据
      statistics.value.totalCouriers = 0
      statistics.value.onlineCouriers = 0
      statistics.value.busyCouriers = 0
    }

    console.log('最终统计数据:', statistics.value)
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
    
    // 设置默认统计数据
    Object.assign(statistics.value, {
      totalOrders: 0,
      pendingOrders: 0,
      completedOrders: 0,
      cancelledOrders: 0,
      totalCouriers: 0,
      onlineCouriers: 0,
      busyCouriers: 0,
      todayRevenue: 0,
      monthRevenue: 0,
    })
  }
}
</script>

<style scoped>
.operator-dashboard {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left h1 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 24px;
}

.header-left p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 10px;
}

.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  height: 100px;
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-item {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 10px;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.stat-icon.pending {
  background-color: #fdf6ec;
  color: #e6a23c;
}
.stat-icon.processed {
  background-color: #f0f9ff;
  color: #67c23a;
}
.stat-icon.online {
  background-color: #ecf5ff;
  color: #409eff;
}
.stat-icon.exception {
  background-color: #fef0f0;
  color: #f56c6c;
}
.stat-icon.revenue {
  background-color: #f0f9ff;
  color: #909399;
}
.stat-icon.satisfaction {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.alerts-section {
  margin-bottom: 20px;
}

.main-content .el-card {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: none;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
}

.courier-status {
  max-height: 350px;
  overflow-y: auto;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.status-item:last-child {
  border-bottom: none;
}

.courier-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.courier-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.courier-details .location {
  font-size: 12px;
  color: #999;
}

.courier-details .orders {
  font-size: 12px;
  color: #666;
}

.courier-actions {
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 5px;
}

.table-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 10px;
}

.map-container {
  height: 250px;
  background-color: #f9f9f9;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.map-placeholder p {
  margin: 10px 0 5px 0;
}

.map-stats {
  font-size: 12px;
  color: #666;
}

.order-info {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.order-info h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.order-info p {
  margin: 5px 0;
  font-size: 14px;
}

.batch-assign-content {
  max-height: 500px;
  overflow-y: auto;
}

.selected-orders h4,
.courier-assignment h4 {
  margin: 0 0 15px 0;
  color: #333;
}

.assignment-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.assignment-item:last-child {
  border-bottom: none;
}

.order-label {
  font-weight: 500;
  min-width: 100px;
}
</style>
