<template>
  <div class="customer-dashboard">
    <div class="dashboard-header">
      <h1>客户中心</h1>
      <p>欢迎回来，{{ userStore.userInfo?.realName }}</p>
    </div>

    <div class="dashboard-content">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card" @click="goToOrders('PENDING')">
            <div class="stat-item">
              <div class="stat-icon">
                <el-icon size="40" color="#409EFF"><Box /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.pendingOrders }}</div>
                <div class="stat-label">待发货订单</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stat-card" @click="goToOrders('IN_TRANSIT')">
            <div class="stat-item">
              <div class="stat-icon">
                <el-icon size="40" color="#67C23A"><Van /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.inTransitOrders }}</div>
                <div class="stat-label">运输中订单</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stat-card" @click="goToOrders('OUT_FOR_DELIVERY')">
            <div class="stat-item">
              <div class="stat-icon">
                <el-icon size="40" color="#E6A23C"><Clock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.outForDeliveryOrders }}</div>
                <div class="stat-label">待收货订单</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stat-card" @click="goToOrders('DELIVERED')">
            <div class="stat-item">
              <div class="stat-icon">
                <el-icon size="40" color="#F56C6C"><CircleCheck /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.deliveredOrders }}</div>
                <div class="stat-label">已完成订单</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px">
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>最近订单</span>
                <el-button type="text" @click="goToOrderList">查看全部</el-button>
              </div>
            </template>
            <div class="recent-orders" v-loading="loading">
              <div
                class="order-item"
                v-for="order in recentOrders"
                :key="order.id"
                @click="viewOrderDetail(order.id)"
              >
                <div class="order-info">
                  <div class="order-number">#{{ order.orderNumber }}</div>
                  <div class="order-status" :class="getStatusClass(order.orderStatus)">
                    {{ getStatusText(order.orderStatus) }}
                  </div>
                </div>
                <div class="order-details">
                  <div class="order-receiver">{{ order.receiverName }}</div>
                  <div class="order-time">{{ formatTime(order.createTime) }}</div>
                </div>
                <div class="order-amount">¥{{ order.totalFee.toFixed(2) }}</div>
              </div>
              <div v-if="recentOrders.length === 0 && !loading" class="empty-state">
                <el-empty description="暂无订单" />
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>快速操作</span>
              </div>
            </template>
            <div class="quick-actions">
              <el-button
                type="primary"
                size="large"
                style="width: 100%; margin-bottom: 10px"
                @click="createOrder"
              >
                <el-icon><Plus /></el-icon>
                创建新订单
              </el-button>
              <el-button
                size="large"
                style="width: 100%; margin-bottom: 10px"
                @click="goToOrderList"
              >
                <el-icon><Search /></el-icon>
                订单查询
              </el-button>
              <el-button size="large" style="width: 100%" @click="goToTracking">
                <el-icon><Location /></el-icon>
                物流追踪
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { orderApi } from '@/api/order'
import type { Order, OrderStatus } from '@/types/order'
import { Box, Van, Clock, CircleCheck, Plus, Search, Location } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

const router = useRouter()
const userStore = useAuthStore()

// 状态
const loading = ref(false)
const recentOrders = ref<Order[]>([])
const stats = ref({
  pendingOrders: 0,
  inTransitOrders: 0,
  outForDeliveryOrders: 0,
  deliveredOrders: 0,
})

// 加载最近订单
const loadRecentOrders = async () => {
  loading.value = true
  try {
    const response = await orderApi.getOrderList({
      page: 1,
      size: 5,
    })

    console.log('订单列表响应:', response)

    // 修复：正确处理API响应结构
    if (response.code === 200 && response.data) {
      // 后端返回的是IPage结构
      recentOrders.value = response.data.records || []

      // 计算统计数据
      try {
        const allOrdersResponse = await orderApi.getOrderList({
          page: 1,
          size: 1000, // 获取所有订单用于统计
        })

        if (allOrdersResponse.code === 200 && allOrdersResponse.data) {
          const allOrders: Order[] = allOrdersResponse.data.records || []
          stats.value = {
            pendingOrders: allOrders.filter((o: Order) => o.orderStatus === 'PENDING').length,
            inTransitOrders: allOrders.filter((o: Order) => o.orderStatus === 'IN_TRANSIT').length,
            outForDeliveryOrders: allOrders.filter(
              (o: Order) => o.orderStatus === 'OUT_FOR_DELIVERY',
            ).length,
            deliveredOrders: allOrders.filter((o: Order) => o.orderStatus === 'DELIVERED').length,
          }
        }
      } catch (statsError) {
        console.error('加载统计数据失败:', statsError)
        // 使用默认值，不影响页面显示
      }
    }
  } catch (error) {
    console.error('加载订单失败:', error)
    // 不显示错误消息，因为http拦截器已经处理了
  } finally {
    loading.value = false
  }
}
// 导航方法
const createOrder = () => {
  router.push('/customer/order/create')
}

const goToOrderList = () => {
  router.push('/customer/order/list')
}

const goToTracking = () => {
  router.push('/customer/tracking/index')
}

const goToOrders = (status: OrderStatus) => {
  router.push({
    path: '/customer/order/list',
    query: { status },
  })
}

const viewOrderDetail = (orderId: number) => {
  router.push(`/customer/order/detail/${orderId}`)
}

// 工具方法 - 修复类型错误
const getStatusText = (status: OrderStatus): string => {
  const statusMap: Record<OrderStatus, string> = {
    PENDING: '待发货',
    PICKED_UP: '已揽收',
    IN_TRANSIT: '运输中',
    OUT_FOR_DELIVERY: '派送中',
    DELIVERED: '已送达',
    CANCELLED: '已取消',
    RETURNED: '已退回',
  }
  return statusMap[status] || status
}

const getStatusClass = (status: OrderStatus): string => {
  const classMap: Record<OrderStatus, string> = {
    PENDING: 'status-pending',
    PICKED_UP: 'status-picked-up',
    IN_TRANSIT: 'status-in-transit',
    OUT_FOR_DELIVERY: 'status-out-for-delivery',
    DELIVERED: 'status-delivered',
    CANCELLED: 'status-cancelled',
    RETURNED: 'status-returned',
  }
  return classMap[status] || ''
}

const formatTime = (time: string) => {
  return dayjs(time).format('MM-DD HH:mm')
}

onMounted(() => {
  loadRecentOrders()
})
</script>

<style scoped>
.customer-dashboard {
  background: #f5f5f5;
  min-height: calc(100vh - 60px);
  padding: 0;
}

.dashboard-header {
  background: white;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dashboard-header h1 {
  margin: 0 0 10px 0;
  color: #333;
}

.dashboard-header p {
  margin: 0;
  color: #666;
}

.stat-card {
  height: 120px;
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-item {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 15px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.recent-orders {
  max-height: 300px;
  overflow-y: auto;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.order-item:hover {
  background-color: #f8f9fa;
  margin: 0 -16px;
  padding: 12px 16px;
  border-radius: 4px;
}

.order-item:last-child {
  border-bottom: none;
}

.order-number {
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.order-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  color: white;
}

.status-pending {
  background-color: #909399;
}
.status-picked-up {
  background-color: #409eff;
}
.status-in-transit {
  background-color: #67c23a;
}
.status-out-for-delivery {
  background-color: #e6a23c;
}
.status-delivered {
  background-color: #67c23a;
}
.status-cancelled {
  background-color: #f56c6c;
}
.status-returned {
  background-color: #f56c6c;
}

.order-details {
  flex: 1;
  text-align: center;
}

.order-receiver {
  color: #333;
  margin-bottom: 4px;
}

.order-time {
  color: #999;
  font-size: 12px;
}

.order-amount {
  font-weight: bold;
  color: #f56c6c;
  font-size: 16px;
}

.quick-actions {
  padding: 10px 0;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}
</style>
