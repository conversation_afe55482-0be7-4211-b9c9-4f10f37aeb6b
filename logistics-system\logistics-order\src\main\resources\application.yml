server:
  port: 8002

spring:
  application:
    name: logistics-order
  datasource:
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ****************************************************************************************************************************************************
      username: root
      password: liyuqw8017
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
  redis:
    host: localhost
    port: 6379
    password: redis123
    database: 0

mybatis-plus:
  mapper-locations: classpath*:mapper/*.xml
  type-aliases-package: org.example.logisticsorder.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

jwt:
  secret: logistics-secret-key-2024-for-microservice-authentication-system
  expiration: 86400

# 定价配置
logistics:
  pricing:
    base-fee: 10.00          # 基础运费
    weight-fee: 2.00         # 每公斤重量费
    distance-fee: 0.50       # 每公里距离费
    free-weight: 1.00        # 免费重量（公斤）
    free-distance: 10.00     # 免费距离（公里）
    insurance-rate: 0.01     # 保险费率（1%）
    packing-fee: 5.00        # 包装费

logging:
  level:
    org.example.logisticsorder: debug