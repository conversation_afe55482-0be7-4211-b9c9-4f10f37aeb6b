package org.example.logisticsuser.controller;

import com.logistics.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.example.logisticsuser.dto.AddressDTO;
import org.example.logisticsuser.service.AddressService;
import org.example.logisticsuser.service.UserService;
import org.example.logisticsuser.vo.AddressVO;
import org.example.logisticsuser.vo.UserInfoVO;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * 地址管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/user/addresses")
@CrossOrigin(origins = "*")
public class AddressController {

    @Autowired
    private AddressService addressService;

    @Autowired
    private UserService userService;

    /**
     * 获取用户地址列表
     */
    @GetMapping
    public Result<List<AddressVO>> getUserAddresses(
            @RequestParam(required = false) Integer addressType,
            HttpServletRequest request) {
        try {
            Long userId = getCurrentUserId(request);
            List<AddressVO> addresses = addressService.getUserAddresses(userId, addressType);
            return Result.success("获取地址列表成功", addresses);
        } catch (Exception e) {
            log.error("获取地址列表失败: {}", e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取地址详情
     */
    @GetMapping("/{addressId}")
    public Result<AddressVO> getAddressDetail(@PathVariable Long addressId, HttpServletRequest request) {
        try {
            Long userId = getCurrentUserId(request);
            AddressVO address = addressService.getAddressById(addressId, userId);
            return Result.success("获取地址详情成功", address);
        } catch (Exception e) {
            log.error("获取地址详情失败: addressId={}, error={}", addressId, e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 创建地址
     */
    @PostMapping
    public Result<AddressVO> createAddress(@Valid @RequestBody AddressDTO addressDTO, HttpServletRequest request) {
        try {
            Long userId = getCurrentUserId(request);
            AddressVO address = addressService.createAddress(userId, addressDTO);
            return Result.success("创建地址成功", address);
        } catch (Exception e) {
            log.error("创建地址失败: {}", e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 更新地址
     */
    @PutMapping("/{addressId}")
    public Result<AddressVO> updateAddress(
            @PathVariable Long addressId,
            @Valid @RequestBody AddressDTO addressDTO,
            HttpServletRequest request) {
        try {
            Long userId = getCurrentUserId(request);
            AddressVO address = addressService.updateAddress(addressId, userId, addressDTO);
            return Result.success("更新地址成功", address);
        } catch (Exception e) {
            log.error("更新地址失败: addressId={}, error={}", addressId, e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 删除地址
     */
    @DeleteMapping("/{addressId}")
    public Result<Void> deleteAddress(@PathVariable Long addressId, HttpServletRequest request) {
        try {
            Long userId = getCurrentUserId(request);
            addressService.deleteAddress(addressId, userId);
            return Result.success("删除地址成功", null);
        } catch (Exception e) {
            log.error("删除地址失败: addressId={}, error={}", addressId, e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 设置默认地址
     */
    @PostMapping("/{addressId}/default")
    public Result<Void> setDefaultAddress(@PathVariable Long addressId, HttpServletRequest request) {
        try {
            Long userId = getCurrentUserId(request);
            addressService.setDefaultAddress(addressId, userId);
            return Result.success("设置默认地址成功", null);
        } catch (Exception e) {
            log.error("设置默认地址失败: addressId={}, error={}", addressId, e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取省市区数据
     */
    @GetMapping("/regions")
    public Result<Object> getRegionData() {
        try {
            Object regionData = addressService.getRegionData();
            return Result.success("获取省市区数据成功", regionData);
        } catch (Exception e) {
            log.error("获取省市区数据失败: {}", e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 从请求中获取当前用户ID
     */
    private Long getCurrentUserId(HttpServletRequest request) {
        String token = extractTokenFromRequest(request);
        if (token == null) {
            throw new RuntimeException("Token不能为空");
        }

        UserInfoVO userInfo = userService.getCurrentUserInfo(token);
        return userInfo.getId();
    }

    /**
     * 从请求中提取Token
     */
    private String extractTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}