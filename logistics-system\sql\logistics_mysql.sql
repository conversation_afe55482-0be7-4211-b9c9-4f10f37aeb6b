-- =============================================
-- 物流跟踪系统数据库建表脚本
-- 数据库：logistics
-- 创建时间：2024-12-25
-- =============================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `logistics` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `logistics`;

-- =============================================
-- 用户相关表
-- =============================================

-- 用户表
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` VARCHAR(50) NOT NULL COMMENT '用户名',
  `password` VARCHAR(255) NOT NULL COMMENT '密码',
  `real_name` VARCHAR(50) NOT NULL COMMENT '真实姓名',
  `phone` VARCHAR(20) NOT NULL COMMENT '手机号',
  `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
  `id_card` VARCHAR(20) DEFAULT NULL COMMENT '身份证号',
  `user_type` VARCHAR(20) NOT NULL COMMENT '用户类型：CUSTOMER-普通用户，OPERATOR-操作员，COURIER-配送员，ADMIN-管理员',
  `status` VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '用户状态：ACTIVE-活跃，INACTIVE-非活跃，LOCKED-锁定',
  `avatar` VARCHAR(255) DEFAULT NULL COMMENT '头像URL',
  `last_login_time` TIMESTAMP NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` VARCHAR(50) DEFAULT NULL COMMENT '最后登录IP',
  `gender` TINYINT(1) DEFAULT NULL COMMENT '性别：1-男，2-女',
  `birthday` DATE DEFAULT NULL COMMENT '生日',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`),
  UNIQUE KEY `uk_phone` (`phone`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 角色表
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` VARCHAR(50) NOT NULL COMMENT '角色名称',
  `role_code` VARCHAR(50) NOT NULL COMMENT '角色编码',
  `description` VARCHAR(255) DEFAULT NULL COMMENT '角色描述',
  `status` VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-启用，INACTIVE-禁用',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_code` (`role_code`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 权限表
DROP TABLE IF EXISTS `permissions`;
CREATE TABLE `permissions` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `permission_name` VARCHAR(50) NOT NULL COMMENT '权限名称',
  `permission_code` VARCHAR(50) NOT NULL COMMENT '权限编码',
  `resource_type` VARCHAR(20) DEFAULT NULL COMMENT '资源类型：menu-菜单，button-按钮，api-接口',
  `resource_url` VARCHAR(255) DEFAULT NULL COMMENT '资源URL',
  `parent_id` BIGINT(20) DEFAULT '0' COMMENT '父级权限ID',
  `sort_order` INT(11) DEFAULT '0' COMMENT '排序',
  `icon` VARCHAR(100) DEFAULT NULL COMMENT '图标',
  `description` VARCHAR(255) DEFAULT NULL COMMENT '权限描述',
  `status` VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-启用，INACTIVE-禁用',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_permission_code` (`permission_code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';

-- 用户角色关联表
DROP TABLE IF EXISTS `user_roles`;
CREATE TABLE `user_roles` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` BIGINT(20) NOT NULL COMMENT '用户ID',
  `role_id` BIGINT(20) NOT NULL COMMENT '角色ID',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_role` (`user_id`,`role_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_role_id` (`role_id`),
  CONSTRAINT `fk_user_roles_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_roles_role_id` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';

-- 角色权限关联表
DROP TABLE IF EXISTS `role_permissions`;
CREATE TABLE `role_permissions` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `role_id` BIGINT(20) NOT NULL COMMENT '角色ID',
  `permission_id` BIGINT(20) NOT NULL COMMENT '权限ID',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_permission` (`role_id`,`permission_id`),
  KEY `idx_role_id` (`role_id`),
  KEY `idx_permission_id` (`permission_id`),
  CONSTRAINT `fk_role_permissions_role_id` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_role_permissions_permission_id` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';

-- =============================================
-- 订单相关表
-- =============================================

-- 订单主表
DROP TABLE IF EXISTS `orders`;
CREATE TABLE `orders` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_number` VARCHAR(50) NOT NULL COMMENT '订单号',
  `user_id` BIGINT(20) NOT NULL COMMENT '下单用户ID',
  `sender_name` VARCHAR(50) NOT NULL COMMENT '寄件人姓名',
  `sender_phone` VARCHAR(20) NOT NULL COMMENT '寄件人电话',
  `sender_address` TEXT NOT NULL COMMENT '寄件人地址',
  `sender_province` VARCHAR(20) DEFAULT NULL COMMENT '寄件人省份',
  `sender_city` VARCHAR(20) DEFAULT NULL COMMENT '寄件人城市',
  `sender_district` VARCHAR(20) DEFAULT NULL COMMENT '寄件人区域',
  `sender_longitude` DECIMAL(10,8) DEFAULT NULL COMMENT '寄件人经度',
  `sender_latitude` DECIMAL(10,8) DEFAULT NULL COMMENT '寄件人纬度',
  `receiver_name` VARCHAR(50) NOT NULL COMMENT '收件人姓名',
  `receiver_phone` VARCHAR(20) NOT NULL COMMENT '收件人电话',
  `receiver_address` TEXT NOT NULL COMMENT '收件人地址',
  `receiver_province` VARCHAR(20) DEFAULT NULL COMMENT '收件人省份',
  `receiver_city` VARCHAR(20) DEFAULT NULL COMMENT '收件人城市',
  `receiver_district` VARCHAR(20) DEFAULT NULL COMMENT '收件人区域',
  `receiver_longitude` DECIMAL(10,8) DEFAULT NULL COMMENT '收件人经度',
  `receiver_latitude` DECIMAL(10,8) DEFAULT NULL COMMENT '收件人纬度',
  `item_name` VARCHAR(255) DEFAULT NULL COMMENT '物品名称',
  `item_type` VARCHAR(50) DEFAULT NULL COMMENT '物品类型',
  `item_weight` DECIMAL(10,2) DEFAULT NULL COMMENT '物品重量(kg)',
  `item_volume` DECIMAL(10,2) DEFAULT NULL COMMENT '物品体积(m³)',
  `item_length` DECIMAL(10,2) DEFAULT NULL COMMENT '物品长度(cm)',
  `item_width` DECIMAL(10,2) DEFAULT NULL COMMENT '物品宽度(cm)',
  `item_height` DECIMAL(10,2) DEFAULT NULL COMMENT '物品高度(cm)',
  `item_value` DECIMAL(10,2) DEFAULT NULL COMMENT '物品价值',
  `is_fragile` TINYINT(1) DEFAULT '0' COMMENT '是否易碎品：0-否，1-是',
  `service_type` VARCHAR(20) DEFAULT 'STANDARD' COMMENT '服务类型：STANDARD-标准，EXPRESS-快递，URGENT-加急',
  `shipping_fee` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT '运费',
  `insurance_fee` DECIMAL(10,2) DEFAULT '0.00' COMMENT '保险费',
  `packing_fee` DECIMAL(10,2) DEFAULT '0.00' COMMENT '包装费',
  `total_fee` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT '总费用',
  `payment_method` VARCHAR(20) DEFAULT 'ONLINE' COMMENT '支付方式：ONLINE-在线支付，COD-货到付款',
  `payment_status` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '支付状态：0-未支付，1-已支付，2-已退款',
  `order_status` VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '订单状态',
  `pickup_time` TIMESTAMP NULL DEFAULT NULL COMMENT '揽件时间',
  `delivery_time` TIMESTAMP NULL DEFAULT NULL COMMENT '配送时间',
  `sign_time` TIMESTAMP NULL DEFAULT NULL COMMENT '签收时间',
  `estimated_delivery_time` TIMESTAMP NULL DEFAULT NULL COMMENT '预计送达时间',
  `remarks` TEXT DEFAULT NULL COMMENT '备注',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_number` (`order_number`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_sender_phone` (`sender_phone`),
  KEY `idx_receiver_phone` (`receiver_phone`),
  CONSTRAINT `fk_orders_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- 订单状态变更日志表
DROP TABLE IF EXISTS `order_status_log`;
CREATE TABLE `order_status_log` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `order_id` BIGINT(20) NOT NULL COMMENT '订单ID',
  `order_number` VARCHAR(50) NOT NULL COMMENT '订单号',
  `old_status` VARCHAR(20) DEFAULT NULL COMMENT '原状态',
  `new_status` VARCHAR(20) NOT NULL COMMENT '新状态',
  `operator_id` BIGINT(20) DEFAULT NULL COMMENT '操作员ID',
  `operator_name` VARCHAR(50) DEFAULT NULL COMMENT '操作员姓名',
  `operator_type` VARCHAR(20) DEFAULT NULL COMMENT '操作员类型',
  `change_reason` VARCHAR(255) DEFAULT NULL COMMENT '变更原因',
  `location` VARCHAR(255) DEFAULT NULL COMMENT '操作地点',
  `remarks` TEXT DEFAULT NULL COMMENT '备注',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_number` (`order_number`),
  KEY `idx_new_status` (`new_status`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_order_status_log_order_id` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单状态变更日志表';

ALTER TABLE order_status_log
    ADD COLUMN update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        AFTER create_time;

-- 收发货地址表
DROP TABLE IF EXISTS `shipping_addresses`;
CREATE TABLE `shipping_addresses` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '地址ID',
  `user_id` BIGINT(20) NOT NULL COMMENT '用户ID',
  `contact_name` VARCHAR(50) NOT NULL COMMENT '联系人姓名',
  `contact_phone` VARCHAR(20) NOT NULL COMMENT '联系人电话',
  `province` VARCHAR(20) NOT NULL COMMENT '省份',
  `city` VARCHAR(20) NOT NULL COMMENT '城市',
  `district` VARCHAR(20) NOT NULL COMMENT '区域',
  `detailed_address` TEXT NOT NULL COMMENT '详细地址',
  `postal_code` VARCHAR(10) DEFAULT NULL COMMENT '邮政编码',
  `longitude` DECIMAL(10,8) DEFAULT NULL COMMENT '经度',
  `latitude` DECIMAL(10,8) DEFAULT NULL COMMENT '纬度',
  `address_type` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '地址类型：1-收货地址，2-发货地址',
  `is_default` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '是否默认地址：0-否，1-是',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_address_type` (`address_type`),
  KEY `idx_is_default` (`is_default`),
  CONSTRAINT `fk_shipping_addresses_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='收发货地址表';

-- =============================================
-- 配送相关表
-- =============================================

-- 配送员表
DROP TABLE IF EXISTS `couriers`;
CREATE TABLE `couriers` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '配送员ID',
  `user_id` BIGINT(20) NOT NULL COMMENT '用户ID',
  `courier_code` VARCHAR(50) NOT NULL COMMENT '配送员编号',
  `courier_name` VARCHAR(50) NOT NULL COMMENT '配送员姓名',
  `phone` VARCHAR(20) NOT NULL COMMENT '手机号',
  `id_card` VARCHAR(20) DEFAULT NULL COMMENT '身份证号',
  `work_number` VARCHAR(20) DEFAULT NULL COMMENT '工号',
  `vehicle_type` VARCHAR(20) DEFAULT NULL COMMENT '车辆类型：BIKE-自行车，MOTORCYCLE-摩托车，CAR-汽车',
  `vehicle_number` VARCHAR(20) DEFAULT NULL COMMENT '车牌号',
  `work_area` VARCHAR(255) DEFAULT NULL COMMENT '工作区域',
  `max_weight` DECIMAL(10,2) DEFAULT NULL COMMENT '最大承重(kg)',
  `max_volume` DECIMAL(10,2) DEFAULT NULL COMMENT '最大体积(m³)',
  `status` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '状态：0-离线，1-在线，2-忙碌，3-休息',
  `longitude` DECIMAL(11,8) DEFAULT NULL COMMENT '当前经度',
  `latitude` DECIMAL(10,8) DEFAULT NULL COMMENT '当前纬度',
  `current_address` VARCHAR(255) DEFAULT NULL COMMENT '当前地址',
  `last_location_time` TIMESTAMP NULL DEFAULT NULL COMMENT '最后定位时间',
  `emergency_contact` VARCHAR(50) DEFAULT NULL COMMENT '紧急联系人',
  `emergency_phone` VARCHAR(20) DEFAULT NULL COMMENT '紧急联系电话',
  `hire_date` DATE DEFAULT NULL COMMENT '入职日期',
  `rating` DECIMAL(3,2) DEFAULT '5.00' COMMENT '评分',
  `delivery_count` INT(11) DEFAULT '0' COMMENT '配送数量',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_courier_code` (`courier_code`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  UNIQUE KEY `uk_phone` (`phone`),
  KEY `idx_status` (`status`),
  KEY `idx_work_area` (`work_area`),
  CONSTRAINT `fk_couriers_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='配送员表';

-- 配送任务表
DROP TABLE IF EXISTS `delivery_tasks`;
CREATE TABLE `delivery_tasks` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `task_number` VARCHAR(50) NOT NULL COMMENT '任务编号',
  `order_id` BIGINT(20) DEFAULT NULL COMMENT '订单ID',
  `order_number` VARCHAR(50) NOT NULL COMMENT '订单号',
  `courier_id` BIGINT(20) DEFAULT NULL COMMENT '配送员ID',
  `task_type` VARCHAR(20) NOT NULL COMMENT '任务类型：PICKUP-揽件，DELIVERY-派送',
  `priority` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '优先级：1-低，2-中，3-高，4-紧急',
  `weight` DECIMAL(10,2) DEFAULT NULL COMMENT '重量(kg)',
  `volume` DECIMAL(10,2) DEFAULT NULL COMMENT '体积(m³)',
  `goods_description` VARCHAR(255) DEFAULT NULL COMMENT '货物描述',
  `special_requirements` TEXT DEFAULT NULL COMMENT '特殊要求',
  `delivery_fee` DECIMAL(10,2) DEFAULT '0.00' COMMENT '配送费',
  `estimated_time` TIMESTAMP NULL DEFAULT NULL COMMENT '预计完成时间',
  `actual_start_time` TIMESTAMP NULL DEFAULT NULL COMMENT '实际开始时间',
  `actual_end_time` TIMESTAMP NULL DEFAULT NULL COMMENT '实际完成时间',
  `actual_time` TIMESTAMP NULL DEFAULT NULL COMMENT '实际完成时间',
  `task_status` VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '任务状态：PENDING-待分配，ASSIGNED-已分配，PICKED_UP-已揽收，IN_TRANSIT-运输中，COMPLETED-已完成，CANCELLED-已取消',
  `sender_name` VARCHAR(50) NOT NULL COMMENT '发件人姓名',
  `sender_phone` VARCHAR(20) NOT NULL COMMENT '发件人电话',
  `sender_address` TEXT NOT NULL COMMENT '发件人地址',
  `receiver_name` VARCHAR(50) NOT NULL COMMENT '收件人姓名',
  `receiver_phone` VARCHAR(20) NOT NULL COMMENT '收件人电话',
  `receiver_address` TEXT NOT NULL COMMENT '收件人地址',
  `pickup_address` TEXT DEFAULT NULL COMMENT '取件地址',
  `delivery_address` TEXT DEFAULT NULL COMMENT '送达地址',
  `contact_name` VARCHAR(50) DEFAULT NULL COMMENT '联系人姓名',
  `contact_phone` VARCHAR(20) DEFAULT NULL COMMENT '联系人电话',
  `special_instructions` TEXT DEFAULT NULL COMMENT '特殊说明',
  `completion_proof` TEXT DEFAULT NULL COMMENT '完成凭证(签收照片等)',
  `failure_reason` VARCHAR(255) DEFAULT NULL COMMENT '失败原因',
  `retry_count` INT(11) NOT NULL DEFAULT '0' COMMENT '重试次数',
  `remarks` TEXT DEFAULT NULL COMMENT '备注',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_number` (`task_number`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_courier_id` (`courier_id`),
  KEY `idx_task_status` (`task_status`),
  KEY `idx_task_type` (`task_type`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_sender_phone` (`sender_phone`),
  KEY `idx_receiver_phone` (`receiver_phone`),
  CONSTRAINT `fk_delivery_tasks_order_id` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`),
  CONSTRAINT `fk_delivery_tasks_courier_id` FOREIGN KEY (`courier_id`) REFERENCES `couriers` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='配送任务表';

-- 配送路线表
DROP TABLE IF EXISTS `delivery_routes`;
CREATE TABLE `delivery_routes` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '路线ID',
  `route_number` VARCHAR(50) NOT NULL COMMENT '路线编号',
  `route_name` VARCHAR(100) NOT NULL COMMENT '路线名称',
  `courier_id` BIGINT(20) NOT NULL COMMENT '配送员ID',
  `route_date` DATE NOT NULL COMMENT '路线日期',
  `start_time` TIMESTAMP NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` TIMESTAMP NULL DEFAULT NULL COMMENT '结束时间',
  `start_address` VARCHAR(255) DEFAULT NULL COMMENT '起始地址',
  `end_address` VARCHAR(255) DEFAULT NULL COMMENT '结束地址',
  `total_distance` DECIMAL(10,2) DEFAULT NULL COMMENT '总距离(km)',
  `estimated_duration` INT(11) DEFAULT NULL COMMENT '预计用时(分钟)',
  `actual_duration` INT(11) DEFAULT NULL COMMENT '实际用时(分钟)',
  `task_count` INT(11) NOT NULL DEFAULT '0' COMMENT '任务数量',
  `completed_count` INT(11) NOT NULL DEFAULT '0' COMMENT '完成数量',
  `route_status` VARCHAR(20) NOT NULL DEFAULT 'PLANNED' COMMENT '路线状态：PLANNED-计划中，IN_PROGRESS-进行中，COMPLETED-已完成，CANCELLED-已取消',
  `route_data` TEXT DEFAULT NULL COMMENT '路线数据(JSON格式)',
  `route_points` TEXT DEFAULT NULL COMMENT '路线点(JSON格式)',
  `remarks` TEXT DEFAULT NULL COMMENT '备注',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_route_number` (`route_number`),
  UNIQUE KEY `uk_courier_date` (`courier_id`, `route_date`),
  KEY `idx_courier_id` (`courier_id`),
  KEY `idx_route_date` (`route_date`),
  KEY `idx_route_status` (`route_status`),
  CONSTRAINT `fk_delivery_routes_courier_id` FOREIGN KEY (`courier_id`) REFERENCES `couriers` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='配送路线表';

-- 配送绩效统计表
DROP TABLE IF EXISTS `delivery_performance`;
CREATE TABLE `delivery_performance` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '绩效ID',
  `courier_id` BIGINT(20) NOT NULL COMMENT '配送员ID',
  `stat_date` DATE NOT NULL COMMENT '统计日期',
  `total_tasks` INT(11) NOT NULL DEFAULT '0' COMMENT '总任务数',
  `completed_tasks` INT(11) NOT NULL DEFAULT '0' COMMENT '完成任务数',
  `failed_tasks` INT(11) NOT NULL DEFAULT '0' COMMENT '失败任务数',
  `completion_rate` DECIMAL(5,2) NOT NULL DEFAULT '0.00' COMMENT '完成率',
  `total_distance` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT '总行驶距离(km)',
  `total_duration` INT(11) NOT NULL DEFAULT '0' COMMENT '总工作时长(分钟)',
  `average_delivery_time` INT(11) NOT NULL DEFAULT '0' COMMENT '平均配送时间(分钟)',
  `customer_rating` DECIMAL(3,2) DEFAULT NULL COMMENT '客户评分',
  `earnings` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT '收入',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_courier_date` (`courier_id`,`stat_date`),
  KEY `idx_stat_date` (`stat_date`),
  KEY `idx_completion_rate` (`completion_rate`),
  CONSTRAINT `fk_delivery_performance_courier_id` FOREIGN KEY (`courier_id`) REFERENCES `couriers` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='配送绩效统计表';

-- =============================================
-- 物流网点表
-- =============================================

-- 物流网点表
DROP TABLE IF EXISTS `logistics_stations`;
CREATE TABLE `logistics_stations` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '网点ID',
  `station_code` VARCHAR(50) NOT NULL COMMENT '网点编码',
  `station_name` VARCHAR(100) NOT NULL COMMENT '网点名称',
  `station_type` VARCHAR(20) NOT NULL COMMENT '网点类型：PICKUP-揽件点，TRANSIT-中转站，DELIVERY-派送点',
  `province` VARCHAR(20) NOT NULL COMMENT '省份',
  `city` VARCHAR(20) NOT NULL COMMENT '城市',
  `district` VARCHAR(20) NOT NULL COMMENT '区域',
  `detailed_address` TEXT NOT NULL COMMENT '详细地址',
  `longitude` DECIMAL(11,8) DEFAULT NULL COMMENT '经度',
  `latitude` DECIMAL(10,8) DEFAULT NULL COMMENT '纬度',
  `contact_person` VARCHAR(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` VARCHAR(20) DEFAULT NULL COMMENT '联系电话',
  `business_hours` VARCHAR(100) DEFAULT NULL COMMENT '营业时间',
  `service_scope` TEXT DEFAULT NULL COMMENT '服务范围',
  `capacity` INT(11) DEFAULT NULL COMMENT '处理能力(件/天)',
  `status` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '状态：0-停用，1-启用',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_station_code` (`station_code`),
  KEY `idx_station_type` (`station_type`),
  KEY `idx_city` (`city`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物流网点表';

-- =============================================
-- 通知相关表
-- =============================================

-- 通知记录表
DROP TABLE IF EXISTS `notifications`;
CREATE TABLE `notifications` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `user_id` BIGINT(20) DEFAULT NULL COMMENT '用户ID',
  `order_number` VARCHAR(50) DEFAULT NULL COMMENT '订单号',
  `business_id` BIGINT(20) DEFAULT NULL COMMENT '业务ID（订单ID、任务ID等）',
  `business_type` VARCHAR(20) DEFAULT NULL COMMENT '业务类型（ORDER、DELIVERY、SYSTEM等）',
  `notification_type` VARCHAR(20) NOT NULL COMMENT '通知类型：SMS-短信，EMAIL-邮件，PUSH-推送，WECHAT-微信',
  `template_code` VARCHAR(50) DEFAULT NULL COMMENT '模板编码',
  `recipient` VARCHAR(100) NOT NULL COMMENT '接收者(手机号/邮箱)',
  `title` VARCHAR(255) DEFAULT NULL COMMENT '通知标题',
  `content` TEXT NOT NULL COMMENT '通知内容',
  `template_params` TEXT DEFAULT NULL COMMENT '模板参数（JSON格式）',
  `send_status` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '发送状态：0-待发送，1-发送中，2-发送成功，3-发送失败，4-已取消',
  `send_time` TIMESTAMP NULL DEFAULT NULL COMMENT '发送时间',
  `failure_reason` VARCHAR(255) DEFAULT NULL COMMENT '失败原因',
  `retry_count` INT(11) NOT NULL DEFAULT '0' COMMENT '重试次数',
  `max_retry_count` INT(11) NOT NULL DEFAULT '3' COMMENT '最大重试次数',
  `next_retry_time` TIMESTAMP NULL DEFAULT NULL COMMENT '下次重试时间',
  `priority` TINYINT(1) NOT NULL DEFAULT '2' COMMENT '优先级：1-低，2-中，3-高，4-紧急',
  `is_read` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '是否已读：0-未读，1-已读',
  `ext_info` TEXT DEFAULT NULL COMMENT '扩展信息（JSON格式）',
  `remarks` VARCHAR(255) DEFAULT NULL COMMENT '备注',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_number` (`order_number`),
  KEY `idx_business` (`business_type`, `business_id`),
  KEY `idx_notification_type` (`notification_type`),
  KEY `idx_send_status` (`send_status`),
  KEY `idx_priority` (`priority`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_next_retry_time` (`next_retry_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知记录表';

-- 通知模板表
DROP TABLE IF EXISTS `notification_templates`;
CREATE TABLE `notification_templates` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `template_code` VARCHAR(50) NOT NULL COMMENT '模板编码',
  `template_name` VARCHAR(100) NOT NULL COMMENT '模板名称',
  `template_type` VARCHAR(20) NOT NULL COMMENT '模板类型：BUSINESS-业务，SYSTEM-系统，MARKETING-营销',
  `notification_type` VARCHAR(20) NOT NULL COMMENT '通知类型：SMS-短信，EMAIL-邮件，PUSH-推送，WECHAT-微信',
  `title` VARCHAR(255) DEFAULT NULL COMMENT '模板标题',
  `content` TEXT NOT NULL COMMENT '模板内容',
  `params_desc` TEXT DEFAULT NULL COMMENT '模板参数说明（JSON格式）',
  `example` TEXT DEFAULT NULL COMMENT '模板示例',
  `is_enabled` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '是否启用：0-禁用，1-启用',
  `sort_order` INT(11) DEFAULT '0' COMMENT '排序',
  `remarks` VARCHAR(255) DEFAULT NULL COMMENT '备注',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_code` (`template_code`),
  KEY `idx_template_type` (`template_type`),
  KEY `idx_notification_type` (`notification_type`),
  KEY `idx_is_enabled` (`is_enabled`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知模板表';

-- =============================================
-- 系统配置表
-- =============================================

-- 系统配置表
DROP TABLE IF EXISTS `system_config`;
CREATE TABLE `system_config` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` VARCHAR(100) NOT NULL COMMENT '配置键',
  `config_value` TEXT NOT NULL COMMENT '配置值',
  `config_type` VARCHAR(20) NOT NULL DEFAULT 'STRING' COMMENT '配置类型：STRING-字符串，NUMBER-数字，BOOLEAN-布尔值，JSON-JSON对象',
  `description` VARCHAR(255) DEFAULT NULL COMMENT '配置描述',
  `is_system` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '是否系统配置：0-否，1-是',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- =============================================
-- 初始化数据
-- =============================================

-- 插入默认角色
INSERT INTO `roles` (`id`, `role_name`, `role_code`, `description`) VALUES
(1, '普通用户', 'CUSTOMER', '普通用户角色，可以下单、查询订单'),
(2, '操作员', 'OPERATOR', '操作员角色，可以处理订单、更新物流状态'),
(3, '配送员', 'COURIER', '配送员角色，可以接单配送、更新配送状态'),
(4, '管理员', 'ADMIN', '管理员角色，拥有系统管理权限'),
(5, '超级管理员', 'SUPER_ADMIN', '系统超级管理员，拥有所有权限');

-- 插入默认权限
INSERT INTO `permissions` (`permission_name`, `permission_code`, `resource_type`, `resource_url`, `parent_id`, `description`) VALUES
('用户管理', 'user:manage', 'menu', '/admin/user', 0, '用户管理菜单'),
('订单管理', 'order:manage', 'menu', '/admin/order', 0, '订单管理菜单'),
('配送管理', 'delivery:manage', 'menu', '/admin/delivery', 0, '配送管理菜单'),
('系统管理', 'system:manage', 'menu', '/admin/system', 0, '系统管理菜单'),
('用户查询', 'user:query', 'api', '/api/user/list', 1, '查询用户列表'),
('用户创建', 'user:create', 'api', '/api/user/create', 1, '创建用户'),
('用户更新', 'user:update', 'api', '/api/user/update', 1, '更新用户'),
('用户删除', 'user:delete', 'api', '/api/user/delete', 1, '删除用户'),
('订单查询', 'order:query', 'api', '/api/order/list', 2, '查询订单列表'),
('订单创建', 'order:create', 'api', '/api/order/create', 2, '创建订单'),
('订单更新', 'order:update', 'api', '/api/order/update', 2, '更新订单'),
('订单删除', 'order:delete', 'api', '/api/order/delete', 2, '删除订单');

-- 插入默认系统配置
INSERT INTO `system_config` (`config_key`, `config_value`, `config_type`, `description`, `is_system`) VALUES
('shipping.base.fee', '10.00', 'NUMBER', '基础运费', 1),
('shipping.weight.fee', '2.00', 'NUMBER', '重量费用(每公斤)', 1),
('shipping.distance.fee', '0.50', 'NUMBER', '距离费用(每公里)', 1),
('shipping.free.weight', '1.00', 'NUMBER', '免费重量(公斤)', 1),
('shipping.free.distance', '10.00', 'NUMBER', '免费距离(公里)', 1),
('notification.sms.enabled', 'true', 'BOOLEAN', '短信通知开关', 1),
('notification.email.enabled', 'true', 'BOOLEAN', '邮件通知开关', 1),
('map.api.key', '', 'STRING', '地图API密钥', 1),
('map.api.provider', 'AMAP', 'STRING', '地图服务提供商', 1);

-- 插入默认物流网点
INSERT INTO `logistics_stations` (`station_code`, `station_name`, `station_type`, `province`, `city`, `district`, `detailed_address`, `contact_person`, `contact_phone`) VALUES
('BJ001', '北京朝阳营业点', 'PICKUP', '北京市', '北京市', '朝阳区', '建国门外大街1号', '张三', '13800138001'),
('BJ002', '北京海淀营业点', 'PICKUP', '北京市', '北京市', '海淀区', '中关村大街1号', '李四', '13800138002'),
('BJ003', '北京分拣中心', 'TRANSIT', '北京市', '北京市', '丰台区', '丰台物流园1号', '王五', '13800138003'),
('SH001', '上海浦东营业点', 'PICKUP', '上海市', '上海市', '浦东新区', '陆家嘴环路1号', '赵六', '13800138004'),
('SH002', '上海分拣中心', 'TRANSIT', '上海市', '上海市', '闵行区', '闵行物流园1号', '孙七', '13800138005');

-- =============================================
-- 创建索引优化
-- =============================================

-- 为订单表创建复合索引
CREATE INDEX `idx_orders_user_status` ON `orders` (`user_id`, `order_status`);
CREATE INDEX `idx_orders_create_status` ON `orders` (`create_time`, `order_status`);
CREATE INDEX `idx_orders_receiver_city` ON `orders` (`receiver_city`, `order_status`);

-- 为配送任务表创建复合索引
CREATE INDEX `idx_delivery_tasks_courier_status` ON `delivery_tasks` (`courier_id`, `task_status`);
CREATE INDEX `idx_delivery_tasks_status_type` ON `delivery_tasks` (`task_status`, `task_type`);

-- 为物流轨迹相关查询创建索引
CREATE INDEX `idx_order_status_log_order_time` ON `order_status_log` (`order_id`, `create_time`);

-- =============================================
-- 测试数据
-- =============================================

-- 清除可能存在的测试数据
SET FOREIGN_KEY_CHECKS = 0;
DELETE FROM `user_roles` WHERE `user_id` IN (SELECT `id` FROM `users` WHERE `username` IN ('admin', 'operator01', 'courier01', 'customer01', 'customer02'));
DELETE FROM `couriers` WHERE `user_id` IN (SELECT `id` FROM `users` WHERE `username` IN ('admin', 'operator01', 'courier01', 'customer01', 'customer02'));
DELETE FROM `users` WHERE `username` IN ('admin', 'operator01', 'courier01', 'customer01', 'customer02');
SET FOREIGN_KEY_CHECKS = 1;

-- 插入测试用户数据 (密码: Admin123456)
INSERT INTO `users` (`id`, `username`, `password`, `real_name`, `phone`, `email`, `id_card`, `user_type`, `status`, `create_time`, `update_time`) VALUES
                                                                                                                                                      (1, 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyZOgM7rmckABg1T2UlJkNNdwQa', '系统管理员', '13800000001', '<EMAIL>', '110101199001011001', 'ADMIN', 'ACTIVE', NOW(), NOW()),
                                                                                                                                                      (2, 'operator01', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyZOgM7rmckABg1T2UlJkNNdwQa', '张操作员', '13800000101', '<EMAIL>', '110101199001011101', 'OPERATOR', 'ACTIVE', NOW(), NOW()),
                                                                                                                                                      (3, 'courier01', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyZOgM7rmckABg1T2UlJkNNdwQa', '赵配送员', '13800000201', '<EMAIL>', '110101199001011201', 'COURIER', 'ACTIVE', NOW(), NOW()),
                                                                                                                                                      (4, 'customer01', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyZOgM7rmckABg1T2UlJkNNdwQa', '张三', '13800000301', '<EMAIL>', '110101199001011301', 'CUSTOMER', 'ACTIVE', NOW(), NOW()),
                                                                                                                                                      (5, 'customer02', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyZOgM7rmckABg1T2UlJkNNdwQa', '李四', '13800000302', '<EMAIL>', '110101199001011302', 'CUSTOMER', 'ACTIVE', NOW(), NOW());

-- 分配用户角色
INSERT INTO `user_roles` (`user_id`, `role_id`, `create_time`) VALUES
                                                                   (1, 4, NOW()),  -- admin -> 管理员
                                                                   (2, 2, NOW()),  -- operator01 -> 操作员
                                                                   (3, 3, NOW()),  -- courier01 -> 配送员
                                                                   (4, 1, NOW()),  -- customer01 -> 普通用户
                                                                   (5, 1, NOW());  -- customer02 -> 普通用户

-- 配送员详细信息
INSERT INTO `couriers` (`user_id`, `courier_code`, `courier_name`, `phone`, `work_area`, `status`, `create_time`, `update_time`) VALUES
    (3, 'C001', '赵配送员', '13800000201', '朝阳区', 1, NOW(), NOW());

-- =============================================
-- 测试账号信息
-- =============================================
/*
测试账号（密码统一：Admin123456）：
- admin / Admin123456 (系统管理员)
- operator01 / Admin123456 (张操作员)
- courier01 / Admin123456 (赵配送员)
- customer01 / Admin123456 (张三)
- customer02 / Admin123456 (李四)
*/

-- =============================================
-- 结束
-- ============================================= 