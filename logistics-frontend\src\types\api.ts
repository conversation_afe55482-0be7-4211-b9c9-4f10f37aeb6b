// 统一的API响应格式
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp?: number
}

// 分页响应格式
export interface PageResponse<T = any> {
  records: T[]
  total: number
  current: number
  size: number
  pages: number
}

// 分页请求参数
export interface PageRequest {
  pageNum?: number
  pageSize?: number
  keyword?: string
  sortField?: string
  sortOrder?: 'asc' | 'desc'
}

// 订单状态枚举（与后端保持完全一致）
export enum OrderStatus {
  PENDING = 'PENDING', // 待处理
  PAID = 'PAID', // 已支付
  PICKUP_ASSIGNED = 'PICKUP_ASSIGNED', // 已分配揽件员
  PICKUP = 'PICKUP', // 已揽收
  PICKED_UP = 'PICKED_UP', // 已揽收（别名）
  SORTING = 'SORTING', // 分拣中
  DISPATCHING = 'DISPATCHING', // 发车中
  TRANSFERRING = 'TRANSFERRING', // 中转中
  TRANSIT = 'TRANSIT', // 运输中
  IN_TRANSIT = 'IN_TRANSIT', // 运输中（别名）
  ARRIVED = 'ARRIVED', // 到达目的地
  DELIVERING = 'DELIVERING', // 派送中
  OUT_FOR_DELIVERY = 'OUT_FOR_DELIVERY', // 派送中（别名）
  SIGNED = 'SIGNED', // 已签收
  DELIVERED = 'DELIVERED', // 已配送（别名）
  CANCELLED = 'CANCELLED', // 已取消
  EXCEPTION = 'EXCEPTION', // 异常
  REJECTED = 'REJECTED', // 拒收
  RETURNED = 'RETURNED', // 退货
}

// 用户类型枚举
export enum UserType {
  CUSTOMER = 'CUSTOMER',
  COURIER = 'COURIER',
  ADMIN = 'ADMIN',
  OPERATOR = 'OPERATOR',
}

// 配送员状态枚举
export enum CourierStatus {
  ONLINE = 'ONLINE', // 在线
  BUSY = 'BUSY', // 忙碌
  OFFLINE = 'OFFLINE', // 离线
  REST = 'REST', // 休息
}

// 任务类型枚举
export enum TaskType {
  PICKUP = 'PICKUP', // 取货
  DELIVERY = 'DELIVERY', // 配送
}

// 任务状态枚举
export enum TaskStatus {
  ASSIGNED = 'ASSIGNED', // 已分配
  ACCEPTED = 'ACCEPTED', // 已接受
  STARTED = 'STARTED', // 已开始
  COMPLETED = 'COMPLETED', // 已完成
  CANCELLED = 'CANCELLED', // 已取消
}
