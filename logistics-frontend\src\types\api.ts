// 统一的API响应格式
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp?: number
}

// 分页响应格式
export interface PageResponse<T = any> {
  records: T[]
  total: number
  current: number
  size: number
  pages: number
}

// 分页请求参数
export interface PageRequest {
  pageNum?: number
  pageSize?: number
  keyword?: string
  sortField?: string
  sortOrder?: 'asc' | 'desc'
}

// 订单状态枚举
export enum OrderStatus {
  PENDING = 'PENDING', // 待支付
  PAID = 'PAID', // 已支付
  PICKUP_ASSIGNED = 'PICKUP_ASSIGNED', // 已分配揽件员
  PICKED_UP = 'PICKED_UP', // 已揽件
  IN_TRANSIT = 'IN_TRANSIT', // 运输中
  DELIVERY_ASSIGNED = 'DELIVERY_ASSIGNED', // 已分配配送员
  OUT_FOR_DELIVERY = 'OUT_FOR_DELIVERY', // 配送中
  DELIVERED = 'DELIVERED', // 已送达
  CANCELLED = 'CANCELLED', // 已取消
  EXCEPTION = 'EXCEPTION', // 异常
}

// 用户类型枚举
export enum UserType {
  CUSTOMER = 'CUSTOMER',
  COURIER = 'COURIER',
  ADMIN = 'ADMIN',
  OPERATOR = 'OPERATOR',
}

// 配送员状态枚举
export enum CourierStatus {
  ONLINE = 'ONLINE', // 在线
  BUSY = 'BUSY', // 忙碌
  OFFLINE = 'OFFLINE', // 离线
  REST = 'REST', // 休息
}

// 任务类型枚举
export enum TaskType {
  PICKUP = 'PICKUP', // 取货
  DELIVERY = 'DELIVERY', // 配送
}

// 任务状态枚举
export enum TaskStatus {
  ASSIGNED = 'ASSIGNED', // 已分配
  ACCEPTED = 'ACCEPTED', // 已接受
  STARTED = 'STARTED', // 已开始
  COMPLETED = 'COMPLETED', // 已完成
  CANCELLED = 'CANCELLED', // 已取消
}
