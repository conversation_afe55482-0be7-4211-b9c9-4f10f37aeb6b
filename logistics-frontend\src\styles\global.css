/* 物流系统全局样式 */

/* 导入主题变量 */
@import './theme.css';

/* ========== Element Plus 样式覆盖 ========== */

/* 按钮样式增强 */
.el-button {
  border-radius: var(--radius-md);
  font-weight: 500;
  transition: all var(--duration-normal);
  box-shadow: none;
}

.el-button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.el-button--primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
}

.el-button--success {
  background: linear-gradient(135deg, var(--success-color), #389e0d);
  border: none;
}

.el-button--warning {
  background: linear-gradient(135deg, var(--warning-color), #d48806);
  border: none;
}

.el-button--danger {
  background: linear-gradient(135deg, var(--error-color), #cf1322);
  border: none;
}

/* 卡片样式增强 */
.el-card {
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-secondary);
  box-shadow: var(--shadow-sm);
  transition: all var(--duration-normal);
}

.el-card:hover {
  box-shadow: var(--shadow-md);
}

.el-card__header {
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-light);
  padding: var(--spacing-lg);
}

.el-card__body {
  padding: var(--spacing-lg);
}

/* 表格样式增强 */
.el-table {
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.el-table__header-wrapper {
  background: var(--bg-tertiary);
}

.el-table th {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  font-weight: 600;
  border-bottom: 2px solid var(--border-secondary);
}

.el-table td {
  border-bottom: 1px solid var(--border-light);
}

.el-table__row:hover {
  background: var(--bg-tertiary);
}

/* 表单样式增强 */
.el-form-item__label {
  color: var(--text-secondary);
  font-weight: 500;
}

.el-input__wrapper {
  border-radius: var(--radius-md);
  border: 1px solid var(--border-secondary);
  transition: all var(--duration-normal);
}

.el-input__wrapper:hover {
  border-color: var(--primary-color);
}

.el-input__wrapper.is-focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.el-select .el-input__wrapper {
  cursor: pointer;
}

/* 标签样式增强 */
.el-tag {
  border-radius: var(--radius-sm);
  font-weight: 500;
  border: none;
}

.el-tag--primary {
  background: rgba(24, 144, 255, 0.1);
  color: var(--primary-color);
}

.el-tag--success {
  background: rgba(82, 196, 26, 0.1);
  color: var(--success-color);
}

.el-tag--warning {
  background: rgba(250, 173, 20, 0.1);
  color: var(--warning-color);
}

.el-tag--danger {
  background: rgba(245, 34, 45, 0.1);
  color: var(--error-color);
}

.el-tag--info {
  background: rgba(19, 194, 194, 0.1);
  color: var(--info-color);
}

/* 进度条样式增强 */
.el-progress-bar__outer {
  border-radius: var(--radius-sm);
  background: var(--bg-quaternary);
}

.el-progress-bar__inner {
  border-radius: var(--radius-sm);
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

/* 分页样式增强 */
.el-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-xl);
}

.el-pagination .el-pager li {
  border-radius: var(--radius-sm);
  transition: all var(--duration-normal);
}

.el-pagination .el-pager li:hover {
  background: var(--primary-color);
  color: white;
}

.el-pagination .el-pager li.is-active {
  background: var(--primary-color);
  color: white;
}

/* 菜单样式增强 */
.el-menu {
  border: none;
}

.el-menu-item {
  border-radius: var(--radius-md);
  margin: var(--spacing-xs) var(--spacing-md);
  transition: all var(--duration-normal);
}

.el-menu-item:hover {
  background: var(--bg-tertiary);
}

.el-menu-item.is-active {
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.1), rgba(114, 46, 209, 0.1));
  color: var(--primary-color);
}

/* 对话框样式增强 */
.el-dialog {
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
}

.el-dialog__header {
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-light);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.el-dialog__body {
  padding: var(--spacing-2xl);
}

/* 抽屉样式增强 */
.el-drawer {
  border-radius: var(--radius-xl) 0 0 var(--radius-xl);
}

.el-drawer__header {
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-light);
  padding: var(--spacing-xl);
}

/* 消息提示样式增强 */
.el-message {
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  border: none;
}

.el-message--success {
  background: rgba(82, 196, 26, 0.1);
  color: var(--success-color);
  border-left: 4px solid var(--success-color);
}

.el-message--warning {
  background: rgba(250, 173, 20, 0.1);
  color: var(--warning-color);
  border-left: 4px solid var(--warning-color);
}

.el-message--error {
  background: rgba(245, 34, 45, 0.1);
  color: var(--error-color);
  border-left: 4px solid var(--error-color);
}

.el-message--info {
  background: rgba(24, 144, 255, 0.1);
  color: var(--primary-color);
  border-left: 4px solid var(--primary-color);
}

/* ========== 自定义工具类 ========== */

/* 布局工具类 */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

/* 间距工具类 */
.gap-xs { gap: var(--spacing-xs); }
.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }
.gap-xl { gap: var(--spacing-xl); }

.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }
.m-xl { margin: var(--spacing-xl); }

/* 文字工具类 */
.text-xs { font-size: var(--font-xs); }
.text-sm { font-size: var(--font-sm); }
.text-md { font-size: var(--font-md); }
.text-lg { font-size: var(--font-lg); }
.text-xl { font-size: var(--font-xl); }

.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* 显示工具类 */
.hidden { display: none; }
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }

/* 位置工具类 */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* 宽高工具类 */
.w-full { width: 100%; }
.h-full { height: 100%; }
.w-auto { width: auto; }
.h-auto { height: auto; }

/* 圆角工具类 */
.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: 50%; }

/* 阴影工具类 */
.shadow-none { box-shadow: none; }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* 过渡工具类 */
.transition { transition: all var(--duration-normal); }
.transition-fast { transition: all var(--duration-fast); }
.transition-slow { transition: all var(--duration-slow); }

/* 鼠标悬停效果 */
.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.hover-scale:hover {
  transform: scale(1.02);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(24, 144, 255, 0.3);
}

/* ========== 滚动条样式 ========== */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--border-primary);
  border-radius: var(--radius-sm);
  transition: background var(--duration-normal);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* ========== 选择文本样式 ========== */
::selection {
  background: rgba(24, 144, 255, 0.2);
  color: var(--text-primary);
}

/* ========== 焦点样式 ========== */
:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* ========== 打印样式 ========== */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break {
    page-break-after: always;
  }
  
  * {
    box-shadow: none !important;
    text-shadow: none !important;
  }
}
