package org.example.logisticsorder.vo;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单详情响应VO
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
public class OrderVO {

    /**
     * 订单ID
     */
    private Long id;

    /**
     * 订单数
     */
    private String orderNumber;

    /**
     * 下单用户ID
     */
    private Long userId;

    // 寄件人信�?
    private String senderName;
    private String senderPhone;
    private String senderAddress;
    private String senderProvince;
    private String senderCity;
    private String senderDistrict;

    // 收件人信息
    private String receiverName;
    private String receiverPhone;
    private String receiverAddress;
    private String receiverProvince;
    private String receiverCity;
    private String receiverDistrict;

    // 物品信息
    private String itemName;
    private String itemType;
    private BigDecimal itemWeight;
    private BigDecimal itemVolume;
    private BigDecimal itemValue;
    private Boolean isFragile;

    // 服务信息
    private String serviceType;
    private String serviceTypeDesc;

    // 费用信息
    private BigDecimal shippingFee;
    private BigDecimal insuranceFee;
    private BigDecimal packingFee;
    private BigDecimal totalFee;

    // 支付信息
    private String paymentMethod;
    private String paymentMethodDesc;
    private Integer paymentStatus;
    private String paymentStatusDesc;

    // 订单状态
    private String orderStatus;
    private String orderStatusDesc;

    // 时间信息
    private LocalDateTime pickupTime;
    private LocalDateTime deliveryTime;
    private LocalDateTime signTime;
    private LocalDateTime estimatedDeliveryTime;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;

    // 其他信息
    private String remarks;

    /**
     * 是否可以取消
     */
    private Boolean canCancel;

    /**
     * 是否已完成
     */
    private Boolean isCompleted;
}
