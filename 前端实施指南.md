# 物流跟踪系统前端实施指南

## 🎯 项目概述

**项目名称：** 物流跟踪系统前端  
**技术栈：** Vue 3.5 + TypeScript + Vite + Element Plus + Pinia  
**开发时间：** 预计 7-10 天  
**目标：** 构建现代化、响应式的物流管理前端应用  

---

## 📋 第一步：项目初始化

### 1.1 删除现有项目并创建新项目
```bash
# 1. 删除现有前端项目
cd /e:/桌面/springBoot/node
rm -rf logistics-frontend

# 2. 创建新的 Vue 3 项目
npm create vue@latest logistics-frontend

# 在创建过程中选择以下选项：
# ✅ Add TypeScript? Yes
# ✅ Add JSX Support? No
# ✅ Add Vue Router for Single Page Application development? Yes
# ✅ Add Pinia for state management? Yes
# ✅ Add Vitest for Unit Testing? No
# ✅ Add an End-to-End Testing Solution? No
# ✅ Add ESLint for code quality? Yes
# ✅ Add Prettier for code formatting? Yes

# 3. 进入项目目录
cd logistics-frontend

# 4. 安装基础依赖
npm install

# 5. 安装额外依赖
npm install element-plus @element-plus/icons-vue
npm install axios dayjs lodash-es
npm install @types/lodash-es -D
npm install unplugin-auto-import unplugin-vue-components -D
```

### 1.2 配置 Vite

**vite.config.ts**
```typescript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

export default defineConfig({
  plugins: [
    vue(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
      imports: ['vue', 'vue-router', 'pinia']
    }),
    Components({
      resolvers: [ElementPlusResolver()]
    })
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true
      }
    }
  }
})
```

### 1.3 配置 TypeScript

**tsconfig.json**
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  },
  "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

---

## 📁 第二步：创建项目结构

### 2.1 创建目录结构
```bash
# 在 src 目录下创建以下目录
mkdir -p src/api
mkdir -p src/assets/images
mkdir -p src/assets/icons
mkdir -p src/assets/styles
mkdir -p src/components/Layout
mkdir -p src/components/Common
mkdir -p src/composables
mkdir -p src/layouts
mkdir -p src/stores
mkdir -p src/types
mkdir -p src/utils
mkdir -p src/views/auth
mkdir -p src/views/customer/order
mkdir -p src/views/customer/tracking
mkdir -p src/views/customer/profile
mkdir -p src/views/admin/user
mkdir -p src/views/admin/order
mkdir -p src/views/operator
mkdir -p src/views/courier
mkdir -p src/views/error
```

### 2.2 项目结构预览
```
src/
├── api/                    # API 接口封装
├── assets/                 # 静态资源
├── components/             # 公共组件
├── composables/            # 组合式函数
├── layouts/                # 布局组件
├── router/                 # 路由配置
├── stores/                 # 状态管理
├── types/                  # TypeScript 类型
├── utils/                  # 工具函数
├── views/                  # 页面组件
├── App.vue                 # 根组件
└── main.ts                 # 应用入口
```

---

## 🔧 第三步：核心功能实现

### 3.1 HTTP 请求封装

**src/utils/http.ts**
```typescript
import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import router from '@/router'

// 响应数据类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
}

// 创建 axios 实例
const http: AxiosInstance = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
http.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    const token = authStore.token
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
http.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { data } = response
    
    if (data.code === 200 || data.success) {
      return data
    } else {
      ElMessage.error(data.message || '请求失败')
      return Promise.reject(new Error(data.message || '请求失败'))
    }
  },
  (error) => {
    if (error.response?.status === 401) {
      const authStore = useAuthStore()
      authStore.logout()
      router.push('/login')
      ElMessage.error('登录已过期，请重新登录')
    } else {
      ElMessage.error(error.message || '网络错误')
    }
    return Promise.reject(error)
  }
)

export default http
```

### 3.2 类型定义

**src/types/user.ts**
```typescript
// 用户信息
export interface UserInfo {
  id: number
  username: string
  realName: string
  phone: string
  email: string
  userType: 'CUSTOMER' | 'OPERATOR' | 'COURIER' | 'ADMIN'
  status: string
  avatar?: string
  lastLoginTime?: string
  roles: string[]
  permissions: string[]
  createTime: string
}

// 登录请求
export interface LoginRequest {
  username: string
  password: string
  loginType?: 'USERNAME' | 'PHONE' | 'EMAIL'
  rememberMe?: boolean
}

// 登录响应
export interface LoginResponse {
  token: string
  tokenType: string
  expiresIn: number
  userInfo: UserInfo
  refreshToken?: string
  loginTime: string
}

// 注册请求
export interface RegisterRequest {
  username: string
  password: string
  confirmPassword?: string
  realName: string
  phone: string
  email: string
  idCard: string
  userType: 'CUSTOMER' | 'OPERATOR' | 'COURIER' | 'ADMIN'
  verificationCode: string
}
```

### 3.3 认证 API

**src/api/auth.ts**
```typescript
import http, { type ApiResponse } from '@/utils/http'
import type { LoginRequest, LoginResponse, RegisterRequest } from '@/types/user'

export const authApi = {
  // 用户登录
  login(data: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    return http.post('/user/login', data)
  },

  // 用户注册
  register(data: RegisterRequest): Promise<ApiResponse<LoginResponse>> {
    return http.post('/user/register', data)
  },

  // 获取用户信息
  getUserInfo(): Promise<ApiResponse<any>> {
    return http.get('/user/profile')
  },

  // 检查用户名是否存在
  checkUsername(username: string): Promise<ApiResponse<boolean>> {
    return http.get('/user/check-username', { params: { username } })
  },

  // 检查手机号是否存在
  checkPhone(phone: string): Promise<ApiResponse<boolean>> {
    return http.get('/user/check-phone', { params: { phone } })
  },

  // 检查邮箱是否存在
  checkEmail(email: string): Promise<ApiResponse<boolean>> {
    return http.get('/user/check-email', { params: { email } })
  },

  // 用户登出
  logout(): Promise<ApiResponse<void>> {
    return http.post('/user/logout')
  }
}
```

### 3.4 认证状态管理

**src/stores/auth.ts**
```typescript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authApi } from '@/api/auth'
import type { UserInfo, LoginRequest, RegisterRequest } from '@/types/user'
import { ElMessage } from 'element-plus'
import router from '@/router'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string>(localStorage.getItem('token') || '')
  const userInfo = ref<UserInfo | null>(null)
  const loading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const userRole = computed(() => userInfo.value?.userType || '')
  const permissions = computed(() => userInfo.value?.permissions || [])

  // 登录
  const login = async (loginData: LoginRequest): Promise<boolean> => {
    try {
      loading.value = true
      const response = await authApi.login(loginData)
      
      if (response.success) {
        token.value = response.data.token
        userInfo.value = response.data.userInfo
        
        // 保存到本地存储
        localStorage.setItem('token', response.data.token)
        localStorage.setItem('userInfo', JSON.stringify(response.data.userInfo))
        
        ElMessage.success('登录成功')
        
        // 根据用户角色跳转到对应页面
        const redirectPath = getRedirectPath(response.data.userInfo.userType)
        router.push(redirectPath)
        
        return true
      }
      return false
    } catch (error) {
      console.error('登录失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (registerData: RegisterRequest): Promise<boolean> => {
    try {
      loading.value = true
      const response = await authApi.register(registerData)
      
      if (response.success) {
        ElMessage.success('注册成功，请登录')
        router.push('/login')
        return true
      }
      return false
    } catch (error) {
      console.error('注册失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 获取用户信息
  const fetchUserInfo = async (): Promise<void> => {
    try {
      const response = await authApi.getUserInfo()
      if (response.success) {
        userInfo.value = response.data
        localStorage.setItem('userInfo', JSON.stringify(response.data))
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      logout()
    }
  }

  // 登出
  const logout = (): void => {
    token.value = ''
    userInfo.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    router.push('/login')
    ElMessage.success('已退出登录')
  }

  // 初始化用户信息
  const initUserInfo = (): void => {
    const savedUserInfo = localStorage.getItem('userInfo')
    if (savedUserInfo) {
      try {
        userInfo.value = JSON.parse(savedUserInfo)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        logout()
      }
    }
  }

  // 根据用户角色获取重定向路径
  const getRedirectPath = (userType: string): string => {
    switch (userType) {
      case 'ADMIN':
        return '/admin/dashboard'
      case 'OPERATOR':
        return '/operator/dashboard'
      case 'COURIER':
        return '/courier/dashboard'
      case 'CUSTOMER':
      default:
        return '/customer/dashboard'
    }
  }

  // 检查权限
  const hasPermission = (permission: string): boolean => {
    return permissions.value.includes(permission)
  }

  // 检查角色
  const hasRole = (role: string): boolean => {
    return userRole.value === role
  }

  return {
    // 状态
    token,
    userInfo,
    loading,
    // 计算属性
    isLoggedIn,
    userRole,
    permissions,
    // 方法
    login,
    register,
    logout,
    fetchUserInfo,
    initUserInfo,
    hasPermission,
    hasRole
  }
})
```

---

## 🚀 第四步：启动和测试

### 4.1 准备测试数据
```sql
-- 在数据库中执行以下SQL
USE logistics;

DELETE FROM user_roles;
DELETE FROM users;
ALTER TABLE users AUTO_INCREMENT = 1;
ALTER TABLE user_roles AUTO_INCREMENT = 1;

-- 插入测试用户 (密码: Admin123456)
INSERT INTO users (username, password, real_name, phone, email, id_card, user_type, status, create_time, update_time) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyZOgM7rmckABg1T2UlJkNNdwQa', '系统管理员', '13800000001', '<EMAIL>', '110101199001011001', 'ADMIN', 'ACTIVE', NOW(), NOW()),
('customer01', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyZOgM7rmckABg1T2UlJkNNdwQa', '张三', '13800000301', '<EMAIL>', '110101199001011301', 'CUSTOMER', 'ACTIVE', NOW(), NOW());

-- 分配用户角色
INSERT INTO user_roles (user_id, role_id, create_time) VALUES
(1, 4, NOW()),  -- admin -> 管理员
(2, 1, NOW());  -- customer01 -> 普通用户
```

### 4.2 启动项目
```bash
# 1. 确保后端服务正在运行
# 用户服务: http://localhost:8001
# 网关服务: http://localhost:8080

# 2. 启动前端项目
cd /e:/桌面/springBoot/node/logistics-frontend
npm run dev

# 3. 访问前端应用
# http://localhost:3000
```

### 4.3 测试账号
- **管理员**: admin / Admin123456
- **普通用户**: customer01 / Admin123456

---

## 📋 开发检查清单

### ✅ 第一阶段：基础架构
- [ ] 项目初始化完成
- [ ] 依赖安装完成
- [ ] 配置文件设置完成
- [ ] 目录结构创建完成
- [ ] HTTP 封装实现完成
- [ ] 类型定义完成
- [ ] 状态管理实现完成

### ✅ 第二阶段：认证模块
- [ ] 登录页面实现完成
- [ ] 注册页面实现完成
- [ ] 路由配置完成
- [ ] 路由守卫实现完成
- [ ] 权限控制实现完成

### ✅ 第三阶段：基础测试
- [ ] 登录功能测试通过
- [ ] 注册功能测试通过
- [ ] 权限跳转测试通过
- [ ] API 接口对接测试通过

---

## 🎯 下一步开发计划

1. **完善客户端功能**
   - 订单创建页面
   - 订单列表页面
   - 物流追踪页面
   - 个人中心页面

2. **开发管理端功能**
   - 管理员仪表板
   - 用户管理
   - 订单管理
   - 系统配置

3. **集成高级功能**
   - 地图服务集成
   - 实时通信
   - 文件上传
   - 数据可视化

4. **优化和测试**
   - 性能优化
   - 响应式适配
   - 错误处理
   - 用户体验优化

---

## 📞 技术支持

如果在实施过程中遇到问题，请检查：

1. **依赖安装问题** - 确保 Node.js 版本 >= 16
2. **端口冲突** - 确保 3000 端口未被占用
3. **API 连接问题** - 确保后端服务正常运行
4. **数据库连接** - 确保测试数据已正确插入

这个实施指南提供了详细的步骤和代码，您可以按照顺序逐步实现整个前端项目。 