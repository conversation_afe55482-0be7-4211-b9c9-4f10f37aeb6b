<template>
  <div class="delivery-route-map">
    <div class="map-controls">
      <el-button-group>
        <el-button
          :type="viewMode === 'route' ? 'primary' : 'default'"
          @click="setViewMode('route')"
        >
          路线视图
        </el-button>
        <el-button
          :type="viewMode === 'tasks' ? 'primary' : 'default'"
          @click="setViewMode('tasks')"
        >
          任务视图
        </el-button>
        <el-button
          :type="viewMode === 'realtime' ? 'primary' : 'default'"
          @click="setViewMode('realtime')"
        >
          实时位置
        </el-button>
      </el-button-group>

      <div class="route-info" v-if="routeData">
        <span>总距离：{{ routeData.totalDistance }}km</span>
        <span>预计用时：{{ routeData.estimatedDuration }}分钟</span>
        <span>任务数量：{{ routeData.taskCount }}</span>
      </div>
    </div>

    <BaseMap
      ref="mapRef"
      :center="mapCenter"
      :zoom="mapZoom"
      :markers="mapMarkers"
      :polylines="mapPolylines"
      @map-click="handleMapClick"
      @marker-click="handleMarkerClick"
    />

    <div class="task-panel" v-if="showTaskPanel">
      <div class="panel-header">
        <h3>配送任务</h3>
        <el-button type="text" @click="showTaskPanel = false">
          <el-icon><Close /></el-icon>
        </el-button>
      </div>

      <div class="task-list">
        <div
          v-for="task in tasks"
          :key="task.id"
          :class="['task-item', { active: selectedTaskId === task.id }]"
          @click="selectTask(task)"
        >
          <div class="task-header">
            <span class="task-number">{{ task.taskNumber }}</span>
            <el-tag :type="getTaskStatusType(task.taskStatus)" size="small">
              {{ getTaskStatusText(task.taskStatus) }}
            </el-tag>
          </div>

          <div class="task-info">
            <p>
              <strong>{{ task.taskType === 'PICKUP' ? '揽件' : '派送' }}</strong>
            </p>
            <p>{{ task.receiverName }} {{ task.receiverPhone }}</p>
            <p>{{ task.receiverAddress }}</p>
          </div>

          <div class="task-actions">
            <el-button
              v-if="task.taskStatus === 'ASSIGNED'"
              type="primary"
              size="small"
              @click.stop="startTask(task)"
            >
              开始任务
            </el-button>
            <el-button
              v-if="task.taskStatus === 'IN_PROGRESS'"
              type="success"
              size="small"
              @click.stop="completeTask(task)"
            >
              完成任务
            </el-button>
            <el-button type="text" size="small" @click.stop="navigateToTask(task)">
              导航
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Close } from '@element-plus/icons-vue'
import BaseMap from './BaseMap.vue'
import { courierApi, type DeliveryTask } from '@/api/courier'
import { mapApi } from '@/api/map'

interface Props {
  courierId?: number
  routeId?: number
  showControls?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showControls: true,
})

const emit = defineEmits<{
  taskSelected: [task: DeliveryTask]
  taskStarted: [task: DeliveryTask]
  taskCompleted: [task: DeliveryTask]
}>()

const mapRef = ref()
const loading = ref(false)
const viewMode = ref<'route' | 'tasks' | 'realtime'>('route')
const showTaskPanel = ref(true)
const selectedTaskId = ref<number | null>(null)

const tasks = ref<DeliveryTask[]>([])
const routeData = ref<any>(null)
const courierLocation = ref<{ lat: number; lng: number } | null>(null)

const mapCenter = ref({ lat: 39.915, lng: 116.404 })
const mapZoom = ref(12)

// 地图标记
const mapMarkers = computed(() => {
  const markers: any[] = []

  if (viewMode.value === 'tasks' || viewMode.value === 'route') {
    // 添加任务标记
    tasks.value.forEach((task, index) => {
      if (task.receiverLatitude && task.receiverLongitude) {
        markers.push({
          id: `task-${task.id}`,
          position: {
            lat: task.receiverLatitude,
            lng: task.receiverLongitude,
          },
          title: task.taskNumber,
          content: `${task.receiverName}<br/>${task.receiverAddress}`,
          icon: getTaskIcon(task.taskStatus),
          zIndex: selectedTaskId.value === task.id ? 100 : 10,
        })
      }
    })
  }

  if (viewMode.value === 'realtime' && courierLocation.value) {
    // 添加配送员位置标记
    markers.push({
      id: 'courier-location',
      position: courierLocation.value,
      title: '配送员位置',
      content: '当前位置',
      icon: 'courier',
      zIndex: 200,
    })
  }

  return markers
})

// 地图路线
const mapPolylines = computed(() => {
  const polylines: any[] = []

  if (viewMode.value === 'route' && routeData.value?.routePoints) {
    polylines.push({
      id: 'delivery-route',
      path: routeData.value.routePoints,
      strokeColor: '#409EFF',
      strokeWeight: 4,
    })
  }

  return polylines
})

// 获取任务图标
const getTaskIcon = (status: string) => {
  switch (status) {
    case 'ASSIGNED':
      return 'task-assigned'
    case 'IN_PROGRESS':
      return 'task-progress'
    case 'COMPLETED':
      return 'task-completed'
    default:
      return 'task-default'
  }
}

// 获取任务状态类型
const getTaskStatusType = (status: string) => {
  switch (status) {
    case 'ASSIGNED':
      return 'warning'
    case 'IN_PROGRESS':
      return 'primary'
    case 'COMPLETED':
      return 'success'
    case 'CANCELLED':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取任务状态文本
const getTaskStatusText = (status: string) => {
  switch (status) {
    case 'ASSIGNED':
      return '已分配'
    case 'IN_PROGRESS':
      return '进行中'
    case 'COMPLETED':
      return '已完成'
    case 'CANCELLED':
      return '已取消'
    default:
      return '未知'
  }
}

// 设置视图模式
const setViewMode = (mode: 'route' | 'tasks' | 'realtime') => {
  viewMode.value = mode

  if (mode === 'realtime') {
    loadCourierLocation()
  }
}

// 加载配送任务
const loadTasks = async () => {
  if (!props.courierId) return

  try {
    loading.value = true
    const response = await courierApi.getDeliveryTasks({
      courierId: props.courierId,
      status: 'ASSIGNED,IN_PROGRESS',
    })

    if (response.data.code === 200) {
      tasks.value = response.data.data.records || []

      // 为没有坐标的任务进行地理编码
      await geocodeTasks()

      // 自动调整地图视野
      if (tasks.value.length > 0) {
        adjustMapView()
      }
    }
  } catch (error) {
    console.error('加载配送任务失败:', error)
    ElMessage.error('加载配送任务失败')
  } finally {
    loading.value = false
  }
}

// 地理编码任务地址
const geocodeTasks = async () => {
  const tasksNeedGeocode = tasks.value.filter(
    (task) => !task.receiverLatitude || !task.receiverLongitude,
  )

  for (const task of tasksNeedGeocode) {
    try {
      const result = await mapApi.geocode(task.receiverAddress)
      if (result.data.code === 200) {
        const location = result.data.data
        task.receiverLatitude = location.lat
        task.receiverLongitude = location.lng
      }
    } catch (error) {
      console.error(`地址解析失败: ${task.receiverAddress}`, error)
    }
  }
}

// 加载路线数据
const loadRouteData = async () => {
  if (!props.routeId) return

  try {
    const response = await courierApi.getRouteDetail(props.routeId)
    if (response.data.code === 200) {
      routeData.value = response.data.data
    }
  } catch (error) {
    console.error('加载路线数据失败:', error)
    ElMessage.error('加载路线数据失败')
  }
}

// 加载配送员位置
const loadCourierLocation = async () => {
  if (!props.courierId) return

  try {
    const response = await courierApi.getCourierLocation(props.courierId)
    if (response.data.code === 200) {
      const location = response.data.data
      courierLocation.value = {
        lat: location.latitude,
        lng: location.longitude,
      }

      if (viewMode.value === 'realtime') {
        mapCenter.value = courierLocation.value
      }
    }
  } catch (error) {
    console.error('获取配送员位置失败:', error)
  }
}

// 调整地图视野
const adjustMapView = () => {
  const validTasks = tasks.value.filter((task) => task.receiverLatitude && task.receiverLongitude)

  if (validTasks.length === 0) return

  if (validTasks.length === 1) {
    mapCenter.value = {
      lat: validTasks[0].receiverLatitude!,
      lng: validTasks[0].receiverLongitude!,
    }
    mapZoom.value = 15
  } else {
    // 计算中心点和合适的缩放级别
    const bounds = calculateBounds(validTasks)
    mapCenter.value = bounds.center
    mapZoom.value = bounds.zoom
  }
}

// 计算边界
const calculateBounds = (taskList: DeliveryTask[]) => {
  const lats = taskList.map((task) => task.receiverLatitude!).filter(Boolean)
  const lngs = taskList.map((task) => task.receiverLongitude!).filter(Boolean)

  const minLat = Math.min(...lats)
  const maxLat = Math.max(...lats)
  const minLng = Math.min(...lngs)
  const maxLng = Math.max(...lngs)

  const centerLat = (minLat + maxLat) / 2
  const centerLng = (minLng + maxLng) / 2

  // 简单的缩放级别计算
  const latDiff = maxLat - minLat
  const lngDiff = maxLng - minLng
  const maxDiff = Math.max(latDiff, lngDiff)

  let zoom = 15
  if (maxDiff > 0.1) zoom = 10
  else if (maxDiff > 0.05) zoom = 12
  else if (maxDiff > 0.01) zoom = 14

  return {
    center: { lat: centerLat, lng: centerLng },
    zoom,
  }
}

// 选择任务
const selectTask = (task: DeliveryTask) => {
  selectedTaskId.value = task.id

  if (task.receiverLatitude && task.receiverLongitude) {
    mapCenter.value = {
      lat: task.receiverLatitude,
      lng: task.receiverLongitude,
    }
  }

  emit('taskSelected', task)
}

// 开始任务
const startTask = async (task: DeliveryTask) => {
  try {
    await courierApi.startTask(task.id)
    ElMessage.success('任务已开始')
    task.taskStatus = 'IN_PROGRESS'
    emit('taskStarted', task)
  } catch (error) {
    console.error('开始任务失败:', error)
    ElMessage.error('开始任务失败')
  }
}

// 完成任务
const completeTask = async (task: DeliveryTask) => {
  try {
    await courierApi.completeTask(task.id, {
      completionProof: '',
      remarks: '',
    })
    ElMessage.success('任务已完成')
    task.taskStatus = 'COMPLETED'
    emit('taskCompleted', task)
  } catch (error) {
    console.error('完成任务失败:', error)
    ElMessage.error('完成任务失败')
  }
}

// 导航到任务
const navigateToTask = (task: DeliveryTask) => {
  if (task.receiverLatitude && task.receiverLongitude) {
    // 打开高德地图导航
    const url = `https://uri.amap.com/navigation?to=${task.receiverLongitude},${task.receiverLatitude},${task.receiverAddress}&mode=car&policy=1&src=logistics-system`
    window.open(url, '_blank')
  } else {
    ElMessage.warning('该任务没有位置信息')
  }
}

// 地图点击事件
const handleMapClick = (event: { lat: number; lng: number }) => {
  console.log('Map clicked:', event)
}

// 标记点击事件
const handleMarkerClick = (marker: any) => {
  if (marker.id.startsWith('task-')) {
    const taskId = parseInt(marker.id.replace('task-', ''))
    const task = tasks.value.find((t) => t.id === taskId)
    if (task) {
      selectTask(task)
    }
  }
}

// 监听属性变化
watch(
  () => props.courierId,
  () => {
    loadTasks()
  },
)

watch(
  () => props.routeId,
  () => {
    loadRouteData()
  },
)

onMounted(() => {
  loadTasks()
  if (props.routeId) {
    loadRouteData()
  }
})

// 暴露方法
defineExpose({
  loadTasks,
  loadRouteData,
  selectTask,
  setViewMode,
})
</script>

<style scoped>
.delivery-route-map {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.map-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
}

.route-info {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #666;
}

.task-panel {
  position: absolute;
  top: 80px;
  right: 16px;
  width: 320px;
  max-height: calc(100% - 100px);
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  z-index: 1000;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.task-list {
  max-height: 400px;
  overflow-y: auto;
}

.task-item {
  padding: 16px;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: background-color 0.3s;
}

.task-item:hover {
  background-color: #f8f9fa;
}

.task-item.active {
  background-color: #e6f7ff;
  border-left: 3px solid #409eff;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.task-number {
  font-weight: bold;
  color: #333;
}

.task-info {
  margin-bottom: 12px;
}

.task-info p {
  margin: 4px 0;
  font-size: 14px;
  color: #666;
}

.task-actions {
  display: flex;
  gap: 8px;
}

@media (max-width: 768px) {
  .task-panel {
    width: calc(100% - 32px);
    right: 16px;
    left: 16px;
  }

  .map-controls {
    flex-direction: column;
    gap: 12px;
  }

  .route-info {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
