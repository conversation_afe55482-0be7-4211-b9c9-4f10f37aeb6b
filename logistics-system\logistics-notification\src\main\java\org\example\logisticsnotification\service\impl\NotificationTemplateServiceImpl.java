package org.example.logisticsnotification.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.example.logisticsnotification.constants.NotificationType;
import org.example.logisticsnotification.constants.TemplateType;
import org.example.logisticsnotification.entity.NotificationTemplate;
import org.example.logisticsnotification.mapper.NotificationTemplateMapper;
import org.example.logisticsnotification.service.NotificationTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 通知模板服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Slf4j
@Service
public class NotificationTemplateServiceImpl extends ServiceImpl<NotificationTemplateMapper, NotificationTemplate> 
        implements NotificationTemplateService {

    @Autowired
    private ObjectMapper objectMapper;

    private static final Pattern TEMPLATE_PATTERN = Pattern.compile("\\{\\{(.*?)\\}\\}");

    @Override
    @Transactional(rollbackFor = Exception.class)
    public NotificationTemplate createTemplate(NotificationTemplate template) {
        // 检查模板编码是否存在
        if (isTemplateCodeExists(template.getTemplateCode(), null)) {
            throw new RuntimeException("模板编码已存在: " + template.getTemplateCode());
        }

        // 设置默认值
        if (template.getIsEnabled() == null) {
            template.setIsEnabled(1);
        }
        if (template.getSortOrder() == null) {
            template.setSortOrder(0);
        }

        return save(template) ? template : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTemplate(NotificationTemplate template) {
        // 检查模板编码是否存在（排除自己）
        if (isTemplateCodeExists(template.getTemplateCode(), template.getId())) {
            throw new RuntimeException("模板编码已存在: " + template.getTemplateCode());
        }

        return updateById(template);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTemplate(Long templateId) {
        return removeById(templateId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteTemplates(List<Long> templateIds) {
        if (CollectionUtils.isEmpty(templateIds)) {
            return false;
        }
        return removeByIds(templateIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean toggleTemplateStatus(Long templateId, Integer isEnabled) {
        NotificationTemplate template = getById(templateId);
        if (template == null) {
            return false;
        }
        template.setIsEnabled(isEnabled);
        return updateById(template);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateTemplateStatus(List<Long> templateIds, Integer isEnabled) {
        if (CollectionUtils.isEmpty(templateIds)) {
            return 0;
        }
        return baseMapper.batchUpdateStatus(templateIds, isEnabled);
    }

    @Override
    public NotificationTemplate getTemplateById(Long templateId) {
        return getById(templateId);
    }

    @Override
    public NotificationTemplate getTemplateByCode(String templateCode) {
        return baseMapper.findByTemplateCode(templateCode);
    }

    @Override
    public List<NotificationTemplate> getTemplatesByType(String templateType) {
        return baseMapper.findByTemplateType(templateType);
    }

    @Override
    public List<NotificationTemplate> getTemplatesByNotificationType(String notificationType) {
        return baseMapper.findByNotificationType(notificationType);
    }

    @Override
    public List<NotificationTemplate> getEnabledTemplates(String notificationType, String templateType) {
        return baseMapper.findEnabledTemplates(notificationType, templateType);
    }

    @Override
    public IPage<NotificationTemplate> getTemplatesPage(Page<NotificationTemplate> page, String templateType,
                                                      String notificationType, String templateName, Integer isEnabled) {
        return baseMapper.findTemplatesPage(page, templateType, notificationType, templateName, isEnabled);
    }

    @Override
    public boolean isTemplateCodeExists(String templateCode, Long excludeId) {
        return baseMapper.countByTemplateCode(templateCode, excludeId) > 0;
    }

    @Override
    public String renderTemplateContent(String templateCode, Map<String, Object> params) {
        NotificationTemplate template = getTemplateByCode(templateCode);
        if (template == null || !Integer.valueOf(1).equals(template.getIsEnabled())) {
            log.warn("模板不存在或已禁用: {}", templateCode);
            return null;
        }

        return renderContent(template.getContent(), params);
    }

    @Override
    public String renderTemplateTitle(String templateCode, Map<String, Object> params) {
        NotificationTemplate template = getTemplateByCode(templateCode);
        if (template == null || !Integer.valueOf(1).equals(template.getIsEnabled())) {
            log.warn("模板不存在或已禁用: {}", templateCode);
            return null;
        }

        return renderContent(template.getTitle(), params);
    }

    @Override
    public Map<String, String> generateNotificationContent(String templateCode, Map<String, Object> params) {
        NotificationTemplate template = getTemplateByCode(templateCode);
        if (template == null || !Integer.valueOf(1).equals(template.getIsEnabled())) {
            log.warn("模板不存在或已禁用: {}", templateCode);
            return null;
        }

        Map<String, String> result = new HashMap<>();
        result.put("title", renderContent(template.getTitle(), params));
        result.put("content", renderContent(template.getContent(), params));
        return result;
    }

    @Override
    public boolean validateTemplateParams(String templateCode, Map<String, Object> params) {
        NotificationTemplate template = getTemplateByCode(templateCode);
        if (template == null) {
            return false;
        }

        try {
            Map<String, Object> paramsDesc = getTemplateParamsDesc(templateCode);
            if (paramsDesc == null || paramsDesc.isEmpty()) {
                return true;
            }

            Set<String> requiredParams = new HashSet<>();
            for (Map.Entry<String, Object> entry : paramsDesc.entrySet()) {
                if (entry.getValue() instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> paramInfo = (Map<String, Object>) entry.getValue();
                    if (Boolean.TRUE.equals(paramInfo.get("required"))) {
                        requiredParams.add(entry.getKey());
                    }
                }
            }

            for (String requiredParam : requiredParams) {
                if (params == null || !params.containsKey(requiredParam)) {
                    log.warn("缺少必需参数: {}", requiredParam);
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            log.error("验证模板参数异常", e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getTemplateParamsDesc(String templateCode) {
        NotificationTemplate template = getTemplateByCode(templateCode);
        if (template == null || !StringUtils.hasText(template.getParamsDesc())) {
            return new HashMap<>();
        }

        try {
            return objectMapper.readValue(template.getParamsDesc(), new TypeReference<Map<String, Object>>() {});
        } catch (Exception e) {
            log.error("解析模板参数描述失败: {}", templateCode, e);
            return new HashMap<>();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public NotificationTemplate copyTemplate(Long templateId, String newTemplateCode, String newTemplateName) {
        NotificationTemplate sourceTemplate = getById(templateId);
        if (sourceTemplate == null) {
            throw new RuntimeException("源模板不存在");
        }

        if (isTemplateCodeExists(newTemplateCode, null)) {
            throw new RuntimeException("目标模板编码已存在: " + newTemplateCode);
        }

        NotificationTemplate newTemplate = new NotificationTemplate();
        newTemplate.setTemplateCode(newTemplateCode);
        newTemplate.setTemplateName(newTemplateName);
        newTemplate.setTemplateType(sourceTemplate.getTemplateType());
        newTemplate.setNotificationType(sourceTemplate.getNotificationType());
        newTemplate.setTitle(sourceTemplate.getTitle());
        newTemplate.setContent(sourceTemplate.getContent());
        newTemplate.setParamsDesc(sourceTemplate.getParamsDesc());
        newTemplate.setExample(sourceTemplate.getExample());
        newTemplate.setIsEnabled(0);
        newTemplate.setSortOrder(sourceTemplate.getSortOrder());
        newTemplate.setRemarks("从模板 " + sourceTemplate.getTemplateName() + " 复制");

        return createTemplate(newTemplate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean importTemplates(List<NotificationTemplate> templates) {
        if (CollectionUtils.isEmpty(templates)) {
            return false;
        }

        List<NotificationTemplate> validTemplates = new ArrayList<>();
        for (NotificationTemplate template : templates) {
            if (!isTemplateCodeExists(template.getTemplateCode(), null)) {
                validTemplates.add(template);
            } else {
                log.warn("模板编码已存在，跳过导入: {}", template.getTemplateCode());
            }
        }

        return !validTemplates.isEmpty() && saveBatch(validTemplates);
    }

    @Override
    public List<NotificationTemplate> exportTemplates(List<Long> templateIds) {
        if (CollectionUtils.isEmpty(templateIds)) {
            return new ArrayList<>();
        }
        return listByIds(templateIds);
    }

    @Override
    public List<String> getAllTemplateTypes() {
        return Arrays.stream(TemplateType.values())
                .map(TemplateType::getCode)
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getAllNotificationTypes() {
        return Arrays.stream(NotificationType.values())
                .map(NotificationType::getCode)
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> getTemplateUsageStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        long totalCount = count();
        statistics.put("totalCount", totalCount);
        
        long enabledCount = lambdaQuery()
                .eq(NotificationTemplate::getIsEnabled, 1)
                .count();
        statistics.put("enabledCount", enabledCount);
        
        statistics.put("disabledCount", totalCount - enabledCount);
        
        Map<String, Long> typeStatistics = new HashMap<>();
        for (TemplateType templateType : TemplateType.values()) {
            long count = lambdaQuery()
                    .eq(NotificationTemplate::getTemplateType, templateType.getCode())
                    .count();
            typeStatistics.put(templateType.getCode(), count);
        }
        statistics.put("typeStatistics", typeStatistics);
        
        Map<String, Long> notificationTypeStatistics = new HashMap<>();
        for (NotificationType notificationType : NotificationType.values()) {
            long count = lambdaQuery()
                    .eq(NotificationTemplate::getNotificationType, notificationType.getCode())
                    .count();
            notificationTypeStatistics.put(notificationType.getCode(), count);
        }
        statistics.put("notificationTypeStatistics", notificationTypeStatistics);
        
        return statistics;
    }

    @Override
    public List<NotificationTemplate> getPopularTemplates(Integer limit) {
        return lambdaQuery()
                .eq(NotificationTemplate::getIsEnabled, 1)
                .orderByDesc(NotificationTemplate::getCreateTime)
                .last(limit != null ? "LIMIT " + limit : "LIMIT 10")
                .list();
    }

    private String renderContent(String template, Map<String, Object> params) {
        if (!StringUtils.hasText(template)) {
            return template;
        }

        if (params == null || params.isEmpty()) {
            return template;
        }

        String result = template;
        Matcher matcher = TEMPLATE_PATTERN.matcher(template);
        
        while (matcher.find()) {
            String placeholder = matcher.group(0);
            String key = matcher.group(1).trim();
            
            Object value = params.get(key);
            String replacement = value != null ? value.toString() : "";
            
            result = result.replace(placeholder, replacement);
        }
        
        return result;
    }
} 