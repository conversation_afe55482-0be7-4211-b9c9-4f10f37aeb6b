<template>
  <div class="delivery-confirm">
    <div class="page-header">
      <el-button @click="$router.back()" :icon="ArrowLeft">返回</el-button>
      <h2>配送签收</h2>
    </div>

    <div v-if="taskInfo" class="delivery-content">
      <!-- 订单信息 -->
      <el-card class="order-info">
        <template #header>
          <div class="card-header">
            <el-icon><Document /></el-icon>
            <span>配送信息</span>
          </div>
        </template>

        <div class="order-details">
          <div class="info-row">
            <span class="label">订单号：</span>
            <span class="value">{{ taskInfo.orderNumber }}</span>
          </div>
          
          <div class="address-section">
            <h4>收件人信息</h4>
            <div class="address-info">
              <p><strong>{{ taskInfo.receiverName }}</strong> {{ taskInfo.receiverPhone }}</p>
              <p>{{ taskInfo.receiverAddress }}</p>
            </div>
          </div>

          <div class="item-section">
            <h4>物品信息</h4>
            <div class="item-info">
              <p>物品名称：{{ taskInfo.itemName || '包裹' }}</p>
              <p>重量：{{ taskInfo.weight || 1 }}kg</p>
              <p>代收货款：{{ taskInfo.codAmount ? '¥' + taskInfo.codAmount : '无' }}</p>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 签收方式选择 -->
      <el-card class="delivery-method">
        <template #header>
          <div class="card-header">
            <el-icon><User /></el-icon>
            <span>签收方式</span>
          </div>
        </template>

        <el-radio-group v-model="deliveryForm.signType" @change="onSignTypeChange">
          <div class="sign-option">
            <el-radio value="SELF">
              <div class="option-content">
                <div class="option-title">本人签收</div>
                <div class="option-desc">收件人本人签收</div>
              </div>
            </el-radio>
          </div>

          <div class="sign-option">
            <el-radio value="PROXY">
              <div class="option-content">
                <div class="option-title">代收签收</div>
                <div class="option-desc">家人、朋友或物业代收</div>
              </div>
            </el-radio>
          </div>

          <div class="sign-option">
            <el-radio value="PICKUP_POINT">
              <div class="option-content">
                <div class="option-title">自提点签收</div>
                <div class="option-desc">放置在指定自提点</div>
              </div>
            </el-radio>
          </div>

          <div class="sign-option">
            <el-radio value="LOCKER">
              <div class="option-content">
                <div class="option-title">智能柜签收</div>
                <div class="option-desc">放置在智能快递柜</div>
              </div>
            </el-radio>
          </div>
        </el-radio-group>
      </el-card>

      <!-- 签收详情表单 -->
      <el-card class="delivery-form">
        <template #header>
          <div class="card-header">
            <el-icon><EditPen /></el-icon>
            <span>签收详情</span>
          </div>
        </template>

        <el-form :model="deliveryForm" :rules="deliveryRules" ref="deliveryFormRef" label-width="120px">
          <!-- 代收人信息 -->
          <div v-if="deliveryForm.signType === 'PROXY'" class="proxy-info">
            <el-form-item label="代收人姓名" prop="proxyName">
              <el-input v-model="deliveryForm.proxyName" placeholder="请输入代收人姓名" />
            </el-form-item>
            <el-form-item label="代收人电话" prop="proxyPhone">
              <el-input v-model="deliveryForm.proxyPhone" placeholder="请输入代收人电话" />
            </el-form-item>
            <el-form-item label="与收件人关系" prop="relationship">
              <el-select v-model="deliveryForm.relationship" placeholder="请选择关系">
                <el-option label="家人" value="FAMILY" />
                <el-option label="朋友" value="FRIEND" />
                <el-option label="同事" value="COLLEAGUE" />
                <el-option label="物业" value="PROPERTY" />
                <el-option label="邻居" value="NEIGHBOR" />
                <el-option label="其他" value="OTHER" />
              </el-select>
            </el-form-item>
          </div>

          <!-- 自提点信息 -->
          <div v-if="deliveryForm.signType === 'PICKUP_POINT'" class="pickup-point-info">
            <el-form-item label="自提点名称" prop="pickupPointName">
              <el-input v-model="deliveryForm.pickupPointName" placeholder="请输入自提点名称" />
            </el-form-item>
            <el-form-item label="自提点地址" prop="pickupPointAddress">
              <el-input v-model="deliveryForm.pickupPointAddress" placeholder="请输入自提点地址" />
            </el-form-item>
            <el-form-item label="联系电话" prop="pickupPointPhone">
              <el-input v-model="deliveryForm.pickupPointPhone" placeholder="请输入联系电话" />
            </el-form-item>
          </div>

          <!-- 智能柜信息 -->
          <div v-if="deliveryForm.signType === 'LOCKER'" class="locker-info">
            <el-form-item label="快递柜编号" prop="lockerCode">
              <el-input v-model="deliveryForm.lockerCode" placeholder="请输入快递柜编号" />
            </el-form-item>
            <el-form-item label="取件码" prop="pickupCode">
              <el-input v-model="deliveryForm.pickupCode" placeholder="请输入取件码" />
            </el-form-item>
          </div>

          <!-- 代收货款 -->
          <div v-if="taskInfo.codAmount" class="cod-section">
            <el-form-item label="代收货款" prop="codCollected">
              <el-radio-group v-model="deliveryForm.codCollected">
                <el-radio :value="true">已收取 ¥{{ taskInfo.codAmount }}</el-radio>
                <el-radio :value="false">未收取（异常情况）</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="!deliveryForm.codCollected" label="未收取原因" prop="codFailReason">
              <el-input v-model="deliveryForm.codFailReason" placeholder="请说明未收取货款的原因" />
            </el-form-item>
          </div>

          <!-- 签收拍照 -->
          <el-form-item label="签收照片" prop="signPhotos">
            <div class="photo-upload">
              <el-upload
                v-model:file-list="signPhotoList"
                action="#"
                list-type="picture-card"
                :auto-upload="false"
                :limit="3"
                accept="image/*"
                @change="handleSignPhotoChange"
              >
                <el-icon><Plus /></el-icon>
              </el-upload>
              <div class="upload-tip">
                请拍摄签收照片（必须包含收件人或代收人）
              </div>
            </div>
          </el-form-item>

          <!-- 备注 -->
          <el-form-item label="配送备注">
            <el-input
              v-model="deliveryForm.remarks"
              type="textarea"
              :rows="3"
              placeholder="请输入配送备注信息"
            />
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button size="large" @click="reportDeliveryException">配送异常</el-button>
        <el-button type="primary" size="large" :loading="confirming" @click="confirmDelivery">
          确认签收
        </el-button>
      </div>
    </div>

    <!-- 配送异常对话框 -->
    <el-dialog v-model="showExceptionDialog" title="配送异常" width="500px">
      <el-form :model="exceptionForm" label-width="100px">
        <el-form-item label="异常类型" required>
          <el-select v-model="exceptionForm.type" placeholder="请选择异常类型">
            <el-option label="收件人不在" value="RECIPIENT_ABSENT" />
            <el-option label="拒收" value="REJECTED" />
            <el-option label="地址错误" value="WRONG_ADDRESS" />
            <el-option label="货物损坏" value="GOODS_DAMAGED" />
            <el-option label="无法联系" value="UNREACHABLE" />
            <el-option label="其他" value="OTHER" />
          </el-select>
        </el-form-item>
        <el-form-item label="详细描述" required>
          <el-input
            v-model="exceptionForm.description"
            type="textarea"
            :rows="4"
            placeholder="请详细描述异常情况"
          />
        </el-form-item>
        <el-form-item label="现场照片">
          <el-upload
            v-model:file-list="exceptionPhotoList"
            action="#"
            list-type="picture-card"
            :auto-upload="false"
            :limit="3"
            accept="image/*"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="后续处理">
          <el-radio-group v-model="exceptionForm.nextAction">
            <el-radio value="RETRY">稍后重试</el-radio>
            <el-radio value="RETURN">退回寄件人</el-radio>
            <el-radio value="WAREHOUSE">暂存仓库</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showExceptionDialog = false">取消</el-button>
        <el-button type="primary" @click="submitException" :loading="submittingException">
          提交异常
        </el-button>
      </template>
    </el-dialog>

    <!-- 确认签收对话框 -->
    <el-dialog v-model="showConfirmDialog" title="确认签收" width="400px">
      <div class="confirm-content">
        <p>请确认签收信息：</p>
        <ul>
          <li>签收方式：{{ getSignTypeText(deliveryForm.signType) }}</li>
          <li v-if="deliveryForm.signType === 'PROXY'">代收人：{{ deliveryForm.proxyName }}</li>
          <li v-if="taskInfo.codAmount">代收货款：{{ deliveryForm.codCollected ? '已收取' : '未收取' }}</li>
          <li>签收照片：{{ signPhotoList.length }}张</li>
        </ul>
        <p style="color: #f56c6c; font-size: 14px;">
          确认后订单将标记为已完成，请仔细核对
        </p>
      </div>
      <template #footer>
        <el-button @click="showConfirmDialog = false">取消</el-button>
        <el-button type="primary" @click="submitDeliveryConfirm" :loading="confirming">
          确认完成
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import {
  ArrowLeft,
  Document,
  User,
  EditPen,
  Plus
} from '@element-plus/icons-vue'
import { courierApi } from '@/api/courier'

const route = useRoute()
const router = useRouter()

// 表单引用
const deliveryFormRef = ref<FormInstance>()

// 状态
const confirming = ref(false)
const submittingException = ref(false)
const showExceptionDialog = ref(false)
const showConfirmDialog = ref(false)
const taskInfo = ref<any>(null)
const signPhotoList = ref<any[]>([])
const exceptionPhotoList = ref<any[]>([])

// 配送表单
const deliveryForm = reactive({
  signType: 'SELF',
  proxyName: '',
  proxyPhone: '',
  relationship: '',
  pickupPointName: '',
  pickupPointAddress: '',
  pickupPointPhone: '',
  lockerCode: '',
  pickupCode: '',
  codCollected: true,
  codFailReason: '',
  signPhotos: [] as string[],
  remarks: ''
})

// 异常表单
const exceptionForm = reactive({
  type: '',
  description: '',
  photos: [] as string[],
  nextAction: 'RETRY'
})

// 表单验证规则
const deliveryRules: FormRules = {
  proxyName: [{ required: true, message: '请输入代收人姓名', trigger: 'blur' }],
  proxyPhone: [{ required: true, message: '请输入代收人电话', trigger: 'blur' }],
  relationship: [{ required: true, message: '请选择与收件人关系', trigger: 'change' }],
  pickupPointName: [{ required: true, message: '请输入自提点名称', trigger: 'blur' }],
  pickupPointAddress: [{ required: true, message: '请输入自提点地址', trigger: 'blur' }],
  lockerCode: [{ required: true, message: '请输入快递柜编号', trigger: 'blur' }],
  pickupCode: [{ required: true, message: '请输入取件码', trigger: 'blur' }],
  codFailReason: [{ required: true, message: '请说明未收取货款的原因', trigger: 'blur' }]
}

// 加载任务信息
const loadTaskInfo = async () => {
  const taskId = route.params.id as string
  if (!taskId) {
    ElMessage.error('任务ID不正确')
    router.back()
    return
  }

  try {
    // 模拟数据
    taskInfo.value = {
      id: taskId,
      orderNumber: 'LP' + Date.now().toString().slice(-8),
      receiverName: '李四',
      receiverPhone: '13800138002',
      receiverAddress: '上海市浦东新区某某路456号',
      itemName: '电子产品',
      weight: 2.5,
      codAmount: 299.00 // 代收货款金额，如果没有则为null
    }
  } catch (error) {
    console.error('加载任务信息失败:', error)
    ElMessage.error('加载任务信息失败')
    router.back()
  }
}

// 签收方式变化
const onSignTypeChange = () => {
  // 清空相关表单数据
  if (deliveryForm.signType !== 'PROXY') {
    deliveryForm.proxyName = ''
    deliveryForm.proxyPhone = ''
    deliveryForm.relationship = ''
  }
  if (deliveryForm.signType !== 'PICKUP_POINT') {
    deliveryForm.pickupPointName = ''
    deliveryForm.pickupPointAddress = ''
    deliveryForm.pickupPointPhone = ''
  }
  if (deliveryForm.signType !== 'LOCKER') {
    deliveryForm.lockerCode = ''
    deliveryForm.pickupCode = ''
  }
}

// 处理签收照片变化
const handleSignPhotoChange = (uploadFile: any, uploadFiles: any[]) => {
  signPhotoList.value = uploadFiles
}

// 确认配送
const confirmDelivery = async () => {
  if (!deliveryFormRef.value) return

  try {
    await deliveryFormRef.value.validate()
    
    if (signPhotoList.value.length === 0) {
      ElMessage.warning('请至少上传一张签收照片')
      return
    }

    showConfirmDialog.value = true
  } catch (error) {
    console.log('表单验证失败:', error)
  }
}

// 提交配送确认
const submitDeliveryConfirm = async () => {
  confirming.value = true
  
  try {
    const deliveryData = {
      taskId: taskInfo.value.id,
      signType: deliveryForm.signType,
      proxyName: deliveryForm.proxyName,
      proxyPhone: deliveryForm.proxyPhone,
      relationship: deliveryForm.relationship,
      pickupPointName: deliveryForm.pickupPointName,
      pickupPointAddress: deliveryForm.pickupPointAddress,
      pickupPointPhone: deliveryForm.pickupPointPhone,
      lockerCode: deliveryForm.lockerCode,
      pickupCode: deliveryForm.pickupCode,
      codCollected: deliveryForm.codCollected,
      codFailReason: deliveryForm.codFailReason,
      signPhotos: deliveryForm.signPhotos,
      remarks: deliveryForm.remarks
    }
    
    console.log('配送数据:', deliveryData)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('配送完成，签收成功')
    showConfirmDialog.value = false
    router.push('/courier/dashboard')
  } catch (error) {
    console.error('确认配送失败:', error)
    ElMessage.error('确认配送失败')
  } finally {
    confirming.value = false
  }
}

// 上报配送异常
const reportDeliveryException = () => {
  showExceptionDialog.value = true
}

// 提交异常
const submitException = async () => {
  if (!exceptionForm.type || !exceptionForm.description) {
    ElMessage.warning('请填写异常类型和详细描述')
    return
  }

  submittingException.value = true
  
  try {
    const exceptionData = {
      taskId: taskInfo.value.id,
      type: exceptionForm.type,
      description: exceptionForm.description,
      photos: exceptionForm.photos,
      nextAction: exceptionForm.nextAction
    }
    
    console.log('异常数据:', exceptionData)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    ElMessage.success('配送异常上报成功')
    showExceptionDialog.value = false
    router.push('/courier/dashboard')
  } catch (error) {
    console.error('异常上报失败:', error)
    ElMessage.error('异常上报失败')
  } finally {
    submittingException.value = false
  }
}

// 获取签收方式文本
const getSignTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    SELF: '本人签收',
    PROXY: '代收签收',
    PICKUP_POINT: '自提点签收',
    LOCKER: '智能柜签收'
  }
  return typeMap[type] || type
}

onMounted(() => {
  loadTaskInfo()
})
</script>

<style scoped>
.delivery-confirm {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.delivery-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.order-details {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.info-row {
  display: flex;
  gap: 10px;
}

.info-row .label {
  color: #666;
  min-width: 80px;
}

.info-row .value {
  font-weight: bold;
  color: #333;
}

.address-section, .item-section {
  padding: 15px;
  background: #f5f7fa;
  border-radius: 8px;
}

.address-section h4, .item-section h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 16px;
}

.address-info p, .item-info p {
  margin: 5px 0;
  color: #666;
}

.sign-option {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  transition: all 0.3s;
}

.sign-option:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.option-content {
  margin-left: 25px;
}

.option-title {
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.option-desc {
  color: #666;
  font-size: 14px;
}

.proxy-info, .pickup-point-info, .locker-info, .cod-section {
  padding: 15px;
  background: #f9f9f9;
  border-radius: 8px;
  margin-bottom: 15px;
}

.photo-upload {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.upload-tip {
  color: #666;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  padding: 20px;
}

.confirm-content ul {
  margin: 15px 0;
  padding-left: 20px;
}

.confirm-content li {
  margin: 5px 0;
  color: #333;
}
</style> 