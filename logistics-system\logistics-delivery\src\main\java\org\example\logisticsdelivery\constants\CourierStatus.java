package org.example.logisticsdelivery.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 配送员状态枚举
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Getter
@AllArgsConstructor
public enum CourierStatus {

    OFFLINE(0, "离线", "配送员离线状态"),
    ONLINE(1, "在线", "配送员在线空闲状态"),
    BUSY(2, "忙碌", "配送员忙碌配送中"),
    REST(3, "休息", "配送员休息状态");

    private final Integer code;
    private final String name;
    private final String description;

    /**
     * 根据状态码获取枚举
     */
    public static CourierStatus fromCode(Integer code) {
        for (CourierStatus status : CourierStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的配送员状态: " + code);
    }

    /**
     * 根据状态码获取名称
     */
    public static String getNameByCode(Integer code) {
        for (CourierStatus status : CourierStatus.values()) {
            if (status.getCode().equals(code)) {
                return status.getName();
            }
        }
        return "未知状态";
    }

    /**
     * 判断是否为可用状态
     */
    public boolean isAvailable() {
        return this == ONLINE;
    }
}