package org.example.logisticslogistics.controller;

import com.logistics.common.result.Result;
import org.example.logisticslogistics.entity.LocationInfo;
import org.example.logisticslogistics.entity.TrackingInfo;
import org.example.logisticslogistics.service.LocationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 位置管理控制器
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@RestController
@RequestMapping("/logistics/location")
public class LocationController {

    @Autowired
    private LocationService locationService;

    /**
     * 根据经纬度范围查询轨迹信息
     */
    @GetMapping("/range")
    public Result<List<TrackingInfo>> findByLocationRange(
            @RequestParam double minLng,
            @RequestParam double maxLng,
            @RequestParam double minLat,
            @RequestParam double maxLat) {
        try {
            List<TrackingInfo> trackings = locationService.findByLocationRange(minLng, maxLng, minLat, maxLat);
            return Result.success(trackings);
        } catch (Exception e) {
            return Result.error("根据范围查询轨迹信息失败: " + e.getMessage());
        }
    }

    /**
     * 查询指定坐标点附近的轨迹信息
     */
    @GetMapping("/nearby")
    public Result<List<TrackingInfo>> findNearby(
            @RequestParam double longitude,
            @RequestParam double latitude,
            @RequestParam double radiusKm) {
        try {
            List<TrackingInfo> trackings = locationService.findNearby(longitude, latitude, radiusKm);
            return Result.success(trackings);
        } catch (Exception e) {
            return Result.error("查询附近轨迹信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据城市查询轨迹信息
     */
    @GetMapping("/city/{city}")
    public Result<List<TrackingInfo>> findByCity(@PathVariable String city) {
        try {
            List<TrackingInfo> trackings = locationService.findByCity(city);
            return Result.success(trackings);
        } catch (Exception e) {
            return Result.error("根据城市查询轨迹信息失败: " + e.getMessage());
        }
    }

    /**
     * 计算两点间距离
     */
    @PostMapping("/distance")
    public Result<Double> calculateDistance(@RequestBody Map<String, LocationInfo> locations) {
        try {
            LocationInfo from = locations.get("from");
            LocationInfo to = locations.get("to");
            
            if (from == null || to == null) {
                return Result.error("起始位置和目标位置不能为空");
            }
            
            Double distance = locationService.calculateDistance(from, to);
            return Result.success(distance);
        } catch (Exception e) {
            return Result.error("计算距离失败: " + e.getMessage());
        }
    }

    /**
     * 验证经纬度坐标是否有效
     */
    @GetMapping("/validate")
    public Result<Boolean> isValidCoordinate(@RequestParam double longitude, @RequestParam double latitude) {
        try {
            boolean isValid = locationService.isValidCoordinate(longitude, latitude);
            return Result.success(isValid);
        } catch (Exception e) {
            return Result.error("验证坐标失败: " + e.getMessage());
        }
    }

    /**
     * 解析地址信息
     */
    @PostMapping("/parse-address")
    public Result<LocationInfo> parseAddress(@RequestParam String address) {
        try {
            LocationInfo location = locationService.parseAddress(address);
            return Result.success(location);
        } catch (Exception e) {
            return Result.error("解析地址失败: " + e.getMessage());
        }
    }

    /**
     * 格式化地址显示
     */
    @PostMapping("/format-address")
    public Result<String> formatAddress(@RequestBody LocationInfo location) {
        try {
            String formattedAddress = locationService.formatAddress(location);
            return Result.success(formattedAddress);
        } catch (Exception e) {
            return Result.error("格式化地址失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有城市列表
     */
    @GetMapping("/cities")
    public Result<List<String>> getAllCities() {
        try {
            List<String> cities = locationService.getAllCities();
            return Result.success(cities);
        } catch (Exception e) {
            return Result.error("获取城市列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据省份获取城市列表
     */
    @GetMapping("/cities/province/{province}")
    public Result<List<String>> getCitiesByProvince(@PathVariable String province) {
        try {
            List<String> cities = locationService.getCitiesByProvince(province);
            return Result.success(cities);
        } catch (Exception e) {
            return Result.error("根据省份获取城市列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取热力图数据
     */
    @GetMapping("/heatmap/{city}")
    public Result<List<Map<String, Object>>> getLocationHeatMapData(@PathVariable String city) {
        try {
            List<Map<String, Object>> heatMapData = locationService.getLocationHeatMapData(city);
            return Result.success(heatMapData);
        } catch (Exception e) {
            return Result.error("获取热力图数据失败: " + e.getMessage());
        }
    }

    /**
     * 批量验证坐标
     */
    @PostMapping("/validate-batch")
    public Result<Map<String, Boolean>> validateCoordinatesBatch(@RequestBody List<LocationInfo> locations) {
        try {
            Map<String, Boolean> validationResults = new java.util.HashMap<>();
            
            for (int i = 0; i < locations.size(); i++) {
                LocationInfo location = locations.get(i);
                boolean isValid = false;
                
                if (location.getLongitude() != null && location.getLatitude() != null) {
                    isValid = locationService.isValidCoordinate(
                            location.getLongitude().doubleValue(),
                            location.getLatitude().doubleValue()
                    );
                }
                
                validationResults.put("location_" + i, isValid);
            }
            
            return Result.success(validationResults);
        } catch (Exception e) {
            return Result.error("批量验证坐标失败: " + e.getMessage());
        }
    }

    /**
     * 获取两点间的中点坐标
     */
    @PostMapping("/midpoint")
    public Result<LocationInfo> getMidpoint(@RequestBody Map<String, LocationInfo> locations) {
        try {
            LocationInfo from = locations.get("from");
            LocationInfo to = locations.get("to");
            
            if (from == null || to == null || 
                from.getLongitude() == null || from.getLatitude() == null ||
                to.getLongitude() == null || to.getLatitude() == null) {
                return Result.error("起始位置和目标位置的经纬度不能为空");
            }
            
            // 计算中点坐标
            double midLng = (from.getLongitude().doubleValue() + to.getLongitude().doubleValue()) / 2;
            double midLat = (from.getLatitude().doubleValue() + to.getLatitude().doubleValue()) / 2;
            
            LocationInfo midpoint = new LocationInfo();
            midpoint.setLongitude(java.math.BigDecimal.valueOf(midLng));
            midpoint.setLatitude(java.math.BigDecimal.valueOf(midLat));
            
            return Result.success(midpoint);
        } catch (Exception e) {
            return Result.error("计算中点坐标失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定范围内的配送统计
     */
    @GetMapping("/delivery-stats")
    public Result<Map<String, Object>> getDeliveryStatsInRange(
            @RequestParam double minLng,
            @RequestParam double maxLng,
            @RequestParam double minLat,
            @RequestParam double maxLat) {
        try {
            List<TrackingInfo> trackings = locationService.findByLocationRange(minLng, maxLng, minLat, maxLat);
            
            Map<String, Object> stats = new java.util.HashMap<>();
            stats.put("totalCount", trackings.size());
            stats.put("completedCount", trackings.stream()
                    .filter(t -> "DELIVERED".equals(t.getCurrentStatus()) || "SELF_PICKUP".equals(t.getCurrentStatus()))
                    .count());
            stats.put("exceptionCount", trackings.stream()
                    .filter(TrackingInfo::getIsException)
                    .count());
            stats.put("inProgressCount", trackings.stream()
                    .filter(t -> "OUT_FOR_DELIVERY".equals(t.getCurrentStatus()) || 
                               "DELIVERY_ATTEMPT".equals(t.getCurrentStatus()))
                    .count());
            
            return Result.success(stats);
        } catch (Exception e) {
            return Result.error("获取配送统计失败: " + e.getMessage());
        }
    }
} 