package org.example.logisticsmap.util;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;

import java.net.SocketTimeoutException;
import java.net.URI;
import java.util.Map;

/**
 * HTTP客户端工具类
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Slf4j
@Component
public class HttpClientUtil {

    private static final int DEFAULT_CONNECT_TIMEOUT = 5000;
    private static final int DEFAULT_READ_TIMEOUT = 10000;

    /**
     * 发送GET请求
     *
     * @param url    请求URL
     * @param params 请求参数
     * @return 响应结果
     */
    public String doGet(String url, Map<String, String> params) {
        try {
            URIBuilder builder = new URIBuilder(url);
            if (params != null) {
                for (String key : params.keySet()) {
                    builder.addParameter(key, params.get(key));
                }
            }

            HttpGet httpGet = new HttpGet(builder.build());

            // 设置超时时间为5秒
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(15000)
                    .setConnectionRequestTimeout(15000)
                    .setSocketTimeout(15000)
                    .build();
            httpGet.setConfig(requestConfig);

            try (CloseableHttpClient httpClient = HttpClients.createDefault();
                 CloseableHttpResponse response = httpClient.execute(httpGet)) {

                if (response.getStatusLine().getStatusCode() == 200) {
                    HttpEntity entity = response.getEntity();
                    return EntityUtils.toString(entity, "UTF-8");
                }
            }
        } catch (SocketTimeoutException e) {
            log.error("HTTP请求超时: {}", e.getMessage());
            throw new RuntimeException("请求超时，请稍后重试");
        } catch (Exception e) {
            log.error("HTTP GET请求失败: {}", e.getMessage(), e);
            throw new RuntimeException("请求失败: " + e.getMessage());
        }

        return null;
    }

    /**
     * 发送GET请求（带超时配置）
     *
     * @param url            请求URL
     * @param params         请求参数
     * @param connectTimeout 连接超时时间
     * @param readTimeout    读取超时时间
     * @return 响应结果
     */
    public String doGet(String url, Map<String, String> params, int connectTimeout, int readTimeout) {
        CloseableHttpClient httpClient = null;
        String result = null;

        try {
            // 创建HTTP客户端
            httpClient = HttpClients.createDefault();

            // 构建URI
            URIBuilder uriBuilder = new URIBuilder(url);
            if (params != null && !params.isEmpty()) {
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    if (entry.getValue() != null) {
                        uriBuilder.addParameter(entry.getKey(), entry.getValue());
                    }
                }
            }
            URI uri = uriBuilder.build();

            // 创建GET请求
            HttpGet httpGet = new HttpGet(uri);

            // 设置请求配置
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(connectTimeout)
                    .setSocketTimeout(readTimeout)
                    .build();
            httpGet.setConfig(requestConfig);

            // 设置请求头
            httpGet.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            httpGet.setHeader("Accept", "application/json");
            httpGet.setHeader("Accept-Charset", "UTF-8");

            log.debug("发送HTTP GET请求: {}", uri.toString());

            // 执行请求
            HttpResponse response = httpClient.execute(httpGet);
            int statusCode = response.getStatusLine().getStatusCode();

            if (statusCode == 200) {
                result = EntityUtils.toString(response.getEntity(), "UTF-8");
                log.debug("HTTP请求成功，响应内容: {}", result);
            } else {
                log.error("HTTP请求失败，状态码: {}, 响应: {}", statusCode,
                         EntityUtils.toString(response.getEntity(), "UTF-8"));
            }

        } catch (Exception e) {
            log.error("HTTP请求异常: {}", e.getMessage(), e);
        } finally {
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
            } catch (Exception e) {
                log.error("关闭HTTP客户端异常: {}", e.getMessage(), e);
            }
        }

        return result;
    }

    /**
     * 发送GET请求并解析为JSON对象
     *
     * @param url    请求URL
     * @param params 请求参数
     * @return JSON对象
     */
    public Map<String, Object> doGetForJson(String url, Map<String, String> params) {
        String result = doGet(url, params);
        if (result != null && !result.trim().isEmpty()) {
            try {
                return JSON.parseObject(result, new TypeReference<Map<String, Object>>() {});
            } catch (Exception e) {
                log.error("解析JSON异常: {}, 原始数据: {}", e.getMessage(), result);
            }
        }
        return null;
    }

    /**
     * 发送GET请求并解析为指定类型对象
     *
     * @param url         请求URL
     * @param params      请求参数
     * @param targetClass 目标类型
     * @param <T>         泛型类型
     * @return 解析后的对象
     */
    public <T> T doGetForObject(String url, Map<String, String> params, Class<T> targetClass) {
        String result = doGet(url, params);
        if (result != null && !result.trim().isEmpty()) {
            try {
                return JSON.parseObject(result, targetClass);
            } catch (Exception e) {
                log.error("解析对象异常: {}, 原始数据: {}", e.getMessage(), result);
            }
        }
        return null;
    }

    /**
     * 验证响应是否成功
     *
     * @param response 响应结果
     * @return 是否成功
     */
    public boolean isSuccess(Map<String, Object> response) {
        if (response == null) {
            return false;
        }
        
        // 检查status字段
        Object status = response.get("status");
        if (status != null) {
            return "1".equals(status.toString()) || "OK".equalsIgnoreCase(status.toString());
        }
        
        // 检查infocode字段
        Object infocode = response.get("infocode");
        if (infocode != null) {
            return "10000".equals(infocode.toString());
        }
        
        return false;
    }

    /**
     * 获取响应错误信息
     *
     * @param response 响应结果
     * @return 错误信息
     */
    public String getErrorMessage(Map<String, Object> response) {
        if (response == null) {
            return "响应为空";
        }
        
        Object info = response.get("info");
        if (info != null) {
            return info.toString();
        }
        
        Object message = response.get("message");
        if (message != null) {
            return message.toString();
        }
        
        return "未知错误";
    }
} 