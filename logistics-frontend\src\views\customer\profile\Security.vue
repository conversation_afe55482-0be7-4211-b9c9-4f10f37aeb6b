<template>
  <div class="profile-security">
    <div class="page-header">
      <h2>账号安全</h2>
      <p>管理您的账号安全设置</p>
    </div>

    <!-- 修改密码 -->
    <el-card class="security-card">
      <template #header>
        <div class="card-header">
          <el-icon><Lock /></el-icon>
          <span>修改密码</span>
        </div>
      </template>

      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="120px"
        @submit.prevent="handlePasswordSubmit"
      >
        <el-form-item label="当前密码" prop="currentPassword">
          <el-input
            v-model="passwordForm.currentPassword"
            type="password"
            placeholder="请输入当前密码"
            show-password
          />
        </el-form-item>

        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>

        <el-form-item label="确认新密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handlePasswordSubmit" :loading="passwordLoading">
            修改密码
          </el-button>
          <el-button @click="resetPasswordForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 安全信息 -->
    <el-card class="security-card">
      <template #header>
        <div class="card-header">
          <el-icon><Shield /></el-icon>
          <span>安全信息</span>
        </div>
      </template>

      <div class="security-info">
        <div class="info-item">
          <div class="info-label">登录状态</div>
          <div class="info-value">
            <el-tag type="success">正常</el-tag>
          </div>
        </div>

        <div class="info-item">
          <div class="info-label">最后登录时间</div>
          <div class="info-value">{{ formatTime(userInfo?.lastLoginTime) }}</div>
        </div>

        <div class="info-item">
          <div class="info-label">账号创建时间</div>
          <div class="info-value">{{ formatTime(userInfo?.createTime) }}</div>
        </div>
      </div>
    </el-card>

    <!-- 安全操作 -->
    <el-card class="security-card">
      <template #header>
        <div class="card-header">
          <el-icon><Setting /></el-icon>
          <span>安全操作</span>
        </div>
      </template>

      <div class="security-actions">
        <div class="action-item">
          <div class="action-info">
            <h4>注销账号</h4>
            <p>永久删除您的账号及所有相关数据</p>
          </div>
          <el-button type="danger" plain @click="handleDeleteAccount">
            注销账号
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Lock, Shield, Setting } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import dayjs from 'dayjs'

const authStore = useAuthStore()
const passwordFormRef = ref<FormInstance>()
const passwordLoading = ref(false)

// 用户信息
const userInfo = computed(() => authStore.userInfo)

// 密码表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: '',
})

// 密码验证规则
const passwordRules: FormRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' },
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' },
    {
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/,
      message: '密码必须包含大小写字母和数字',
      trigger: 'blur',
    },
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
}

// 修改密码
const handlePasswordSubmit = async () => {
  if (!passwordFormRef.value) return

  try {
    await passwordFormRef.value.validate()
    passwordLoading.value = true

    // TODO: 调用API修改密码
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用

    ElMessage.success('密码修改成功')
    resetPasswordForm()
  } catch (error) {
    console.error('密码修改失败:', error)
    ElMessage.error('密码修改失败，请重试')
  } finally {
    passwordLoading.value = false
  }
}

// 重置密码表单
const resetPasswordForm = () => {
  passwordForm.currentPassword = ''
  passwordForm.newPassword = ''
  passwordForm.confirmPassword = ''
  passwordFormRef.value?.clearValidate()
}

// 注销账号
const handleDeleteAccount = async () => {
  try {
    await ElMessageBox.confirm(
      '注销账号将永久删除您的所有数据，此操作不可撤销。确定要继续吗？',
      '确认注销账号',
      {
        confirmButtonText: '确定注销',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger',
      }
    )

    // TODO: 调用API注销账号
    ElMessage.success('账号注销申请已提交，将在3个工作日内处理')
  } catch {
    // 用户取消
  }
}

// 格式化时间
const formatTime = (time?: string) => {
  if (!time) return '未知'
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}
</script>

<style scoped>
.profile-security {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 10px 0;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
}

.security-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.security-info {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-weight: 500;
  color: #333;
}

.info-value {
  color: #666;
}

.security-actions {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.action-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.action-info h4 {
  margin: 0 0 5px 0;
  color: #333;
}

.action-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

@media (max-width: 768px) {
  .profile-security {
    padding: 15px;
  }

  .action-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}
</style> 