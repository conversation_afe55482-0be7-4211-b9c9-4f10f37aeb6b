<template>
  <div class="profile-index">
    <div class="page-header">
      <h2>个人中心</h2>
    </div>

    <div class="profile-content">
      <!-- 用户信息卡片 -->
      <el-card class="user-info-card">
        <div class="user-info">
          <div class="avatar-section">
            <el-avatar :size="80" :src="userInfo?.avatar">
              {{ userInfo?.realName?.charAt(0) }}
            </el-avatar>
            <el-button size="small" @click="uploadAvatar">更换头像</el-button>
          </div>
          <div class="info-section">
            <h3>{{ userInfo?.realName }}</h3>
            <p>用户名：{{ userInfo?.username }}</p>
            <p>手机号：{{ maskPhone(userInfo?.phone || '') }}</p>
            <p>邮箱：{{ maskEmail(userInfo?.email || '') }}</p>
          </div>
          <div class="action-section">
            <el-button type="primary" @click="editProfile">编辑资料</el-button>
          </div>
        </div>
      </el-card>

      <!-- 功能菜单 -->
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><Setting /></el-icon>
            <span>功能菜单</span>
          </div>
        </template>
        <div class="menu-grid">
          <el-card
            v-for="menu in menuList"
            :key="menu.key"
            class="menu-item"
            @click="handleMenuClick(menu)"
          >
            <div class="menu-content">
              <el-icon :size="24" :color="menu.color">
                <component :is="menu.icon" />
              </el-icon>
              <div class="menu-info">
                <h4>{{ menu.title }}</h4>
                <p>{{ menu.description }}</p>
              </div>
              <el-icon class="menu-arrow">
                <ArrowRight />
              </el-icon>
            </div>
          </el-card>
        </div>
      </el-card>

      <!-- 数据统计 -->
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><DataBoard /></el-icon>
            <span>数据统计</span>
          </div>
        </template>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-number">{{ stats.totalOrders }}</div>
            <div class="stat-label">总订单数</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ stats.pendingOrders }}</div>
            <div class="stat-label">待处理订单</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ stats.completedOrders }}</div>
            <div class="stat-label">已完成订单</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">¥{{ stats.totalAmount.toFixed(2) }}</div>
            <div class="stat-label">总消费金额</div>
          </div>
        </div>
      </el-card>

      <!-- 最近订单 -->
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><Document /></el-icon>
            <span>最近订单</span>
          </div>
        </template>
        <div class="recent-orders" v-loading="ordersLoading">
          <div
            v-for="order in recentOrders"
            :key="order.id"
            class="order-item"
            @click="viewOrder(order.id)"
          >
            <div class="order-info">
              <div class="order-number">#{{ order.orderNumber }}</div>
              <el-tag :type="getStatusTagType(order.orderStatus)" size="small">
                {{ getStatusText(order.orderStatus) }}
              </el-tag>
            </div>
            <div class="order-details">
              <p>{{ order.receiverName }} {{ order.receiverAddress }}</p>
              <p class="order-time">{{ formatTime(order.createTime) }}</p>
            </div>
            <div class="order-amount">¥{{ order.totalFee.toFixed(2) }}</div>
          </div>

          <div v-if="recentOrders.length === 0 && !ordersLoading" class="empty-orders">
            <el-empty description="暂无订单" />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 头像上传对话框 -->
    <el-dialog v-model="showAvatarDialog" title="更换头像" width="400px">
      <el-upload
        class="avatar-uploader"
        :show-file-list="false"
        :on-success="handleAvatarSuccess"
        :before-upload="beforeAvatarUpload"
        :http-request="customUpload"
      >
        <img v-if="newAvatar" :src="newAvatar" class="avatar" />
        <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
      </el-upload>
      <template #footer>
        <el-button @click="showAvatarDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmAvatar" :disabled="!newAvatar"> 确认 </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  ArrowRight,
  DataBoard,
  Document,
  Plus,
  LocationInformation,
  Lock,
  Setting,
  Bell,
  QuestionFilled,
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { orderApi } from '@/api/order'
import type { Order, OrderStatus } from '@/types/order'
import dayjs from 'dayjs'
import type { Component } from 'vue'

const router = useRouter()
const authStore = useAuthStore()

// 菜单项类型定义
interface MenuItem {
  key: string
  title: string
  description: string
  icon: Component
  color: string
  path: string
}

// 上传响应类型定义
interface UploadResponse {
  code: number
  data: {
    url: string
  }
}

// 状态
const ordersLoading = ref(false)
const showAvatarDialog = ref(false)
const newAvatar = ref('')
const recentOrders = ref<Order[]>([])

// 用户信息
const userInfo = computed(() => authStore.userInfo)

// 统计数据
const stats = ref({
  totalOrders: 0,
  pendingOrders: 0,
  completedOrders: 0,
  totalAmount: 0,
})

// 功能菜单
const menuList: MenuItem[] = [
  {
    key: 'address',
    title: '地址管理',
    description: '管理收发货地址',
    icon: LocationInformation,
    color: '#409EFF',
    path: '/customer/profile/address',
  },
  {
    key: 'security',
    title: '账号安全',
    description: '修改密码、绑定手机',
    icon: Lock,
    color: '#67C23A',
    path: '/customer/profile/security',
  },
  {
    key: 'notification',
    title: '消息通知',
    description: '通知设置、消息中心',
    icon: Bell,
    color: '#E6A23C',
    path: '/customer/profile/notification',
  },
  {
    key: 'settings',
    title: '系统设置',
    description: '个性化设置',
    icon: Setting,
    color: '#F56C6C',
    path: '/customer/profile/settings',
  },
  {
    key: 'help',
    title: '帮助中心',
    description: '常见问题、联系客服',
    icon: QuestionFilled,
    color: '#909399',
    path: '/customer/profile/help',
  },
]

// 加载最近订单
const loadRecentOrders = async () => {
  ordersLoading.value = true
  try {
    const response = await orderApi.getOrderList({
      page: 1,
      size: 5,
    })

    console.log('订单列表响应:', response)

    // 修复：正确处理API响应结构
    // response 已经是 ApiResponse<OrderListData> 格式
    if (response.code === 200 && response.data) {
      recentOrders.value = response.data.records || []

      // 计算统计数据
      stats.value.totalOrders = response.data.total
      stats.value.pendingOrders = recentOrders.value.filter((o: Order) =>
        ['PENDING', 'PICKED_UP', 'IN_TRANSIT', 'OUT_FOR_DELIVERY'].includes(o.orderStatus),
      ).length
      stats.value.completedOrders = recentOrders.value.filter(
        (o: Order) => o.orderStatus === 'DELIVERED',
      ).length
      stats.value.totalAmount = recentOrders.value.reduce(
        (sum: number, order: Order) => sum + order.totalFee,
        0,
      )
    } else {
      console.warn('获取订单列表失败:', response.message)
    }
  } catch (error) {
    console.error('加载订单失败:', error)
    // 不显示错误消息，因为http拦截器已经处理了
  } finally {
    ordersLoading.value = false
  }
}

// 处理菜单点击
const handleMenuClick = (menu: MenuItem) => {
  if (menu.path) {
    router.push(menu.path)
  } else {
    ElMessage.info(`${menu.title}功能开发中...`)
  }
}

// 编辑资料
const editProfile = () => {
  router.push('/customer/profile/edit')
}

// 上传头像
const uploadAvatar = () => {
  showAvatarDialog.value = true
  newAvatar.value = userInfo.value?.avatar || ''
}

const customUpload = async (options: {
  file: File
  onSuccess?: (response: ApiResponse<AvatarUploadResponse>) => void
  onError?: (error: Error) => void
}) => {
  try {
    const response = await userApi.uploadAvatar(options.file)
    if (options.onSuccess) {
      options.onSuccess(response)
    }
  } catch (error) {
    console.error('头像上传失败:', error)
    if (options.onError) {
      options.onError(error)
    }
  }
}

// 头像上传成功
const handleAvatarSuccess = (response: UploadResponse) => {
  console.log('头像上传成功:', response)
  if (response && response.code === 200 && response.data && response.data.url) {
    newAvatar.value = response.data.url
    ElMessage.success('头像上传成功')
  } else {
    console.error('上传响应格式错误:', response)
    ElMessage.error('头像上传失败：响应格式错误')
  }
}

// 确认头像
const confirmAvatar = async () => {
  if (!newAvatar.value) return

  try {
    // 这里应该调用更新用户信息的接口来保存头像
    // 暂时只是更新本地状态
    if (authStore.userInfo) {
      authStore.userInfo.avatar = newAvatar.value
    }
    showAvatarDialog.value = false
    ElMessage.success('头像更新成功')
  } catch (error) {
    console.error('更新头像失败:', error)
    ElMessage.error('更新头像失败')
  }
}

// 头像上传前检查
const beforeAvatarUpload = (file: File) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('头像图片只能是 JPG/PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('头像图片大小不能超过 2MB!')
  }
  return isJPG && isLt2M
}

// 查看订单
const viewOrder = (orderId: number) => {
  router.push(`/customer/order/detail/${orderId}`)
}

// 工具函数
const maskPhone = (phone: string) => {
  if (!phone) return ''
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

const maskEmail = (email: string) => {
  if (!email) return ''
  const [username, domain] = email.split('@')
  if (username.length <= 2) return email
  return `${username.substring(0, 2)}***@${domain}`
}

const getStatusTagType = (status: OrderStatus) => {
  const typeMap = {
    PENDING: '',
    PICKED_UP: 'info',
    IN_TRANSIT: 'warning',
    OUT_FOR_DELIVERY: 'warning',
    DELIVERED: 'success',
    CANCELLED: 'danger',
    RETURNED: 'danger',
  }
  return typeMap[status] || ''
}

const getStatusText = (status: OrderStatus): string => {
  const statusMap: Record<OrderStatus, string> = {
    PENDING: '待发货',
    PICKED_UP: '已揽收',
    IN_TRANSIT: '运输中',
    OUT_FOR_DELIVERY: '派送中',
    DELIVERED: '已送达',
    CANCELLED: '已取消',
    RETURNED: '已退回',
  }
  return statusMap[status] || status
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm')
}

onMounted(() => {
  loadRecentOrders()
})
</script>

<style scoped>
.profile-index {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header h2 {
  margin: 0 0 20px 0;
  color: #333;
}

.profile-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.user-info-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 30px;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.info-section {
  flex: 1;
}

.info-section h3 {
  margin: 0 0 15px 0;
  font-size: 24px;
}

.info-section p {
  margin: 8px 0;
  opacity: 0.9;
}

.action-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.menu-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.menu-item {
  cursor: pointer;
  transition: all 0.3s;
}

.menu-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.menu-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.menu-info {
  flex: 1;
}

.menu-info h4 {
  margin: 0 0 5px 0;
  color: #333;
}

.menu-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.menu-arrow {
  color: #c0c4cc;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.recent-orders {
  max-height: 400px;
  overflow-y: auto;
}

.order-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.order-item:hover {
  background-color: #f8f9fa;
}

.order-item:last-child {
  border-bottom: none;
}

.order-info {
  min-width: 120px;
}

.order-number {
  font-weight: bold;
  margin-bottom: 5px;
}

.order-details {
  flex: 1;
}

.order-details p {
  margin: 2px 0;
  color: #666;
  font-size: 14px;
}

.order-time {
  color: #999 !important;
  font-size: 12px !important;
}

.order-amount {
  font-weight: bold;
  color: #f56c6c;
}

.empty-orders {
  text-align: center;
  padding: 40px 0;
}

.avatar-uploader {
  text-align: center;
}

.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
  border-radius: 6px;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
}

@media (max-width: 768px) {
  .user-info {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .menu-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .order-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style>
