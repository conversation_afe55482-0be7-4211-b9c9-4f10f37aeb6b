package org.example.logisticsorder.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.logistics.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 订单状态日志实体
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("order_status_log")
public class OrderStatusLog extends BaseEntity {

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 旧状态
     */
    private String oldStatus;

    /**
     * 新状态
     */
    private String newStatus;

    /**
     * 操作员ID
     */
    private Long operatorId;

    /**
     * 操作员姓名
     */
    private String operatorName;

    /**
     * 操作员类型
     */
    private String operatorType;

    /**
     * 变更原因
     */
    private String changeReason;

    /**
     * 操作地点
     */
    private String location;

    /**
     * 备注
     */
    private String remarks;
}
