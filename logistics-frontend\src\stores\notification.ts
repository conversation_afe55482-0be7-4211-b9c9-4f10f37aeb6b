import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'
import { ElNotification } from 'element-plus'

// 通知类型定义
export interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  duration?: number
  showClose?: boolean
  onClick?: () => void
  onClose?: () => void
  timestamp: number
  read: boolean
  category: 'system' | 'order' | 'delivery' | 'user'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  data?: any
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: 'notification' | 'status_update' | 'location_update' | 'order_update'
  data: any
  timestamp: number
}

export const useNotificationStore = defineStore('notification', () => {
  // 状态
  const notifications = ref<Notification[]>([])
  const unreadCount = ref(0)
  const isConnected = ref(false)
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = 5
  
  // WebSocket连接
  let websocket: WebSocket | null = null
  let reconnectTimer: NodeJS.Timeout | null = null

  // 配置
  const config = reactive({
    enableSound: true,
    enableDesktop: true,
    enableInApp: true,
    autoMarkRead: false,
    maxNotifications: 100
  })

  // 初始化WebSocket连接
  const initWebSocket = (token: string) => {
    if (websocket?.readyState === WebSocket.OPEN) {
      return
    }

    const wsUrl = `ws://localhost:8080/ws/notifications?token=${token}`
    
    try {
      websocket = new WebSocket(wsUrl)
      
      websocket.onopen = () => {
        console.log('WebSocket连接已建立')
        isConnected.value = true
        reconnectAttempts.value = 0
        
        // 发送心跳
        startHeartbeat()
      }
      
      websocket.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data)
          handleWebSocketMessage(message)
        } catch (error) {
          console.error('解析WebSocket消息失败:', error)
        }
      }
      
      websocket.onclose = (event) => {
        console.log('WebSocket连接已关闭:', event.code, event.reason)
        isConnected.value = false
        stopHeartbeat()
        
        // 自动重连
        if (reconnectAttempts.value < maxReconnectAttempts) {
          reconnectAttempts.value++
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.value), 30000)
          
          reconnectTimer = setTimeout(() => {
            console.log(`尝试重连 WebSocket (${reconnectAttempts.value}/${maxReconnectAttempts})`)
            initWebSocket(token)
          }, delay)
        }
      }
      
      websocket.onerror = (error) => {
        console.error('WebSocket错误:', error)
      }
      
    } catch (error) {
      console.error('创建WebSocket连接失败:', error)
    }
  }

  // 心跳机制
  let heartbeatTimer: NodeJS.Timeout | null = null
  
  const startHeartbeat = () => {
    heartbeatTimer = setInterval(() => {
      if (websocket?.readyState === WebSocket.OPEN) {
        websocket.send(JSON.stringify({ type: 'heartbeat' }))
      }
    }, 30000) // 30秒心跳
  }
  
  const stopHeartbeat = () => {
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer)
      heartbeatTimer = null
    }
  }

  // 处理WebSocket消息
  const handleWebSocketMessage = (message: WebSocketMessage) => {
    switch (message.type) {
      case 'notification':
        addNotification(message.data)
        break
      case 'status_update':
        handleStatusUpdate(message.data)
        break
      case 'location_update':
        handleLocationUpdate(message.data)
        break
      case 'order_update':
        handleOrderUpdate(message.data)
        break
      default:
        console.log('未知消息类型:', message.type)
    }
  }

  // 添加通知
  const addNotification = (notificationData: Partial<Notification>) => {
    const notification: Notification = {
      id: generateId(),
      type: notificationData.type || 'info',
      title: notificationData.title || '通知',
      message: notificationData.message || '',
      timestamp: Date.now(),
      read: false,
      category: notificationData.category || 'system',
      priority: notificationData.priority || 'medium',
      duration: notificationData.duration,
      showClose: notificationData.showClose !== false,
      onClick: notificationData.onClick,
      onClose: notificationData.onClose,
      data: notificationData.data
    }

    // 添加到通知列表
    notifications.value.unshift(notification)
    
    // 限制通知数量
    if (notifications.value.length > config.maxNotifications) {
      notifications.value = notifications.value.slice(0, config.maxNotifications)
    }
    
    // 更新未读数量
    updateUnreadCount()
    
    // 显示通知
    showNotification(notification)
    
    // 播放声音
    if (config.enableSound) {
      playNotificationSound(notification.priority)
    }
    
    // 桌面通知
    if (config.enableDesktop) {
      showDesktopNotification(notification)
    }
  }

  // 显示应用内通知
  const showNotification = (notification: Notification) => {
    if (!config.enableInApp) return

    ElNotification({
      type: notification.type,
      title: notification.title,
      message: notification.message,
      duration: notification.duration || getDurationByPriority(notification.priority),
      showClose: notification.showClose,
      onClick: () => {
        markAsRead(notification.id)
        notification.onClick?.()
      },
      onClose: notification.onClose
    })
  }

  // 显示桌面通知
  const showDesktopNotification = (notification: Notification) => {
    if (!('Notification' in window)) {
      return
    }

    if (Notification.permission === 'granted') {
      const desktopNotification = new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
        tag: notification.id
      })

      desktopNotification.onclick = () => {
        window.focus()
        markAsRead(notification.id)
        notification.onClick?.()
        desktopNotification.close()
      }

      // 自动关闭
      setTimeout(() => {
        desktopNotification.close()
      }, getDurationByPriority(notification.priority))
    }
  }

  // 播放通知声音
  const playNotificationSound = (priority: string) => {
    try {
      const audio = new Audio()
      
      switch (priority) {
        case 'urgent':
          audio.src = '/sounds/urgent.mp3'
          break
        case 'high':
          audio.src = '/sounds/high.mp3'
          break
        default:
          audio.src = '/sounds/default.mp3'
      }
      
      audio.volume = 0.3
      audio.play().catch(error => {
        console.log('播放通知声音失败:', error)
      })
    } catch (error) {
      console.log('播放通知声音失败:', error)
    }
  }

  // 根据优先级获取持续时间
  const getDurationByPriority = (priority: string): number => {
    switch (priority) {
      case 'urgent': return 0 // 不自动关闭
      case 'high': return 8000
      case 'medium': return 4500
      case 'low': return 3000
      default: return 4500
    }
  }

  // 标记为已读
  const markAsRead = (notificationId: string) => {
    const notification = notifications.value.find(n => n.id === notificationId)
    if (notification && !notification.read) {
      notification.read = true
      updateUnreadCount()
    }
  }

  // 标记全部为已读
  const markAllAsRead = () => {
    notifications.value.forEach(notification => {
      notification.read = true
    })
    updateUnreadCount()
  }

  // 删除通知
  const removeNotification = (notificationId: string) => {
    const index = notifications.value.findIndex(n => n.id === notificationId)
    if (index > -1) {
      notifications.value.splice(index, 1)
      updateUnreadCount()
    }
  }

  // 清空通知
  const clearNotifications = (category?: string) => {
    if (category) {
      notifications.value = notifications.value.filter(n => n.category !== category)
    } else {
      notifications.value = []
    }
    updateUnreadCount()
  }

  // 更新未读数量
  const updateUnreadCount = () => {
    unreadCount.value = notifications.value.filter(n => !n.read).length
  }

  // 处理状态更新
  const handleStatusUpdate = (data: any) => {
    // 这里可以触发其他store的状态更新
    console.log('状态更新:', data)
  }

  // 处理位置更新
  const handleLocationUpdate = (data: any) => {
    // 这里可以更新地图上的配送员位置
    console.log('位置更新:', data)
  }

  // 处理订单更新
  const handleOrderUpdate = (data: any) => {
    // 这里可以更新订单状态
    console.log('订单更新:', data)
    
    // 添加订单状态变更通知
    addNotification({
      type: 'info',
      title: '订单状态更新',
      message: `订单 ${data.orderNumber} 状态已更新为 ${data.status}`,
      category: 'order',
      priority: 'medium',
      data: data
    })
  }

  // 请求桌面通知权限
  const requestNotificationPermission = async (): Promise<boolean> => {
    if (!('Notification' in window)) {
      console.log('浏览器不支持桌面通知')
      return false
    }

    if (Notification.permission === 'granted') {
      return true
    }

    if (Notification.permission === 'denied') {
      return false
    }

    const permission = await Notification.requestPermission()
    return permission === 'granted'
  }

  // 更新配置
  const updateConfig = (newConfig: Partial<typeof config>) => {
    Object.assign(config, newConfig)
    
    // 保存到本地存储
    localStorage.setItem('notification-config', JSON.stringify(config))
  }

  // 加载配置
  const loadConfig = () => {
    try {
      const savedConfig = localStorage.getItem('notification-config')
      if (savedConfig) {
        Object.assign(config, JSON.parse(savedConfig))
      }
    } catch (error) {
      console.error('加载通知配置失败:', error)
    }
  }

  // 断开WebSocket连接
  const disconnect = () => {
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }
    
    stopHeartbeat()
    
    if (websocket) {
      websocket.close()
      websocket = null
    }
    
    isConnected.value = false
  }

  // 生成唯一ID
  const generateId = (): string => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  // 获取分类通知
  const getNotificationsByCategory = (category: string) => {
    return notifications.value.filter(n => n.category === category)
  }

  // 获取未读通知
  const getUnreadNotifications = () => {
    return notifications.value.filter(n => !n.read)
  }

  // 初始化
  const init = (token: string) => {
    loadConfig()
    initWebSocket(token)
    requestNotificationPermission()
  }

  return {
    // 状态
    notifications,
    unreadCount,
    isConnected,
    config,
    
    // 方法
    init,
    disconnect,
    addNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearNotifications,
    updateConfig,
    requestNotificationPermission,
    getNotificationsByCategory,
    getUnreadNotifications
  }
}) 