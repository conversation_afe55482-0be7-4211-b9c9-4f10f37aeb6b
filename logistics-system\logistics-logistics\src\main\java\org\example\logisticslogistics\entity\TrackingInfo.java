package org.example.logisticslogistics.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 物流轨迹信息主实体(MongoDB文档)
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
@Document(collection = "tracking_info")
public class TrackingInfo {

    /**
     * MongoDB主键
     */
    @Id
    private String id;

    /**
     * 订单号
     */
    @Field("order_number")
    private String orderNumber;

    /**
     * 订单ID
     */
    @Field("order_id")
    private Long orderId;

    /**
     * 当前状态
     */
    @Field("current_status")
    private String currentStatus;

    /**
     * 当前位置
     */
    @Field("current_location")
    private LocationInfo currentLocation;

    /**
     * 轨迹节点列表
     */
    @Field("tracking_nodes")
    private List<TrackingNode> trackingNodes;

    /**
     * 预计到达时间
     */
    @Field("estimated_delivery_time")
    private LocalDateTime estimatedDeliveryTime;

    /**
     * 实际签收时间
     */
    @Field("actual_delivery_time")
    private LocalDateTime actualDeliveryTime;

    /**
     * 总里程数(公里)
     */
    @Field("total_distance")
    private Double totalDistance;

    /**
     * 配送员ID
     */
    @Field("courier_id")
    private Long courierId;

    /**
     * 配送员姓名
     */
    @Field("courier_name")
    private String courierName;

    /**
     * 配送员电话
     */
    @Field("courier_phone")
    private String courierPhone;

    /**
     * 是否异常
     */
    @Field("is_exception")
    private Boolean isException = false;

    /**
     * 异常描述
     */
    @Field("exception_description")
    private String exceptionDescription;

    /**
     * 最后更新时间
     */
    @Field("last_update_time")
    private LocalDateTime lastUpdateTime;

    /**
     * 创建时间
     */
    @Field("create_time")
    private LocalDateTime createTime;

    /**
     * 扩展信息(JSON格式)
     */
    @Field("extra_info")
    private String extraInfo;
}
