package org.example.logisticsdelivery.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.logisticsdelivery.constants.CourierStatus;
import org.example.logisticsdelivery.entity.Courier;
import org.example.logisticsdelivery.mapper.CourierMapper;
import org.example.logisticsdelivery.service.CourierService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 配送员服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CourierServiceImpl implements CourierService {

    private final CourierMapper courierMapper;

    @Override
    @Transactional
    public Courier createCourier(Courier courier) {
        try {
            // 参数验证
            validateCourierInfo(courier);
            
            // 检查配送员编号是否已存在
            if (StringUtils.hasText(courier.getCourierCode())) {
                Courier existing = courierMapper.findByCourierCode(courier.getCourierCode());
                if (existing != null) {
                    throw new RuntimeException("配送员编号已存在");
                }
            } else {
                // 自动生成配送员编号
                courier.setCourierCode(generateCourierCode());
            }
            
            // 检查用户ID是否已绑定
            if (courier.getUserId() != null) {
                Courier existingUser = courierMapper.findByUserId(courier.getUserId());
                if (existingUser != null) {
                    throw new RuntimeException("该用户已绑定配送员账号");
                }
            }
            
            // 设置默认值
            courier.setStatus(CourierStatus.ONLINE.getCode());
            courier.setRating(BigDecimal.valueOf(5.0));
            courier.setDeliveryCount(0);
            courier.setCreateTime(LocalDateTime.now());
            courier.setUpdateTime(LocalDateTime.now());
            
            courierMapper.insert(courier);
            log.info("创建配送员成功，ID: {}, 编号: {}", courier.getId(), courier.getCourierCode());
            return courier;
        } catch (Exception e) {
            log.error("创建配送员失败", e);
            throw new RuntimeException("创建配送员失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Courier updateCourier(Courier courier) {
        try {
            if (courier.getId() == null) {
                throw new RuntimeException("配送员ID不能为空");
            }
            
            Courier existing = courierMapper.selectById(courier.getId());
            if (existing == null) {
                throw new RuntimeException("配送员不存在");
            }
            
            courier.setUpdateTime(LocalDateTime.now());
            courierMapper.updateById(courier);
            log.info("更新配送员信息成功，ID: {}", courier.getId());
            return courierMapper.selectById(courier.getId());
        } catch (Exception e) {
            log.error("更新配送员信息失败，ID: {}", courier.getId(), e);
            throw new RuntimeException("更新配送员信息失败: " + e.getMessage());
        }
    }

    @Override
    public Courier getCourierById(Long id) {
        if (id == null) {
            throw new RuntimeException("配送员ID不能为空");
        }
        return courierMapper.selectById(id);
    }

    @Override
    public Courier getCourierByCode(String courierCode) {
        if (!StringUtils.hasText(courierCode)) {
            throw new RuntimeException("配送员编号不能为空");
        }
        return courierMapper.findByCourierCode(courierCode);
    }

    @Override
    public Courier getCourierByUserId(Long userId) {
        if (userId == null) {
            throw new RuntimeException("用户ID不能为空");
        }
        return courierMapper.findByUserId(userId);
    }

    @Override
    public List<Courier> getCouriersByStatus(Integer status) {
        if (status == null) {
            throw new RuntimeException("状态不能为空");
        }
        return courierMapper.findByStatus(status);
    }

    @Override
    public List<Courier> getCouriersByWorkArea(String workArea) {
        if (!StringUtils.hasText(workArea)) {
            throw new RuntimeException("工作区域不能为空");
        }
        return courierMapper.findByWorkArea(workArea);
    }

    @Override
    public List<Courier> getCouriersInRange(BigDecimal minLng, BigDecimal maxLng, 
                                           BigDecimal minLat, BigDecimal maxLat) {
        if (minLng == null || maxLng == null || minLat == null || maxLat == null) {
            throw new RuntimeException("坐标范围参数不能为空");
        }
        return courierMapper.findByLocationRange(minLng, maxLng, minLat, maxLat);
    }

    @Override
    public List<Courier> getAvailableCouriers(String workArea) {
        return courierMapper.findAvailableCouriers(workArea);
    }

    @Override
    @Transactional
    public boolean updateCourierLocation(Long id, BigDecimal longitude, 
                                        BigDecimal latitude, String address) {
        try {
            if (id == null || longitude == null || latitude == null) {
                throw new RuntimeException("位置信息参数不能为空");
            }
            
            int result = courierMapper.updateLocation(id, longitude, latitude, address);
            if (result > 0) {
                log.info("更新配送员位置成功，ID: {}, 坐标: ({}, {})", id, longitude, latitude);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("更新配送员位置失败，ID: {}", id, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean updateCourierStatus(Long id, Integer status) {
        try {
            if (id == null || status == null) {
                throw new RuntimeException("参数不能为空");
            }
            
            int result = courierMapper.updateStatus(id, status);
            if (result > 0) {
                log.info("更新配送员状态成功，ID: {}, 状态: {}", id, status);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("更新配送员状态失败，ID: {}", id, e);
            return false;
        }
    }

    @Override
    public IPage<Courier> getCouriersPage(Page<Courier> page, Integer status, 
                                         String workArea, String courierName) {
        return courierMapper.findCouriersPage(page, status, workArea, courierName);
    }

    @Override
    public Map<String, Long> getStatusStatistics() {
        try {
            List<Map<String, Object>> statistics = courierMapper.countByStatus();
            Map<String, Long> result = new HashMap<>();
            
            for (Map<String, Object> stat : statistics) {
                // 安全地获取status值，处理不同的数据类型
                Object statusObj = stat.get("status");
                Integer status = null;
                if (statusObj instanceof Integer) {
                    status = (Integer) statusObj;
                } else if (statusObj instanceof Number) {
                    status = ((Number) statusObj).intValue();
                } else if (statusObj instanceof String) {
                    try {
                        status = Integer.parseInt((String) statusObj);
                    } catch (NumberFormatException e) {
                        log.warn("无法解析状态值: {}", statusObj);
                        continue;
                    }
                } else {
                    log.warn("未知的状态数据类型: {}, 值: {}", statusObj != null ? statusObj.getClass() : "null", statusObj);
                    continue;
                }
                
                // 安全地获取count值
                Object countObj = stat.get("count");
                Long count = 0L;
                if (countObj instanceof Number) {
                    count = ((Number) countObj).longValue();
                }
                
                String statusName = CourierStatus.getNameByCode(status);
                result.put(statusName, count);
            }
            
            return result;
        } catch (Exception e) {
            log.error("获取配送员状态统计失败", e);
            // 返回空的统计结果而不是抛出异常
            return new HashMap<>();
        }
    }

    @Override
    @Transactional
    public boolean deleteCourier(Long id) {
        try {
            if (id == null) {
                throw new RuntimeException("配送员ID不能为空");
            }
            
            Courier courier = courierMapper.selectById(id);
            if (courier == null) {
                throw new RuntimeException("配送员不存在");
            }
            
            int result = courierMapper.deleteById(id);
            if (result > 0) {
                log.info("删除配送员成功，ID: {}", id);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("删除配送员失败，ID: {}", id, e);
            return false;
        }
    }

    @Override
    public Courier assignBestCourier(String workArea, BigDecimal longitude, BigDecimal latitude) {
        try {
            List<Courier> availableCouriers = courierMapper.findAvailableCouriers(workArea);
            
            if (availableCouriers.isEmpty()) {
                return null;
            }
            
            // 简单分配策略：选择评分最高的可用配送员
            return availableCouriers.stream()
                    .filter(courier -> courier.getStatus().equals(CourierStatus.ONLINE.getCode()))
                    .max((c1, c2) -> c1.getRating().compareTo(c2.getRating()))
                    .orElse(null);
        } catch (Exception e) {
            log.error("自动分配配送员失败", e);
            return null;
        }
    }

    @Override
    @Transactional
    public boolean batchUpdateStatus(List<Long> courierIds, Integer status) {
        try {
            if (courierIds == null || courierIds.isEmpty()) {
                throw new RuntimeException("配送员ID列表不能为空");
            }
            
            if (status == null) {
                throw new RuntimeException("状态不能为空");
            }
            
            LambdaQueryWrapper<Courier> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(Courier::getId, courierIds);
            
            Courier updateCourier = new Courier();
            updateCourier.setStatus(status);
            updateCourier.setUpdateTime(LocalDateTime.now());
            
            int result = courierMapper.update(updateCourier, wrapper);
            log.info("批量更新配送员状态成功，数量: {}", result);
            return result > 0;
        } catch (Exception e) {
            log.error("批量更新配送员状态失败", e);
            return false;
        }
    }

    /**
     * 验证配送员信息
     */
    private void validateCourierInfo(Courier courier) {
        if (!StringUtils.hasText(courier.getCourierName())) {
            throw new RuntimeException("配送员姓名不能为空");
        }
        
        if (!StringUtils.hasText(courier.getPhone())) {
            throw new RuntimeException("手机号不能为空");
        }
        
        if (!StringUtils.hasText(courier.getWorkArea())) {
            throw new RuntimeException("工作区域不能为空");
        }
    }

    /**
     * 生成配送员编号
     */
    private String generateCourierCode() {
        return "CUR" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 4).toUpperCase();
    }
}