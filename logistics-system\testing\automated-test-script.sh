#!/bin/bash

# =============================================
# 物流系统自动化测试脚本
# 执行完整的系统功能测试
# =============================================

echo "🚀 开始物流系统自动化测试..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试结果记录函数
log_test_result() {
    local test_name="$1"
    local result="$2"
    local message="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [ "$result" = "PASS" ]; then
        echo -e "${GREEN}✅ PASS${NC} - $test_name: $message"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}❌ FAIL${NC} - $test_name: $message"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
}

# HTTP请求测试函数
test_api() {
    local url="$1"
    local expected_status="$2"
    local test_name="$3"
    
    response=$(curl -s -w "%{http_code}" -o /dev/null "$url")
    
    if [ "$response" = "$expected_status" ]; then
        log_test_result "$test_name" "PASS" "API响应正常 (HTTP $response)"
    else
        log_test_result "$test_name" "FAIL" "API响应异常 (HTTP $response, 期望 $expected_status)"
    fi
}

# 数据库连接测试
test_database() {
    echo -e "${BLUE}📊 测试数据库连接...${NC}"
    
    # 测试MySQL连接
    mysql -u root -p123456 -e "SELECT 1;" logistics 2>/dev/null
    if [ $? -eq 0 ]; then
        log_test_result "数据库连接" "PASS" "MySQL连接正常"
    else
        log_test_result "数据库连接" "FAIL" "MySQL连接失败"
    fi
    
    # 测试基础数据
    user_count=$(mysql -u root -p123456 -se "SELECT COUNT(*) FROM users;" logistics 2>/dev/null)
    if [ "$user_count" -gt 0 ]; then
        log_test_result "基础数据检查" "PASS" "用户数据: $user_count 条"
    else
        log_test_result "基础数据检查" "FAIL" "用户数据为空"
    fi
    
    order_count=$(mysql -u root -p123456 -se "SELECT COUNT(*) FROM orders;" logistics 2>/dev/null)
    if [ "$order_count" -gt 0 ]; then
        log_test_result "订单数据检查" "PASS" "订单数据: $order_count 条"
    else
        log_test_result "订单数据检查" "FAIL" "订单数据为空"
    fi
}

# 微服务健康检查
test_microservices() {
    echo -e "${BLUE}🔧 测试微服务状态...${NC}"
    
    # 服务列表
    declare -A services=(
        ["订单服务"]="http://localhost:8081/actuator/health"
        ["配送服务"]="http://localhost:8082/actuator/health"
        ["物流服务"]="http://localhost:8083/actuator/health"
        ["通知服务"]="http://localhost:8084/actuator/health"
        ["用户服务"]="http://localhost:8085/actuator/health"
        ["支付服务"]="http://localhost:8086/actuator/health"
        ["邮件服务"]="http://localhost:8087/actuator/health"
    )
    
    for service_name in "${!services[@]}"; do
        test_api "${services[$service_name]}" "200" "$service_name健康检查"
    done
}

# API功能测试
test_api_functions() {
    echo -e "${BLUE}🔌 测试API功能...${NC}"
    
    # 订单相关API
    test_api "http://localhost:8081/order/list?page=1&size=10" "200" "订单列表查询"
    test_api "http://localhost:8081/order/statistics" "200" "订单统计查询"
    
    # 配送相关API
    test_api "http://localhost:8082/delivery/tasks?page=1&size=10" "200" "配送任务查询"
    test_api "http://localhost:8082/delivery/couriers" "200" "配送员列表查询"
    
    # 物流轨迹API
    test_api "http://localhost:8083/logistics/tracking/LO202412300001" "200" "物流轨迹查询"
    test_api "http://localhost:8083/logistics/stations" "200" "网点列表查询"
    
    # 用户相关API
    test_api "http://localhost:8085/user/list?page=1&size=10" "200" "用户列表查询"
    
    # 邮件服务API
    test_api "http://localhost:8087/email/templates" "200" "邮件模板查询"
}

# 邮件功能测试
test_email_service() {
    echo -e "${BLUE}📧 测试邮件服务...${NC}"
    
    # 测试邮件模板
    template_response=$(curl -s -X GET "http://localhost:8087/email/templates")
    if echo "$template_response" | grep -q "ORDER_CREATED"; then
        log_test_result "邮件模板检查" "PASS" "邮件模板加载正常"
    else
        log_test_result "邮件模板检查" "FAIL" "邮件模板加载失败"
    fi
    
    # 测试邮件发送（使用测试邮箱）
    email_test_response=$(curl -s -X POST "http://localhost:8087/email/test" \
        -H "Content-Type: application/json" \
        -d '{
            "to": "<EMAIL>",
            "subject": "系统测试邮件",
            "content": "这是一封系统自动化测试邮件"
        }')
    
    if echo "$email_test_response" | grep -q "success\|成功"; then
        log_test_result "邮件发送测试" "PASS" "邮件发送功能正常"
    else
        log_test_result "邮件发送测试" "FAIL" "邮件发送功能异常"
    fi
}

# Redis缓存测试
test_redis_cache() {
    echo -e "${BLUE}⚡测试Redis缓存...${NC}"
    
    # 检查Redis连接
    redis-cli ping 2>/dev/null | grep -q "PONG"
    if [ $? -eq 0 ]; then
        log_test_result "Redis连接" "PASS" "Redis连接正常"
    else
        log_test_result "Redis连接" "FAIL" "Redis连接失败"
    fi
    
    # 检查缓存数据
    cache_keys=$(redis-cli KEYS "logistics:*" 2>/dev/null | wc -l)
    if [ "$cache_keys" -gt 0 ]; then
        log_test_result "缓存数据检查" "PASS" "缓存键数量: $cache_keys"
    else
        log_test_result "缓存数据检查" "FAIL" "缓存数据为空"
    fi
}

# 业务流程测试
test_business_flow() {
    echo -e "${BLUE}🔄 测试业务流程...${NC}"
    
    # 创建测试订单
    order_response=$(curl -s -X POST "http://localhost:8081/order/create" \
        -H "Content-Type: application/json" \
        -d '{
            "senderName": "测试用户",
            "senderPhone": "***********",
            "senderAddress": "北京市朝阳区测试地址",
            "receiverName": "测试收件人",
            "receiverPhone": "***********",
            "receiverAddress": "上海市浦东新区测试地址",
            "itemName": "测试物品",
            "itemWeight": 1.0,
            "serviceType": "STANDARD"
        }')
    
    if echo "$order_response" | grep -q "success\|成功\|orderNumber"; then
        log_test_result "订单创建流程" "PASS" "订单创建成功"
        
        # 提取订单号进行后续测试
        order_number=$(echo "$order_response" | grep -o '"orderNumber":"[^"]*"' | cut -d'"' -f4)
        if [ -n "$order_number" ]; then
            # 测试订单查询
            order_detail=$(curl -s "http://localhost:8081/order/$order_number")
            if echo "$order_detail" | grep -q "$order_number"; then
                log_test_result "订单查询流程" "PASS" "订单查询成功"
            else
                log_test_result "订单查询流程" "FAIL" "订单查询失败"
            fi
        fi
    else
        log_test_result "订单创建流程" "FAIL" "订单创建失败"
    fi
}

# 性能测试
test_performance() {
    echo -e "${BLUE}⚡ 测试系统性能...${NC}"
    
    # 测试API响应时间
    start_time=$(date +%s%N)
    curl -s "http://localhost:8081/order/list?page=1&size=10" > /dev/null
    end_time=$(date +%s%N)
    response_time=$(( (end_time - start_time) / 1000000 )) # 转换为毫秒
    
    if [ "$response_time" -lt 1000 ]; then
        log_test_result "API响应性能" "PASS" "响应时间: ${response_time}ms"
    else
        log_test_result "API响应性能" "FAIL" "响应时间过长: ${response_time}ms"
    fi
    
    # 测试并发性能（简单测试）
    echo "测试并发请求..."
    for i in {1..5}; do
        curl -s "http://localhost:8081/order/statistics" > /dev/null &
    done
    wait
    log_test_result "并发性能测试" "PASS" "5个并发请求完成"
}

# 前端资源测试
test_frontend() {
    echo -e "${BLUE}🎨 测试前端资源...${NC}"
    
    # 检查前端服务是否运行
    frontend_response=$(curl -s -w "%{http_code}" -o /dev/null "http://localhost:5173")
    if [ "$frontend_response" = "200" ]; then
        log_test_result "前端服务" "PASS" "前端服务运行正常"
    else
        log_test_result "前端服务" "FAIL" "前端服务无法访问"
    fi
    
    # 检查静态资源
    if [ -f "logistics-frontend/dist/index.html" ]; then
        log_test_result "前端构建" "PASS" "前端构建文件存在"
    else
        log_test_result "前端构建" "FAIL" "前端构建文件不存在"
    fi
}

# 主测试流程
main() {
    echo "======================================"
    echo "🧪 物流系统自动化测试开始"
    echo "======================================"
    echo ""
    
    # 执行各项测试
    test_database
    echo ""
    
    test_microservices
    echo ""
    
    test_api_functions
    echo ""
    
    test_email_service
    echo ""
    
    test_redis_cache
    echo ""
    
    test_business_flow
    echo ""
    
    test_performance
    echo ""
    
    test_frontend
    echo ""
    
    # 输出测试总结
    echo "======================================"
    echo "📊 测试结果总结"
    echo "======================================"
    echo -e "总测试数: ${BLUE}$TOTAL_TESTS${NC}"
    echo -e "通过数: ${GREEN}$PASSED_TESTS${NC}"
    echo -e "失败数: ${RED}$FAILED_TESTS${NC}"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        echo -e "测试结果: ${GREEN}✅ 全部通过${NC}"
        echo ""
        echo "🎉 恭喜！物流系统所有功能测试通过，系统可以投入使用！"
    else
        echo -e "测试结果: ${RED}❌ 存在失败项${NC}"
        echo ""
        echo "⚠️  请检查失败的测试项并修复相关问题。"
    fi
    
    echo ""
    echo "测试完成时间: $(date)"
    echo "======================================"
}

# 执行主函数
main "$@"
