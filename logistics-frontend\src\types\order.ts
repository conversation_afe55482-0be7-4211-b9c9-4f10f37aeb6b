import type { ApiResponse } from './api'

// 订单状态枚举
export type OrderStatus =
  | 'PENDING' // 待发货
  | 'PICKED_UP' // 已揽收
  | 'IN_TRANSIT' // 运输中
  | 'OUT_FOR_DELIVERY' // 派送中
  | 'DELIVERED' // 已送达
  | 'CANCELLED' // 已取消
  | 'RETURNED' // 已退回

// 服务类型
export type ServiceType = 'STANDARD' | 'EXPRESS' | 'URGENT'

// 支付方式
export type PaymentMethod = 'ONLINE' | 'COD'

// 支付状态
export type PaymentStatus = 0 | 1 | 2 // 0-未支付，1-已支付，2-已退款

// 订单基本信息
export interface Order {
  id: number
  orderNumber: string
  userId: number

  // 寄件人信息
  senderName: string
  senderPhone: string
  senderAddress: string
  senderProvince?: string
  senderCity?: string
  senderDistrict?: string
  senderLongitude?: number
  senderLatitude?: number

  // 收件人信息
  receiverName: string
  receiverPhone: string
  receiverAddress: string
  receiverProvince?: string
  receiverCity?: string
  receiverDistrict?: string
  receiverLongitude?: number
  receiverLatitude?: number

  // 物品信息
  itemName?: string
  itemType?: string
  itemWeight?: number
  itemVolume?: number
  itemLength?: number
  itemWidth?: number
  itemHeight?: number
  itemValue?: number
  isFragile: boolean

  // 服务和费用
  serviceType: ServiceType
  shippingFee: number
  insuranceFee: number
  packingFee: number
  totalFee: number

  // 支付信息
  paymentMethod: PaymentMethod
  paymentStatus: PaymentStatus

  // 状态和时间
  orderStatus: OrderStatus
  pickupTime?: string
  deliveryTime?: string
  signTime?: string
  estimatedDeliveryTime?: string

  // 备注
  remarks?: string

  // 系统字段
  createTime: string
  updateTime: string
}

// 创建订单请求
export interface CreateOrderRequest {
  // 寄件人信息
  senderName: string
  senderPhone: string
  senderAddress: string
  senderProvince?: string
  senderCity?: string
  senderDistrict?: string

  // 收件人信息
  receiverName: string
  receiverPhone: string
  receiverAddress: string
  receiverProvince?: string
  receiverCity?: string
  receiverDistrict?: string

  // 物品信息
  itemName: string
  itemType?: string
  itemWeight?: number
  itemVolume?: number
  itemValue?: number
  isFragile: boolean

  // 服务类型
  serviceType: ServiceType
  paymentMethod: PaymentMethod
  remarks?: string
}

// 订单查询参数
export interface OrderQueryParams {
  page?: number
  size?: number
  orderStatus?: OrderStatus
  keyword?: string
  startDate?: string
  endDate?: string
  paymentStatus?: PaymentStatus
  userId?: number
}

// 计算费用请求
export interface CalculateFeeRequest {
  senderCity: string
  receiverCity: string
  itemWeight: number
  itemVolume?: number
  serviceType: ServiceType
  itemValue?: number
  isFragile: boolean
}

// 计算费用响应
export interface CalculateFeeResponse {
  shippingFee: number
  insuranceFee: number
  packingFee: number
  totalFee: number
  estimatedDeliveryTime: string
}

// 分页数据结构（MyBatis Plus的IPage结构）
export interface IPageData<T> {
  records: T[]
  total: number
  current: number
  size: number
  pages: number
}

// 订单列表响应类型
export type OrderListResponse = ApiResponse<IPageData<Order>>
