package org.example.logisticsnotification.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 模板类型枚举
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Getter
@AllArgsConstructor
public enum TemplateType {

    ORDER_CREATED("ORDER_CREATED", "订单创建通知", "用户下单成功通知"),
    ORDER_PAID("ORDER_PAID", "订单支付通知", "订单支付成功通知"),
    ORDER_CANCELLED("ORDER_CANCELLED", "订单取消通知", "订单取消通知"),

    PICKUP_SCHEDULED("PICKUP_SCHEDULED", "揽件安排通知", "配送员揽件安排通知"),
    PICKUP_COMPLETED("PICKUP_COMPLETED", "揽件完成通知", "货物揽件完成通知"),

    DELIVERY_STARTED("DELIVERY_STARTED", "开始配送通知", "开始配送通知"),
    DELIVERY_IN_TRANSIT("DELIVERY_IN_TRANSIT", "运输中通知", "货物运输中通知"),
    DELIVERY_ARRIVED("DELIVERY_ARRIVED", "到达通知", "货物到达目的地通知"),
    DELIVERY_COMPLETED("DELIVERY_COMPLETED", "配送完成通知", "货物配送完成通知"),

    SYSTEM_MAINTENANCE("SYSTEM_MAINTENANCE", "系统维护通知", "系统维护通知"),
    PROMOTION("PROMOTION", "促销活动通知", "促销活动通知");

    private final String code;
    private final String name;
    private final String description;

    /**
     * 根据编码获取枚举
     */
    public static TemplateType fromCode(String code) {
        for (TemplateType type : TemplateType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的模板类型: " + code);
    }
}