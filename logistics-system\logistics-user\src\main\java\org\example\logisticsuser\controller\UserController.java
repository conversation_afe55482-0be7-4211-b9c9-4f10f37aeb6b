package org.example.logisticsuser.controller;

import org.example.logisticsuser.dto.UpdateUserInfoDTO;
import org.example.logisticsuser.service.UserService;
import org.example.logisticsuser.dto.UserLoginDTO;
import org.example.logisticsuser.dto.UserRegisterDTO;
import org.example.logisticsuser.vo.LoginResponseVO;
import org.example.logisticsuser.vo.UserInfoVO;
import com.logistics.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping("/user")
@CrossOrigin(origins = "*")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public Result<LoginResponseVO> register(@Valid @RequestBody UserRegisterDTO registerDTO) {
        try {
            log.info("用户注册请求: username={}, userType={}", registerDTO.getUsername(), registerDTO.getUserType());
            LoginResponseVO response = userService.register(registerDTO);
            return Result.success( "注册成功",response);
        } catch (Exception e) {
            log.error("用户注册失败: {}", e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public Result<LoginResponseVO> login(@Valid @RequestBody UserLoginDTO loginDTO) {
        try {
            log.info("用户登录请求: username={}, loginType={}", loginDTO.getUsername(), loginDTO.getLoginType());
            LoginResponseVO response = userService.login(loginDTO);
            return Result.success( "登录成功",response);
        } catch (Exception e) {
            log.error("用户登录失败: {}", e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/profile")
    public Result<UserInfoVO> getCurrentUserInfo(HttpServletRequest request) {
        try {
            String token = extractTokenFromRequest(request);
            if (token == null) {
                return Result.error("Token不能为空");
            }

            UserInfoVO userInfo = userService.getCurrentUserInfo(token);
            return Result.success("获取用户信息成功",userInfo);
        } catch (Exception e) {
            log.error("获取用户信息失败: {}", e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 根据用户ID获取用户信息
     */
    @GetMapping("/{userId}")
    public Result<UserInfoVO> getUserInfo(@PathVariable Long userId) {
        try {
            UserInfoVO userInfo = userService.getUserInfoById(userId);
            return Result.success( "获取用户信息成功",userInfo);
        } catch (Exception e) {
            log.error("获取用户信息失败: userId={}, error={}", userId, e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 检查用户名是否存在
     */
    @GetMapping("/check-username")
    public Result<Boolean> checkUsername(@RequestParam String username) {
        try {
            boolean exists = userService.checkUsernameExists(username);
            return Result.success( exists ? "用户名已存在" : "用户名可用",exists);
        } catch (Exception e) {
            log.error("检查用户名失败: {}", e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 检查手机号是否存在
     */
    @GetMapping("/check-phone")
    public Result<Boolean> checkPhone(@RequestParam String phone) {
        try {
            boolean exists = userService.checkPhoneExists(phone);
            return Result.success( exists ? "手机号已被注册" : "手机号可用",exists);
        } catch (Exception e) {
            log.error("检查手机号失败: {}", e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 检查邮箱是否存在
     */
    @GetMapping("/check-email")
    public Result<Boolean> checkEmail(@RequestParam String email) {
        try {
            boolean exists = userService.checkEmailExists(email);
            return Result.success( exists ? "邮箱已被注册" : "邮箱可用",exists);
        } catch (Exception e) {
            log.error("检查邮箱失败: {}", e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public Result<Void> logout(HttpServletRequest request) {
        try {
            String token = extractTokenFromRequest(request);
            // TODO: 将token加入黑名单
            log.info("用户登出成功");
            return Result.success( "登出成功",null);
        } catch (Exception e) {
            log.error("用户登出失败: {}", e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 修改密码
     */
    @PostMapping("/change-password")
    public Result<Void> changePassword(@RequestBody ChangePasswordRequest request, HttpServletRequest httpRequest) {
        try {
            String token = extractTokenFromRequest(httpRequest);
            UserInfoVO userInfo = userService.getCurrentUserInfo(token);

            boolean success = userService.changePassword(userInfo.getId(), request.getOldPassword(), request.getNewPassword());
            if (success) {
                return Result.success( "密码修改成功",null);
            } else {
                return Result.error("密码修改失败");
            }
        } catch (Exception e) {
            log.error("修改密码失败: {}", e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 从请求中提取Token
     */
    private String extractTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }

    /**
     * 修改密码请求DTO
     */
    public static class ChangePasswordRequest {
        private String oldPassword;
        private String newPassword;

        // getters and setters
        public String getOldPassword() { return oldPassword; }
        public void setOldPassword(String oldPassword) { this.oldPassword = oldPassword; }

        public String getNewPassword() { return newPassword; }
        public void setNewPassword(String newPassword) { this.newPassword = newPassword; }
    }
    /**
     * 更新用户信息
     */
    @PutMapping("/profile")
    public Result<UserInfoVO> updateUserInfo(@Valid @RequestBody UpdateUserInfoDTO updateDTO,
                                             HttpServletRequest request) {
        try {
            String token = extractTokenFromRequest(request);
            if (token == null) {
                return Result.error("Token不能为空");
            }

            UserInfoVO currentUser = userService.getCurrentUserInfo(token);
            UserInfoVO updatedUser = userService.updateUserInfo(currentUser.getId(), updateDTO);

            return Result.success("更新用户信息成功", updatedUser);
        } catch (Exception e) {
            log.error("更新用户信息失败: {}", e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取用户原始信息（用于编辑）
     */
    @GetMapping("/profile/edit")
    public Result<UpdateUserInfoDTO> getUserInfoForEdit(HttpServletRequest request) {
        try {
            String token = extractTokenFromRequest(request);
            if (token == null) {
                return Result.error("Token不能为空");
            }

            UpdateUserInfoDTO userInfo = userService.getUserInfoForEdit(token);
            return Result.success("获取用户信息成功", userInfo);
        } catch (Exception e) {
            log.error("获取用户编辑信息失败: {}", e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

}