<template>
  <div class="operator-layout">
    <!-- 顶部导航栏 -->
    <div class="layout-header">
      <div class="header-left">
        <div class="logo">
          <el-icon size="24" color="#409EFF"><Van /></el-icon>
          <span class="logo-text">物流调度系统</span>
        </div>
      </div>

      <div class="header-center">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/operator/dashboard' }">工作台</el-breadcrumb-item>
          <el-breadcrumb-item v-if="currentPageTitle">{{ currentPageTitle }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>

      <div class="header-right">
        <el-badge :value="notificationCount" class="notification-badge">
          <el-button text @click="showNotifications = true">
            <el-icon size="20"><Bell /></el-icon>
          </el-button>
        </el-badge>

        <el-dropdown @command="handleCommand">
          <div class="user-info">
            <el-avatar :size="32" :src="userInfo?.avatar">
              {{ userInfo?.realName?.charAt(0) }}
            </el-avatar>
            <span class="username">{{ userInfo?.realName }}</span>
            <el-icon><ArrowDown /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">个人设置</el-dropdown-item>
              <el-dropdown-item command="settings">系统设置</el-dropdown-item>
              <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 主体区域 -->
    <div class="layout-body">
      <!-- 侧边栏 -->
      <div class="layout-sidebar" :class="{ collapsed: sidebarCollapsed }">
        <div class="sidebar-toggle">
          <el-button text @click="toggleSidebar">
            <el-icon><Fold v-if="!sidebarCollapsed" /><Expand v-else /></el-icon>
          </el-button>
        </div>

        <el-menu
          :default-active="activeMenu"
          :collapse="sidebarCollapsed"
          :unique-opened="true"
          router
          class="sidebar-menu"
        >
          <el-menu-item index="/operator/dashboard">
            <el-icon><Monitor /></el-icon>
            <template #title>工作台</template>
          </el-menu-item>

          <el-menu-item index="/operator/orders">
            <el-icon><Box /></el-icon>
            <template #title>订单调度</template>
          </el-menu-item>

          <el-menu-item index="/operator/couriers">
            <el-icon><User /></el-icon>
            <template #title>配送员管理</template>
          </el-menu-item>

          <el-menu-item index="/operator/tickets">
            <el-icon><ChatDotSquare /></el-icon>
            <template #title>客服工单</template>
          </el-menu-item>

          <el-menu-item index="/operator/map">
            <el-icon><MapLocation /></el-icon>
            <template #title>实时地图</template>
          </el-menu-item>

          <el-menu-item index="/operator/dispatch">
            <el-icon><Document /></el-icon>
            <template #title>调度记录</template>
          </el-menu-item>
        </el-menu>
      </div>

      <!-- 内容区域 -->
      <div class="layout-content">
        <router-view />
      </div>
    </div>

    <!-- 通知抽屉 -->
    <el-drawer v-model="showNotifications" title="系统通知" direction="rtl" size="400px">
      <div class="notification-list">
        <div v-for="notification in notifications" :key="notification.id" class="notification-item">
          <div class="notification-header">
            <el-tag :type="getNotificationType(notification.type)" size="small">
              {{ getNotificationTypeText(notification.type) }}
            </el-tag>
            <span class="notification-time">{{ formatTime(notification.createTime) }}</span>
          </div>
          <div class="notification-content">
            <h4>{{ notification.title }}</h4>
            <p>{{ notification.message }}</p>
          </div>
          <div class="notification-actions">
            <el-button size="small" @click="markAsRead(notification.id)">标记已读</el-button>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Van,
  Bell,
  ArrowDown,
  Fold,
  Expand,
  Monitor,
  Box,
  User,
  ChatDotSquare,
  MapLocation,
  Document,
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { operatorApi } from '@/api/operator'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const sidebarCollapsed = ref(false)
const showNotifications = ref(false)
const notifications = ref<any[]>([])
const notificationCount = ref(0)

// 计算属性
const userInfo = computed(() => authStore.userInfo)
const activeMenu = computed(() => route.path)
const currentPageTitle = computed(() => {
  const routeTitle = route.meta?.title as string
  return routeTitle !== '操作员工作台' ? routeTitle : ''
})

// 生命周期
onMounted(() => {
  loadNotifications()
  // 定时刷新通知
  setInterval(loadNotifications, 30000)
})

// 监听路由变化
watch(route, () => {
  // 可以在这里添加页面切换的逻辑
})

// 方法
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

const handleCommand = (command: string) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人设置功能开发中')
      break
    case 'settings':
      ElMessage.info('系统设置功能开发中')
      break
    case 'logout':
      handleLogout()
      break
  }
}

const handleLogout = () => {
  authStore.logout()
  router.push('/login')
}

const loadNotifications = async () => {
  try {
    const response = await operatorApi.getSystemAlerts()
    if (response.code === 200) {
      notifications.value = response.data
      notificationCount.value = response.data.filter((n: any) => !n.read).length
    }
  } catch (error) {
    console.error('加载通知失败:', error)
    notifications.value = []
    notificationCount.value = 0
  }
}

const markAsRead = async (notificationId: number) => {
  try {
    // 先更新本地状态
    const notification = notifications.value.find((n) => n.id === notificationId)
    if (notification) {
      notification.read = true
      notificationCount.value = notifications.value.filter((n) => !n.read).length
    }
    
    // 调用API更新服务器状态
    await operatorApi.resolveAlert(notificationId)
    ElMessage.success('通知已标记为已读')
  } catch (error) {
    console.error('标记通知失败:', error)
    // 如果API调用失败，恢复本地状态
    const notification = notifications.value.find((n) => n.id === notificationId)
    if (notification) {
      notification.read = false
      notificationCount.value = notifications.value.filter((n) => !n.read).length
    }
    ElMessage.error('标记通知失败')
  }
}

const getNotificationType = (type: string) => {
  switch (type) {
    case 'urgent':
      return 'danger'
    case 'warning':
      return 'warning'
    case 'system':
      return 'info'
    default:
      return 'primary'
  }
}

const getNotificationTypeText = (type: string) => {
  switch (type) {
    case 'urgent':
      return '紧急'
    case 'warning':
      return '警告'
    case 'system':
      return '系统'
    default:
      return '通知'
  }
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}
</script>

<style scoped>
.operator-layout {
  min-height: 100vh;
  background: #f5f7fa;
}

.layout-header {
  height: 60px;
  background: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1000;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.notification-badge {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.username {
  font-size: 14px;
  color: #333;
}

.layout-body {
  display: flex;
  height: calc(100vh - 60px);
}

.layout-sidebar {
  width: 200px;
  background: white;
  box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);
  transition: width 0.3s;
  position: relative;
}

.layout-sidebar.collapsed {
  width: 64px;
}

.sidebar-toggle {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #f0f0f0;
}

.sidebar-menu {
  border: none;
  height: calc(100% - 40px);
}

.layout-content {
  flex: 1;
  overflow: auto;
}

.notification-list {
  padding: 0;
}

.notification-item {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.notification-time {
  font-size: 12px;
  color: #999;
}

.notification-content h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #333;
}

.notification-content p {
  margin: 0;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.notification-actions {
  margin-top: 12px;
  text-align: right;
}
</style>
