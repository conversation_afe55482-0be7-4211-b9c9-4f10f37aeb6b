package org.example.logisticsuser.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.example.logisticsuser.dto.AddressDTO;
import org.example.logisticsuser.entity.ShippingAddress;
import org.example.logisticsuser.mapper.ShippingAddressMapper;
import org.example.logisticsuser.service.AddressService;
import org.example.logisticsuser.vo.AddressVO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 地址服务实现
 */
@Slf4j
@Service
public class AddressServiceImpl implements AddressService {

    @Autowired
    private ShippingAddressMapper addressMapper;

    @Override
    public List<AddressVO> getUserAddresses(Long userId, Integer addressType) {
        LambdaQueryWrapper<ShippingAddress> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ShippingAddress::getUserId, userId);
        if (addressType != null) {
            wrapper.eq(ShippingAddress::getAddressType, addressType);
        }
        wrapper.orderByDesc(ShippingAddress::getIsDefault)
                .orderByDesc(ShippingAddress::getUpdateTime);

        List<ShippingAddress> addresses = addressMapper.selectList(wrapper);
        return addresses.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public AddressVO getAddressById(Long addressId, Long userId) {
        LambdaQueryWrapper<ShippingAddress> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ShippingAddress::getId, addressId)
                .eq(ShippingAddress::getUserId, userId);

        ShippingAddress address = addressMapper.selectOne(wrapper);
        if (address == null) {
            throw new RuntimeException("地址不存在");
        }

        return convertToVO(address);
    }

    @Override
    @Transactional
    public AddressVO createAddress(Long userId, AddressDTO addressDTO) {
        ShippingAddress address = new ShippingAddress();
        BeanUtils.copyProperties(addressDTO, address);
        address.setUserId(userId);

        // 如果设置为默认地址，先取消同类型的其他默认地址
        if (Boolean.TRUE.equals(addressDTO.getIsDefault())) {
            addressMapper.cancelDefaultByUserAndType(userId, addressDTO.getAddressType());
        }

        addressMapper.insert(address);
        return convertToVO(address);
    }

    @Override
    @Transactional
    public AddressVO updateAddress(Long addressId, Long userId, AddressDTO addressDTO) {
        // 检查地址是否存在且属于当前用户
        LambdaQueryWrapper<ShippingAddress> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ShippingAddress::getId, addressId)
                .eq(ShippingAddress::getUserId, userId);

        ShippingAddress existingAddress = addressMapper.selectOne(wrapper);
        if (existingAddress == null) {
            throw new RuntimeException("地址不存在");
        }

        // 如果设置为默认地址，先取消同类型的其他默认地址
        if (Boolean.TRUE.equals(addressDTO.getIsDefault())) {
            addressMapper.cancelDefaultByUserAndType(userId, addressDTO.getAddressType());
        }

        // 更新地址信息
        BeanUtils.copyProperties(addressDTO, existingAddress);
        addressMapper.updateById(existingAddress);

        return convertToVO(existingAddress);
    }

    @Override
    @Transactional
    public void deleteAddress(Long addressId, Long userId) {
        LambdaQueryWrapper<ShippingAddress> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ShippingAddress::getId, addressId)
                .eq(ShippingAddress::getUserId, userId);

        int deleted = addressMapper.delete(wrapper);
        if (deleted == 0) {
            throw new RuntimeException("地址不存在或删除失败");
        }
    }

    @Override
    @Transactional
    public void setDefaultAddress(Long addressId, Long userId) {
        // 获取要设置的地址
        ShippingAddress address = addressMapper.selectById(addressId);
        if (address == null || !address.getUserId().equals(userId)) {
            throw new RuntimeException("地址不存在");
        }

        // 取消同类型的其他默认地址
        addressMapper.cancelDefaultByUserAndType(userId, address.getAddressType());

        // 设置当前地址为默认
        address.setIsDefault(true);
        addressMapper.updateById(address);
    }

    @Override
    public Object getRegionData() {
        // 这里可以返回静态的省市区数据，或者调用第三方API
        // 为了简化，返回一个基本的数据结构
        return buildRegionData();
    }

    private AddressVO convertToVO(ShippingAddress address) {
        AddressVO vo = new AddressVO();
        BeanUtils.copyProperties(address, vo);
        return vo;
    }

    private Object buildRegionData() {
        // 这里返回简化的省市区数据
        // 实际项目中应该从数据库或第三方API获取完整数据
        return "{\n" +
                "  \"provinces\": [\n" +
                "    {\n" +
                "      \"code\": \"110000\",\n" +
                "      \"name\": \"北京市\",\n" +
                "      \"children\": [\n" +
                "        {\n" +
                "          \"code\": \"110100\",\n" +
                "          \"name\": \"北京市\",\n" +
                "          \"children\": [\n" +
                "            {\"code\": \"110101\", \"name\": \"东城区\"},\n" +
                "            {\"code\": \"110102\", \"name\": \"西城区\"},\n" +
                "            {\"code\": \"110105\", \"name\": \"朝阳区\"},\n" +
                "            {\"code\": \"110106\", \"name\": \"丰台区\"},\n" +
                "            {\"code\": \"110107\", \"name\": \"石景山区\"},\n" +
                "            {\"code\": \"110108\", \"name\": \"海淀区\"}\n" +
                "          ]\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"code\": \"310000\",\n" +
                "      \"name\": \"上海市\",\n" +
                "      \"children\": [\n" +
                "        {\n" +
                "          \"code\": \"310100\",\n" +
                "          \"name\": \"上海市\",\n" +
                "          \"children\": [\n" +
                "            {\"code\": \"310101\", \"name\": \"黄浦区\"},\n" +
                "            {\"code\": \"310104\", \"name\": \"徐汇区\"},\n" +
                "            {\"code\": \"310105\", \"name\": \"长宁区\"},\n" +
                "            {\"code\": \"310106\", \"name\": \"静安区\"},\n" +
                "            {\"code\": \"310107\", \"name\": \"普陀区\"},\n" +
                "            {\"code\": \"310115\", \"name\": \"浦东新区\"}\n" +
                "          ]\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}";
    }
}