<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.logisticsnotification.mapper.NotificationMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="org.example.logisticsnotification.entity.Notification">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="order_number" jdbcType="VARCHAR" property="orderNumber"/>
        <result column="business_id" jdbcType="BIGINT" property="businessId"/>
        <result column="business_type" jdbcType="VARCHAR" property="businessType"/>
        <result column="notification_type" jdbcType="VARCHAR" property="notificationType"/>
        <result column="template_code" jdbcType="VARCHAR" property="templateCode"/>
        <result column="recipient" jdbcType="VARCHAR" property="recipient"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="content" jdbcType="CLOB" property="content"/>
        <result column="template_params" jdbcType="CLOB" property="templateParams"/>
        <result column="send_status" jdbcType="INTEGER" property="sendStatus"/>
        <result column="send_time" jdbcType="TIMESTAMP" property="sendTime"/>
        <result column="failure_reason" jdbcType="VARCHAR" property="failureReason"/>
        <result column="retry_count" jdbcType="INTEGER" property="retryCount"/>
        <result column="max_retry_count" jdbcType="INTEGER" property="maxRetryCount"/>
        <result column="next_retry_time" jdbcType="TIMESTAMP" property="nextRetryTime"/>
        <result column="priority" jdbcType="INTEGER" property="priority"/>
        <result column="is_read" jdbcType="INTEGER" property="isRead"/>
        <result column="ext_info" jdbcType="CLOB" property="extInfo"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        id, user_id, order_number, business_id, business_type, notification_type,
        template_code, recipient, title, content, template_params, send_status,
        send_time, failure_reason, retry_count, max_retry_count, next_retry_time,
        priority, is_read, ext_info, remarks, create_time, update_time
    </sql>

    <!-- 根据用户ID查询通知列表 -->
    <select id="findByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM notifications
        WHERE user_id = #{userId}
        <if test="isRead != null">
            AND is_read = #{isRead}
        </if>
        ORDER BY priority DESC, create_time DESC
    </select>

    <!-- 根据业务信息查询通知 -->
    <select id="findByBusiness" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM notifications
        WHERE business_type = #{businessType}
        <if test="businessId != null">
            AND business_id = #{businessId}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据通知类型查询通知列表 -->
    <select id="findByNotificationType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM notifications
        WHERE notification_type = #{notificationType}
        <if test="sendStatus != null">
            AND send_status = #{sendStatus}
        </if>
        ORDER BY priority DESC, create_time DESC
    </select>

    <!-- 查询待发送的通知 -->
    <select id="findPendingNotifications" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM notifications
        WHERE send_status = 0
        <if test="notificationType != null and notificationType != ''">
            AND notification_type = #{notificationType}
        </if>
        ORDER BY priority DESC, create_time ASC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询需要重试的通知 -->
    <select id="findRetryNotifications" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM notifications
        WHERE send_status = 3
        AND retry_count &lt; max_retry_count
        AND (next_retry_time IS NULL OR next_retry_time &lt;= #{currentTime})
        ORDER BY priority DESC, next_retry_time ASC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询失败的通知 -->
    <select id="findFailedNotifications" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM notifications
        WHERE send_status = 3
        AND create_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY create_time DESC
    </select>

    <!-- 批量更新通知状态 -->
    <update id="batchUpdateStatus">
        UPDATE notifications
        SET send_status = #{status},
        <if test="failureReason != null">
            failure_reason = #{failureReason},
        </if>
        update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 更新重试信息 -->
    <update id="updateRetryInfo">
        UPDATE notifications
        SET retry_count = #{retryCount},
        next_retry_time = #{nextRetryTime},
        <if test="failureReason != null">
            failure_reason = #{failureReason},
        </if>
        update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 标记为已读 -->
    <update id="markAsRead">
        UPDATE notifications
        SET is_read = 1,
        update_time = NOW()
        WHERE user_id = #{userId}
        AND id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 分页查询通知 -->
    <select id="findNotificationsPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM notifications
        WHERE 1=1
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        <if test="notificationType != null and notificationType != ''">
            AND notification_type = #{notificationType}
        </if>
        <if test="sendStatus != null">
            AND send_status = #{sendStatus}
        </if>
        <if test="businessType != null and businessType != ''">
            AND business_type = #{businessType}
        </if>
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        ORDER BY priority DESC, create_time DESC
    </select>

    <!-- 统计通知数据 -->
    <select id="getNotificationStatistics" resultType="java.util.Map">
        SELECT
        COUNT(*) as totalCount,
        COUNT(CASE WHEN send_status = 0 THEN 1 END) as pendingCount,
        COUNT(CASE WHEN send_status = 1 THEN 1 END) as sendingCount,
        COUNT(CASE WHEN send_status = 2 THEN 1 END) as successCount,
        COUNT(CASE WHEN send_status = 3 THEN 1 END) as failedCount,
        COUNT(CASE WHEN is_read = 0 THEN 1 END) as unreadCount
        FROM notifications
        WHERE 1=1
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
    </select>

    <!-- 统计各状态通知数量 -->
    <select id="countByStatus" resultType="java.util.Map">
        SELECT
        send_status,
        COUNT(*) as count
        FROM notifications
        WHERE 1=1
        <if test="notificationType != null and notificationType != ''">
            AND notification_type = #{notificationType}
        </if>
        GROUP BY send_status
    </select>

    <!-- 统计各类型通知数量 -->
    <select id="countByType" resultType="java.util.Map">
        SELECT
        notification_type,
        COUNT(*) as count
        FROM notifications
        WHERE 1=1
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        GROUP BY notification_type
    </select>

    <!-- 删除过期通知 -->
    <delete id="deleteExpiredNotifications">
        DELETE FROM notifications
        WHERE create_time &lt; #{expireTime}
          AND send_status IN (2, 4)
    </delete>

    <!-- 查询用户未读通知数量 -->
    <select id="countUnreadByUserId" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM notifications
        WHERE user_id = #{userId}
          AND is_read = 0
    </select>

</mapper>