#!/bin/bash

# 前端错误修复脚本
# 用于快速修复前端项目中的常见问题

echo "🔧 开始修复前端项目..."

# 1. 清理缓存和临时文件
echo "📁 清理缓存文件..."
rm -rf node_modules/.cache
rm -rf node_modules/.tmp
rm -rf node_modules/.vite
rm -rf dist

# 2. 重新安装依赖
echo "📦 重新安装依赖..."
npm install

# 3. 检查TypeScript配置
echo "🔍 检查TypeScript配置..."
if [ ! -f "tsconfig.json" ]; then
    echo "❌ tsconfig.json 不存在，创建默认配置..."
    cat > tsconfig.json << 'EOF'
{
  "files": [],
  "references": [
    {
      "path": "./tsconfig.node.json"
    },
    {
      "path": "./tsconfig.app.json"
    }
  ]
}
EOF
fi

# 4. 检查并修复tsconfig.app.json
echo "🔧 检查 tsconfig.app.json..."
if grep -q '"composite": true' tsconfig.app.json; then
    echo "✅ tsconfig.app.json 配置正确"
else
    echo "⚠️  修复 tsconfig.app.json..."
    # 这里可以添加自动修复逻辑
fi

# 5. 运行类型检查
echo "🔍 运行TypeScript类型检查..."
npx vue-tsc --noEmit

# 6. 运行ESLint检查
echo "🔍 运行ESLint检查..."
npx eslint src --ext .ts,.vue --fix

# 7. 尝试启动开发服务器
echo "🚀 尝试启动开发服务器..."
echo "如果没有错误，请手动运行: npm run dev"

echo "✅ 前端修复完成！"
echo ""
echo "📋 修复总结："
echo "  ✅ 清理了缓存文件"
echo "  ✅ 重新安装了依赖"
echo "  ✅ 检查了TypeScript配置"
echo "  ✅ 运行了代码检查和修复"
echo ""
echo "🎯 下一步："
echo "  1. 运行 'npm run dev' 启动开发服务器"
echo "  2. 检查浏览器控制台是否有错误"
echo "  3. 测试主要功能是否正常"
