package org.example.logisticsnotification.controller;

import lombok.extern.slf4j.Slf4j;
import org.example.logisticsnotification.entity.EmailTemplate;
import org.example.logisticsnotification.service.EmailTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 邮件控制器
 * 在现有notification模块中提供邮件相关API
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Slf4j
@RestController
@RequestMapping("/email")
public class EmailController {

    @Autowired
    private EmailTemplateService emailTemplateService;

    /**
     * 获取所有邮件模板
     */
    @GetMapping("/templates")
    public Map<String, Object> getTemplates() {
        Map<String, Object> result = new HashMap<>();
        try {
            List<EmailTemplate> templates = emailTemplateService.getAllEnabledTemplates();
            result.put("success", true);
            result.put("data", templates);
            result.put("message", "获取邮件模板成功");
        } catch (Exception e) {
            log.error("获取邮件模板失败", e);
            result.put("success", false);
            result.put("message", "获取邮件模板失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 根据编码获取模板
     */
    @GetMapping("/template/{templateCode}")
    public Map<String, Object> getTemplate(@PathVariable String templateCode) {
        Map<String, Object> result = new HashMap<>();
        try {
            EmailTemplate template = emailTemplateService.getTemplateByCode(templateCode);
            if (template != null) {
                result.put("success", true);
                result.put("data", template);
                result.put("message", "获取模板成功");
            } else {
                result.put("success", false);
                result.put("message", "模板不存在");
            }
        } catch (Exception e) {
            log.error("获取邮件模板失败: {}", templateCode, e);
            result.put("success", false);
            result.put("message", "获取模板失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 发送模板邮件
     */
    @PostMapping("/send")
    public Map<String, Object> sendTemplateEmail(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();
        try {
            String to = (String) request.get("to");
            String templateCode = (String) request.get("templateCode");
            @SuppressWarnings("unchecked")
            Map<String, Object> variables = (Map<String, Object>) request.get("variables");

            if (to == null || templateCode == null) {
                result.put("success", false);
                result.put("message", "收件人和模板编码不能为空");
                return result;
            }

            boolean success = emailTemplateService.sendTemplateEmail(to, templateCode, variables);
            result.put("success", success);
            result.put("message", success ? "邮件发送成功" : "邮件发送失败");
            
        } catch (Exception e) {
            log.error("发送邮件失败", e);
            result.put("success", false);
            result.put("message", "发送邮件失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 批量发送模板邮件
     */
    @PostMapping("/send/batch")
    public Map<String, Object> sendBatchTemplateEmail(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();
        try {
            @SuppressWarnings("unchecked")
            List<String> recipients = (List<String>) request.get("recipients");
            String templateCode = (String) request.get("templateCode");
            @SuppressWarnings("unchecked")
            Map<String, Object> variables = (Map<String, Object>) request.get("variables");

            if (recipients == null || recipients.isEmpty() || templateCode == null) {
                result.put("success", false);
                result.put("message", "收件人列表和模板编码不能为空");
                return result;
            }

            int successCount = emailTemplateService.sendBatchTemplateEmail(recipients, templateCode, variables);
            result.put("success", true);
            result.put("data", Map.of(
                "total", recipients.size(),
                "success", successCount,
                "failed", recipients.size() - successCount
            ));
            result.put("message", String.format("批量发送完成，成功%d封，失败%d封", 
                successCount, recipients.size() - successCount));
            
        } catch (Exception e) {
            log.error("批量发送邮件失败", e);
            result.put("success", false);
            result.put("message", "批量发送邮件失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 测试邮件发送
     */
    @PostMapping("/test")
    public Map<String, Object> testEmail(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();
        try {
            String to = (String) request.get("to");
            String subject = (String) request.getOrDefault("subject", "测试邮件");
            String content = (String) request.getOrDefault("content", "这是一封测试邮件，用于验证邮件服务是否正常工作。");

            if (to == null) {
                result.put("success", false);
                result.put("message", "收件人不能为空");
                return result;
            }

            // 使用简单的测试模板
            Map<String, Object> variables = new HashMap<>();
            variables.put("subject", subject);
            variables.put("content", content);
            variables.put("testTime", java.time.LocalDateTime.now().toString());

            boolean success = emailTemplateService.sendTemplateEmail(to, "TEST", variables);
            result.put("success", success);
            result.put("message", success ? "测试邮件发送成功" : "测试邮件发送失败");
            
        } catch (Exception e) {
            log.error("发送测试邮件失败", e);
            result.put("success", false);
            result.put("message", "发送测试邮件失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 渲染模板预览
     */
    @PostMapping("/preview")
    public Map<String, Object> previewTemplate(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();
        try {
            String templateCode = (String) request.get("templateCode");
            @SuppressWarnings("unchecked")
            Map<String, Object> variables = (Map<String, Object>) request.get("variables");

            if (templateCode == null) {
                result.put("success", false);
                result.put("message", "模板编码不能为空");
                return result;
            }

            String renderedContent = emailTemplateService.renderTemplate(templateCode, variables);
            if (renderedContent != null) {
                result.put("success", true);
                result.put("data", renderedContent);
                result.put("message", "模板渲染成功");
            } else {
                result.put("success", false);
                result.put("message", "模板不存在或渲染失败");
            }
            
        } catch (Exception e) {
            log.error("渲染模板失败", e);
            result.put("success", false);
            result.put("message", "渲染模板失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 创建或更新模板
     */
    @PostMapping("/template")
    public Map<String, Object> saveTemplate(@RequestBody EmailTemplate template) {
        Map<String, Object> result = new HashMap<>();
        try {
            EmailTemplate savedTemplate = emailTemplateService.saveTemplate(template);
            result.put("success", true);
            result.put("data", savedTemplate);
            result.put("message", "模板保存成功");
        } catch (Exception e) {
            log.error("保存邮件模板失败", e);
            result.put("success", false);
            result.put("message", "保存模板失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 删除模板
     */
    @DeleteMapping("/template/{templateCode}")
    public Map<String, Object> deleteTemplate(@PathVariable String templateCode) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = emailTemplateService.deleteTemplate(templateCode);
            result.put("success", success);
            result.put("message", success ? "模板删除成功" : "模板删除失败");
        } catch (Exception e) {
            log.error("删除邮件模板失败: {}", templateCode, e);
            result.put("success", false);
            result.put("message", "删除模板失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 启用/禁用模板
     */
    @PutMapping("/template/{templateCode}/toggle")
    public Map<String, Object> toggleTemplate(@PathVariable String templateCode, 
                                             @RequestParam boolean enabled) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = emailTemplateService.toggleTemplate(templateCode, enabled);
            result.put("success", success);
            result.put("message", success ? 
                (enabled ? "模板启用成功" : "模板禁用成功") : 
                "操作失败");
        } catch (Exception e) {
            log.error("切换模板状态失败: {}", templateCode, e);
            result.put("success", false);
            result.put("message", "操作失败: " + e.getMessage());
        }
        return result;
    }
}
