package org.example.logisticslogistics.service;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 监控统计服务接口
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
public interface MonitorService {

    /**
     * 获取实时监控数据
     */
    Map<String, Object> getRealTimeMonitorData();

    /**
     * 统计各状态的订单数量
     */
    Map<String, Long> getStatusStatistics();

    /**
     * 统计各城市的订单数量
     */
    Map<String, Long> getCityStatistics();

    /**
     * 获取配送员绩效数据
     */
    Map<String, Object> getCourierPerformance(Long courierId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取今日统计数据
     */
    Map<String, Object> getTodayStatistics();

    /**
     * 获取本周统计数据
     */
    Map<String, Object> getWeeklyStatistics();

    /**
     * 获取本月统计数据
     */
    Map<String, Object> getMonthlyStatistics();

    /**
     * 获取异常订单统计
     */
    Map<String, Object> getExceptionStatistics();

    /**
     * 获取配送效率统计
     */
    Map<String, Object> getDeliveryEfficiencyStats();

    /**
     * 获取热门配送区域统计
     */
    Map<String, Object> getPopularDeliveryAreas();

    /**
     * 获取配送时间分布统计
     */
    Map<String, Object> getDeliveryTimeDistribution();

    /**
     * 获取系统健康状态
     */
    Map<String, Object> getSystemHealthStatus();
} 