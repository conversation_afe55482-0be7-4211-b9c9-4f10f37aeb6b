package com.logistics.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户角色枚举
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Getter
@AllArgsConstructor
public enum UserRole {

    /**
     * 超级管理员
     */
    SUPER_ADMIN("SUPER_ADMIN", "超级管理员"),

    /**
     * 管理员
     */
    ADMIN("ADMIN", "管理员"),

    /**
     * 普通用户
     */
    USER("USER", "普通用户"),

    /**
     * 配送员
     */
    COURIER("COURIER", "配送员"),

    /**
     * 操作员
     */
    OPERATOR("OPERATOR", "操作员"),

    /**
     * 揽件员
     */
    PICKUP_STAFF("PICKUP_STAFF", "揽件员"),

    /**
     * 分拣员
     */
    SORTING_STAFF("SORTING_STAFF", "分拣员"),

    /**
     * 中转员
     */
    TRANSIT_STAFF("TRANSIT_STAFF", "中转员"),

    /**
     * 客服
     */
    CUSTOMER_SERVICE("CUSTOMER_SERVICE", "客服");

    /**
     * 角色编码
     */
    private final String code;

    /**
     * 角色描述
     */
    private final String desc;

    /**
     * 根据角色编码获取枚举
     */
    public static UserRole getByCode(String code) {
        for (UserRole role : UserRole.values()) {
            if (role.getCode().equals(code)) {
                return role;
            }
        }
        return null;
    }

    /**
     * 检查是否为管理员角色
     */
    public boolean isAdmin() {
        return this == SUPER_ADMIN || this == ADMIN;
    }

    /**
     * 检查是否为操作员角色
     */
    public boolean isOperator() {
        return this == OPERATOR || this == PICKUP_STAFF || this == SORTING_STAFF || this == TRANSIT_STAFF;
    }

    /**
     * 检查是否为配送相关角色
     */
    public boolean isDeliveryRole() {
        return this == COURIER || this == PICKUP_STAFF;
    }
}
