-- 修复订单表结构
USE logistics;

-- 备份现有数据
CREATE TABLE orders_backup AS SELECT * FROM orders;

-- 删除现有订单表
DROP TABLE IF EXISTS orders;

-- 重新创建订单表（包含所有必需字段）
CREATE TABLE `orders` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_number` VARCHAR(32) NOT NULL COMMENT '订单号',
  `user_id` BIGINT(20) NOT NULL COMMENT '下单用户ID',
  
  -- 寄件人信息
  `sender_name` VARCHAR(50) NOT NULL COMMENT '寄件人姓名',
  `sender_phone` VARCHAR(20) NOT NULL COMMENT '寄件人电话',
  `sender_address` VARCHAR(255) NOT NULL COMMENT '寄件人地址',
  `sender_province` VARCHAR(50) DEFAULT NULL COMMENT '寄件人省份',
  `sender_city` VARCHAR(50) DEFAULT NULL COMMENT '寄件人城市',
  `sender_district` VARCHAR(50) DEFAULT NULL COMMENT '寄件人区域',
  `sender_longitude` DECIMAL(10,6) DEFAULT NULL COMMENT '寄件人经度',
  `sender_latitude` DECIMAL(10,6) DEFAULT NULL COMMENT '寄件人纬度',
  
  -- 收件人信息
  `receiver_name` VARCHAR(50) NOT NULL COMMENT '收件人姓名',
  `receiver_phone` VARCHAR(20) NOT NULL COMMENT '收件人电话',
  `receiver_address` VARCHAR(255) NOT NULL COMMENT '收件人地址',
  `receiver_province` VARCHAR(50) DEFAULT NULL COMMENT '收件人省份',
  `receiver_city` VARCHAR(50) DEFAULT NULL COMMENT '收件人城市',
  `receiver_district` VARCHAR(50) DEFAULT NULL COMMENT '收件人区域',
  `receiver_longitude` DECIMAL(10,6) DEFAULT NULL COMMENT '收件人经度',
  `receiver_latitude` DECIMAL(10,6) DEFAULT NULL COMMENT '收件人纬度',
  
  -- 物品信息
  `item_name` VARCHAR(100) NOT NULL COMMENT '物品名称',
  `item_weight` DECIMAL(10,2) NOT NULL COMMENT '物品重量(kg)',
  `item_volume` DECIMAL(10,2) DEFAULT NULL COMMENT '物品体积(立方米)',
  `item_value` DECIMAL(10,2) DEFAULT NULL COMMENT '物品价值(元)',
  `item_category` VARCHAR(50) DEFAULT NULL COMMENT '物品类别',
  `is_fragile` TINYINT(1) DEFAULT 0 COMMENT '是否易碎：0-否，1-是',
  
  -- 服务和费用
  `service_type` VARCHAR(20) NOT NULL COMMENT '服务类型：STANDARD-标准，EXPRESS-快递，URGENT-加急',
  `shipping_fee` DECIMAL(10,2) DEFAULT NULL COMMENT '运费',
  `insurance_fee` DECIMAL(10,2) DEFAULT NULL COMMENT '保险费',
  `packing_fee` DECIMAL(10,2) DEFAULT NULL COMMENT '包装费',
  `total_fee` DECIMAL(10,2) NOT NULL COMMENT '总费用(元)',
  
  -- 支付信息
  `payment_method` VARCHAR(20) DEFAULT NULL COMMENT '支付方式：ONLINE-在线支付，COD-货到付款',
  `payment_status` VARCHAR(20) NOT NULL DEFAULT 'UNPAID' COMMENT '支付状态：UNPAID-未支付，PAID-已支付',
  
  -- 状态和时间
  `order_status` VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '订单状态',
  `pickup_time` TIMESTAMP NULL DEFAULT NULL COMMENT '揽件时间',
  `delivery_time` TIMESTAMP NULL DEFAULT NULL COMMENT '派送时间',
  `sign_time` TIMESTAMP NULL DEFAULT NULL COMMENT '签收时间',
  `estimated_delivery_time` TIMESTAMP NULL DEFAULT NULL COMMENT '预计送达时间',
  
  -- 备注
  `remarks` TEXT DEFAULT NULL COMMENT '备注',
  
  -- 系统字段
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_number` (`order_number`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_sender_city` (`sender_city`),
  KEY `idx_receiver_city` (`receiver_city`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- 如果有备份数据，可以尝试迁移（需要根据实际情况调整）
-- INSERT INTO orders (order_number, user_id, sender_name, sender_phone, sender_address, 
--                    receiver_name, receiver_phone, receiver_address, item_name, item_weight, 
--                    service_type, total_fee, payment_status, order_status, create_time, update_time)
-- SELECT order_number, customer_id, sender_name, sender_phone, sender_address,
--        receiver_name, receiver_phone, receiver_address, item_name, item_weight,
--        service_type, total_fee, 
--        CASE WHEN payment_status = 1 THEN 'PAID' ELSE 'UNPAID' END,
--        order_status, create_time, update_time
-- FROM orders_backup;

-- 插入一些测试数据
INSERT INTO orders (order_number, user_id, sender_name, sender_phone, sender_address, sender_city,
                   receiver_name, receiver_phone, receiver_address, receiver_city,
                   item_name, item_weight, service_type, total_fee, payment_status, order_status) VALUES
('LO202412300001', 5, '张三', '13800000001', '北京市朝阳区建国门外大街1号', '北京市',
 '李四', '13800000002', '上海市浦东新区陆家嘴环路1000号', '上海市',
 '重要文件', 1.5, 'STANDARD', 25.00, 'PAID', 'PICKUP'),
('LO202412300002', 5, '王五', '13800000003', '广州市天河区珠江新城', '广州市',
 '赵六', '13800000004', '深圳市南山区科技园', '深圳市',
 '电子产品', 2.0, 'EXPRESS', 35.00, 'UNPAID', 'PENDING'),
('LO202412300003', 6, '钱七', '13800000005', '杭州市西湖区文三路', '杭州市',
 '孙八', '13800000006', '南京市鼓楼区中山路', '南京市',
 '书籍资料', 0.8, 'STANDARD', 18.00, 'PAID', 'TRANSIT');
