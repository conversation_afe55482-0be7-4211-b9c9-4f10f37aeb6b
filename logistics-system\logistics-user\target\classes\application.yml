server:
  port: 8001

spring:
  application:
    name: logistics-user
  datasource:
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ****************************************************************************************************************************************************
      username: root
      password: liyuqw8017
      # 添加连接池配置
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: true
      test-on-return: false
      # 连接超时配置
      connection-timeout: 30000
      socket-timeout: 60000
      # 防止连接被回收
      keep-alive: true
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
  redis:
    host: localhost
    port: 6379
    password: redis123
    database: 0

mybatis-plus:
  mapper-locations: classpath*:mapper/*.xml
  type-aliases-package: org.example.logisticsuser.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
# JWT配置
jwt:
  secret: logistics-secret-key-2024-for-microservice-authentication-system
  expiration: 86400
# 文件上传配置
file:
  upload:
    path: /tmp/uploads
    domain: http://localhost:8001

logging:
  level:
    com.logistics.user: debug