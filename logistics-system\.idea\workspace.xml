<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="47dbaf6b-0adb-4798-8eb2-b9befcf370e6" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="spring-beans.schema" />
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2z1uL4auYtdZIVNmxet3jvB5dLA" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Application.Main.executor&quot;: &quot;Run&quot;,
    &quot;Maven.logistics-system [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.logistics-system [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.logistics-system [install].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.LogisticsDeliveryApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.LogisticsGatewayApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.LogisticsLogisticsApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.LogisticsMapApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.LogisticsNotificationApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.LogisticsOrderApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.LogisticsUserApplication.executor&quot;: &quot;Run&quot;,
    &quot;jdk.selected.JAVA_MODULE&quot;: &quot;17&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/桌面/微服务/cloud5&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.35287356&quot;,
    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\桌面\springBoot\node\logistics-system\logistics-map\src\main\java\org\example\logisticsmap" />
      <recent name="E:\桌面\springBoot\node\logistics-system\logistics-notification\src\main\java\org\example\logisticsnotification" />
      <recent name="E:\桌面\springBoot\node\logistics-system\logistics-logistics\src\main\java\org\example\logisticslogistics" />
      <recent name="E:\桌面\springBoot\node\logistics-system\logistics-order\src\main\java\org\example\logisticsorder" />
      <recent name="E:\桌面\springBoot\node\logistics-system\logistics-delivery\src\main\java\org\example\logisticsdelivery" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\桌面\springBoot\node\logistics-system\logistics-email\src\main\resources" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Application.Main">
    <configuration name="Main" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="org.example.Main" />
      <module name="logistics-system" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="org.example.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LogisticsDeliveryApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="logistics-delivery" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.example.logisticsdelivery.LogisticsDeliveryApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LogisticsGatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="logistics-gateway" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.example.logisticsgateway.LogisticsGatewayApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LogisticsLogisticsApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="logistics-logistics" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.example.logisticslogistics.LogisticsLogisticsApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LogisticsMapApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="logistics-map" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.example.logisticsmap.LogisticsMapApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LogisticsNotificationApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="logistics-notification" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.example.logisticsnotification.LogisticsNotificationApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LogisticsOrderApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="logistics-order" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.example.logisticsorder.LogisticsOrderApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LogisticsUserApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="logistics-user" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.example.logisticsuser.LogisticsUserApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Application.Main" />
      <item itemvalue="Spring Boot.LogisticsGatewayApplication" />
      <item itemvalue="Spring Boot.LogisticsUserApplication" />
      <item itemvalue="Spring Boot.LogisticsOrderApplication" />
      <item itemvalue="Spring Boot.LogisticsLogisticsApplication" />
      <item itemvalue="Spring Boot.LogisticsDeliveryApplication" />
      <item itemvalue="Spring Boot.LogisticsNotificationApplication" />
      <item itemvalue="Spring Boot.LogisticsMapApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Application.Main" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="47dbaf6b-0adb-4798-8eb2-b9befcf370e6" name="Changes" comment="" />
      <created>1750910843671</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750910843671</updated>
      <workItem from="1750910851069" duration="15766000" />
      <workItem from="1750992164009" duration="2878000" />
      <workItem from="1750995079838" duration="204000" />
      <workItem from="1750995295890" duration="16749000" />
      <workItem from="1751083239590" duration="18885000" />
      <workItem from="1751165794249" duration="7295000" />
      <workItem from="1751179327443" duration="382000" />
      <workItem from="1751179727689" duration="48728000" />
      <workItem from="1751252073170" duration="6866000" />
      <workItem from="1751266882995" duration="6798000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>