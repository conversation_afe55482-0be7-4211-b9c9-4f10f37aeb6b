package org.example.logisticsmap.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.example.logisticsmap.config.AmapConfig;
import org.example.logisticsmap.config.MapConfig;
import org.example.logisticsmap.dto.DirectionRequest;
import org.example.logisticsmap.dto.GeocodingRequest;
import org.example.logisticsmap.dto.ReGeocodingRequest;
import org.example.logisticsmap.entity.Location;
import org.example.logisticsmap.service.MapService;
import org.example.logisticsmap.util.HttpClientUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 地图服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Slf4j
@Service
public class MapServiceImpl implements MapService {

    @Autowired
    private AmapConfig amapConfig;

    @Autowired
    private MapConfig mapConfig;

    @Autowired
    private HttpClientUtil httpClientUtil;

    @Override
    @Cacheable(value = "geocoding",
            key = "#request.address + ':' + (#request.city != null ? #request.city : 'default')",
            condition = "#mapConfig != null && #mapConfig.cache != null && #mapConfig.cache.enabled")
    public List<Location> geocoding(GeocodingRequest request) {
        log.info("开始地理编码: {}", request);

        // 构建请求参数
        Map<String, String> params = new HashMap<>();
        params.put("key", amapConfig.getWebApiKey());
        params.put("address", request.getAddress());
        params.put("output", request.getOutput());
        
        if (StringUtils.hasText(request.getCity())) {
            params.put("city", request.getCity());
        }
        if (StringUtils.hasText(request.getCallback())) {
            params.put("callback", request.getCallback());
        }

        // 构建请求URL
        String url = amapConfig.getBaseUrl() + amapConfig.getGeocoding().getUrl();

        try {
            // 发送HTTP请求
            Map<String, Object> response = httpClientUtil.doGetForJson(url, params);
            
            if (!httpClientUtil.isSuccess(response)) {
                log.error("地理编码失败: {}", httpClientUtil.getErrorMessage(response));
                return new ArrayList<>();
            }

            // 解析响应结果
            List<Location> locations = parseGeocodingResponse(response);
            log.info("地理编码成功，返回{}个结果", locations.size());
            
            return locations;

        } catch (Exception e) {
            log.error("地理编码异常: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    @Cacheable(value = "regeocoding",
            key = "#request.location",
            condition = "#mapConfig != null && #mapConfig.cache != null && #mapConfig.cache.enabled")
    public Location reGeocoding(ReGeocodingRequest request) {
        log.info("开始逆地理编码: {}", request);

        // 构建请求参数
        Map<String, String> params = new HashMap<>();
        params.put("key", amapConfig.getWebApiKey());
        params.put("location", request.getLocation());
        params.put("output", request.getOutput());
        params.put("extensions", request.getExtensions());
        
        if (request.getRadius() != null) {
            params.put("radius", request.getRadius().toString());
        }
        if (request.getRoadlevel() != null) {
            params.put("roadlevel", request.getRoadlevel().toString());
        }

        // 构建请求URL
        String url = amapConfig.getBaseUrl() + amapConfig.getGeocoding().getRegeoUrl();

        try {
            // 发送HTTP请求
            Map<String, Object> response = httpClientUtil.doGetForJson(url, params);
            
            if (!httpClientUtil.isSuccess(response)) {
                log.error("逆地理编码失败: {}", httpClientUtil.getErrorMessage(response));
                return null;
            }

            // 解析响应结果
            Location location = parseReGeocodingResponse(response, request.getLocation());
            log.info("逆地理编码成功: {}", location);
            
            return location;

        } catch (Exception e) {
            log.error("逆地理编码异常: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Map<String, Object> drivingDirection(DirectionRequest request) {
        return getDirection(request, amapConfig.getDirection().getUrl());
    }

    @Override
    public Map<String, Object> walkingDirection(DirectionRequest request) {
        return getDirection(request, amapConfig.getDirection().getWalkingUrl());
    }

    @Override
    public Map<String, Object> calculateDistance(List<String> origins, String destination, Integer type) {
        log.info("开始计算距离: origins={}, destination={}, type={}", origins, destination, type);

        // 构建请求参数
        Map<String, String> params = new HashMap<>();
        params.put("key", amapConfig.getWebApiKey());
        params.put("origins", String.join("|", origins));
        params.put("destination", destination);
        params.put("type", type != null ? type.toString() : "1");
        params.put("output", "json");

        // 构建请求URL
        String url = amapConfig.getBaseUrl() + amapConfig.getDistance().getUrl();

        try {
            // 发送HTTP请求
            Map<String, Object> response = httpClientUtil.doGetForJson(url, params);
            
            if (!httpClientUtil.isSuccess(response)) {
                log.error("距离计算失败: {}", httpClientUtil.getErrorMessage(response));
                return null;
            }

            log.info("距离计算成功");
            return response;

        } catch (Exception e) {
            log.error("距离计算异常: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Map<String, Object> inputTips(String keywords, String city) {
        log.info("开始搜索提示: keywords={}, city={}", keywords, city);

        // 构建请求参数
        Map<String, String> params = new HashMap<>();
        params.put("key", amapConfig.getWebApiKey());
        params.put("keywords", keywords);
        params.put("output", "json");
        
        if (StringUtils.hasText(city)) {
            params.put("city", city);
        }

        // 构建请求URL
        String url = amapConfig.getBaseUrl() + amapConfig.getInputtips().getUrl();

        try {
            return httpClientUtil.doGetForJson(url, params);
        } catch (Exception e) {
            log.error("搜索提示异常: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Map<String, Object> searchPOI(String keywords, String city, String types) {
        log.info("开始POI搜索: keywords={}, city={}, types={}", keywords, city, types);

        // 构建请求参数
        Map<String, String> params = new HashMap<>();
        params.put("key", amapConfig.getWebApiKey());
        params.put("keywords", keywords);
        params.put("output", "json");
        params.put("offset", amapConfig.getDefaultConfig().getOffset().toString());
        params.put("extensions", amapConfig.getDefaultConfig().getExtensions());
        
        if (StringUtils.hasText(city)) {
            params.put("city", city);
        }
        if (StringUtils.hasText(types)) {
            params.put("types", types);
        }

        // 构建请求URL
        String url = amapConfig.getBaseUrl() + amapConfig.getPlace().getTextUrl();

        try {
            return httpClientUtil.doGetForJson(url, params);
        } catch (Exception e) {
            log.error("POI搜索异常: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Map<String, Object> searchAroundPOI(String keywords, String location, Integer radius, String types) {
        log.info("开始周边POI搜索: keywords={}, location={}, radius={}, types={}", 
                keywords, location, radius, types);

        // 构建请求参数
        Map<String, String> params = new HashMap<>();
        params.put("key", amapConfig.getWebApiKey());
        params.put("keywords", keywords);
        params.put("location", location);
        params.put("output", "json");
        params.put("offset", amapConfig.getDefaultConfig().getOffset().toString());
        params.put("extensions", amapConfig.getDefaultConfig().getExtensions());
        
        if (radius != null) {
            params.put("radius", radius.toString());
        } else {
            params.put("radius", amapConfig.getDefaultConfig().getRadius().toString());
        }
        
        if (StringUtils.hasText(types)) {
            params.put("types", types);
        }

        // 构建请求URL
        String url = amapConfig.getBaseUrl() + amapConfig.getPlace().getAroundUrl();

        try {
            return httpClientUtil.doGetForJson(url, params);
        } catch (Exception e) {
            log.error("周边POI搜索异常: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Map<String, Object> getWeather(String city, String extensions) {
        log.info("开始获取天气信息: city={}, extensions={}", city, extensions);

        // 构建请求参数
        Map<String, String> params = new HashMap<>();
        params.put("key", amapConfig.getWebApiKey());
        params.put("city", city);
        params.put("output", "json");
        params.put("extensions", extensions != null ? extensions : "base");

        // 构建请求URL
        String url = amapConfig.getBaseUrl() + amapConfig.getWeather().getUrl();

        try {
            return httpClientUtil.doGetForJson(url, params);
        } catch (Exception e) {
            log.error("获取天气信息异常: {}", e.getMessage(), e);
            return null;
        }
    }

    // ===== 便捷方法实现 =====

    @Override
    public Location addressToLocation(String address) {
        return addressToLocation(address, null);
    }

    @Override
    public Location addressToLocation(String address, String city) {
        if (!StringUtils.hasText(address)) {
            return null;
        }

        List<Location> locations = geocoding(GeocodingRequest.of(address, city));
        return locations.isEmpty() ? null : locations.get(0);
    }

    @Override
    public Location locationToAddress(Double longitude, Double latitude) {
        if (longitude == null || latitude == null) {
            return null;
        }

        return reGeocoding(ReGeocodingRequest.of(longitude, latitude));
    }

    @Override
    public Double calculateStraightDistance(Double fromLng, Double fromLat, Double toLng, Double toLat) {
        if (fromLng == null || fromLat == null || toLng == null || toLat == null) {
            return null;
        }

        // 使用Haversine公式计算球面距离
        double earthRadius = 6371000; // 地球半径，单位：米
        
        double lat1Rad = Math.toRadians(fromLat);
        double lat2Rad = Math.toRadians(toLat);
        double deltaLatRad = Math.toRadians(toLat - fromLat);
        double deltaLngRad = Math.toRadians(toLng - fromLng);

        double a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
                   Math.cos(lat1Rad) * Math.cos(lat2Rad) * 
                   Math.sin(deltaLngRad / 2) * Math.sin(deltaLngRad / 2);
        
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return earthRadius * c;
    }

    @Override
    public Double calculateDrivingDistance(Double fromLng, Double fromLat, Double toLng, Double toLat) {
        if (fromLng == null || fromLat == null || toLng == null || toLat == null) {
            return null;
        }

        List<String> origins = Arrays.asList(fromLng + "," + fromLat);
        String destination = toLng + "," + toLat;
        
        Map<String, Object> result = calculateDistance(origins, destination, 3);
        if (result != null && httpClientUtil.isSuccess(result)) {
            try {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> results = (List<Map<String, Object>>) result.get("results");
                if (results != null && !results.isEmpty()) {
                    Object distance = results.get(0).get("distance");
                    if (distance != null) {
                        return Double.parseDouble(distance.toString());
                    }
                }
            } catch (Exception e) {
                log.error("解析驾车距离异常: {}", e.getMessage(), e);
            }
        }
        
        return null;
    }

    @Override
    public Map<String, Object> getBestDrivingRoute(Double fromLng, Double fromLat, Double toLng, Double toLat) {
        if (fromLng == null || fromLat == null || toLng == null || toLat == null) {
            return null;
        }

        DirectionRequest request = DirectionRequest.driving(fromLng, fromLat, toLng, toLat, 0);
        return drivingDirection(request);
    }

    @Override
    public List<Location> batchAddressToLocation(List<String> addresses) {
        if (addresses == null || addresses.isEmpty()) {
            return new ArrayList<>();
        }

        return addresses.stream()
                .map(this::addressToLocation)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public List<Location> batchLocationToAddress(List<String> locations) {
        if (locations == null || locations.isEmpty()) {
            return new ArrayList<>();
        }

        return locations.stream()
                .map(location -> {
                    String[] coords = location.split(",");
                    if (coords.length == 2) {
                        try {
                            Double lng = Double.parseDouble(coords[0]);
                            Double lat = Double.parseDouble(coords[1]);
                            return locationToAddress(lng, lat);
                        } catch (NumberFormatException e) {
                            log.warn("坐标格式错误: {}", location);
                        }
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    // ===== 私有辅助方法 =====

    /**
     * 通用路径规划方法
     */
    private Map<String, Object> getDirection(DirectionRequest request, String apiPath) {
        log.info("开始路径规划: {}", request);

        // 构建请求参数
        Map<String, String> params = buildDirectionParams(request);

        // 构建请求URL
        String url = amapConfig.getBaseUrl() + apiPath;

        try {
            // 发送HTTP请求
            Map<String, Object> response = httpClientUtil.doGetForJson(url, params);
            
            if (!httpClientUtil.isSuccess(response)) {
                log.error("路径规划失败: {}", httpClientUtil.getErrorMessage(response));
                return null;
            }

            log.info("路径规划成功");
            return response;

        } catch (Exception e) {
            log.error("路径规划异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 构建路径规划请求参数
     */
    private Map<String, String> buildDirectionParams(DirectionRequest request) {
        Map<String, String> params = new HashMap<>();
        params.put("key", amapConfig.getWebApiKey());
        params.put("origin", request.getOrigin());
        params.put("destination", request.getDestination());
        params.put("output", request.getOutput());
        params.put("extensions", request.getExtensions());
        
        if (request.getStrategy() != null) {
            params.put("strategy", request.getStrategy().toString());
        }
        if (StringUtils.hasText(request.getWaypoints())) {
            params.put("waypoints", request.getWaypoints());
        }
        if (StringUtils.hasText(request.getAvoidpolygons())) {
            params.put("avoidpolygons", request.getAvoidpolygons());
        }
        if (StringUtils.hasText(request.getAvoidroad())) {
            params.put("avoidroad", request.getAvoidroad());
        }
        if (StringUtils.hasText(request.getProvince())) {
            params.put("province", request.getProvince());
        }
        if (StringUtils.hasText(request.getNumber())) {
            params.put("number", request.getNumber());
        }
        if (request.getSize() != null) {
            params.put("size", request.getSize().toString());
        }
        
        return params;
    }

    /**
     * 解析地理编码响应
     */
    private List<Location> parseGeocodingResponse(Map<String, Object> response) {
        List<Location> locations = new ArrayList<>();
        
        try {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> geocodes = (List<Map<String, Object>>) response.get("geocodes");
            
            if (geocodes != null) {
                for (Map<String, Object> geocode : geocodes) {
                    Location location = parseLocationFromGeocode(geocode);
                    if (location != null) {
                        locations.add(location);
                    }
                }
            }
        } catch (Exception e) {
            log.error("解析地理编码响应异常: {}", e.getMessage(), e);
        }
        
        return locations;
    }

    /**
     * 解析逆地理编码响应
     */
    private Location parseReGeocodingResponse(Map<String, Object> response, String originalLocation) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> regeocode = (Map<String, Object>) response.get("regeocode");
            
            if (regeocode != null) {
                Location location = parseLocationFromRegeocode(regeocode);
                
                // 设置原始坐标
                if (location != null && StringUtils.hasText(originalLocation)) {
                    String[] coords = originalLocation.split(",");
                    if (coords.length == 2) {
                        try {
                            location.setLongitude(Double.parseDouble(coords[0]));
                            location.setLatitude(Double.parseDouble(coords[1]));
                        } catch (NumberFormatException e) {
                            log.warn("坐标格式错误: {}", originalLocation);
                        }
                    }
                }
                
                return location;
            }
        } catch (Exception e) {
            log.error("解析逆地理编码响应异常: {}", e.getMessage(), e);
        }
        
        return null;
    }

    /**
     * 从地理编码结果中解析位置信息
     */
    private Location parseLocationFromGeocode(Map<String, Object> geocode) {
        try {
            Location location = new Location();
            
            // 解析坐标
            String locationStr = (String) geocode.get("location");
            if (StringUtils.hasText(locationStr)) {
                String[] coords = locationStr.split(",");
                if (coords.length == 2) {
                    location.setLongitude(Double.parseDouble(coords[0]));
                    location.setLatitude(Double.parseDouble(coords[1]));
                }
            }
            
            // 解析地址信息
            location.setAddress((String) geocode.get("formatted_address"));
            location.setProvince((String) geocode.get("province"));
            location.setCity((String) geocode.get("city"));
            location.setDistrict((String) geocode.get("district"));
            location.setAdcode((String) geocode.get("adcode"));
            
            return location;
        } catch (Exception e) {
            log.error("解析位置信息异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从逆地理编码结果中解析位置信息
     */
    private Location parseLocationFromRegeocode(Map<String, Object> regeocode) {
        try {
            Location location = new Location();
            
            // 解析格式化地址
            String formattedAddress = (String) regeocode.get("formatted_address");
            location.setAddress(formattedAddress);
            
            // 解析详细地址组件
            @SuppressWarnings("unchecked")
            Map<String, Object> addressComponent = (Map<String, Object>) regeocode.get("addressComponent");
            if (addressComponent != null) {
                location.setCountry((String) addressComponent.get("country"));
                location.setProvince((String) addressComponent.get("province"));
                location.setCity((String) addressComponent.get("city"));
                location.setDistrict((String) addressComponent.get("district"));
                location.setStreet((String) addressComponent.get("streetNumber"));
                location.setAdcode((String) addressComponent.get("adcode"));
            }
            
            return location;
        } catch (Exception e) {
            log.error("解析逆地理编码位置信息异常: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Map<String, Object> getDistrictData(String keywords, String subdistrict) {
        try {
            Map<String, String> params = new HashMap<>();
            params.put("key", amapConfig.getWebApiKey());
            params.put("subdistrict", subdistrict);
            params.put("extensions", "base");

            if (StringUtils.hasText(keywords)) {
                params.put("keywords", keywords);
            }
            log.info("调用高德API参数: {}", params);

            String response = httpClientUtil.doGet(amapConfig.getBaseUrl() + "/v3/config/district", params);


            if (StringUtils.hasText(response)) {
                Map<String, Object> result = JSON.parseObject(response, Map.class);
                // 检查响应状态
                if ("1".equals(result.get("status"))) {
                    return result;
                } else {
                    log.error("高德API返回错误: {}", result.get("info"));
                    throw new RuntimeException("高德API返回错误: " + result.get("info"));
                }
            }

            throw new RuntimeException("高德API响应为空");

        } catch (Exception e) {
            log.error("获取行政区划数据失败", e);
            throw new RuntimeException("获取行政区划数据失败: " + e.getMessage());
        }
    }

    @Override
    @Cacheable(value = "amap:provinces", unless = "#result == null")
    public List<Map<String, Object>> getAllProvinces() {
        try {
            // 获取全国省份数据
            Map<String, Object> response = getDistrictData("中国", "1");

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> districts = (List<Map<String, Object>>) response.get("districts");

            if (districts != null && !districts.isEmpty()) {
                // 返回第一个元素（中国）的下级行政区（省份）
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> provinces = (List<Map<String, Object>>) districts.get(0).get("districts");
                return provinces != null ? provinces : new ArrayList<>();
            }

            return new ArrayList<>();

        } catch (Exception e) {
            log.error("获取省份列表失败", e);
            return new ArrayList<>();
        }
    }
} 