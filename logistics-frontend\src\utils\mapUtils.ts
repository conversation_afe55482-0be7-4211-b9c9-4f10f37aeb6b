/**
 * 地图工具类
 * 提供坐标转换、距离计算、地理围栏等功能
 */

// 坐标类型
export interface Coordinate {
  lat: number
  lng: number
}

// 地理围栏类型
export interface GeofenceOptions {
  center: Coordinate
  radius: number // 米
}

// 多边形围栏
export interface PolygonGeofence {
  coordinates: Coordinate[]
}

/**
 * 计算两点之间的距离（使用Haversine公式）
 * @param point1 起点坐标
 * @param point2 终点坐标
 * @returns 距离（米）
 */
export function calculateDistance(point1: Coordinate, point2: Coordinate): number {
  const R = 6371000 // 地球半径（米）
  const lat1Rad = (point1.lat * Math.PI) / 180
  const lat2Rad = (point2.lat * Math.PI) / 180
  const deltaLatRad = ((point2.lat - point1.lat) * Math.PI) / 180
  const deltaLngRad = ((point2.lng - point1.lng) * Math.PI) / 180

  const a = 
    Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
    Math.cos(lat1Rad) * Math.cos(lat2Rad) *
    Math.sin(deltaLngRad / 2) * Math.sin(deltaLngRad / 2)
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  
  return R * c
}

/**
 * 计算方位角
 * @param from 起点
 * @param to 终点
 * @returns 方位角（度）
 */
export function calculateBearing(from: Coordinate, to: Coordinate): number {
  const lat1Rad = (from.lat * Math.PI) / 180
  const lat2Rad = (to.lat * Math.PI) / 180
  const deltaLngRad = ((to.lng - from.lng) * Math.PI) / 180

  const y = Math.sin(deltaLngRad) * Math.cos(lat2Rad)
  const x = 
    Math.cos(lat1Rad) * Math.sin(lat2Rad) -
    Math.sin(lat1Rad) * Math.cos(lat2Rad) * Math.cos(deltaLngRad)

  const bearing = Math.atan2(y, x)
  return ((bearing * 180) / Math.PI + 360) % 360
}

/**
 * 计算多个点的中心点
 * @param points 坐标点数组
 * @returns 中心点坐标
 */
export function calculateCenter(points: Coordinate[]): Coordinate {
  if (points.length === 0) {
    throw new Error('Points array cannot be empty')
  }

  if (points.length === 1) {
    return points[0]
  }

  let totalLat = 0
  let totalLng = 0
  
  points.forEach(point => {
    totalLat += point.lat
    totalLng += point.lng
  })

  return {
    lat: totalLat / points.length,
    lng: totalLng / points.length
  }
}

/**
 * 计算包含所有点的边界框
 * @param points 坐标点数组
 * @returns 边界框
 */
export function calculateBounds(points: Coordinate[]): {
  northeast: Coordinate
  southwest: Coordinate
} {
  if (points.length === 0) {
    throw new Error('Points array cannot be empty')
  }

  let minLat = points[0].lat
  let maxLat = points[0].lat
  let minLng = points[0].lng
  let maxLng = points[0].lng

  points.forEach(point => {
    minLat = Math.min(minLat, point.lat)
    maxLat = Math.max(maxLat, point.lat)
    minLng = Math.min(minLng, point.lng)
    maxLng = Math.max(maxLng, point.lng)
  })

  return {
    northeast: { lat: maxLat, lng: maxLng },
    southwest: { lat: minLat, lng: minLng }
  }
}

/**
 * 检查点是否在圆形地理围栏内
 * @param point 检查的点
 * @param geofence 圆形围栏
 * @returns 是否在围栏内
 */
export function isPointInCircleGeofence(
  point: Coordinate, 
  geofence: GeofenceOptions
): boolean {
  const distance = calculateDistance(point, geofence.center)
  return distance <= geofence.radius
}

/**
 * 检查点是否在多边形围栏内（射线法）
 * @param point 检查的点
 * @param polygon 多边形围栏
 * @returns 是否在围栏内
 */
export function isPointInPolygonGeofence(
  point: Coordinate,
  polygon: PolygonGeofence
): boolean {
  const { coordinates } = polygon
  let inside = false
  
  for (let i = 0, j = coordinates.length - 1; i < coordinates.length; j = i++) {
    const xi = coordinates[i].lng
    const yi = coordinates[i].lat
    const xj = coordinates[j].lng
    const yj = coordinates[j].lat
    
    if (((yi > point.lat) !== (yj > point.lat)) &&
        (point.lng < (xj - xi) * (point.lat - yi) / (yj - yi) + xi)) {
      inside = !inside
    }
  }
  
  return inside
}

/**
 * 根据起点、距离和方位角计算终点
 * @param start 起点
 * @param distance 距离（米）
 * @param bearing 方位角（度）
 * @returns 终点坐标
 */
export function calculateDestination(
  start: Coordinate,
  distance: number,
  bearing: number
): Coordinate {
  const R = 6371000 // 地球半径（米）
  const bearingRad = (bearing * Math.PI) / 180
  const lat1Rad = (start.lat * Math.PI) / 180
  const lng1Rad = (start.lng * Math.PI) / 180

  const lat2Rad = Math.asin(
    Math.sin(lat1Rad) * Math.cos(distance / R) +
    Math.cos(lat1Rad) * Math.sin(distance / R) * Math.cos(bearingRad)
  )

  const lng2Rad = lng1Rad + Math.atan2(
    Math.sin(bearingRad) * Math.sin(distance / R) * Math.cos(lat1Rad),
    Math.cos(distance / R) - Math.sin(lat1Rad) * Math.sin(lat2Rad)
  )

  return {
    lat: (lat2Rad * 180) / Math.PI,
    lng: (lng2Rad * 180) / Math.PI
  }
}

/**
 * 坐标系转换：WGS84 -> GCJ02 (火星坐标系)
 * @param wgs84 WGS84坐标
 * @returns GCJ02坐标
 */
export function wgs84ToGcj02(wgs84: Coordinate): Coordinate {
  const a = 6378245.0
  const ee = 0.00669342162296594323
  
  let dLat = transformLat(wgs84.lng - 105.0, wgs84.lat - 35.0)
  let dLng = transformLng(wgs84.lng - 105.0, wgs84.lat - 35.0)
  
  const radLat = (wgs84.lat / 180.0) * Math.PI
  let magic = Math.sin(radLat)
  magic = 1 - ee * magic * magic
  const sqrtMagic = Math.sqrt(magic)
  
  dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * Math.PI)
  dLng = (dLng * 180.0) / (a / sqrtMagic * Math.cos(radLat) * Math.PI)
  
  return {
    lat: wgs84.lat + dLat,
    lng: wgs84.lng + dLng
  }
}

/**
 * 坐标系转换：GCJ02 -> BD09 (百度坐标系)
 * @param gcj02 GCJ02坐标
 * @returns BD09坐标
 */
export function gcj02ToBd09(gcj02: Coordinate): Coordinate {
  const z = Math.sqrt(gcj02.lng * gcj02.lng + gcj02.lat * gcj02.lat) + 0.00002 * Math.sin(gcj02.lat * Math.PI * 3000.0 / 180.0)
  const theta = Math.atan2(gcj02.lat, gcj02.lng) + 0.000003 * Math.cos(gcj02.lng * Math.PI * 3000.0 / 180.0)
  
  return {
    lng: z * Math.cos(theta) + 0.0065,
    lat: z * Math.sin(theta) + 0.006
  }
}

/**
 * 坐标系转换：BD09 -> GCJ02
 * @param bd09 BD09坐标
 * @returns GCJ02坐标
 */
export function bd09ToGcj02(bd09: Coordinate): Coordinate {
  const x = bd09.lng - 0.0065
  const y = bd09.lat - 0.006
  const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * Math.PI * 3000.0 / 180.0)
  const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * Math.PI * 3000.0 / 180.0)
  
  return {
    lng: z * Math.cos(theta),
    lat: z * Math.sin(theta)
  }
}

/**
 * 坐标系转换：GCJ02 -> WGS84
 * @param gcj02 GCJ02坐标
 * @returns WGS84坐标
 */
export function gcj02ToWgs84(gcj02: Coordinate): Coordinate {
  const a = 6378245.0
  const ee = 0.00669342162296594323
  
  let dLat = transformLat(gcj02.lng - 105.0, gcj02.lat - 35.0)
  let dLng = transformLng(gcj02.lng - 105.0, gcj02.lat - 35.0)
  
  const radLat = (gcj02.lat / 180.0) * Math.PI
  let magic = Math.sin(radLat)
  magic = 1 - ee * magic * magic
  const sqrtMagic = Math.sqrt(magic)
  
  dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * Math.PI)
  dLng = (dLng * 180.0) / (a / sqrtMagic * Math.cos(radLat) * Math.PI)
  
  return {
    lat: gcj02.lat - dLat,
    lng: gcj02.lng - dLng
  }
}

// 辅助函数
function transformLat(lng: number, lat: number): number {
  let ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng))
  ret += (20.0 * Math.sin(6.0 * lng * Math.PI) + 20.0 * Math.sin(2.0 * lng * Math.PI)) * 2.0 / 3.0
  ret += (20.0 * Math.sin(lat * Math.PI) + 40.0 * Math.sin(lat / 3.0 * Math.PI)) * 2.0 / 3.0
  ret += (160.0 * Math.sin(lat / 12.0 * Math.PI) + 320 * Math.sin(lat * Math.PI / 30.0)) * 2.0 / 3.0
  return ret
}

function transformLng(lng: number, lat: number): number {
  let ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng))
  ret += (20.0 * Math.sin(6.0 * lng * Math.PI) + 20.0 * Math.sin(2.0 * lng * Math.PI)) * 2.0 / 3.0
  ret += (20.0 * Math.sin(lng * Math.PI) + 40.0 * Math.sin(lng / 3.0 * Math.PI)) * 2.0 / 3.0
  ret += (150.0 * Math.sin(lng / 12.0 * Math.PI) + 300.0 * Math.sin(lng / 30.0 * Math.PI)) * 2.0 / 3.0
  return ret
}

/**
 * 格式化距离显示
 * @param distance 距离（米）
 * @returns 格式化的距离字符串
 */
export function formatDistance(distance: number): string {
  if (distance < 1000) {
    return `${Math.round(distance)}m`
  } else if (distance < 10000) {
    return `${(distance / 1000).toFixed(1)}km`
  } else {
    return `${Math.round(distance / 1000)}km`
  }
}

/**
 * 格式化时间显示
 * @param seconds 秒数
 * @returns 格式化的时间字符串
 */
export function formatDuration(seconds: number): string {
  if (seconds < 60) {
    return `${Math.round(seconds)}秒`
  } else if (seconds < 3600) {
    const minutes = Math.round(seconds / 60)
    return `${minutes}分钟`
  } else {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.round((seconds % 3600) / 60)
    if (minutes === 0) {
      return `${hours}小时`
    }
    return `${hours}小时${minutes}分钟`
  }
}

/**
 * 解析polyline编码字符串
 * @param encoded 编码的polyline字符串
 * @returns 坐标点数组
 */
export function decodePolyline(encoded: string): Coordinate[] {
  const coordinates: Coordinate[] = []
  let index = 0
  let lat = 0
  let lng = 0

  while (index < encoded.length) {
    let shift = 0
    let result = 0
    let byte: number

    do {
      byte = encoded.charCodeAt(index++) - 63
      result |= (byte & 0x1f) << shift
      shift += 5
    } while (byte >= 0x20)

    const deltaLat = ((result & 1) !== 0 ? ~(result >> 1) : (result >> 1))
    lat += deltaLat

    shift = 0
    result = 0

    do {
      byte = encoded.charCodeAt(index++) - 63
      result |= (byte & 0x1f) << shift
      shift += 5
    } while (byte >= 0x20)

    const deltaLng = ((result & 1) !== 0 ? ~(result >> 1) : (result >> 1))
    lng += deltaLng

    coordinates.push({
      lat: lat / 1e5,
      lng: lng / 1e5
    })
  }

  return coordinates
}

/**
 * 编码坐标数组为polyline字符串
 * @param coordinates 坐标点数组
 * @returns 编码的polyline字符串
 */
export function encodePolyline(coordinates: Coordinate[]): string {
  let encoded = ''
  let prevLat = 0
  let prevLng = 0

  coordinates.forEach(coord => {
    const lat = Math.round(coord.lat * 1e5)
    const lng = Math.round(coord.lng * 1e5)

    const deltaLat = lat - prevLat
    const deltaLng = lng - prevLng

    encoded += encodeSignedNumber(deltaLat)
    encoded += encodeSignedNumber(deltaLng)

    prevLat = lat
    prevLng = lng
  })

  return encoded
}

function encodeSignedNumber(num: number): string {
  let sgn_num = num << 1
  if (num < 0) {
    sgn_num = ~sgn_num
  }
  return encodeNumber(sgn_num)
}

function encodeNumber(num: number): string {
  let encoded = ''
  while (num >= 0x20) {
    encoded += String.fromCharCode((0x20 | (num & 0x1f)) + 63)
    num >>= 5
  }
  encoded += String.fromCharCode(num + 63)
  return encoded
}

/**
 * 简化路径（Douglas-Peucker算法）
 * @param coordinates 原始坐标数组
 * @param tolerance 容差（米）
 * @returns 简化后的坐标数组
 */
export function simplifyPath(coordinates: Coordinate[], tolerance: number): Coordinate[] {
  if (coordinates.length <= 2) {
    return coordinates
  }

  const simplified = douglasPeucker(coordinates, tolerance)
  return simplified
}

function douglasPeucker(coordinates: Coordinate[], tolerance: number): Coordinate[] {
  if (coordinates.length <= 2) {
    return coordinates
  }

  let maxDistance = 0
  let maxIndex = 0
  const start = coordinates[0]
  const end = coordinates[coordinates.length - 1]

  for (let i = 1; i < coordinates.length - 1; i++) {
    const distance = pointToLineDistance(coordinates[i], start, end)
    if (distance > maxDistance) {
      maxDistance = distance
      maxIndex = i
    }
  }

  if (maxDistance > tolerance) {
    const left = douglasPeucker(coordinates.slice(0, maxIndex + 1), tolerance)
    const right = douglasPeucker(coordinates.slice(maxIndex), tolerance)
    return left.slice(0, -1).concat(right)
  } else {
    return [start, end]
  }
}

function pointToLineDistance(point: Coordinate, lineStart: Coordinate, lineEnd: Coordinate): number {
  const A = point.lat - lineStart.lat
  const B = point.lng - lineStart.lng
  const C = lineEnd.lat - lineStart.lat
  const D = lineEnd.lng - lineStart.lng

  const dot = A * C + B * D
  const lenSq = C * C + D * D
  
  if (lenSq === 0) {
    return calculateDistance(point, lineStart)
  }

  const param = dot / lenSq
  let closestPoint: Coordinate

  if (param < 0) {
    closestPoint = lineStart
  } else if (param > 1) {
    closestPoint = lineEnd
  } else {
    closestPoint = {
      lat: lineStart.lat + param * C,
      lng: lineStart.lng + param * D
    }
  }

  return calculateDistance(point, closestPoint)
}

/**
 * 地图工具类的默认导出
 */
export default {
  calculateDistance,
  calculateBearing,
  calculateCenter,
  calculateBounds,
  calculateDestination,
  isPointInCircleGeofence,
  isPointInPolygonGeofence,
  wgs84ToGcj02,
  gcj02ToBd09,
  bd09ToGcj02,
  gcj02ToWgs84,
  formatDistance,
  formatDuration,
  decodePolyline,
  encodePolyline,
  simplifyPath
} 