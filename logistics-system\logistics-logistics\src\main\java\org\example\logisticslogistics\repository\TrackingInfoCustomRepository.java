package org.example.logisticslogistics.repository;

import org.example.logisticslogistics.entity.TrackingInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 物流轨迹信息自定义数据访问层
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
public interface TrackingInfoCustomRepository {

    /**
     * 根据经纬度范围查询轨迹信息
     */
    List<TrackingInfo> findByLocationRange(double minLng, double maxLng, double minLat, double maxLat);

    /**
     * 查询指定坐标点附近的轨迹信息
     */
    List<TrackingInfo> findNearby(double longitude, double latitude, double radiusKm);

    /**
     * 查询配送员的配送路径
     */
    List<TrackingInfo> findDeliveryRoute(Long courierId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计各状态的订单数量
     */
    Map<String, Long> countByStatus();

    /**
     * 统计各城市的订单数量
     */
    Map<String, Long> countByCity();

    /**
     * 统计配送员的绩效数据
     */
    Map<String, Object> getCourierPerformance(Long courierId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取实时监控数据
     */
    Map<String, Object> getRealTimeMonitorData();

    /**
     * 复杂条件分页查询
     */
    Page<TrackingInfo> findByComplexConditions(
            String status,
            String city,
            Long courierId,
            Boolean isException,
            LocalDateTime startTime,
            LocalDateTime endTime,
            Pageable pageable
    );

    /**
     * 获取热力图数据
     */
    List<Map<String, Object>> getHeatMapData(String city, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 批量更新位置信息
     */
    void batchUpdateLocation(List<TrackingInfo> trackingInfos);

    /**
     * 删除过期数据
     */
    long deleteExpiredData(LocalDateTime expireTime);

    /**
     * 查询配送中的订单（用于实时监控）
     */
    List<TrackingInfo> findDeliveryInProgress();

    /**
     * 查询指定配送员在指定日期的配送任务
     */
    List<TrackingInfo> findByCourierAndDate(Long courierId, LocalDateTime startOfDay, LocalDateTime endOfDay);
} 