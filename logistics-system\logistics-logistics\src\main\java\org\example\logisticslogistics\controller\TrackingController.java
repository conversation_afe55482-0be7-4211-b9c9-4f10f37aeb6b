package org.example.logisticslogistics.controller;

import com.logistics.common.result.Result;
import org.example.logisticslogistics.entity.LocationInfo;
import org.example.logisticslogistics.entity.TrackingInfo;
import org.example.logisticslogistics.entity.TrackingNode;
import org.example.logisticslogistics.service.TrackingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;

/**
 * 物流轨迹管理控制器
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@RestController
@RequestMapping("/logistics/tracking")
public class TrackingController {

    @Autowired
    private TrackingService trackingService;

    /**
     * 创建轨迹信息
     */
    @PostMapping("/create")
    public Result<TrackingInfo> createTracking(@RequestParam String orderNumber, 
                                             @RequestParam Long orderId) {
        try {
            TrackingInfo trackingInfo = trackingService.createTracking(orderNumber, orderId);
            return Result.success(trackingInfo);
        } catch (Exception e) {
            return Result.error("创建轨迹信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据订单号查询轨迹信息
     */
    @GetMapping("/order/{orderNumber}")
    public Result<TrackingInfo> getTrackingByOrderNumber(@PathVariable String orderNumber) {
        try {
            TrackingInfo trackingInfo = trackingService.getTrackingByOrderNumber(orderNumber);
            return Result.success(trackingInfo);
        } catch (Exception e) {
            return Result.error("查询轨迹信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据订单ID查询轨迹信息
     */
    @GetMapping("/order-id/{orderId}")
    public Result<TrackingInfo> getTrackingByOrderId(@PathVariable Long orderId) {
        try {
            TrackingInfo trackingInfo = trackingService.getTrackingByOrderId(orderId);
            return Result.success(trackingInfo);
        } catch (Exception e) {
            return Result.error("查询轨迹信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取轨迹详情
     */
    @GetMapping("/{trackingId}/details")
    public Result<TrackingInfo> getTrackingDetails(@PathVariable String trackingId) {
        try {
            TrackingInfo trackingInfo = trackingService.getTrackingDetails(trackingId);
            return Result.success(trackingInfo);
        } catch (Exception e) {
            return Result.error("查询轨迹详情失败: " + e.getMessage());
        }
    }

    /**
     * 更新轨迹状态
     */
    @PutMapping("/{trackingId}/status")
    public Result<TrackingInfo> updateTrackingStatus(
            @PathVariable String trackingId,
            @RequestParam String status,
            @RequestParam(required = false) String description,
            @RequestBody(required = false) LocationInfo location,
            @RequestParam(required = false) Long operatorId,
            @RequestParam(required = false) String operatorName) {
        try {
            TrackingInfo trackingInfo = trackingService.updateTrackingStatus(
                    trackingId, status, description, location, operatorId, operatorName);
            return Result.success(trackingInfo);
        } catch (Exception e) {
            return Result.error("更新轨迹状态失败: " + e.getMessage());
        }
    }

    /**
     * 添加轨迹节点
     */
    @PostMapping("/{trackingId}/nodes")
    public Result<TrackingInfo> addTrackingNode(@PathVariable String trackingId,
                                               @RequestBody TrackingNode node) {
        try {
            TrackingInfo trackingInfo = trackingService.addTrackingNode(trackingId, node);
            return Result.success(trackingInfo);
        } catch (Exception e) {
            return Result.error("添加轨迹节点失败: " + e.getMessage());
        }
    }

    /**
     * 批量添加轨迹节点
     */
    @PostMapping("/{trackingId}/nodes/batch")
    public Result<TrackingInfo> addTrackingNodes(@PathVariable String trackingId,
                                                @RequestBody List<TrackingNode> nodes) {
        try {
            TrackingInfo trackingInfo = trackingService.addTrackingNodes(trackingId, nodes);
            return Result.success(trackingInfo);
        } catch (Exception e) {
            return Result.error("批量添加轨迹节点失败: " + e.getMessage());
        }
    }

    /**
     * 更新当前位置
     */
    @PutMapping("/{trackingId}/location")
    public Result<TrackingInfo> updateCurrentLocation(@PathVariable String trackingId,
                                                     @RequestBody LocationInfo location) {
        try {
            TrackingInfo trackingInfo = trackingService.updateCurrentLocation(trackingId, location);
            return Result.success(trackingInfo);
        } catch (Exception e) {
            return Result.error("更新位置信息失败: " + e.getMessage());
        }
    }

    /**
     * 分配配送员
     */
    @PutMapping("/{trackingId}/courier")
    public Result<TrackingInfo> assignCourier(@PathVariable String trackingId,
                                             @RequestParam Long courierId,
                                             @RequestParam String courierName,
                                             @RequestParam String courierPhone) {
        try {
            TrackingInfo trackingInfo = trackingService.assignCourier(
                    trackingId, courierId, courierName, courierPhone);
            return Result.success(trackingInfo);
        } catch (Exception e) {
            return Result.error("分配配送员失败: " + e.getMessage());
        }
    }

    /**
     * 更新预计到达时间
     */
    @PutMapping("/{trackingId}/estimated-time")
    public Result<TrackingInfo> updateEstimatedDeliveryTime(
            @PathVariable String trackingId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime estimatedTime) {
        try {
            TrackingInfo trackingInfo = trackingService.updateEstimatedDeliveryTime(trackingId, estimatedTime);
            return Result.success(trackingInfo);
        } catch (Exception e) {
            return Result.error("更新预计到达时间失败: " + e.getMessage());
        }
    }

    /**
     * 标记异常
     */
    @PutMapping("/{trackingId}/exception")
    public Result<TrackingInfo> markAsException(@PathVariable String trackingId,
                                               @RequestParam String exceptionDescription) {
        try {
            TrackingInfo trackingInfo = trackingService.markAsException(trackingId, exceptionDescription);
            return Result.success(trackingInfo);
        } catch (Exception e) {
            return Result.error("标记异常失败: " + e.getMessage());
        }
    }

    /**
     * 清除异常状态
     */
    @DeleteMapping("/{trackingId}/exception")
    public Result<TrackingInfo> clearException(@PathVariable String trackingId) {
        try {
            TrackingInfo trackingInfo = trackingService.clearException(trackingId);
            return Result.success(trackingInfo);
        } catch (Exception e) {
            return Result.error("清除异常状态失败: " + e.getMessage());
        }
    }

    /**
     * 完成配送
     */
    @PutMapping("/{trackingId}/complete")
    public Result<TrackingInfo> completeDelivery(@PathVariable String trackingId,
                                                @RequestParam(required = false) String completionNotes) {
        try {
            TrackingInfo trackingInfo = trackingService.completeDelivery(trackingId, completionNotes);
            return Result.success(trackingInfo);
        } catch (Exception e) {
            return Result.error("完成配送失败: " + e.getMessage());
        }
    }

    /**
     * 根据状态查询轨迹信息
     */
    @GetMapping("/status/{status}")
    public Result<List<TrackingInfo>> getTrackingsByStatus(@PathVariable String status) {
        try {
            List<TrackingInfo> trackings = trackingService.getTrackingsByStatus(status);
            return Result.success(trackings);
        } catch (Exception e) {
            return Result.error("查询轨迹信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据配送员查询轨迹信息
     */
    @GetMapping("/courier/{courierId}")
    public Result<Page<TrackingInfo>> getTrackingsByCourier(
            @PathVariable Long courierId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<TrackingInfo> trackings = trackingService.getTrackingsByCourier(courierId, pageable);
            return Result.success(trackings);
        } catch (Exception e) {
            return Result.error("查询配送员轨迹信息失败: " + e.getMessage());
        }
    }

    /**
     * 查询异常轨迹信息
     */
    @GetMapping("/exceptions")
    public Result<Page<TrackingInfo>> getExceptionTrackings(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<TrackingInfo> trackings = trackingService.getExceptionTrackings(pageable);
            return Result.success(trackings);
        } catch (Exception e) {
            return Result.error("查询异常轨迹信息失败: " + e.getMessage());
        }
    }

    /**
     * 查询配送中的轨迹信息
     */
    @GetMapping("/delivery-in-progress")
    public Result<List<TrackingInfo>> getDeliveryInProgress() {
        try {
            List<TrackingInfo> trackings = trackingService.getDeliveryInProgress();
            return Result.success(trackings);
        } catch (Exception e) {
            return Result.error("查询配送中轨迹信息失败: " + e.getMessage());
        }
    }

    /**
     * 复杂条件查询
     */
    @GetMapping("/search")
    public Result<Page<TrackingInfo>> searchTrackings(
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String city,
            @RequestParam(required = false) Long courierId,
            @RequestParam(required = false) Boolean isException,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<TrackingInfo> trackings = trackingService.searchTrackings(
                    status, city, courierId, isException, startTime, endTime, pageable);
            return Result.success(trackings);
        } catch (Exception e) {
            return Result.error("搜索轨迹信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取配送员当日任务
     */
    @GetMapping("/courier/{courierId}/daily-tasks")
    public Result<List<TrackingInfo>> getCourierDailyTasks(
            @PathVariable Long courierId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDateTime date) {
        try {
            LocalDateTime queryDate = date != null ? date : LocalDateTime.now();
            List<TrackingInfo> tasks = trackingService.getCourierDailyTasks(courierId, queryDate);
            return Result.success(tasks);
        } catch (Exception e) {
            return Result.error("查询配送员当日任务失败: " + e.getMessage());
        }
    }

    /**
     * 计算总里程
     */
    @GetMapping("/{trackingId}/distance")
    public Result<Double> calculateTotalDistance(@PathVariable String trackingId) {
        try {
            Double distance = trackingService.calculateTotalDistance(trackingId);
            return Result.success(distance);
        } catch (Exception e) {
            return Result.error("计算总里程失败: " + e.getMessage());
        }
    }

    /**
     * 获取轨迹时间线
     */
    @GetMapping("/{trackingId}/timeline")
    public Result<List<TrackingNode>> getTrackingTimeline(@PathVariable String trackingId) {
        try {
            List<TrackingNode> timeline = trackingService.getTrackingTimeline(trackingId);
            return Result.success(timeline);
        } catch (Exception e) {
            return Result.error("获取轨迹时间线失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新位置
     */
    @PutMapping("/locations/batch")
    public Result<String> batchUpdateLocations(@RequestBody List<TrackingInfo> trackingInfos) {
        try {
            trackingService.batchUpdateLocations(trackingInfos);
            return Result.success("批量更新位置成功");
        } catch (Exception e) {
            return Result.error("批量更新位置失败: " + e.getMessage());
        }
    }

    /**
     * 清理过期数据
     */
    @DeleteMapping("/cleanup")
    public Result<Long> cleanExpiredData(@RequestParam(defaultValue = "30") int expireDays) {
        try {
            long deletedCount = trackingService.cleanExpiredData(expireDays);
            return Result.success(deletedCount);
        } catch (Exception e) {
            return Result.error("清理过期数据失败: " + e.getMessage());
        }
    }

    /**
     * 检查轨迹是否存在
     */
    @GetMapping("/exists/{orderNumber}")
    public Result<Boolean> trackingExists(@PathVariable String orderNumber) {
        try {
            boolean exists = trackingService.trackingExists(orderNumber);
            return Result.success(exists);
        } catch (Exception e) {
            return Result.error("检查轨迹是否存在失败: " + e.getMessage());
        }
    }

    /**
     * 公开查询接口 - 根据订单号查询物流轨迹（前端专用）
     */
    @PostMapping("/query")
    @CrossOrigin(origins = "*")
    public Result<TrackingResponse> queryTracking(@RequestBody TrackingQueryRequest request) {
        try {
            TrackingInfo trackingInfo = trackingService.getTrackingByOrderNumber(request.getOrderNumber());
            
            // 转换为前端需要的格式
            TrackingResponse response = convertToTrackingResponse(trackingInfo);
            return Result.success(response);
        } catch (Exception e) {
            return Result.error("查询物流信息失败: " + e.getMessage());
        }
    }

    /**
     * 公开查询接口 - 根据订单号直接查询（前端专用）
     */
    @GetMapping("/{orderNumber}")
    @CrossOrigin(origins = "*")
    public Result<TrackingResponse> getTrackingForFrontend(@PathVariable String orderNumber) {
        try {
            TrackingInfo trackingInfo = trackingService.getTrackingByOrderNumber(orderNumber);
            
            // 转换为前端需要的格式
            TrackingResponse response = convertToTrackingResponse(trackingInfo);
            return Result.success(response);
        } catch (Exception e) {
            return Result.error("查询物流信息失败: " + e.getMessage());
        }
    }

    /**
     * 订阅物流通知
     */
    @PostMapping("/subscribe")
    @CrossOrigin(origins = "*")
    public Result<String> subscribeTracking(@RequestBody SubscribeRequest request) {
        try {
            // TODO: 实现订阅逻辑，这里简化处理
            return Result.success("订阅成功");
        } catch (Exception e) {
            return Result.error("订阅失败: " + e.getMessage());
        }
    }

    // 转换方法
    private TrackingResponse convertToTrackingResponse(TrackingInfo trackingInfo) {
        TrackingResponse response = new TrackingResponse();
        response.setOrderNumber(trackingInfo.getOrderNumber());
        response.setCurrentStatus(trackingInfo.getCurrentStatus());
        response.setEstimatedDeliveryTime(trackingInfo.getEstimatedDeliveryTime());
        
        // 转换轨迹节点
        List<TrackingNodeResponse> nodes = new ArrayList<>();
        if (trackingInfo.getTrackingNodes() != null) {
            for (TrackingNode node : trackingInfo.getTrackingNodes()) {
                TrackingNodeResponse nodeResponse = new TrackingNodeResponse();
                nodeResponse.setId(node.getNodeId());
                nodeResponse.setOrderNumber(trackingInfo.getOrderNumber());
                nodeResponse.setStatus(node.getStatus());
                nodeResponse.setDescription(node.getDescription());
                // 转换LocationInfo为String
                if (node.getLocation() != null) {
                    String locationStr = "";
                    if (node.getLocation().getCity() != null) {
                        locationStr += node.getLocation().getCity();
                    }
                    if (node.getLocation().getAddress() != null) {
                        locationStr += " " + node.getLocation().getAddress();
                    }
                    nodeResponse.setLocation(locationStr.trim());
                } else {
                    nodeResponse.setLocation(null);
                }
                nodeResponse.setOperatorName(node.getOperatorName());
                nodeResponse.setOperatorType(node.getOperatorType());
                nodeResponse.setCreateTime(node.getTimestamp());
                nodeResponse.setRemarks(node.getRemarks());
                nodes.add(nodeResponse);
            }
        }
        response.setTrackingNodes(nodes);
        
        // TODO: 从订单服务获取订单信息，这里使用模拟数据
        OrderInfoResponse orderInfo = new OrderInfoResponse();
        orderInfo.setSenderName("发件人");
        orderInfo.setSenderAddress("发件地址");
        orderInfo.setReceiverName("收件人");
        orderInfo.setReceiverAddress("收件地址");
        orderInfo.setItemName("货物");
        orderInfo.setServiceType("标准快递");
        response.setOrderInfo(orderInfo);
        
        return response;
    }

    // 内部类定义
    public static class TrackingQueryRequest {
        private String orderNumber;
        private String phone;
        
        public String getOrderNumber() { return orderNumber; }
        public void setOrderNumber(String orderNumber) { this.orderNumber = orderNumber; }
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
    }

    public static class SubscribeRequest {
        private String orderNumber;
        private String phone;
        private String email;
        
        public String getOrderNumber() { return orderNumber; }
        public void setOrderNumber(String orderNumber) { this.orderNumber = orderNumber; }
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
    }

    public static class TrackingResponse {
        private String orderNumber;
        private String currentStatus;
        private LocalDateTime estimatedDeliveryTime;
        private List<TrackingNodeResponse> trackingNodes;
        private OrderInfoResponse orderInfo;
        
        // getters and setters
        public String getOrderNumber() { return orderNumber; }
        public void setOrderNumber(String orderNumber) { this.orderNumber = orderNumber; }
        public String getCurrentStatus() { return currentStatus; }
        public void setCurrentStatus(String currentStatus) { this.currentStatus = currentStatus; }
        public LocalDateTime getEstimatedDeliveryTime() { return estimatedDeliveryTime; }
        public void setEstimatedDeliveryTime(LocalDateTime estimatedDeliveryTime) { this.estimatedDeliveryTime = estimatedDeliveryTime; }
        public List<TrackingNodeResponse> getTrackingNodes() { return trackingNodes; }
        public void setTrackingNodes(List<TrackingNodeResponse> trackingNodes) { this.trackingNodes = trackingNodes; }
        public OrderInfoResponse getOrderInfo() { return orderInfo; }
        public void setOrderInfo(OrderInfoResponse orderInfo) { this.orderInfo = orderInfo; }
    }

    public static class TrackingNodeResponse {
        private String id;
        private String orderNumber;
        private String status;
        private String description;
        private String location;
        private String operatorName;
        private String operatorType;
        private LocalDateTime createTime;
        private String remarks;
        
        // getters and setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getOrderNumber() { return orderNumber; }
        public void setOrderNumber(String orderNumber) { this.orderNumber = orderNumber; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public String getLocation() { return location; }
        public void setLocation(String location) { this.location = location; }
        public String getOperatorName() { return operatorName; }
        public void setOperatorName(String operatorName) { this.operatorName = operatorName; }
        public String getOperatorType() { return operatorType; }
        public void setOperatorType(String operatorType) { this.operatorType = operatorType; }
        public LocalDateTime getCreateTime() { return createTime; }
        public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }
        public String getRemarks() { return remarks; }
        public void setRemarks(String remarks) { this.remarks = remarks; }
    }

    public static class OrderInfoResponse {
        private String senderName;
        private String senderAddress;
        private String receiverName;
        private String receiverAddress;
        private String itemName;
        private String serviceType;
        
        // getters and setters
        public String getSenderName() { return senderName; }
        public void setSenderName(String senderName) { this.senderName = senderName; }
        public String getSenderAddress() { return senderAddress; }
        public void setSenderAddress(String senderAddress) { this.senderAddress = senderAddress; }
        public String getReceiverName() { return receiverName; }
        public void setReceiverName(String receiverName) { this.receiverName = receiverName; }
        public String getReceiverAddress() { return receiverAddress; }
        public void setReceiverAddress(String receiverAddress) { this.receiverAddress = receiverAddress; }
        public String getItemName() { return itemName; }
        public void setItemName(String itemName) { this.itemName = itemName; }
        public String getServiceType() { return serviceType; }
        public void setServiceType(String serviceType) { this.serviceType = serviceType; }
    }

    /**
     * 测试接口
     */
    @GetMapping("/test/hello")
    public Result<String> hello() {
        return Result.success("物流跟踪服务启动成功！端口：8004");
    }
} 