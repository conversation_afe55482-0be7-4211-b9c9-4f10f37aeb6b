package org.example.logisticslogistics.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 网点类型枚举
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Getter
@AllArgsConstructor
public enum StationType {

    /**
     * 揽件点
     */
    PICKUP("PICKUP", "揽件点", "负责货物揽收的网点"),

    /**
     * 中转站
     */
    TRANSIT("TRANSIT", "中转站", "负责货物分拣和中转的网点"),

    /**
     * 派送点
     */
    DELIVERY("DELIVERY", "派送点", "负责最后一公里配送的网点");

    /**
     * 类型编码
     */
    private final String code;

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 类型描述
     */
    private final String description;

    /**
     * 根据编码获取枚举
     */
    public static StationType fromCode(String code) {
        for (StationType type : StationType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的网点类型: " + code);
    }

    /**
     * 根据编码获取枚举（安全方法）
     */
    public static StationType getByCode(String code) {
        for (StationType type : StationType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
