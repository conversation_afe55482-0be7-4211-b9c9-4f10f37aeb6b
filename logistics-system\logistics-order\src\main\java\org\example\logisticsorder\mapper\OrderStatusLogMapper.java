package org.example.logisticsorder.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.logisticsorder.entity.OrderStatusLog;
import java.util.List;

/**
 * 订单状态日志Mapper接口
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Mapper
public interface OrderStatusLogMapper extends BaseMapper<OrderStatusLog> {

    /**
     * 根据订单ID查询状态日志
     */
    List<OrderStatusLog> selectByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据订单号查询状态日志
     */
    List<OrderStatusLog> selectByOrderNumber(@Param("orderNumber") String orderNumber);

    /**
     * 根据状态查询日志
     */
    List<OrderStatusLog> selectByStatus(@Param("newStatus") String newStatus,
                                        @Param("startTime") java.time.LocalDateTime startTime,
                                        @Param("endTime") java.time.LocalDateTime endTime);
}
