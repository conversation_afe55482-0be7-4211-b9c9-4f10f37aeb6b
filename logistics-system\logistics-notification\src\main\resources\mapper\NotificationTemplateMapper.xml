<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.logisticsnotification.mapper.NotificationTemplateMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="org.example.logisticsnotification.entity.NotificationTemplate">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="template_code" jdbcType="VARCHAR" property="templateCode"/>
        <result column="template_name" jdbcType="VARCHAR" property="templateName"/>
        <result column="template_type" jdbcType="VARCHAR" property="templateType"/>
        <result column="notification_type" jdbcType="VARCHAR" property="notificationType"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="content" jdbcType="CLOB" property="content"/>
        <result column="params_desc" jdbcType="CLOB" property="paramsDesc"/>
        <result column="example" jdbcType="CLOB" property="example"/>
        <result column="is_enabled" jdbcType="INTEGER" property="isEnabled"/>
        <result column="sort_order" jdbcType="INTEGER" property="sortOrder"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        id, template_code, template_name, template_type, notification_type,
        title, content, params_desc, example, is_enabled, sort_order,
        remarks, create_time, update_time
    </sql>

    <!-- 根据模板编码查询模板 -->
    <select id="findByTemplateCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM notification_templates
        WHERE template_code = #{templateCode}
    </select>

    <!-- 根据模板类型查询模板列表 -->
    <select id="findByTemplateType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM notification_templates
        WHERE template_type = #{templateType}
        ORDER BY sort_order ASC, create_time DESC
    </select>

    <!-- 根据通知类型查询模板列表 -->
    <select id="findByNotificationType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM notification_templates
        WHERE notification_type = #{notificationType}
        ORDER BY sort_order ASC, create_time DESC
    </select>

    <!-- 查询启用的模板 -->
    <select id="findEnabledTemplates" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM notification_templates
        WHERE is_enabled = 1
        <if test="notificationType != null and notificationType != ''">
            AND notification_type = #{notificationType}
        </if>
        <if test="templateType != null and templateType != ''">
            AND template_type = #{templateType}
        </if>
        ORDER BY sort_order ASC, create_time DESC
    </select>

    <!-- 分页查询模板 -->
    <select id="findTemplatesPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM notification_templates
        WHERE 1=1
        <if test="templateType != null and templateType != ''">
            AND template_type = #{templateType}
        </if>
        <if test="notificationType != null and notificationType != ''">
            AND notification_type = #{notificationType}
        </if>
        <if test="templateName != null and templateName != ''">
            AND template_name LIKE CONCAT('%', #{templateName}, '%')
        </if>
        <if test="isEnabled != null">
            AND is_enabled = #{isEnabled}
        </if>
        ORDER BY sort_order ASC, create_time DESC
    </select>

    <!-- 批量更新模板状态 -->
    <update id="batchUpdateStatus">
        UPDATE notification_templates
        SET is_enabled = #{isEnabled},
        update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 检查模板编码是否存在 -->
    <select id="countByTemplateCode" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM notification_templates
        WHERE template_code = #{templateCode}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

</mapper>