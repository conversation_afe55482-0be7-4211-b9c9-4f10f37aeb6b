package org.example.logisticsorder.service;

import org.example.logisticsorder.dto.CreateOrderDTO;
import java.math.BigDecimal;

/**
 * 价格计算服务接口
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
public interface PricingService {

    /**
     * 计算运费
     */
    BigDecimal calculateShippingFee(CreateOrderDTO orderDTO);

    /**
     * 计算保险费
     */
    BigDecimal calculateInsuranceFee(BigDecimal itemValue);

    /**
     * 计算包装费
     */
    BigDecimal calculatePackingFee(CreateOrderDTO orderDTO);

    /**
     * 计算总费用
     */
    OrderService.PriceResult calculateTotalPrice(CreateOrderDTO orderDTO);

    /**
     * 根据距离计算费用
     */
    BigDecimal calculateDistanceFee(BigDecimal distance);

    /**
     * 根据重量计算费用
     */
    BigDecimal calculateWeightFee(BigDecimal weight);

    /**
     * 根据服务类型获取价格倍数
     */
    Double getServiceTypeMultiplier(String serviceType);
}
