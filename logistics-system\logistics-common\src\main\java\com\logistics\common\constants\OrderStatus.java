package com.logistics.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单状态枚举
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Getter
@AllArgsConstructor
public enum OrderStatus {

    /**
     * 待处理
     */
    PENDING("PENDING", "待处理"),

    /**
     * 已支付
     */
    PAID("PAID", "已支付"),

    /**
     * 已分配揽件员
     */
    PICKUP_ASSIGNED("PICKUP_ASSIGNED", "已分配揽件员"),

    /**
     * 已揽收
     */
    PICKUP("PICKUP", "已揽收"),

    /**
     * 运输中
     */
    TRANSIT("TRANSIT", "运输中"),

    /**
     * 运输中（别名）
     */
    IN_TRANSIT("IN_TRANSIT", "运输中"),

    /**
     * 到达目的地
     */
    ARRIVED("ARRIVED", "到达目的地"),

    /**
     * 派送中
     */
    DELIVERING("DELIVERING", "派送中"),

    /**
     * 派送中（别名）
     */
    OUT_FOR_DELIVERY("OUT_FOR_DELIVERY", "派送中"),

    /**
     * 已签收
     */
    SIGNED("SIGNED", "已签收"),

    /**
     * 已配送（别名）
     */
    DELIVERED("DELIVERED", "已配送"),

    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "已取消"),

    /**
     * 异常
     */
    EXCEPTION("EXCEPTION", "异常"),

    /**
     * 拒收
     */
    REJECTED("REJECTED", "拒收"),

    /**
     * 退货
     */
    RETURNED("RETURNED", "退货");

    /**
     * 状态码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据状态码获取枚举
     */
    public static OrderStatus getByCode(String code) {
        for (OrderStatus status : OrderStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据状态码获取枚举（兼容旧方法名）
     */
    public static OrderStatus fromCode(String code) {
        OrderStatus status = getByCode(code);
        if (status == null) {
            throw new IllegalArgumentException("未知的订单状态: " + code);
        }
        return status;
    }

    /**
     * 获取状态描述（兼容旧方法名）
     */
    public String getDesc() {
        return this.description;
    }

    /**
     * 检查状态是否可以取消
     */
    public boolean canCancel() {
        return this == PENDING || this == PAID;
    }

    /**
     * 检查状态是否为最终状态
     */
    public boolean isFinalStatus() {
        return this == SIGNED || this == DELIVERED || this == CANCELLED || this == REJECTED || this == RETURNED;
    }

    /**
     * 检查状态是否为异常状态
     */
    public boolean isExceptionStatus() {
        return this == EXCEPTION || this == REJECTED || this == RETURNED;
    }

    /**
     * 检查状态是否已完成
     */
    public boolean isCompleted() {
        return this == SIGNED || this == DELIVERED || this == CANCELLED || this == REJECTED || this == RETURNED;
    }
}
