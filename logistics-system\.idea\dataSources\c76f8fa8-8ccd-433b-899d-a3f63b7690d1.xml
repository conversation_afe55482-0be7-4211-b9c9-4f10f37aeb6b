<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="@localhost">
  <database-model serializer="dbm" dbms="MYSQL" family-id="MYSQL" format-version="4.51">
    <root id="1">
      <DefaultCasing>lower/lower</DefaultCasing>
      <DefaultEngine>InnoDB</DefaultEngine>
      <DefaultTmpEngine>InnoDB</DefaultTmpEngine>
      <Grants>|root||root|localhost|ALLOW_NONEXISTENT_DEFINER|G
|root||user_11||ALLOW_NONEXISTENT_DEFINER|G
|root||管理人员||ALLOW_NONEXISTENT_DEFINER|G
|root||管理员||ALLOW_NONEXISTENT_DEFINER|G
|root||root|localhost|ALTER|G
|root||user_11||ALTER|G
|root||管理人员||ALTER|G
|root||管理员||ALTER|G
|root||root|localhost|ALTER ROUTINE|G
|root||user_11||ALTER ROUTINE|G
|root||管理人员||ALTER ROUTINE|G
|root||管理员||ALTER ROUTINE|G
|root||root|localhost|APPLICATION_PASSWORD_ADMIN|G
|root||user_11||APPLICATION_PASSWORD_ADMIN|G
|root||管理人员||APPLICATION_PASSWORD_ADMIN|G
|root||管理员||APPLICATION_PASSWORD_ADMIN|G
|root||mysql.infoschema|localhost|AUDIT_ABORT_EXEMPT|G
|root||mysql.session|localhost|AUDIT_ABORT_EXEMPT|G
|root||mysql.sys|localhost|AUDIT_ABORT_EXEMPT|G
|root||root|localhost|AUDIT_ABORT_EXEMPT|G
|root||user_11||AUDIT_ABORT_EXEMPT|G
|root||管理人员||AUDIT_ABORT_EXEMPT|G
|root||管理员||AUDIT_ABORT_EXEMPT|G
|root||root|localhost|AUDIT_ADMIN|G
|root||user_11||AUDIT_ADMIN|G
|root||管理人员||AUDIT_ADMIN|G
|root||管理员||AUDIT_ADMIN|G
|root||mysql.session|localhost|AUTHENTICATION_POLICY_ADMIN|G
|root||root|localhost|AUTHENTICATION_POLICY_ADMIN|G
|root||user_11||AUTHENTICATION_POLICY_ADMIN|G
|root||管理人员||AUTHENTICATION_POLICY_ADMIN|G
|root||管理员||AUTHENTICATION_POLICY_ADMIN|G
|root||mysql.session|localhost|BACKUP_ADMIN|G
|root||root|localhost|BACKUP_ADMIN|G
|root||user_11||BACKUP_ADMIN|G
|root||管理人员||BACKUP_ADMIN|G
|root||管理员||BACKUP_ADMIN|G
|root||root|localhost|BINLOG_ADMIN|G
|root||user_11||BINLOG_ADMIN|G
|root||管理人员||BINLOG_ADMIN|G
|root||管理员||BINLOG_ADMIN|G
|root||root|localhost|BINLOG_ENCRYPTION_ADMIN|G
|root||user_11||BINLOG_ENCRYPTION_ADMIN|G
|root||管理人员||BINLOG_ENCRYPTION_ADMIN|G
|root||管理员||BINLOG_ENCRYPTION_ADMIN|G
|root||mysql.session|localhost|CLONE_ADMIN|G
|root||root|localhost|CLONE_ADMIN|G
|root||user_11||CLONE_ADMIN|G
|root||管理人员||CLONE_ADMIN|G
|root||管理员||CLONE_ADMIN|G
|root||mysql.session|localhost|CONNECTION_ADMIN|G
|root||root|localhost|CONNECTION_ADMIN|G
|root||user_11||CONNECTION_ADMIN|G
|root||管理人员||CONNECTION_ADMIN|G
|root||管理员||CONNECTION_ADMIN|G
|root||root|localhost|CREATE|G
|root||user_11||CREATE|G
|root||user_13||CREATE|G
|root||管理人员||CREATE|G
|root||管理员||CREATE|G
|root||root|localhost|CREATE ROLE|G
|root||user_11||CREATE ROLE|G
|root||管理人员||CREATE ROLE|G
|root||管理员||CREATE ROLE|G
|root||root|localhost|CREATE ROUTINE|G
|root||user_11||CREATE ROUTINE|G
|root||管理人员||CREATE ROUTINE|G
|root||管理员||CREATE ROUTINE|G
|root||root|localhost|CREATE TABLESPACE|G
|root||user_11||CREATE TABLESPACE|G
|root||管理人员||CREATE TABLESPACE|G
|root||管理员||CREATE TABLESPACE|G
|root||root|localhost|CREATE TEMPORARY TABLES|G
|root||user_11||CREATE TEMPORARY TABLES|G
|root||管理人员||CREATE TEMPORARY TABLES|G
|root||管理员||CREATE TEMPORARY TABLES|G
|root||root|localhost|CREATE USER|G
|root||user_11||CREATE USER|G
|root||管理人员||CREATE USER|G
|root||管理员||CREATE USER|G
|root||root|localhost|CREATE VIEW|G
|root||user_11||CREATE VIEW|G
|root||管理人员||CREATE VIEW|G
|root||管理员||CREATE VIEW|G
|root||root|localhost|DELETE|G
|root||user_11||DELETE|G
|root||管理人员||DELETE|G
|root||管理员||DELETE|G
|root||root|localhost|DROP|G
|root||user_11||DROP|G
|root||管理人员||DROP|G
|root||管理员||DROP|G
|root||root|localhost|DROP ROLE|G
|root||user_11||DROP ROLE|G
|root||管理人员||DROP ROLE|G
|root||管理员||DROP ROLE|G
|root||root|localhost|ENCRYPTION_KEY_ADMIN|G
|root||user_11||ENCRYPTION_KEY_ADMIN|G
|root||管理人员||ENCRYPTION_KEY_ADMIN|G
|root||管理员||ENCRYPTION_KEY_ADMIN|G
|root||root|localhost|EVENT|G
|root||user_11||EVENT|G
|root||管理人员||EVENT|G
|root||管理员||EVENT|G
|root||root|localhost|EXECUTE|G
|root||user_11||EXECUTE|G
|root||管理人员||EXECUTE|G
|root||管理员||EXECUTE|G
|root||root|localhost|FILE|G
|root||user_11||FILE|G
|root||管理人员||FILE|G
|root||管理员||FILE|G
|root||mysql.infoschema|localhost|FIREWALL_EXEMPT|G
|root||mysql.session|localhost|FIREWALL_EXEMPT|G
|root||mysql.sys|localhost|FIREWALL_EXEMPT|G
|root||root|localhost|FIREWALL_EXEMPT|G
|root||user_11||FIREWALL_EXEMPT|G
|root||管理人员||FIREWALL_EXEMPT|G
|root||管理员||FIREWALL_EXEMPT|G
|root||root|localhost|FLUSH_OPTIMIZER_COSTS|G
|root||user_11||FLUSH_OPTIMIZER_COSTS|G
|root||管理人员||FLUSH_OPTIMIZER_COSTS|G
|root||管理员||FLUSH_OPTIMIZER_COSTS|G
|root||root|localhost|FLUSH_STATUS|G
|root||user_11||FLUSH_STATUS|G
|root||管理人员||FLUSH_STATUS|G
|root||管理员||FLUSH_STATUS|G
|root||root|localhost|FLUSH_TABLES|G
|root||user_11||FLUSH_TABLES|G
|root||管理人员||FLUSH_TABLES|G
|root||管理员||FLUSH_TABLES|G
|root||root|localhost|FLUSH_USER_RESOURCES|G
|root||user_11||FLUSH_USER_RESOURCES|G
|root||管理人员||FLUSH_USER_RESOURCES|G
|root||管理员||FLUSH_USER_RESOURCES|G
|root||root|localhost|GROUP_REPLICATION_ADMIN|G
|root||user_11||GROUP_REPLICATION_ADMIN|G
|root||管理人员||GROUP_REPLICATION_ADMIN|G
|root||管理员||GROUP_REPLICATION_ADMIN|G
|root||root|localhost|GROUP_REPLICATION_STREAM|G
|root||user_11||GROUP_REPLICATION_STREAM|G
|root||管理人员||GROUP_REPLICATION_STREAM|G
|root||管理员||GROUP_REPLICATION_STREAM|G
|root||root|localhost|INDEX|G
|root||user_11||INDEX|G
|root||管理人员||INDEX|G
|root||管理员||INDEX|G
|root||root|localhost|INNODB_REDO_LOG_ARCHIVE|G
|root||user_11||INNODB_REDO_LOG_ARCHIVE|G
|root||管理人员||INNODB_REDO_LOG_ARCHIVE|G
|root||管理员||INNODB_REDO_LOG_ARCHIVE|G
|root||root|localhost|INNODB_REDO_LOG_ENABLE|G
|root||user_11||INNODB_REDO_LOG_ENABLE|G
|root||管理人员||INNODB_REDO_LOG_ENABLE|G
|root||管理员||INNODB_REDO_LOG_ENABLE|G
|root||root|localhost|INSERT|G
|root||user_11||INSERT|G
|root||管理人员||INSERT|G
|root||管理员||INSERT|G
|root||root|localhost|LOCK TABLES|G
|root||user_11||LOCK TABLES|G
|root||管理人员||LOCK TABLES|G
|root||管理员||LOCK TABLES|G
|root||root|localhost|PASSWORDLESS_USER_ADMIN|G
|root||user_11||PASSWORDLESS_USER_ADMIN|G
|root||管理人员||PASSWORDLESS_USER_ADMIN|G
|root||管理员||PASSWORDLESS_USER_ADMIN|G
|root||mysql.session|localhost|PERSIST_RO_VARIABLES_ADMIN|G
|root||root|localhost|PERSIST_RO_VARIABLES_ADMIN|G
|root||user_11||PERSIST_RO_VARIABLES_ADMIN|G
|root||管理人员||PERSIST_RO_VARIABLES_ADMIN|G
|root||管理员||PERSIST_RO_VARIABLES_ADMIN|G
|root||root|localhost|PROCESS|G
|root||user_11||PROCESS|G
|root||管理人员||PROCESS|G
|root||管理员||PROCESS|G
|root||root|localhost|REFERENCES|G
|root||user_11||REFERENCES|G
|root||管理人员||REFERENCES|G
|root||管理员||REFERENCES|G
|root||root|localhost|RELOAD|G
|root||user_11||RELOAD|G
|root||管理人员||RELOAD|G
|root||管理员||RELOAD|G
|root||root|localhost|REPLICATION CLIENT|G
|root||user_11||REPLICATION CLIENT|G
|root||管理人员||REPLICATION CLIENT|G
|root||管理员||REPLICATION CLIENT|G
|root||root|localhost|REPLICATION SLAVE|G
|root||user_11||REPLICATION SLAVE|G
|root||管理人员||REPLICATION SLAVE|G
|root||管理员||REPLICATION SLAVE|G
|root||root|localhost|REPLICATION_APPLIER|G
|root||user_11||REPLICATION_APPLIER|G
|root||管理人员||REPLICATION_APPLIER|G
|root||管理员||REPLICATION_APPLIER|G
|root||root|localhost|REPLICATION_SLAVE_ADMIN|G
|root||user_11||REPLICATION_SLAVE_ADMIN|G
|root||管理人员||REPLICATION_SLAVE_ADMIN|G
|root||管理员||REPLICATION_SLAVE_ADMIN|G
|root||root|localhost|RESOURCE_GROUP_ADMIN|G
|root||user_11||RESOURCE_GROUP_ADMIN|G
|root||管理人员||RESOURCE_GROUP_ADMIN|G
|root||管理员||RESOURCE_GROUP_ADMIN|G
|root||root|localhost|RESOURCE_GROUP_USER|G
|root||user_11||RESOURCE_GROUP_USER|G
|root||管理人员||RESOURCE_GROUP_USER|G
|root||管理员||RESOURCE_GROUP_USER|G
|root||root|localhost|ROLE_ADMIN|G
|root||user_11||ROLE_ADMIN|G
|root||管理人员||ROLE_ADMIN|G
|root||管理员||ROLE_ADMIN|G
|root||mysql.infoschema|localhost|SELECT|G
|root||root|localhost|SELECT|G
|root||user_11||SELECT|G
|root||管理人员||SELECT|G
|root||管理员||SELECT|G
|root||root|localhost|SENSITIVE_VARIABLES_OBSERVER|G
|root||user_11||SENSITIVE_VARIABLES_OBSERVER|G
|root||管理人员||SENSITIVE_VARIABLES_OBSERVER|G
|root||管理员||SENSITIVE_VARIABLES_OBSERVER|G
|root||root|localhost|SERVICE_CONNECTION_ADMIN|G
|root||user_11||SERVICE_CONNECTION_ADMIN|G
|root||管理人员||SERVICE_CONNECTION_ADMIN|G
|root||管理员||SERVICE_CONNECTION_ADMIN|G
|root||mysql.session|localhost|SESSION_VARIABLES_ADMIN|G
|root||root|localhost|SESSION_VARIABLES_ADMIN|G
|root||user_11||SESSION_VARIABLES_ADMIN|G
|root||管理人员||SESSION_VARIABLES_ADMIN|G
|root||管理员||SESSION_VARIABLES_ADMIN|G
|root||root|localhost|SET_ANY_DEFINER|G
|root||user_11||SET_ANY_DEFINER|G
|root||管理人员||SET_ANY_DEFINER|G
|root||管理员||SET_ANY_DEFINER|G
|root||root|localhost|SHOW DATABASES|G
|root||user_11||SHOW DATABASES|G
|root||管理人员||SHOW DATABASES|G
|root||管理员||SHOW DATABASES|G
|root||root|localhost|SHOW VIEW|G
|root||user_11||SHOW VIEW|G
|root||管理人员||SHOW VIEW|G
|root||管理员||SHOW VIEW|G
|root||root|localhost|SHOW_ROUTINE|G
|root||user_11||SHOW_ROUTINE|G
|root||管理人员||SHOW_ROUTINE|G
|root||管理员||SHOW_ROUTINE|G
|root||mysql.session|localhost|SHUTDOWN|G
|root||root|localhost|SHUTDOWN|G
|root||user_11||SHUTDOWN|G
|root||管理人员||SHUTDOWN|G
|root||管理员||SHUTDOWN|G
|root||mysql.session|localhost|SUPER|G
|root||root|localhost|SUPER|G
|root||user_11||SUPER|G
|root||管理人员||SUPER|G
|root||管理员||SUPER|G
|root||mysql.infoschema|localhost|SYSTEM_USER|G
|root||mysql.session|localhost|SYSTEM_USER|G
|root||mysql.sys|localhost|SYSTEM_USER|G
|root||root|localhost|SYSTEM_USER|G
|root||user_11||SYSTEM_USER|G
|root||管理人员||SYSTEM_USER|G
|root||管理员||SYSTEM_USER|G
|root||mysql.session|localhost|SYSTEM_VARIABLES_ADMIN|G
|root||root|localhost|SYSTEM_VARIABLES_ADMIN|G
|root||user_11||SYSTEM_VARIABLES_ADMIN|G
|root||管理人员||SYSTEM_VARIABLES_ADMIN|G
|root||管理员||SYSTEM_VARIABLES_ADMIN|G
|root||root|localhost|TABLE_ENCRYPTION_ADMIN|G
|root||user_11||TABLE_ENCRYPTION_ADMIN|G
|root||管理人员||TABLE_ENCRYPTION_ADMIN|G
|root||管理员||TABLE_ENCRYPTION_ADMIN|G
|root||root|localhost|TELEMETRY_LOG_ADMIN|G
|root||user_11||TELEMETRY_LOG_ADMIN|G
|root||管理人员||TELEMETRY_LOG_ADMIN|G
|root||管理员||TELEMETRY_LOG_ADMIN|G
|root||root|localhost|TRANSACTION_GTID_TAG|G
|root||user_11||TRANSACTION_GTID_TAG|G
|root||管理人员||TRANSACTION_GTID_TAG|G
|root||管理员||TRANSACTION_GTID_TAG|G
|root||root|localhost|TRIGGER|G
|root||user_11||TRIGGER|G
|root||管理人员||TRIGGER|G
|root||管理员||TRIGGER|G
|root||root|localhost|UPDATE|G
|root||user_11||UPDATE|G
|root||管理人员||UPDATE|G
|root||管理员||UPDATE|G
|root||root|localhost|XA_RECOVER_ADMIN|G
|root||user_11||XA_RECOVER_ADMIN|G
|root||管理人员||XA_RECOVER_ADMIN|G
|root||管理员||XA_RECOVER_ADMIN|G
|root||root|localhost|grant option|G
java|schema||java|localhost|ALTER|G
java|schema||java|localhost|ALTER ROUTINE|G
java|schema||java|localhost|CREATE|G
java|schema||java|localhost|CREATE ROUTINE|G
java|schema||java|localhost|CREATE TEMPORARY TABLES|G
java|schema||java|localhost|CREATE VIEW|G
java|schema||java|localhost|DELETE|G
java|schema||java|localhost|DROP|G
java|schema||java|localhost|EVENT|G
java|schema||java|localhost|EXECUTE|G
java|schema||java|localhost|INDEX|G
java|schema||java|localhost|INSERT|G
java|schema||java|localhost|LOCK TABLES|G
java|schema||java|localhost|REFERENCES|G
java|schema||java|localhost|SELECT|G
java|schema||java|localhost|SHOW VIEW|G
java|schema||java|localhost|TRIGGER|G
java|schema||java|localhost|UPDATE|G
performance_schema|schema||mysql.session|localhost|SELECT|G
sys|schema||mysql.sys|localhost|TRIGGER|G</Grants>
      <ServerVersion>8.3.0</ServerVersion>
    </root>
    <collation id="2" parent="1" name="armscii8_bin">
      <Charset>armscii8</Charset>
    </collation>
    <collation id="3" parent="1" name="armscii8_general_ci">
      <Charset>armscii8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="4" parent="1" name="ascii_bin">
      <Charset>ascii</Charset>
    </collation>
    <collation id="5" parent="1" name="ascii_general_ci">
      <Charset>ascii</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="6" parent="1" name="big5_bin">
      <Charset>big5</Charset>
    </collation>
    <collation id="7" parent="1" name="big5_chinese_ci">
      <Charset>big5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="8" parent="1" name="binary">
      <Charset>binary</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="9" parent="1" name="cp1250_bin">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="10" parent="1" name="cp1250_croatian_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="11" parent="1" name="cp1250_czech_cs">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="12" parent="1" name="cp1250_general_ci">
      <Charset>cp1250</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="13" parent="1" name="cp1250_polish_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="14" parent="1" name="cp1251_bin">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="15" parent="1" name="cp1251_bulgarian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="16" parent="1" name="cp1251_general_ci">
      <Charset>cp1251</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="17" parent="1" name="cp1251_general_cs">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="18" parent="1" name="cp1251_ukrainian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="19" parent="1" name="cp1256_bin">
      <Charset>cp1256</Charset>
    </collation>
    <collation id="20" parent="1" name="cp1256_general_ci">
      <Charset>cp1256</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="21" parent="1" name="cp1257_bin">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="22" parent="1" name="cp1257_general_ci">
      <Charset>cp1257</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="23" parent="1" name="cp1257_lithuanian_ci">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="24" parent="1" name="cp850_bin">
      <Charset>cp850</Charset>
    </collation>
    <collation id="25" parent="1" name="cp850_general_ci">
      <Charset>cp850</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="26" parent="1" name="cp852_bin">
      <Charset>cp852</Charset>
    </collation>
    <collation id="27" parent="1" name="cp852_general_ci">
      <Charset>cp852</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="28" parent="1" name="cp866_bin">
      <Charset>cp866</Charset>
    </collation>
    <collation id="29" parent="1" name="cp866_general_ci">
      <Charset>cp866</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="30" parent="1" name="cp932_bin">
      <Charset>cp932</Charset>
    </collation>
    <collation id="31" parent="1" name="cp932_japanese_ci">
      <Charset>cp932</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="32" parent="1" name="dec8_bin">
      <Charset>dec8</Charset>
    </collation>
    <collation id="33" parent="1" name="dec8_swedish_ci">
      <Charset>dec8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="34" parent="1" name="eucjpms_bin">
      <Charset>eucjpms</Charset>
    </collation>
    <collation id="35" parent="1" name="eucjpms_japanese_ci">
      <Charset>eucjpms</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="36" parent="1" name="euckr_bin">
      <Charset>euckr</Charset>
    </collation>
    <collation id="37" parent="1" name="euckr_korean_ci">
      <Charset>euckr</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="38" parent="1" name="gb18030_bin">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="39" parent="1" name="gb18030_chinese_ci">
      <Charset>gb18030</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="40" parent="1" name="gb18030_unicode_520_ci">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="41" parent="1" name="gb2312_bin">
      <Charset>gb2312</Charset>
    </collation>
    <collation id="42" parent="1" name="gb2312_chinese_ci">
      <Charset>gb2312</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="43" parent="1" name="gbk_bin">
      <Charset>gbk</Charset>
    </collation>
    <collation id="44" parent="1" name="gbk_chinese_ci">
      <Charset>gbk</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="45" parent="1" name="geostd8_bin">
      <Charset>geostd8</Charset>
    </collation>
    <collation id="46" parent="1" name="geostd8_general_ci">
      <Charset>geostd8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="47" parent="1" name="greek_bin">
      <Charset>greek</Charset>
    </collation>
    <collation id="48" parent="1" name="greek_general_ci">
      <Charset>greek</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="49" parent="1" name="hebrew_bin">
      <Charset>hebrew</Charset>
    </collation>
    <collation id="50" parent="1" name="hebrew_general_ci">
      <Charset>hebrew</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="51" parent="1" name="hp8_bin">
      <Charset>hp8</Charset>
    </collation>
    <collation id="52" parent="1" name="hp8_english_ci">
      <Charset>hp8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="53" parent="1" name="keybcs2_bin">
      <Charset>keybcs2</Charset>
    </collation>
    <collation id="54" parent="1" name="keybcs2_general_ci">
      <Charset>keybcs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="55" parent="1" name="koi8r_bin">
      <Charset>koi8r</Charset>
    </collation>
    <collation id="56" parent="1" name="koi8r_general_ci">
      <Charset>koi8r</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="57" parent="1" name="koi8u_bin">
      <Charset>koi8u</Charset>
    </collation>
    <collation id="58" parent="1" name="koi8u_general_ci">
      <Charset>koi8u</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="59" parent="1" name="latin1_bin">
      <Charset>latin1</Charset>
    </collation>
    <collation id="60" parent="1" name="latin1_danish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="61" parent="1" name="latin1_general_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="62" parent="1" name="latin1_general_cs">
      <Charset>latin1</Charset>
    </collation>
    <collation id="63" parent="1" name="latin1_german1_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="64" parent="1" name="latin1_german2_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="65" parent="1" name="latin1_spanish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="66" parent="1" name="latin1_swedish_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="67" parent="1" name="latin2_bin">
      <Charset>latin2</Charset>
    </collation>
    <collation id="68" parent="1" name="latin2_croatian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="69" parent="1" name="latin2_czech_cs">
      <Charset>latin2</Charset>
    </collation>
    <collation id="70" parent="1" name="latin2_general_ci">
      <Charset>latin2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="71" parent="1" name="latin2_hungarian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="72" parent="1" name="latin5_bin">
      <Charset>latin5</Charset>
    </collation>
    <collation id="73" parent="1" name="latin5_turkish_ci">
      <Charset>latin5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="74" parent="1" name="latin7_bin">
      <Charset>latin7</Charset>
    </collation>
    <collation id="75" parent="1" name="latin7_estonian_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="76" parent="1" name="latin7_general_ci">
      <Charset>latin7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="77" parent="1" name="latin7_general_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="78" parent="1" name="macce_bin">
      <Charset>macce</Charset>
    </collation>
    <collation id="79" parent="1" name="macce_general_ci">
      <Charset>macce</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="80" parent="1" name="macroman_bin">
      <Charset>macroman</Charset>
    </collation>
    <collation id="81" parent="1" name="macroman_general_ci">
      <Charset>macroman</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="82" parent="1" name="sjis_bin">
      <Charset>sjis</Charset>
    </collation>
    <collation id="83" parent="1" name="sjis_japanese_ci">
      <Charset>sjis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="84" parent="1" name="swe7_bin">
      <Charset>swe7</Charset>
    </collation>
    <collation id="85" parent="1" name="swe7_swedish_ci">
      <Charset>swe7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="86" parent="1" name="tis620_bin">
      <Charset>tis620</Charset>
    </collation>
    <collation id="87" parent="1" name="tis620_thai_ci">
      <Charset>tis620</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="88" parent="1" name="ucs2_bin">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="89" parent="1" name="ucs2_croatian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="90" parent="1" name="ucs2_czech_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="91" parent="1" name="ucs2_danish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="92" parent="1" name="ucs2_esperanto_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="93" parent="1" name="ucs2_estonian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="94" parent="1" name="ucs2_general_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="95" parent="1" name="ucs2_general_mysql500_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="96" parent="1" name="ucs2_german2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="97" parent="1" name="ucs2_hungarian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="98" parent="1" name="ucs2_icelandic_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="99" parent="1" name="ucs2_latvian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="100" parent="1" name="ucs2_lithuanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="101" parent="1" name="ucs2_persian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="102" parent="1" name="ucs2_polish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="103" parent="1" name="ucs2_roman_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="104" parent="1" name="ucs2_romanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="105" parent="1" name="ucs2_sinhala_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="106" parent="1" name="ucs2_slovak_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="107" parent="1" name="ucs2_slovenian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="108" parent="1" name="ucs2_spanish2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="109" parent="1" name="ucs2_spanish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="110" parent="1" name="ucs2_swedish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="111" parent="1" name="ucs2_turkish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="112" parent="1" name="ucs2_unicode_520_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="113" parent="1" name="ucs2_unicode_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="114" parent="1" name="ucs2_vietnamese_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="115" parent="1" name="ujis_bin">
      <Charset>ujis</Charset>
    </collation>
    <collation id="116" parent="1" name="ujis_japanese_ci">
      <Charset>ujis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="117" parent="1" name="utf16_bin">
      <Charset>utf16</Charset>
    </collation>
    <collation id="118" parent="1" name="utf16_croatian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="119" parent="1" name="utf16_czech_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="120" parent="1" name="utf16_danish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="121" parent="1" name="utf16_esperanto_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="122" parent="1" name="utf16_estonian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="123" parent="1" name="utf16_general_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="124" parent="1" name="utf16_german2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="125" parent="1" name="utf16_hungarian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="126" parent="1" name="utf16_icelandic_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="127" parent="1" name="utf16_latvian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="128" parent="1" name="utf16_lithuanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="129" parent="1" name="utf16_persian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="130" parent="1" name="utf16_polish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="131" parent="1" name="utf16_roman_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="132" parent="1" name="utf16_romanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="133" parent="1" name="utf16_sinhala_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="134" parent="1" name="utf16_slovak_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="135" parent="1" name="utf16_slovenian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="136" parent="1" name="utf16_spanish2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="137" parent="1" name="utf16_spanish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="138" parent="1" name="utf16_swedish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="139" parent="1" name="utf16_turkish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="140" parent="1" name="utf16_unicode_520_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="141" parent="1" name="utf16_unicode_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="142" parent="1" name="utf16_vietnamese_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="143" parent="1" name="utf16le_bin">
      <Charset>utf16le</Charset>
    </collation>
    <collation id="144" parent="1" name="utf16le_general_ci">
      <Charset>utf16le</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="145" parent="1" name="utf32_bin">
      <Charset>utf32</Charset>
    </collation>
    <collation id="146" parent="1" name="utf32_croatian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="147" parent="1" name="utf32_czech_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="148" parent="1" name="utf32_danish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="149" parent="1" name="utf32_esperanto_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="150" parent="1" name="utf32_estonian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="151" parent="1" name="utf32_general_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="152" parent="1" name="utf32_german2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="153" parent="1" name="utf32_hungarian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="154" parent="1" name="utf32_icelandic_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="155" parent="1" name="utf32_latvian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="156" parent="1" name="utf32_lithuanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="157" parent="1" name="utf32_persian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="158" parent="1" name="utf32_polish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="159" parent="1" name="utf32_roman_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="160" parent="1" name="utf32_romanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="161" parent="1" name="utf32_sinhala_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="162" parent="1" name="utf32_slovak_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="163" parent="1" name="utf32_slovenian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="164" parent="1" name="utf32_spanish2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="165" parent="1" name="utf32_spanish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="166" parent="1" name="utf32_swedish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="167" parent="1" name="utf32_turkish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="168" parent="1" name="utf32_unicode_520_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="169" parent="1" name="utf32_unicode_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="170" parent="1" name="utf32_vietnamese_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="171" parent="1" name="utf8mb3_bin">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="172" parent="1" name="utf8mb3_croatian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="173" parent="1" name="utf8mb3_czech_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="174" parent="1" name="utf8mb3_danish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="175" parent="1" name="utf8mb3_esperanto_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="176" parent="1" name="utf8mb3_estonian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="177" parent="1" name="utf8mb3_general_ci">
      <Charset>utf8mb3</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="178" parent="1" name="utf8mb3_general_mysql500_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="179" parent="1" name="utf8mb3_german2_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="180" parent="1" name="utf8mb3_hungarian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="181" parent="1" name="utf8mb3_icelandic_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="182" parent="1" name="utf8mb3_latvian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="183" parent="1" name="utf8mb3_lithuanian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="184" parent="1" name="utf8mb3_persian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="185" parent="1" name="utf8mb3_polish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="186" parent="1" name="utf8mb3_roman_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="187" parent="1" name="utf8mb3_romanian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="188" parent="1" name="utf8mb3_sinhala_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="189" parent="1" name="utf8mb3_slovak_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="190" parent="1" name="utf8mb3_slovenian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="191" parent="1" name="utf8mb3_spanish2_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="192" parent="1" name="utf8mb3_spanish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="193" parent="1" name="utf8mb3_swedish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="194" parent="1" name="utf8mb3_tolower_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="195" parent="1" name="utf8mb3_turkish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="196" parent="1" name="utf8mb3_unicode_520_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="197" parent="1" name="utf8mb3_unicode_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="198" parent="1" name="utf8mb3_vietnamese_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="199" parent="1" name="utf8mb4_0900_ai_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="200" parent="1" name="utf8mb4_0900_as_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="201" parent="1" name="utf8mb4_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="202" parent="1" name="utf8mb4_0900_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="203" parent="1" name="utf8mb4_bg_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="204" parent="1" name="utf8mb4_bg_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="205" parent="1" name="utf8mb4_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="206" parent="1" name="utf8mb4_bs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="207" parent="1" name="utf8mb4_bs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="208" parent="1" name="utf8mb4_croatian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="209" parent="1" name="utf8mb4_cs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="210" parent="1" name="utf8mb4_cs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="211" parent="1" name="utf8mb4_czech_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="212" parent="1" name="utf8mb4_da_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="213" parent="1" name="utf8mb4_da_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="214" parent="1" name="utf8mb4_danish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="215" parent="1" name="utf8mb4_de_pb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="216" parent="1" name="utf8mb4_de_pb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="217" parent="1" name="utf8mb4_eo_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="218" parent="1" name="utf8mb4_eo_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="219" parent="1" name="utf8mb4_es_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="220" parent="1" name="utf8mb4_es_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="221" parent="1" name="utf8mb4_es_trad_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="222" parent="1" name="utf8mb4_es_trad_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="223" parent="1" name="utf8mb4_esperanto_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="224" parent="1" name="utf8mb4_estonian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="225" parent="1" name="utf8mb4_et_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="226" parent="1" name="utf8mb4_et_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="227" parent="1" name="utf8mb4_general_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="228" parent="1" name="utf8mb4_german2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="229" parent="1" name="utf8mb4_gl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="230" parent="1" name="utf8mb4_gl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="231" parent="1" name="utf8mb4_hr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="232" parent="1" name="utf8mb4_hr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="233" parent="1" name="utf8mb4_hu_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="234" parent="1" name="utf8mb4_hu_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="235" parent="1" name="utf8mb4_hungarian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="236" parent="1" name="utf8mb4_icelandic_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="237" parent="1" name="utf8mb4_is_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="238" parent="1" name="utf8mb4_is_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="239" parent="1" name="utf8mb4_ja_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="240" parent="1" name="utf8mb4_ja_0900_as_cs_ks">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="241" parent="1" name="utf8mb4_la_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="242" parent="1" name="utf8mb4_la_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="243" parent="1" name="utf8mb4_latvian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="244" parent="1" name="utf8mb4_lithuanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="245" parent="1" name="utf8mb4_lt_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="246" parent="1" name="utf8mb4_lt_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="247" parent="1" name="utf8mb4_lv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="248" parent="1" name="utf8mb4_lv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="249" parent="1" name="utf8mb4_mn_cyrl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="250" parent="1" name="utf8mb4_mn_cyrl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="251" parent="1" name="utf8mb4_nb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="252" parent="1" name="utf8mb4_nb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="253" parent="1" name="utf8mb4_nn_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="254" parent="1" name="utf8mb4_nn_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="255" parent="1" name="utf8mb4_persian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="256" parent="1" name="utf8mb4_pl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="257" parent="1" name="utf8mb4_pl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="258" parent="1" name="utf8mb4_polish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="259" parent="1" name="utf8mb4_ro_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="260" parent="1" name="utf8mb4_ro_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="261" parent="1" name="utf8mb4_roman_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="262" parent="1" name="utf8mb4_romanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="263" parent="1" name="utf8mb4_ru_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="264" parent="1" name="utf8mb4_ru_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="265" parent="1" name="utf8mb4_sinhala_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="266" parent="1" name="utf8mb4_sk_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="267" parent="1" name="utf8mb4_sk_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="268" parent="1" name="utf8mb4_sl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="269" parent="1" name="utf8mb4_sl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="270" parent="1" name="utf8mb4_slovak_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="271" parent="1" name="utf8mb4_slovenian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="272" parent="1" name="utf8mb4_spanish2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="273" parent="1" name="utf8mb4_spanish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="274" parent="1" name="utf8mb4_sr_latn_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="275" parent="1" name="utf8mb4_sr_latn_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="276" parent="1" name="utf8mb4_sv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="277" parent="1" name="utf8mb4_sv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="278" parent="1" name="utf8mb4_swedish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="279" parent="1" name="utf8mb4_tr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="280" parent="1" name="utf8mb4_tr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="281" parent="1" name="utf8mb4_turkish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="282" parent="1" name="utf8mb4_unicode_520_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="283" parent="1" name="utf8mb4_unicode_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="284" parent="1" name="utf8mb4_vi_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="285" parent="1" name="utf8mb4_vi_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="286" parent="1" name="utf8mb4_vietnamese_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="287" parent="1" name="utf8mb4_zh_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <schema id="288" parent="1" name="cloud">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="289" parent="1" name="docker">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="290" parent="1" name="employee_management">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="291" parent="1" name="finedb">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="292" parent="1" name="information_schema">
      <CollationName>utf8mb3_general_ci</CollationName>
    </schema>
    <schema id="293" parent="1" name="java">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="294" parent="1" name="java_web">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="295" parent="1" name="javawebwork">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="296" parent="1" name="jdbc_demo">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="297" parent="1" name="logistics">
      <LastIntrospectionLocalTimestamp>2025-06-30.08:44:00</LastIntrospectionLocalTimestamp>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </schema>
    <schema id="298" parent="1" name="mysql">
      <CollationName>utf8mb3_general_ci</CollationName>
    </schema>
    <schema id="299" parent="1" name="performance_schema">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="300" parent="1" name="product_db">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="301" parent="1" name="rouyi">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="302" parent="1" name="rouyicloud">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="303" parent="1" name="ry-config">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="304" parent="1" name="ry_end">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="305" parent="1" name="student_affairs">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="306" parent="1" name="sys">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="307" parent="1" name="test">
      <CollationName>utf8mb3_general_ci</CollationName>
    </schema>
    <schema id="308" parent="1" name="实验3">
      <CollationName>utf8mb3_general_ci</CollationName>
    </schema>
    <schema id="309" parent="1" name="实验4">
      <CollationName>utf8mb3_general_ci</CollationName>
    </schema>
    <schema id="310" parent="1" name="实验七">
      <CollationName>utf8mb3_general_ci</CollationName>
    </schema>
    <schema id="311" parent="1" name="实验八">
      <CollationName>utf8mb3_general_ci</CollationName>
    </schema>
    <schema id="312" parent="1" name="实验六">
      <CollationName>utf8mb3_general_ci</CollationName>
    </schema>
    <user id="313" parent="1" name="B1">
      <Plugin>caching_sha2_password</Plugin>
      <RoleGrants>管理人员
</RoleGrants>
    </user>
    <user id="314" parent="1" name="D1">
      <Plugin>caching_sha2_password</Plugin>
      <RoleGrants>驾驶人员
</RoleGrants>
    </user>
    <user id="315" parent="1" name="F1">
      <Plugin>caching_sha2_password</Plugin>
      <RoleGrants>维修人员
</RoleGrants>
    </user>
    <user id="316" parent="1" name="W1">
      <Plugin>caching_sha2_password</Plugin>
      <RoleGrants>工作人员
</RoleGrants>
    </user>
    <user id="317" parent="1" name="java">
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="318" parent="1" name="java">
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="319" parent="1" name="mysql.infoschema">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="320" parent="1" name="mysql.session">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="321" parent="1" name="mysql.sys">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="322" parent="1" name="root">
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="323" parent="1" name="user_11">
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="324" parent="1" name="user_12">
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="325" parent="1" name="user_13">
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="326" parent="1" name="user_14">
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="327" parent="1" name="保险销售">
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="328" parent="1" name="工作人员">
      <CanLogin>0</CanLogin>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="329" parent="1" name="普通客户">
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="330" parent="1" name="理财经理">
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="331" parent="1" name="管理人员">
      <CanLogin>0</CanLogin>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="332" parent="1" name="管理员">
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="333" parent="1" name="维修人员">
      <CanLogin>0</CanLogin>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="334" parent="1" name="银行柜员">
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="335" parent="1" name="驾驶人员">
      <CanLogin>0</CanLogin>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <table id="336" parent="297" name="addresses">
      <Comment>地址表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="337" parent="297" name="couriers">
      <Comment>配送员表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="338" parent="297" name="delivery_tasks">
      <Comment>配送任务表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="339" parent="297" name="email_templates">
      <Comment>邮件模板表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="340" parent="297" name="logistics_stations">
      <Comment>物流网点表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="341" parent="297" name="logistics_tracking">
      <Comment>物流轨迹表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="342" parent="297" name="orders">
      <Comment>订单表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="343" parent="297" name="permissions">
      <Comment>权限表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="344" parent="297" name="role_permissions">
      <Comment>角色权限关联表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="345" parent="297" name="roles">
      <Comment>角色表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="346" parent="297" name="system_config">
      <Comment>系统配置表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="347" parent="297" name="user_roles">
      <Comment>用户角色关联表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="348" parent="297" name="users">
      <Comment>用户表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <column id="349" parent="336" name="id">
      <AutoIncrement>5</AutoIncrement>
      <Comment>地址ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="350" parent="336" name="user_id">
      <Comment>用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="351" parent="336" name="contact_name">
      <Comment>联系人姓名</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="352" parent="336" name="contact_phone">
      <Comment>联系人电话</Comment>
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="353" parent="336" name="province">
      <Comment>省份</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="354" parent="336" name="city">
      <Comment>城市</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="355" parent="336" name="district">
      <Comment>区县</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="356" parent="336" name="detailed_address">
      <Comment>详细地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="357" parent="336" name="postal_code">
      <Comment>邮政编码</Comment>
      <DasType>varchar(10)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="358" parent="336" name="longitude">
      <Comment>经度</Comment>
      <DasType>decimal(10,6 digit)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="359" parent="336" name="latitude">
      <Comment>纬度</Comment>
      <DasType>decimal(10,6 digit)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="360" parent="336" name="is_default">
      <Comment>是否默认地址：1-是，0-否</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>12</Position>
    </column>
    <column id="361" parent="336" name="address_type">
      <Comment>地址类型：HOME-家庭，OFFICE-办公室</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;HOME&apos;</DefaultExpression>
      <Position>13</Position>
    </column>
    <column id="362" parent="336" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
    </column>
    <column id="363" parent="336" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>15</Position>
    </column>
    <index id="364" parent="336" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="365" parent="336" name="idx_user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="366" parent="336" name="idx_city">
      <ColNames>city</ColNames>
      <Type>btree</Type>
    </index>
    <key id="367" parent="336" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="368" parent="337" name="id">
      <AutoIncrement>3</AutoIncrement>
      <Comment>配送员ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="369" parent="337" name="user_id">
      <Comment>用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="370" parent="337" name="courier_name">
      <Comment>配送员姓名</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="371" parent="337" name="phone">
      <Comment>手机号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="372" parent="337" name="email">
      <Comment>邮箱</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="373" parent="337" name="id_card">
      <Comment>身份证号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="374" parent="337" name="vehicle_type">
      <Comment>车辆类型</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="375" parent="337" name="vehicle_number">
      <Comment>车牌号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="376" parent="337" name="service_area">
      <Comment>服务区域</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="377" parent="337" name="status">
      <Comment>状态：AVAILABLE-可用，BUSY-忙碌，OFFLINE-离线</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;AVAILABLE&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="378" parent="337" name="rating">
      <Comment>评分</Comment>
      <DasType>decimal(3,2 digit)|0s</DasType>
      <DefaultExpression>5.00</DefaultExpression>
      <Position>11</Position>
    </column>
    <column id="379" parent="337" name="total_orders">
      <Comment>总订单数</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>12</Position>
    </column>
    <column id="380" parent="337" name="current_location">
      <Comment>当前位置</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="381" parent="337" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
    </column>
    <column id="382" parent="337" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>15</Position>
    </column>
    <index id="383" parent="337" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="384" parent="337" name="uk_user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="385" parent="337" name="idx_service_area">
      <ColNames>service_area</ColNames>
      <Type>btree</Type>
    </index>
    <index id="386" parent="337" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <key id="387" parent="337" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="388" parent="337" name="uk_user_id">
      <UnderlyingIndexName>uk_user_id</UnderlyingIndexName>
    </key>
    <column id="389" parent="338" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>任务ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="390" parent="338" name="order_number">
      <Comment>订单号</Comment>
      <DasType>varchar(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="391" parent="338" name="order_id">
      <Comment>订单ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="392" parent="338" name="courier_id">
      <Comment>配送员ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="393" parent="338" name="task_type">
      <Comment>任务类型：PICKUP-揽件，DELIVERY-派送</Comment>
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="394" parent="338" name="pickup_address">
      <Comment>揽件地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="395" parent="338" name="delivery_address">
      <Comment>派送地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="396" parent="338" name="contact_name">
      <Comment>联系人姓名</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="397" parent="338" name="contact_phone">
      <Comment>联系人电话</Comment>
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="398" parent="338" name="estimated_time">
      <Comment>预计时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="399" parent="338" name="actual_start_time">
      <Comment>实际开始时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="400" parent="338" name="actual_end_time">
      <Comment>实际结束时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="401" parent="338" name="task_status">
      <Comment>任务状态</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;ASSIGNED&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
    </column>
    <column id="402" parent="338" name="priority">
      <Comment>优先级：1-普通，2-紧急，3-特急</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>14</Position>
    </column>
    <column id="403" parent="338" name="remarks">
      <Comment>备注</Comment>
      <DasType>text|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="404" parent="338" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
    </column>
    <column id="405" parent="338" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>17</Position>
    </column>
    <index id="406" parent="338" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="407" parent="338" name="idx_order_number">
      <ColNames>order_number</ColNames>
      <Type>btree</Type>
    </index>
    <index id="408" parent="338" name="idx_order_id">
      <ColNames>order_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="409" parent="338" name="idx_courier_id">
      <ColNames>courier_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="410" parent="338" name="idx_task_status">
      <ColNames>task_status</ColNames>
      <Type>btree</Type>
    </index>
    <key id="411" parent="338" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="412" parent="339" name="id">
      <AutoIncrement>5</AutoIncrement>
      <Comment>模板ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="413" parent="339" name="template_code">
      <Comment>模板编码</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="414" parent="339" name="template_name">
      <Comment>模板名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="415" parent="339" name="subject">
      <Comment>邮件主题</Comment>
      <DasType>varchar(200)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="416" parent="339" name="content">
      <Comment>邮件内容</Comment>
      <DasType>text|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="417" parent="339" name="template_type">
      <Comment>模板类型</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;BUSINESS&apos;</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="418" parent="339" name="is_enabled">
      <Comment>是否启用</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="419" parent="339" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="420" parent="339" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>9</Position>
    </column>
    <index id="421" parent="339" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="422" parent="339" name="uk_template_code">
      <ColNames>template_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="423" parent="339" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="424" parent="339" name="uk_template_code">
      <UnderlyingIndexName>uk_template_code</UnderlyingIndexName>
    </key>
    <column id="425" parent="340" name="id">
      <AutoIncrement>11</AutoIncrement>
      <Comment>网点ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="426" parent="340" name="station_code">
      <Comment>网点编码</Comment>
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="427" parent="340" name="station_name">
      <Comment>网点名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="428" parent="340" name="station_type">
      <Comment>网点类型：PICKUP-揽件点，TRANSIT-中转站，DELIVERY-派送点</Comment>
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="429" parent="340" name="province">
      <Comment>省份</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="430" parent="340" name="city">
      <Comment>城市</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="431" parent="340" name="district">
      <Comment>区县</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="432" parent="340" name="detailed_address">
      <Comment>详细地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="433" parent="340" name="longitude">
      <Comment>经度</Comment>
      <DasType>decimal(10,6 digit)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="434" parent="340" name="latitude">
      <Comment>纬度</Comment>
      <DasType>decimal(10,6 digit)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="435" parent="340" name="contact_person">
      <Comment>联系人</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="436" parent="340" name="contact_phone">
      <Comment>联系电话</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="437" parent="340" name="business_hours">
      <Comment>营业时间</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="438" parent="340" name="service_scope">
      <Comment>服务范围</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="439" parent="340" name="capacity">
      <Comment>处理能力</Comment>
      <DasType>int|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="440" parent="340" name="status">
      <Comment>状态：1-营业，0-停业</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
    </column>
    <column id="441" parent="340" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>17</Position>
    </column>
    <column id="442" parent="340" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>18</Position>
    </column>
    <index id="443" parent="340" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="444" parent="340" name="uk_station_code">
      <ColNames>station_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="445" parent="340" name="idx_station_type">
      <ColNames>station_type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="446" parent="340" name="idx_city">
      <ColNames>city</ColNames>
      <Type>btree</Type>
    </index>
    <index id="447" parent="340" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <key id="448" parent="340" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="449" parent="340" name="uk_station_code">
      <UnderlyingIndexName>uk_station_code</UnderlyingIndexName>
    </key>
    <column id="450" parent="341" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>轨迹ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="451" parent="341" name="order_number">
      <Comment>订单号</Comment>
      <DasType>varchar(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="452" parent="341" name="order_id">
      <Comment>订单ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="453" parent="341" name="tracking_status">
      <Comment>轨迹状态</Comment>
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="454" parent="341" name="description">
      <Comment>状态描述</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="455" parent="341" name="location">
      <Comment>位置信息</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="456" parent="341" name="longitude">
      <Comment>经度</Comment>
      <DasType>decimal(10,6 digit)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="457" parent="341" name="latitude">
      <Comment>纬度</Comment>
      <DasType>decimal(10,6 digit)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="458" parent="341" name="operator_name">
      <Comment>操作员姓名</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="459" parent="341" name="operator_type">
      <Comment>操作员类型</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="460" parent="341" name="tracking_time">
      <Comment>轨迹时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="461" parent="341" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <column id="462" parent="341" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>13</Position>
    </column>
    <index id="463" parent="341" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="464" parent="341" name="idx_order_number">
      <ColNames>order_number</ColNames>
      <Type>btree</Type>
    </index>
    <index id="465" parent="341" name="idx_order_id">
      <ColNames>order_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="466" parent="341" name="idx_tracking_status">
      <ColNames>tracking_status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="467" parent="341" name="idx_tracking_time">
      <ColNames>tracking_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="468" parent="341" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="469" parent="342" name="id">
      <AutoIncrement>4</AutoIncrement>
      <Comment>订单ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="470" parent="342" name="order_number">
      <Comment>订单号</Comment>
      <DasType>varchar(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="471" parent="342" name="customer_id">
      <Comment>客户ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="472" parent="342" name="sender_name">
      <Comment>寄件人姓名</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="473" parent="342" name="sender_phone">
      <Comment>寄件人电话</Comment>
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="474" parent="342" name="sender_address">
      <Comment>寄件人地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="475" parent="342" name="receiver_name">
      <Comment>收件人姓名</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="476" parent="342" name="receiver_phone">
      <Comment>收件人电话</Comment>
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="477" parent="342" name="receiver_address">
      <Comment>收件人地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="478" parent="342" name="item_name">
      <Comment>物品名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="479" parent="342" name="item_weight">
      <Comment>物品重量(kg)</Comment>
      <DasType>decimal(10,2 digit)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="480" parent="342" name="item_value">
      <Comment>物品价值(元)</Comment>
      <DasType>decimal(10,2 digit)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="481" parent="342" name="service_type">
      <Comment>服务类型：STANDARD-标准，EXPRESS-快递</Comment>
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>13</Position>
    </column>
    <column id="482" parent="342" name="total_fee">
      <Comment>总费用(元)</Comment>
      <DasType>decimal(10,2 digit)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>14</Position>
    </column>
    <column id="483" parent="342" name="payment_status">
      <Comment>支付状态：0-未支付，1-已支付</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
    </column>
    <column id="484" parent="342" name="order_status">
      <Comment>订单状态</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;PENDING&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
    </column>
    <column id="485" parent="342" name="remarks">
      <Comment>备注</Comment>
      <DasType>text|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="486" parent="342" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>18</Position>
    </column>
    <column id="487" parent="342" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>19</Position>
    </column>
    <index id="488" parent="342" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="489" parent="342" name="uk_order_number">
      <ColNames>order_number</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="490" parent="342" name="idx_customer_id">
      <ColNames>customer_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="491" parent="342" name="idx_order_status">
      <ColNames>order_status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="492" parent="342" name="idx_create_time">
      <ColNames>create_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="493" parent="342" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="494" parent="342" name="uk_order_number">
      <UnderlyingIndexName>uk_order_number</UnderlyingIndexName>
    </key>
    <column id="495" parent="343" name="id">
      <AutoIncrement>33</AutoIncrement>
      <Comment>权限ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="496" parent="343" name="permission_name">
      <Comment>权限名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="497" parent="343" name="permission_code">
      <Comment>权限编码</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="498" parent="343" name="resource_type">
      <Comment>资源类型：MENU-菜单，BUTTON-按钮，API-接口</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;MENU&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="499" parent="343" name="resource_url">
      <Comment>资源URL</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="500" parent="343" name="parent_id">
      <Comment>父权限ID</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="501" parent="343" name="sort_order">
      <Comment>排序</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="502" parent="343" name="status">
      <Comment>状态：ACTIVE-启用，INACTIVE-禁用</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;ACTIVE&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="503" parent="343" name="description">
      <Comment>权限描述</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="504" parent="343" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="505" parent="343" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>11</Position>
    </column>
    <index id="506" parent="343" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="507" parent="343" name="uk_permission_code">
      <ColNames>permission_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="508" parent="343" name="idx_parent_id">
      <ColNames>parent_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="509" parent="343" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <key id="510" parent="343" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="511" parent="343" name="uk_permission_code">
      <UnderlyingIndexName>uk_permission_code</UnderlyingIndexName>
    </key>
    <column id="512" parent="344" name="id">
      <AutoIncrement>37</AutoIncrement>
      <Comment>ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="513" parent="344" name="role_id">
      <Comment>角色ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="514" parent="344" name="permission_id">
      <Comment>权限ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="515" parent="344" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <index id="516" parent="344" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="517" parent="344" name="uk_role_permission">
      <ColNames>role_id
permission_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="518" parent="344" name="idx_role_id">
      <ColNames>role_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="519" parent="344" name="idx_permission_id">
      <ColNames>permission_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="520" parent="344" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="521" parent="344" name="uk_role_permission">
      <UnderlyingIndexName>uk_role_permission</UnderlyingIndexName>
    </key>
    <column id="522" parent="345" name="id">
      <AutoIncrement>5</AutoIncrement>
      <Comment>角色ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="523" parent="345" name="role_name">
      <Comment>角色名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="524" parent="345" name="role_code">
      <Comment>角色编码</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="525" parent="345" name="description">
      <Comment>角色描述</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="526" parent="345" name="status">
      <Comment>状态：1-启用，0-禁用</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="527" parent="345" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="528" parent="345" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>7</Position>
    </column>
    <index id="529" parent="345" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="530" parent="345" name="uk_role_code">
      <ColNames>role_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="531" parent="345" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <key id="532" parent="345" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="533" parent="345" name="uk_role_code">
      <UnderlyingIndexName>uk_role_code</UnderlyingIndexName>
    </key>
    <column id="534" parent="346" name="id">
      <AutoIncrement>8</AutoIncrement>
      <Comment>配置ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="535" parent="346" name="config_key">
      <Comment>配置键</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="536" parent="346" name="config_value">
      <Comment>配置值</Comment>
      <DasType>text|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="537" parent="346" name="config_type">
      <Comment>配置类型</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;STRING&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="538" parent="346" name="description">
      <Comment>配置描述</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="539" parent="346" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="540" parent="346" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>7</Position>
    </column>
    <index id="541" parent="346" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="542" parent="346" name="uk_config_key">
      <ColNames>config_key</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="543" parent="346" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="544" parent="346" name="uk_config_key">
      <UnderlyingIndexName>uk_config_key</UnderlyingIndexName>
    </key>
    <column id="545" parent="347" name="id">
      <AutoIncrement>7</AutoIncrement>
      <Comment>ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="546" parent="347" name="user_id">
      <Comment>用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="547" parent="347" name="role_id">
      <Comment>角色ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="548" parent="347" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <index id="549" parent="347" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="550" parent="347" name="uk_user_role">
      <ColNames>user_id
role_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="551" parent="347" name="idx_user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="552" parent="347" name="idx_role_id">
      <ColNames>role_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="553" parent="347" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="554" parent="347" name="uk_user_role">
      <UnderlyingIndexName>uk_user_role</UnderlyingIndexName>
    </key>
    <column id="555" parent="348" name="id">
      <AutoIncrement>7</AutoIncrement>
      <Comment>用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="556" parent="348" name="username">
      <Comment>用户名</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="557" parent="348" name="password">
      <Comment>密码</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="558" parent="348" name="real_name">
      <Comment>真实姓名</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="559" parent="348" name="phone">
      <Comment>手机号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="560" parent="348" name="email">
      <Comment>邮箱</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="561" parent="348" name="id_card">
      <Comment>身份证号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="562" parent="348" name="user_type">
      <Comment>用户类型：CUSTOMER-普通用户，OPERATOR-操作员，COURIER-配送员，ADMIN-管理员</Comment>
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="563" parent="348" name="status">
      <Comment>用户状态：ACTIVE-活跃，INACTIVE-非活跃，LOCKED-锁定</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;ACTIVE&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="564" parent="348" name="avatar">
      <Comment>头像URL</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="565" parent="348" name="last_login_time">
      <Comment>最后登录时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="566" parent="348" name="last_login_ip">
      <Comment>最后登录IP</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="567" parent="348" name="gender">
      <Comment>性别：1-男，2-女</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="568" parent="348" name="birthday">
      <Comment>生日</Comment>
      <DasType>date|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="569" parent="348" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
    </column>
    <column id="570" parent="348" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>16</Position>
    </column>
    <index id="571" parent="348" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="572" parent="348" name="uk_username">
      <ColNames>username</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="573" parent="348" name="uk_phone">
      <ColNames>phone</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="574" parent="348" name="uk_email">
      <ColNames>email</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="575" parent="348" name="idx_user_type">
      <ColNames>user_type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="576" parent="348" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="577" parent="348" name="idx_create_time">
      <ColNames>create_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="578" parent="348" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="579" parent="348" name="uk_username">
      <UnderlyingIndexName>uk_username</UnderlyingIndexName>
    </key>
    <key id="580" parent="348" name="uk_phone">
      <UnderlyingIndexName>uk_phone</UnderlyingIndexName>
    </key>
    <key id="581" parent="348" name="uk_email">
      <UnderlyingIndexName>uk_email</UnderlyingIndexName>
    </key>
  </database-model>
</dataSource>