import { orderApi } from '@/api/order'
import { trackingApi } from '@/api/tracking'
import { ElMessage } from 'element-plus'
import { http } from '@/utils/http'

/**
 * 订单生命周期管理工具
 * 确保订单在每个阶段都正确创建相应的数据
 */
export class OrderLifecycleManager {
  
  /**
   * 创建订单后的处理
   * 1. 创建订单
   * 2. 创建物流轨迹
   * 3. 添加初始轨迹节点
   */
  static async createOrderWithTracking(orderData: any) {
    try {
      console.log('开始创建订单并初始化轨迹...')
      
      // 1. 创建订单
      const orderResponse = await orderApi.createOrder(orderData)
      if (orderResponse.code !== 200) {
        throw new Error(orderResponse.message || '创建订单失败')
      }
      
      const order = orderResponse.data
      console.log('订单创建成功:', order)
      
      // 2. 创建物流轨迹
      try {
        await trackingApi.createTracking(order.orderNumber, order.id)
        console.log('物流轨迹创建成功')
      } catch (trackingError) {
        console.warn('创建物流轨迹失败，但订单创建成功:', trackingError)
      }
      
      return order
    } catch (error) {
      console.error('创建订单失败:', error)
      throw error
    }
  }
  
  /**
   * 支付订单后的处理
   * 1. 更新订单支付状态
   * 2. 添加支付轨迹节点
   */
  static async payOrderWithTracking(orderId: number, paymentMethod: string) {
    try {
      console.log('开始处理订单支付...')
      
      // 1. 支付订单
      const payResponse = await orderApi.payOrder(orderId, paymentMethod)
      if (payResponse.code !== 200) {
        throw new Error(payResponse.message || '支付失败')
      }
      
      // 2. 获取订单信息
      const orderResponse = await orderApi.getOrderById(orderId)
      if (orderResponse.code === 200) {
        const order = orderResponse.data
        
        // 3. 更新轨迹状态
        try {
          // 这里需要先根据订单号获取trackingId
          const trackingResponse = await trackingApi.getTrackingByOrderNumber(order.orderNumber)
          if (trackingResponse.code === 200) {
            await trackingApi.updateTrackingStatus(trackingResponse.data.orderNumber, {
              status: 'PAID',
              description: `订单支付成功，支付方式：${paymentMethod}`,
              operatorName: '客户'
            })
          }
        } catch (trackingError) {
          console.warn('更新支付轨迹失败:', trackingError)
        }
      }
      
      return payResponse
    } catch (error) {
      console.error('支付订单失败:', error)
      throw error
    }
  }
  
  /**
   * 分配配送员后的处理
   * 1. 更新订单配送员信息
   * 2. 创建配送任务
   * 3. 更新轨迹状态
   */
  static async assignCourierWithTracking(orderId: number, courierId: number, notes?: string) {
    try {
      console.log('开始分配配送员...')
      
      // 1. 分配配送员
      const assignResponse = await orderApi.assignDeliveryCourier(orderId, courierId)
      if (assignResponse.code !== 200) {
        throw new Error(assignResponse.message || '分配配送员失败')
      }
      
      // 2. 获取订单信息
      const orderResponse = await orderApi.getOrderById(orderId)
      if (orderResponse.code === 200) {
        const order = orderResponse.data
        
        // 3. 更新轨迹状态
        try {
          const trackingResponse = await trackingApi.getTrackingByOrderNumber(order.orderNumber)
          if (trackingResponse.code === 200) {
            await trackingApi.updateTrackingStatus(trackingResponse.data.orderNumber, {
              status: 'PICKUP_ASSIGNED',
              description: `订单已分配给配送员，配送员ID：${courierId}`,
              operatorName: '操作员'
            })
          }
        } catch (trackingError) {
          console.warn('更新分配轨迹失败:', trackingError)
        }
      }
      
      return assignResponse
    } catch (error) {
      console.error('分配配送员失败:', error)
      throw error
    }
  }
  
  /**
   * 配送员接受任务后的处理
   */
  static async acceptTaskWithTracking(taskId: number, orderNumber: string) {
    try {
      console.log('配送员接受任务...')
      
      // 更新轨迹状态
      try {
        await trackingApi.updateTrackingStatus(orderNumber, {
          status: 'PICKUP',
          description: '配送员已接受任务，准备开始配送',
          operatorName: '配送员'
        })
      } catch (trackingError) {
        console.warn('更新接受任务轨迹失败:', trackingError)
      }
    } catch (error) {
      console.error('接受任务失败:', error)
      throw error
    }
  }
  
  /**
   * 开始配送后的处理
   */
  static async startDeliveryWithTracking(taskId: number, orderNumber: string) {
    try {
      console.log('开始配送...')
      
      // 更新轨迹状态
      try {
        await trackingApi.updateTrackingStatus(orderNumber, {
          status: 'IN_TRANSIT',
          description: '配送员已开始配送，货物运输中',
          operatorName: '配送员'
        })
      } catch (trackingError) {
        console.warn('更新开始配送轨迹失败:', trackingError)
      }
    } catch (error) {
      console.error('开始配送失败:', error)
      throw error
    }
  }
  
  /**
   * 完成配送后的处理
   */
  static async completeDeliveryWithTracking(taskId: number, orderNumber: string, completionProof: string) {
    try {
      console.log('完成配送...')
      
      // 更新轨迹状态
      try {
        await trackingApi.updateTrackingStatus(orderNumber, {
          status: 'DELIVERED',
          description: '配送完成，客户已签收',
          operatorName: '配送员'
        })
      } catch (trackingError) {
        console.warn('更新完成配送轨迹失败:', trackingError)
      }
    } catch (error) {
      console.error('完成配送失败:', error)
      throw error
    }
  }
  
  /**
   * 获取订单完整状态
   */
  static async getOrderFullStatus(orderNumber: string) {
    try {
      // 并行获取订单信息和轨迹信息
      const [orderResponse, trackingResponse] = await Promise.allSettled([
        orderApi.getOrderByNumber(orderNumber),
        trackingApi.getTrackingByOrderNumber(orderNumber)
      ])
      
      let order = null
      let tracking = null
      
      if (orderResponse.status === 'fulfilled' && orderResponse.value.code === 200) {
        order = orderResponse.value.data
      }
      
      if (trackingResponse.status === 'fulfilled' && trackingResponse.value.code === 200) {
        tracking = trackingResponse.value.data
      }
      
      return {
        order,
        tracking,
        hasOrder: !!order,
        hasTracking: !!tracking
      }
    } catch (error) {
      console.error('获取订单完整状态失败:', error)
      return {
        order: null,
        tracking: null,
        hasOrder: false,
        hasTracking: false
      }
    }
  }
}

// 完整的订单分配流程
export async function assignOrderToCourier(orderId: number, courierId: number): Promise<boolean> {
  console.log(`🚀 开始完整的订单分配流程`)
  console.log(`📋 分配参数:`, { orderId, courierId })
  
  try {
    // 1. 分配订单给配送员
    console.log('📝 第1步：分配订单给配送员')
    const assignResponse = await http.put(`/order/${orderId}/assign-delivery`, null, {
      params: { courierId }
    })
    
    console.log('✅ 分配响应:', assignResponse)
    
    if (!assignResponse || (assignResponse.code !== 200 && assignResponse.status !== 200)) {
      throw new Error('订单分配失败')
    }
    
    // 2. 创建配送任务（如果后端支持）
    console.log('📝 第2步：创建配送任务')
    try {
      await http.post('/delivery/task/create-from-order', {
        orderId,
        courierId,
        taskType: 'DELIVERY'
      })
      console.log('✅ 配送任务创建成功')
    } catch (taskError) {
      console.warn('⚠️ 配送任务创建失败，但订单分配成功:', taskError)
      // 不抛出错误，因为主要的分配已经成功
    }
    
    // 3. 更新轨迹状态（如果后端支持）
    console.log('📝 第3步：更新轨迹状态')
    try {
      await http.post('/logistics/tracking/update-status', {
        orderId,
        status: 'PICKUP_ASSIGNED',
        description: `订单已分配给配送员，配送员ID: ${courierId}`,
        operatorName: '系统操作员'
      })
      console.log('✅ 轨迹状态更新成功')
    } catch (trackingError) {
      console.warn('⚠️ 轨迹状态更新失败，但订单分配成功:', trackingError)
      // 不抛出错误，因为主要的分配已经成功
    }
    
    console.log('🎉 订单分配流程完成')
    return true
    
  } catch (error: any) {
    console.error('❌ 订单分配流程失败:', error)
    console.error('❌ 错误详情:', {
      orderId,
      courierId,
      error: error.message,
      response: error.response?.data
    })
    throw error
  }
} 