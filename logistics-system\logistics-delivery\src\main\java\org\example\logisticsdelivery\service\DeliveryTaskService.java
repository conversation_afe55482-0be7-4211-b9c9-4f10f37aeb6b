package org.example.logisticsdelivery.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.example.logisticsdelivery.entity.DeliveryTask;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 配送任务服务接口
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
public interface DeliveryTaskService {

    /**
     * 创建配送任务
     */
    DeliveryTask createTask(DeliveryTask task);

    /**
     * 更新配送任务
     */
    DeliveryTask updateTask(DeliveryTask task);

    /**
     * 根据ID查询任务
     */
    DeliveryTask getTaskById(Long id);

    /**
     * 根据任务编号查询
     */
    DeliveryTask getTaskByNumber(String taskNumber);

    /**
     * 根据订单号查询任务
     */
    DeliveryTask getTaskByOrderNumber(String orderNumber);

    /**
     * 根据配送员ID查询任务列表
     */
    List<DeliveryTask> getTasksByCourierId(Long courierId);

    /**
     * 根据任务状态查询任务列表
     */
    List<DeliveryTask> getTasksByStatus(String taskStatus);

    /**
     * 查询配送员当日任务
     */
    List<DeliveryTask> getCourierDailyTasks(Long courierId, LocalDate taskDate);

    /**
     * 查询配送员进行中的任务
     */
    List<DeliveryTask> getCourierActiveTasks(Long courierId);

    /**
     * 查询待分配的任务
     */
    List<DeliveryTask> getPendingTasks(String taskType, String workArea);

    /**
     * 查询超时的任务
     */
    List<DeliveryTask> getTimeoutTasks(Integer timeoutMinutes);

    /**
     * 分配任务给配送员
     */
    boolean assignTask(Long taskId, Long courierId);

    /**
     * 批量分配任务
     */
    boolean batchAssignTasks(List<Long> taskIds, Long courierId);

    /**
     * 更新任务状态
     */
    boolean updateTaskStatus(Long taskId, String taskStatus);

    /**
     * 批量更新任务状态
     */
    boolean batchUpdateTaskStatus(List<Long> taskIds, String taskStatus);

    /**
     * 完成任务
     */
    boolean completeTask(Long taskId);

    /**
     * 取消任务
     */
    boolean cancelTask(Long taskId, String reason);

    /**
     * 分页查询任务
     */
    IPage<DeliveryTask> getTasksPage(Page<DeliveryTask> page, Long courierId,
                                     String taskStatus, String taskType,
                                     LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取任务统计数据
     */
    Map<String, Object> getTaskStatistics(Long courierId, LocalDate startDate, LocalDate endDate);

    /**
     * 统计各状态任务数量
     */
    Map<String, Long> getStatusStatistics();

    /**
     * 自动分配任务
     */
    boolean autoAssignTasks(String workArea);

    /**
     * 检查并处理超时任务
     */
    int handleTimeoutTasks();

    /**
     * 删除任务
     */
    boolean deleteTask(Long taskId);
}