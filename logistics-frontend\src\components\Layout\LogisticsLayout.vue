<template>
  <div class="logistics-layout">
    <!-- 顶部导航栏 -->
    <header class="layout-header">
      <div class="header-left">
        <div class="logo-section">
          <el-icon class="logo-icon" size="32" color="#1890ff">
            <Van />
          </el-icon>
          <div class="logo-text">
            <h1>智慧物流</h1>
            <span>Logistics System</span>
          </div>
        </div>
        
        <!-- 面包屑导航 -->
        <el-breadcrumb class="breadcrumb" separator="/">
          <el-breadcrumb-item 
            v-for="item in breadcrumbItems" 
            :key="item.path"
            :to="item.path"
          >
            <el-icon v-if="item.icon" class="breadcrumb-icon">
              <component :is="item.icon" />
            </el-icon>
            {{ item.title }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>

      <div class="header-right">
        <!-- 快速操作 -->
        <div class="quick-actions">
          <el-tooltip content="消息通知" placement="bottom">
            <el-badge :value="notificationCount" class="notification-badge">
              <el-button circle size="large" @click="showNotifications">
                <el-icon><Bell /></el-icon>
              </el-button>
            </el-badge>
          </el-tooltip>

          <el-tooltip content="系统设置" placement="bottom">
            <el-button circle size="large" @click="showSettings">
              <el-icon><Setting /></el-icon>
            </el-button>
          </el-tooltip>

          <el-tooltip content="帮助中心" placement="bottom">
            <el-button circle size="large" @click="showHelp">
              <el-icon><QuestionFilled /></el-icon>
            </el-button>
          </el-tooltip>
        </div>

        <!-- 用户信息 -->
        <el-dropdown class="user-dropdown" @command="handleUserCommand">
          <div class="user-info">
            <el-avatar :size="40" :src="userInfo.avatar" class="user-avatar">
              <el-icon><User /></el-icon>
            </el-avatar>
            <div class="user-details">
              <div class="user-name">{{ userInfo.name }}</div>
              <div class="user-role">{{ getRoleText(userInfo.role) }}</div>
            </div>
            <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">
                <el-icon><User /></el-icon>个人资料
              </el-dropdown-item>
              <el-dropdown-item command="settings">
                <el-icon><Setting /></el-icon>账户设置
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <el-icon><SwitchButton /></el-icon>退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </header>

    <!-- 主体内容区域 -->
    <div class="layout-body">
      <!-- 侧边导航栏 -->
      <aside class="layout-sidebar" :class="{ collapsed: sidebarCollapsed }">
        <div class="sidebar-header">
          <el-button 
            class="collapse-btn" 
            circle 
            size="small" 
            @click="toggleSidebar"
          >
            <el-icon>
              <component :is="sidebarCollapsed ? 'Expand' : 'Fold'" />
            </el-icon>
          </el-button>
        </div>

        <el-scrollbar class="sidebar-scrollbar">
          <el-menu
            :default-active="activeMenu"
            :collapse="sidebarCollapsed"
            :unique-opened="true"
            class="sidebar-menu"
            @select="handleMenuSelect"
          >
            <template v-for="item in menuItems" :key="item.path">
              <!-- 单级菜单 -->
              <el-menu-item 
                v-if="!item.children" 
                :index="item.path"
                class="menu-item"
              >
                <el-icon class="menu-icon">
                  <component :is="item.icon" />
                </el-icon>
                <template #title>
                  <span class="menu-title">{{ item.title }}</span>
                  <el-badge 
                    v-if="item.badge" 
                    :value="item.badge" 
                    class="menu-badge"
                  />
                </template>
              </el-menu-item>

              <!-- 多级菜单 -->
              <el-sub-menu v-else :index="item.path" class="sub-menu">
                <template #title>
                  <el-icon class="menu-icon">
                    <component :is="item.icon" />
                  </el-icon>
                  <span class="menu-title">{{ item.title }}</span>
                </template>
                <el-menu-item
                  v-for="child in item.children"
                  :key="child.path"
                  :index="child.path"
                  class="sub-menu-item"
                >
                  <el-icon class="sub-menu-icon">
                    <component :is="child.icon" />
                  </el-icon>
                  <template #title>
                    <span class="sub-menu-title">{{ child.title }}</span>
                    <el-badge 
                      v-if="child.badge" 
                      :value="child.badge" 
                      class="menu-badge"
                    />
                  </template>
                </el-menu-item>
              </el-sub-menu>
            </template>
          </el-menu>
        </el-scrollbar>

        <!-- 侧边栏底部 -->
        <div class="sidebar-footer">
          <div class="system-status">
            <el-icon class="status-icon" color="#52c41a">
              <CircleCheckFilled />
            </el-icon>
            <span v-if="!sidebarCollapsed" class="status-text">系统正常</span>
          </div>
        </div>
      </aside>

      <!-- 主内容区域 -->
      <main class="layout-main">
        <div class="main-content">
          <router-view v-slot="{ Component }">
            <transition name="fade" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </div>

        <!-- 页脚 -->
        <footer class="layout-footer">
          <div class="footer-content">
            <div class="footer-left">
              <span>© 2024 智慧物流系统</span>
              <span>版本 v2.1.0</span>
            </div>
            <div class="footer-right">
              <a href="#" class="footer-link">隐私政策</a>
              <a href="#" class="footer-link">服务条款</a>
              <a href="#" class="footer-link">技术支持</a>
            </div>
          </div>
        </footer>
      </main>
    </div>

    <!-- 快速操作浮动按钮 -->
    <div class="fab-container">
      <el-tooltip content="快速下单" placement="left">
        <el-button 
          class="fab-button primary" 
          circle 
          size="large"
          @click="quickCreateOrder"
        >
          <el-icon><Plus /></el-icon>
        </el-button>
      </el-tooltip>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import {
  Van, Bell, Setting, QuestionFilled, User, ArrowDown, SwitchButton,
  Expand, Fold, CircleCheckFilled, Plus
} from '@element-plus/icons-vue'

// 响应式数据
const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const sidebarCollapsed = ref(false)
const notificationCount = ref(5)

// 计算属性
const userInfo = computed(() => userStore.userInfo)
const activeMenu = computed(() => route.path)

// 面包屑数据
const breadcrumbItems = computed(() => {
  // 根据当前路由生成面包屑
  return [
    { path: '/dashboard', title: '首页', icon: 'House' },
    { path: route.path, title: route.meta?.title || '当前页面' }
  ]
})

// 菜单数据（根据用户角色动态生成）
const menuItems = computed(() => {
  const role = userInfo.value?.role
  const baseMenus = [
    {
      path: '/dashboard',
      title: '工作台',
      icon: 'Monitor'
    }
  ]

  // 根据角色添加不同菜单
  if (role === 'CUSTOMER') {
    return [
      ...baseMenus,
      {
        path: '/order',
        title: '订单管理',
        icon: 'Box',
        children: [
          { path: '/order/create', title: '创建订单', icon: 'Plus' },
          { path: '/order/list', title: '我的订单', icon: 'List', badge: 3 },
          { path: '/order/tracking', title: '物流追踪', icon: 'Location' }
        ]
      },
      {
        path: '/address',
        title: '地址管理',
        icon: 'LocationInformation'
      }
    ]
  }

  // 其他角色的菜单...
  return baseMenus
})

// 方法
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

const handleMenuSelect = (index: string) => {
  router.push(index)
}

const handleUserCommand = (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      userStore.logout()
      router.push('/login')
      break
  }
}

const getRoleText = (role: string) => {
  const roleMap = {
    CUSTOMER: '客户',
    COURIER: '配送员',
    OPERATOR: '操作员',
    ADMIN: '管理员'
  }
  return roleMap[role] || '用户'
}

const showNotifications = () => {
  // 显示通知面板
}

const showSettings = () => {
  router.push('/settings')
}

const showHelp = () => {
  // 显示帮助中心
}

const quickCreateOrder = () => {
  router.push('/order/create')
}

onMounted(() => {
  // 初始化布局
})
</script>

<style scoped>
.logistics-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-secondary);
}

/* ========== 顶部导航栏 ========== */
.layout-header {
  height: 64px;
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-secondary);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-2xl);
  box-shadow: var(--shadow-sm);
  position: relative;
  z-index: var(--z-sticky);
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-3xl);
}

.logo-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.logo-icon {
  background: linear-gradient(135deg, #1890ff, #722ed1);
  border-radius: var(--radius-lg);
  padding: var(--spacing-sm);
}

.logo-text h1 {
  margin: 0;
  font-size: var(--font-xl);
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1;
}

.logo-text span {
  font-size: var(--font-xs);
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.breadcrumb {
  font-size: var(--font-sm);
}

.breadcrumb-icon {
  margin-right: var(--spacing-xs);
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.quick-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.notification-badge {
  position: relative;
}

.user-dropdown {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-lg);
  transition: background-color var(--duration-normal);
}

.user-info:hover {
  background-color: var(--bg-tertiary);
}

.user-avatar {
  border: 2px solid var(--border-secondary);
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-size: var(--font-sm);
  font-weight: 500;
  color: var(--text-primary);
  line-height: 1;
}

.user-role {
  font-size: var(--font-xs);
  color: var(--text-tertiary);
  margin-top: 2px;
}

.dropdown-icon {
  color: var(--text-tertiary);
  transition: transform var(--duration-normal);
}

.user-dropdown:hover .dropdown-icon {
  transform: rotate(180deg);
}

/* ========== 主体布局 ========== */
.layout-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* ========== 侧边栏 ========== */
.layout-sidebar {
  width: 240px;
  background: var(--bg-primary);
  border-right: 1px solid var(--border-secondary);
  display: flex;
  flex-direction: column;
  transition: width var(--duration-normal);
  position: relative;
  z-index: var(--z-sticky);
}

.layout-sidebar.collapsed {
  width: 64px;
}

.sidebar-header {
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
}

.collapse-btn {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-secondary);
}

.sidebar-scrollbar {
  flex: 1;
}

.sidebar-menu {
  border: none;
  background: transparent;
}

.menu-item, .sub-menu-item {
  margin: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-md);
  transition: all var(--duration-normal);
}

.menu-item:hover, .sub-menu-item:hover {
  background-color: var(--bg-tertiary);
}

.menu-item.is-active, .sub-menu-item.is-active {
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.1), rgba(114, 46, 209, 0.1));
  color: var(--primary-color);
  border: 1px solid rgba(24, 144, 255, 0.2);
}

.menu-icon, .sub-menu-icon {
  margin-right: var(--spacing-sm);
  font-size: var(--font-lg);
}

.menu-title, .sub-menu-title {
  font-weight: 500;
}

.menu-badge {
  margin-left: auto;
}

.sidebar-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-light);
}

.system-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-xs);
  color: var(--text-tertiary);
}

/* ========== 主内容区域 ========== */
.layout-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.main-content {
  flex: 1;
  padding: var(--spacing-2xl);
  overflow-y: auto;
  background: var(--bg-secondary);
}

/* ========== 页脚 ========== */
.layout-footer {
  background: var(--bg-primary);
  border-top: 1px solid var(--border-secondary);
  padding: var(--spacing-lg) var(--spacing-2xl);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-xs);
  color: var(--text-tertiary);
}

.footer-left {
  display: flex;
  gap: var(--spacing-lg);
}

.footer-right {
  display: flex;
  gap: var(--spacing-lg);
}

.footer-link {
  color: var(--text-tertiary);
  text-decoration: none;
  transition: color var(--duration-normal);
}

.footer-link:hover {
  color: var(--primary-color);
}

/* ========== 浮动按钮 ========== */
.fab-container {
  position: fixed;
  bottom: var(--spacing-3xl);
  right: var(--spacing-3xl);
  z-index: var(--z-fixed);
}

.fab-button {
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border: none;
  box-shadow: var(--shadow-lg);
  transition: all var(--duration-normal);
}

.fab-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

/* ========== 响应式设计 ========== */
@media (max-width: 768px) {
  .layout-header {
    padding: 0 var(--spacing-lg);
  }

  .header-left {
    gap: var(--spacing-lg);
  }

  .breadcrumb {
    display: none;
  }

  .layout-sidebar {
    position: absolute;
    left: -240px;
    height: 100%;
    z-index: var(--z-modal);
    transition: left var(--duration-normal);
  }

  .layout-sidebar.show {
    left: 0;
  }

  .main-content {
    padding: var(--spacing-lg);
  }

  .fab-container {
    bottom: var(--spacing-2xl);
    right: var(--spacing-2xl);
  }
}

@media (max-width: 480px) {
  .logo-text h1 {
    display: none;
  }

  .user-details {
    display: none;
  }

  .quick-actions {
    gap: var(--spacing-xs);
  }
}
</style>
