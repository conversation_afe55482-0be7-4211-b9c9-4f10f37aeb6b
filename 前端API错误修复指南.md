# 前端API错误修复指南

## 当前遇到的API错误

### 1. 配送员活动任务接口 (400错误)
**错误API调用：** `GET /api/delivery/task/active`
**问题：** 前端调用的路径与后端不匹配

**解决方案：**
后端已有接口：`GET /delivery/task/courier/{courierId}/active`
前端需要传递配送员ID参数

**修复建议：**
在`DeliveryTaskController`中添加一个通用的获取当前用户活动任务的接口：

```java
/**
 * 获取当前配送员的活动任务
 */
@GetMapping("/active")
public Result<List<DeliveryTask>> getCurrentCourierActiveTasks(HttpServletRequest request) {
    try {
        // 从token中获取用户ID
        Long userId = getCurrentUserId(request);
        
        // 根据用户ID查找配送员ID
        Courier courier = courierService.getCourierByUserId(userId);
        if (courier == null) {
            return Result.error("配送员信息不存在");
        }
        
        List<DeliveryTask> tasks = taskService.getCourierActiveTasks(courier.getId());
        return Result.success(tasks);
    } catch (Exception e) {
        log.error("获取当前配送员活动任务失败", e);
        return Result.error("获取活动任务失败: " + e.getMessage());
    }
}
```

### 2. 配送员统计接口 (400错误)
**错误API调用：** `GET /api/delivery/task/courier/statistics`
**问题：** 路径不正确

**解决方案：**
后端已有接口：`GET /delivery/task/statistics?courierId={courierId}`
但前端希望有一个不需要传参的当前用户统计接口

**修复建议：**
在`DeliveryTaskController`中添加：

```java
/**
 * 获取当前配送员的任务统计
 */
@GetMapping("/courier/statistics")
public Result<Map<String, Object>> getCurrentCourierStatistics(HttpServletRequest request) {
    try {
        // 从token中获取用户ID
        Long userId = getCurrentUserId(request);
        
        // 根据用户ID查找配送员ID
        Courier courier = courierService.getCourierByUserId(userId);
        if (courier == null) {
            return Result.error("配送员信息不存在");
        }
        
        Map<String, Object> statistics = taskService.getTaskStatistics(courier.getId(), null, null);
        return Result.success(statistics);
    } catch (Exception e) {
        log.error("获取当前配送员统计失败", e);
        return Result.error("获取统计数据失败: " + e.getMessage());
    }
}
```

### 3. 配送员信息获取接口
**当前问题：** 前端通过用户ID获取配送员信息时出错

**解决方案：**
需要在`CourierController`中添加根据用户ID获取配送员信息的接口：

```java
/**
 * 根据用户ID获取配送员信息
 */
@GetMapping("/user/{userId}")
public Result<Courier> getCourierByUserId(@PathVariable Long userId) {
    try {
        Courier courier = courierService.getCourierByUserId(userId);
        if (courier != null) {
            return Result.success(courier);
        } else {
            return Result.error("配送员信息不存在");
        }
    } catch (Exception e) {
        log.error("根据用户ID获取配送员信息失败，用户ID: {}", userId, e);
        return Result.error("获取配送员信息失败: " + e.getMessage());
    }
}

/**
 * 获取当前登录配送员信息
 */
@GetMapping("/current")
public Result<Courier> getCurrentCourier(HttpServletRequest request) {
    try {
        Long userId = getCurrentUserId(request);
        Courier courier = courierService.getCourierByUserId(userId);
        if (courier != null) {
            return Result.success(courier);
        } else {
            return Result.error("配送员信息不存在");
        }
    } catch (Exception e) {
        log.error("获取当前配送员信息失败", e);
        return Result.error("获取配送员信息失败: " + e.getMessage());
    }
}
```

## 需要在后端Service中添加的方法

### 1. CourierService中添加
```java
/**
 * 根据用户ID获取配送员信息
 */
Courier getCourierByUserId(Long userId);
```

### 2. CourierServiceImpl中实现
```java
@Override
public Courier getCourierByUserId(Long userId) {
    LambdaQueryWrapper<Courier> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(Courier::getUserId, userId);
    return courierMapper.selectOne(wrapper);
}
```

## 前端修复建议

### 1. 修复courier.ts中的API调用
```typescript
// 获取配送员信息 - 使用新的接口
getCourierInfo(): Promise<ApiResponse<CourierInfo>> {
  return http.get('/delivery/courier/current')
},

// 获取进行中的任务 - 使用新的接口
getActiveTasks(): Promise<ApiResponse<DeliveryTask[]>> {
  return http.get('/delivery/task/active')
},

// 获取任务统计 - 使用新的接口
getTaskStatistics(): Promise<ApiResponse<CourierStats>> {
  return http.get('/delivery/task/courier/statistics').then((response) => {
    if (response.code === 200) {
      const stats = response.data
      return {
        code: 200,
        message: '获取统计数据成功',
        data: {
          todayTasks: stats.totalTasks || 0,
          completedTasks: stats.completedTasks || 0,
          pendingTasks: stats.activeTasks || 0,
          todayCompleted: stats.completedTasks || 0,
          avgRating: 5.0,
          todayEarnings: stats.totalDeliveryFee || 0,
          totalEarnings: stats.totalDeliveryFee || 0,
          onlineHours: 8,
          thisMonthTasks: stats.totalTasks || 0,
          thisMonthEarnings: stats.totalDeliveryFee || 0,
        },
      } as ApiResponse<CourierStats>
    }
    throw new Error('获取统计数据失败')
  })
},
```

## 工具方法：从Token获取用户ID

在Controller基类或工具类中添加：

```java
/**
 * 从请求中获取当前用户ID
 */
protected Long getCurrentUserId(HttpServletRequest request) {
    String token = request.getHeader("Authorization");
    if (token != null && token.startsWith("Bearer ")) {
        token = token.substring(7);
        // 解析JWT token获取用户ID
        // 这里需要根据你的JWT工具类实现
        return JwtUtil.getUserIdFromToken(token);
    }
    throw new RuntimeException("未找到有效的用户令牌");
}
```

## 总结

主要问题是前端调用的API路径与后端控制器路径不匹配，以及缺少一些便捷的接口（如获取当前用户的信息而不需要传递用户ID）。

建议的修复顺序：
1. 在后端添加上述便捷接口
2. 修复前端的API调用路径
3. 确保JWT token解析正确工作
4. 测试所有API调用 