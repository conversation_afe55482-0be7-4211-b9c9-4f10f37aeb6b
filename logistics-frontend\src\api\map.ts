import http from '@/utils/http'
import type { ApiResponse } from '@/types/api'

// 地图配置接口
export interface MapConfig {
  jsApiKey: string
  provider: 'amap' | 'baidu' | 'google'
  defaultCenter: {
    lat: number
    lng: number
  }
  defaultZoom: number
  baseUrl: string
}

// 地理编码结果
export interface GeocodeResult {
  lat: number
  lng: number
  address: string
  province?: string
  city?: string
  district?: string
}

// 逆地理编码结果
export interface ReverseGeocodeResult {
  address: string
  province?: string
  city?: string
  district?: string
  street?: string
}

// 距离计算结果
export interface DistanceResult {
  straightDistance: number
  drivingDistance: number
}

// 路线规划结果
export interface RouteResult {
  distance: number
  duration: number
  polyline: string
  steps: RouteStep[]
}

export interface RouteStep {
  instruction: string
  distance: number
  duration: number
  polyline: string
}

// 省市区数据
export interface RegionData {
  districts: Province[]
}

export interface Province {
  adcode: string
  name: string
  districts: City[]
}

export interface City {
  adcode: string
  name: string
  districts: District[]
}

export interface District {
  adcode: string
  name: string
}

// 地址搜索提示
export interface AddressSearchResult {
  suggestions: AddressSuggestion[]
}

export interface AddressSuggestion {
  name: string
  address: string
  location?: {
    lat: number
    lng: number
  }
}

export const mapApi = {
  // 获取地图配置
  getConfig(): Promise<ApiResponse<MapConfig>> {
    return http.get('/map/config')
  },

  // 地址转坐标
  geocode(address: string, city?: string): Promise<ApiResponse<GeocodeResult>> {
    const params: any = { address }
    if (city) {
      params.city = city
    }
    return http.get('/map/address-to-location', { params })
  },

  // 坐标转地址
  reverseGeocode(lat: number, lng: number): Promise<ApiResponse<ReverseGeocodeResult>> {
    return http.get('/map/location-to-address', { params: { lat, lng } })
  },

  // 距离计算
  calculateDistance(
    from: { lat: number; lng: number },
    to: { lat: number; lng: number },
  ): Promise<ApiResponse<DistanceResult>> {
    return http.get('/map/distance', {
      params: {
        fromLat: from.lat,
        fromLng: from.lng,
        toLat: to.lat,
        toLng: to.lng,
      },
    })
  },

  // 路线规划
  calculateRoute(
    origin: { lat: number; lng: number },
    destination: { lat: number; lng: number },
  ): Promise<ApiResponse<RouteResult>> {
    return http.get('/map/route/best', {
      params: {
        fromLat: origin.lat,
        fromLng: origin.lng,
        toLat: destination.lat,
        toLng: destination.lng,
      },
    })
  },

  // 获取省市区数据
  getRegions(keywords?: string): Promise<ApiResponse<RegionData>> {
    const params: any = {}
    if (keywords) {
      params.keywords = keywords
    }
    return http.get('/map/regions', { params })
  },

  // 地址搜索提示
  searchAddress(keyword: string, city?: string): Promise<ApiResponse<AddressSearchResult>> {
    const params: any = { keywords: keyword }
    if (city) {
      params.city = city
    }
    return http.get('/api/map/poi/search', { params })
  },

  // 批量地址转坐标
  batchGeocode(addresses: string[]): Promise<ApiResponse<GeocodeResult[]>> {
    return http.post('/api/map/batch/address-to-location', addresses)
  },

  // 周边POI搜索
  searchAroundPOI(
    keywords: string,
    location: string,
    radius?: number,
    types?: string,
  ): Promise<ApiResponse<any>> {
    const params: any = { keywords, location }
    if (radius) params.radius = radius
    if (types) params.types = types
    return http.get('/api/map/poi/around', { params })
  },

  // 获取天气信息
  getWeather(city: string, extensions: string = 'base'): Promise<ApiResponse<any>> {
    return http.get('/api/map/weather', { params: { city, extensions } })
  },
}
