package org.example.logisticsorder.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付状态枚举
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Getter
@AllArgsConstructor
public enum PaymentStatus {

    UNPAID(0, "未支付"),
    PAID(1, "已支付"),
    REFUNDED(2, "已退款"),
    REFUNDING(3, "退款中");

    private final Integer code;
    private final String description;

    /**
     * 根据状态码获取枚举
     */
    public static PaymentStatus fromCode(Integer code) {
        for (PaymentStatus status : PaymentStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的支付状态: " + code);
    }
}
