package org.example.logisticsmap.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 路径规划请求DTO
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
public class DirectionRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 起点坐标 (格式: 经度,纬度)
     */
    @NotBlank(message = "起点坐标不能为空")
    private String origin;

    /**
     * 终点坐标 (格式: 经度,纬度)
     */
    @NotBlank(message = "终点坐标不能为空")
    private String destination;

    /**
     * 途经点坐标 (格式: 经度,纬度|经度,纬度)
     */
    private String waypoints;

    /**
     * 避让区域坐标点 (格式: 经度,纬度|经度,纬度)
     */
    private String avoidpolygons;

    /**
     * 避让道路名称
     */
    private String avoidroad;

    /**
     * 路径规划策略
     * 0: 速度优先（时间）
     * 1: 费用优先（不走收费路段的最快道路）
     * 2: 距离优先
     * 3: 不走快速路
     * 4: 躲避拥堵的路径
     * 5: 多策略（同时使用速度优先、费用优先、距离优先三个策略）
     * 6: 不走高速
     * 7: 不走高速+速度优先
     * 8: 不走高速+费用优先
     * 9: 不走高速+距离优先
     * 10: 不走高速+不走快速路
     */
    private Integer strategy = 0;

    /**
     * 返回信息详细程度
     */
    private String extensions = "all";

    /**
     * 车牌省份
     */
    private String province;

    /**
     * 车牌号码
     */
    private String number;

    /**
     * 车辆大小
     * 1: 小型汽车
     * 2: 货车
     */
    private Integer size = 1;

    /**
     * 输出格式
     */
    private String output = "json";

    /**
     * 创建驾车路径规划请求
     *
     * @param originLng 起点经度
     * @param originLat 起点纬度
     * @param destLng   终点经度
     * @param destLat   终点纬度
     * @return 请求对象
     */
    public static DirectionRequest driving(Double originLng, Double originLat, 
                                         Double destLng, Double destLat) {
        DirectionRequest request = new DirectionRequest();
        request.setOrigin(originLng + "," + originLat);
        request.setDestination(destLng + "," + destLat);
        return request;
    }

    /**
     * 创建驾车路径规划请求（带策略）
     *
     * @param originLng 起点经度
     * @param originLat 起点纬度
     * @param destLng   终点经度
     * @param destLat   终点纬度
     * @param strategy  路径策略
     * @return 请求对象
     */
    public static DirectionRequest driving(Double originLng, Double originLat, 
                                         Double destLng, Double destLat, Integer strategy) {
        DirectionRequest request = driving(originLng, originLat, destLng, destLat);
        request.setStrategy(strategy);
        return request;
    }

    /**
     * 添加途经点
     *
     * @param waypoints 途经点列表
     */
    public void addWaypoints(List<String> waypoints) {
        if (waypoints != null && !waypoints.isEmpty()) {
            this.waypoints = String.join("|", waypoints);
        }
    }

    /**
     * 添加避让区域
     *
     * @param polygons 避让区域坐标列表
     */
    public void addAvoidPolygons(List<String> polygons) {
        if (polygons != null && !polygons.isEmpty()) {
            this.avoidpolygons = String.join("|", polygons);
        }
    }
} 