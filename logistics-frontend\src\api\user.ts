import http from '@/utils/http'
import type { ApiResponse, PageResponse, PageRequest } from '@/types/api'

// 用户信息接口
export interface UserInfo {
  id: number
  username: string
  realName: string
  phone: string
  email: string
  userType: string
  status: number
  avatar?: string
  createTime: string
  updateTime: string
}

// 用户统计信息
export interface UserStats {
  totalOrders: number
  completedOrders: number
  totalSpent: number
  memberLevel: string
}

// 更新用户信息DTO
export interface UpdateUserInfoDTO {
  realName?: string
  phone?: string
  email?: string
  avatar?: string
}

// 登录DTO
export interface LoginDTO {
  username: string
  password: string
  loginType?: string
}

// 注册DTO
export interface RegisterDTO {
  username: string
  password: string
  confirmPassword: string
  realName: string
  phone: string
  email?: string
  userType: string
}

// 登录响应
export interface LoginResponse {
  token: string
  user: UserInfo
}

// 用户查询参数
export interface UserQueryParams extends PageRequest {
  userType?: string
  status?: number
  keyword?: string
}

// 用户列表项
export interface UserListItem {
  id: number
  username: string
  realName: string
  phone: string
  email: string
  userType: string
  status: number
  createTime: string
}

export const userApi = {
  // 用户登录
  login(data: LoginDTO): Promise<ApiResponse<LoginResponse>> {
    return http.post('/user/login', data)
  },

  // 用户注册
  register(data: RegisterDTO): Promise<ApiResponse<LoginResponse>> {
    return http.post('/user/register', data)
  },

  // 获取当前用户信息
  getCurrentUser(): Promise<ApiResponse<UserInfo>> {
    return http.get('/user/profile')
  },

  // 获取用户编辑信息
  getUserForEdit(): Promise<ApiResponse<UpdateUserInfoDTO>> {
    return http.get('/user/profile/edit')
  },

  // 更新用户信息
  updateUserInfo(data: UpdateUserInfoDTO): Promise<ApiResponse<UserInfo>> {
    return http.put('/user/profile', data)
  },

  // 修改密码
  changePassword(data: { oldPassword: string; newPassword: string }): Promise<ApiResponse<void>> {
    return http.post('/user/change-password', data)
  },

  // 获取用户统计（客户专用）
  getUserStats(): Promise<ApiResponse<UserStats>> {
    return http.get('/user/statistics')
  },

  // 上传头像
  uploadAvatar(file: File): Promise<ApiResponse<{ url: string }>> {
    const formData = new FormData()
    formData.append('file', file)
    return http.post('/user/upload/avatar', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    })
  },

  // 根据ID获取用户信息
  getUserById(id: number): Promise<ApiResponse<UserInfo>> {
    return http.get(`/user/profile/${id}`)
  },

  // 获取用户分页列表（管理员专用）
  getUserPage(params: UserQueryParams): Promise<ApiResponse<PageResponse<UserListItem>>> {
    return http.get('/user/page', { params })
  },

  // 更新用户状态（管理员专用）
  updateUserStatus(id: number, status: number): Promise<ApiResponse<void>> {
    return http.put(`/user/${id}/status`, { status })
  },

  // 删除用户（管理员专用）
  deleteUser(id: number): Promise<ApiResponse<void>> {
    return http.delete(`/user/${id}`)
  },
}
