package org.example.logisticsuser;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
@ComponentScan(basePackages = {
        "org.example.logisticsuser",  // 扫描本服务的包
        "com.logistics.common"        // 扫描公共模块的包
})
@MapperScan("org.example.logisticsuser.mapper")
public class LogisticsUserApplication {

    public static void main(String[] args) {
        SpringApplication.run(LogisticsUserApplication.class, args);
    }

}
