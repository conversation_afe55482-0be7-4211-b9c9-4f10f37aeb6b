-- =============================================
-- 物流系统业务流程完善 - 数据库更新脚本
-- 执行时间：2024-12-30
-- 说明：添加邮件通知模板，无需修改现有表结构
-- =============================================

-- 检查notification_templates表是否存在邮件模板
SELECT COUNT(*) as template_count FROM notification_templates WHERE notification_type = 'EMAIL';

-- 如果没有邮件模板，则插入以下模板数据
-- 1. 订单创建通知模板
INSERT INTO `notification_templates` (`template_code`, `template_name`, `template_type`, `notification_type`, `title`, `content`, `is_enabled`, `sort_order`, `description`, `create_time`, `update_time`) VALUES
('ORDER_CREATED', '订单创建通知', 'BUSINESS', 'EMAIL', '【物流跟踪系统】订单创建成功', 
'<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
    <div style="text-align: center; margin-bottom: 30px;">
        <h2 style="color: #2c3e50; margin: 0;">物流跟踪系统</h2>
        <p style="color: #7f8c8d; margin: 5px 0;">订单创建成功通知</p>
    </div>
    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin-bottom: 20px;">
        <h3 style="color: #2c3e50; margin-top: 0;">尊敬的 {{customerName}}，您好！</h3>
        <p style="color: #34495e; line-height: 1.6;">您的订单已成功创建，详情如下：</p>
        <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
            <tr><td style="padding: 8px 0; color: #7f8c8d; width: 100px;">订单号：</td><td style="padding: 8px 0; color: #2c3e50; font-weight: bold;">{{orderNumber}}</td></tr>
            <tr><td style="padding: 8px 0; color: #7f8c8d;">订单金额：</td><td style="padding: 8px 0; color: #e74c3c; font-weight: bold;">¥{{totalFee}}</td></tr>
            <tr><td style="padding: 8px 0; color: #7f8c8d;">创建时间：</td><td style="padding: 8px 0; color: #2c3e50;">{{createTime}}</td></tr>
        </table>
        <p style="color: #34495e; line-height: 1.6;">请及时完成支付，我们将尽快为您安排揽件服务。</p>
    </div>
    <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ecf0f1;">
        <p style="color: #95a5a6; font-size: 12px; margin: 0;">此邮件由系统自动发送，请勿回复</p>
        <p style="color: #95a5a6; font-size: 12px; margin: 5px 0 0 0;">物流跟踪系统 © 2024</p>
    </div>
</div>', 1, 1, '用户下单成功后发送的通知邮件', NOW(), NOW());

-- 2. 支付成功通知模板
INSERT INTO `notification_templates` (`template_code`, `template_name`, `template_type`, `notification_type`, `title`, `content`, `is_enabled`, `sort_order`, `description`, `create_time`, `update_time`) VALUES
('PAYMENT_SUCCESS', '支付成功通知', 'BUSINESS', 'EMAIL', '【物流跟踪系统】支付成功，订单处理中', 
'<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
    <div style="text-align: center; margin-bottom: 30px;">
        <h2 style="color: #27ae60; margin: 0;">✓ 支付成功</h2>
        <p style="color: #7f8c8d; margin: 5px 0;">您的订单支付已完成</p>
    </div>
    <div style="background-color: #d5f4e6; padding: 20px; border-radius: 6px; margin-bottom: 20px; border-left: 4px solid #27ae60;">
        <h3 style="color: #2c3e50; margin-top: 0;">尊敬的 {{customerName}}，您好！</h3>
        <p style="color: #34495e; line-height: 1.6;">您的订单支付已成功完成，我们将尽快为您安排揽件服务。</p>
        <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
            <tr><td style="padding: 8px 0; color: #7f8c8d; width: 100px;">订单号：</td><td style="padding: 8px 0; color: #2c3e50; font-weight: bold;">{{orderNumber}}</td></tr>
            <tr><td style="padding: 8px 0; color: #7f8c8d;">支付金额：</td><td style="padding: 8px 0; color: #27ae60; font-weight: bold;">¥{{paymentAmount}}</td></tr>
            <tr><td style="padding: 8px 0; color: #7f8c8d;">支付时间：</td><td style="padding: 8px 0; color: #2c3e50;">{{paymentTime}}</td></tr>
        </table>
        <p style="color: #34495e; line-height: 1.6;">我们将在24小时内安排揽件员上门取件，请保持电话畅通。</p>
    </div>
    <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ecf0f1;">
        <p style="color: #95a5a6; font-size: 12px; margin: 0;">此邮件由系统自动发送，请勿回复</p>
        <p style="color: #95a5a6; font-size: 12px; margin: 5px 0 0 0;">物流跟踪系统 © 2024</p>
    </div>
</div>', 1, 2, '用户支付成功后发送的通知邮件', NOW(), NOW());

-- 3. 订单状态更新通知模板
INSERT INTO `notification_templates` (`template_code`, `template_name`, `template_type`, `notification_type`, `title`, `content`, `is_enabled`, `sort_order`, `description`, `create_time`, `update_time`) VALUES
('ORDER_STATUS_UPDATE', '订单状态更新通知', 'BUSINESS', 'EMAIL', '【物流跟踪系统】订单状态更新', 
'<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
    <div style="text-align: center; margin-bottom: 30px;">
        <h2 style="color: #3498db; margin: 0;">📦 订单状态更新</h2>
        <p style="color: #7f8c8d; margin: 5px 0;">您的包裹有新的物流信息</p>
    </div>
    <div style="background-color: #ebf3fd; padding: 20px; border-radius: 6px; margin-bottom: 20px; border-left: 4px solid #3498db;">
        <h3 style="color: #2c3e50; margin-top: 0;">尊敬的 {{customerName}}，您好！</h3>
        <p style="color: #34495e; line-height: 1.6;">您的订单状态已更新，详情如下：</p>
        <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
            <tr><td style="padding: 8px 0; color: #7f8c8d; width: 100px;">订单号：</td><td style="padding: 8px 0; color: #2c3e50; font-weight: bold;">{{orderNumber}}</td></tr>
            <tr><td style="padding: 8px 0; color: #7f8c8d;">原状态：</td><td style="padding: 8px 0; color: #95a5a6;">{{oldStatus}}</td></tr>
            <tr><td style="padding: 8px 0; color: #7f8c8d;">当前状态：</td><td style="padding: 8px 0; color: #3498db; font-weight: bold;">{{newStatus}}</td></tr>
            <tr><td style="padding: 8px 0; color: #7f8c8d;">更新时间：</td><td style="padding: 8px 0; color: #2c3e50;">{{updateTime}}</td></tr>
        </table>
        <p style="color: #34495e; line-height: 1.6;">您可以登录系统查看详细的物流轨迹信息。</p>
    </div>
    <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ecf0f1;">
        <p style="color: #95a5a6; font-size: 12px; margin: 0;">此邮件由系统自动发送，请勿回复</p>
        <p style="color: #95a5a6; font-size: 12px; margin: 5px 0 0 0;">物流跟踪系统 © 2024</p>
    </div>
</div>', 1, 3, '订单状态变更时发送的通知邮件', NOW(), NOW());

-- 4. 配送员任务分配通知模板
INSERT INTO `notification_templates` (`template_code`, `template_name`, `template_type`, `notification_type`, `title`, `content`, `is_enabled`, `sort_order`, `description`, `create_time`, `update_time`) VALUES
('COURIER_TASK_ASSIGNED', '配送员任务分配通知', 'BUSINESS', 'EMAIL', '【物流跟踪系统】新任务分配', 
'<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
    <div style="text-align: center; margin-bottom: 30px;">
        <h2 style="color: #f39c12; margin: 0;">🚚 新任务分配</h2>
        <p style="color: #7f8c8d; margin: 5px 0;">您有新的配送任务</p>
    </div>
    <div style="background-color: #fef9e7; padding: 20px; border-radius: 6px; margin-bottom: 20px; border-left: 4px solid #f39c12;">
        <h3 style="color: #2c3e50; margin-top: 0;">{{courierName}}，您好！</h3>
        <p style="color: #34495e; line-height: 1.6;">系统为您分配了新的配送任务，请及时处理。</p>
        <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
            <tr><td style="padding: 8px 0; color: #7f8c8d; width: 100px;">订单号：</td><td style="padding: 8px 0; color: #2c3e50; font-weight: bold;">{{orderNumber}}</td></tr>
            <tr><td style="padding: 8px 0; color: #7f8c8d;">任务类型：</td><td style="padding: 8px 0; color: #f39c12; font-weight: bold;">{{taskType}}</td></tr>
            <tr><td style="padding: 8px 0; color: #7f8c8d;">分配时间：</td><td style="padding: 8px 0; color: #2c3e50;">{{assignTime}}</td></tr>
        </table>
        <p style="color: #34495e; line-height: 1.6;">请登录系统查看详细任务信息并及时处理。</p>
    </div>
    <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ecf0f1;">
        <p style="color: #95a5a6; font-size: 12px; margin: 0;">此邮件由系统自动发送，请勿回复</p>
        <p style="color: #95a5a6; font-size: 12px; margin: 5px 0 0 0;">物流跟踪系统 © 2024</p>
    </div>
</div>', 1, 4, '配送员任务分配时发送的通知邮件', NOW(), NOW());

-- 5. 配送完成通知模板
INSERT INTO `notification_templates` (`template_code`, `template_name`, `template_type`, `notification_type`, `title`, `content`, `is_enabled`, `sort_order`, `description`, `create_time`, `update_time`) VALUES
('DELIVERY_COMPLETED', '配送完成通知', 'BUSINESS', 'EMAIL', '【物流跟踪系统】包裹已送达', 
'<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
    <div style="text-align: center; margin-bottom: 30px;">
        <h2 style="color: #27ae60; margin: 0;">🎉 包裹已送达</h2>
        <p style="color: #7f8c8d; margin: 5px 0;">您的包裹已成功送达</p>
    </div>
    <div style="background-color: #d5f4e6; padding: 20px; border-radius: 6px; margin-bottom: 20px; border-left: 4px solid #27ae60;">
        <h3 style="color: #2c3e50; margin-top: 0;">尊敬的 {{customerName}}，您好！</h3>
        <p style="color: #34495e; line-height: 1.6;">您的包裹已成功送达，感谢您的信任！</p>
        <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
            <tr><td style="padding: 8px 0; color: #7f8c8d; width: 100px;">订单号：</td><td style="padding: 8px 0; color: #2c3e50; font-weight: bold;">{{orderNumber}}</td></tr>
            <tr><td style="padding: 8px 0; color: #7f8c8d;">送达时间：</td><td style="padding: 8px 0; color: #27ae60; font-weight: bold;">{{deliveryTime}}</td></tr>
        </table>
        <p style="color: #34495e; line-height: 1.6;">如有任何问题，请及时联系我们的客服。期待为您提供更好的服务！</p>
    </div>
    <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ecf0f1;">
        <p style="color: #95a5a6; font-size: 12px; margin: 0;">此邮件由系统自动发送，请勿回复</p>
        <p style="color: #95a5a6; font-size: 12px; margin: 5px 0 0 0;">物流跟踪系统 © 2024</p>
    </div>
</div>', 1, 5, '包裹送达后发送的通知邮件', NOW(), NOW());

-- 验证插入结果
SELECT template_code, template_name, notification_type, is_enabled 
FROM notification_templates 
WHERE notification_type = 'EMAIL' 
ORDER BY sort_order;
