package org.example.logisticsnotification.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.example.logisticsnotification.entity.NotificationTemplate;

import java.util.List;
import java.util.Map;

/**
 * 通知模板服务接口
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
public interface NotificationTemplateService {

    /**
     * 创建模板
     */
    NotificationTemplate createTemplate(NotificationTemplate template);

    /**
     * 更新模板
     */
    boolean updateTemplate(NotificationTemplate template);

    /**
     * 删除模板
     */
    boolean deleteTemplate(Long templateId);

    /**
     * 批量删除模板
     */
    boolean batchDeleteTemplates(List<Long> templateIds);

    /**
     * 启用/禁用模板
     */
    boolean toggleTemplateStatus(Long templateId, Integer isEnabled);

    /**
     * 批量更新模板状态
     */
    int batchUpdateTemplateStatus(List<Long> templateIds, Integer isEnabled);

    /**
     * 根据ID查询模板
     */
    NotificationTemplate getTemplateById(Long templateId);

    /**
     * 根据模板编码查询模板
     */
    NotificationTemplate getTemplateByCode(String templateCode);

    /**
     * 根据模板类型查询模板列表
     */
    List<NotificationTemplate> getTemplatesByType(String templateType);

    /**
     * 根据通知类型查询模板列表
     */
    List<NotificationTemplate> getTemplatesByNotificationType(String notificationType);

    /**
     * 查询启用的模板
     */
    List<NotificationTemplate> getEnabledTemplates(String notificationType, String templateType);

    /**
     * 分页查询模板
     */
    IPage<NotificationTemplate> getTemplatesPage(Page<NotificationTemplate> page, String templateType,
                                               String notificationType, String templateName, Integer isEnabled);

    /**
     * 检查模板编码是否存在
     */
    boolean isTemplateCodeExists(String templateCode, Long excludeId);

    /**
     * 渲染模板内容
     */
    String renderTemplateContent(String templateCode, Map<String, Object> params);

    /**
     * 渲染模板标题
     */
    String renderTemplateTitle(String templateCode, Map<String, Object> params);

    /**
     * 根据模板和参数生成通知内容
     */
    Map<String, String> generateNotificationContent(String templateCode, Map<String, Object> params);

    /**
     * 验证模板参数
     */
    boolean validateTemplateParams(String templateCode, Map<String, Object> params);

    /**
     * 获取模板参数描述
     */
    Map<String, Object> getTemplateParamsDesc(String templateCode);

    /**
     * 复制模板
     */
    NotificationTemplate copyTemplate(Long templateId, String newTemplateCode, String newTemplateName);

    /**
     * 导入模板
     */
    boolean importTemplates(List<NotificationTemplate> templates);

    /**
     * 导出模板
     */
    List<NotificationTemplate> exportTemplates(List<Long> templateIds);

    /**
     * 获取所有模板类型
     */
    List<String> getAllTemplateTypes();

    /**
     * 获取所有通知类型
     */
    List<String> getAllNotificationTypes();

    /**
     * 统计模板使用情况
     */
    Map<String, Object> getTemplateUsageStatistics();

    /**
     * 获取热门模板
     */
    List<NotificationTemplate> getPopularTemplates(Integer limit);
} 