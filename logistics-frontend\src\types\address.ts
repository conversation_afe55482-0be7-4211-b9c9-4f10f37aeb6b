// 地址类型
export type AddressType = 1 | 2 // 1-收货地址，2-发货地址

// 地址信息
export interface Address {
  id: number
  userId: number
  contactName: string
  contactPhone: string
  province: string
  city: string
  district: string
  detailedAddress: string
  postalCode?: string
  longitude?: number
  latitude?: number
  addressType: AddressType
  isDefault: boolean
  status?: number // 0-禁用，1-启用
  createTime: string
  updateTime: string
}

// 创建/更新地址请求
export interface AddressRequest {
  contactName: string
  contactPhone: string
  province: string
  city: string
  district: string
  detailedAddress: string
  postalCode?: string
  addressType: AddressType
  isDefault: boolean
}

// 省市区数据
export interface RegionData {
  code: string
  name: string
  children?: RegionData[]
}

// 地址选择组件数据
export interface AddressOption {
  value: number
  label: string
  address: Address
}
