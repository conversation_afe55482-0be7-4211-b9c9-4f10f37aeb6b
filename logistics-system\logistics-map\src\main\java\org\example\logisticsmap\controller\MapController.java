package org.example.logisticsmap.controller;

import com.logistics.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.example.logisticsmap.dto.DirectionRequest;
import org.example.logisticsmap.dto.GeocodingRequest;
import org.example.logisticsmap.dto.ReGeocodingRequest;
import org.example.logisticsmap.entity.Location;
import org.example.logisticsmap.service.MapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 地图服务控制器
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Slf4j
@RestController
@RequestMapping("/api/map")
@Validated
public class MapController {

    @Autowired
    private MapService mapService;

    /**
     * 地理编码 - 地址转坐标
     *
     * @param request 地理编码请求
     * @return 位置信息列表
     */
    @PostMapping("/geocoding")
    public Result<List<Location>> geocoding(@Valid @RequestBody GeocodingRequest request) {
        try {
            List<Location> locations = mapService.geocoding(request);
            return Result.success(locations);
        } catch (Exception e) {
            log.error("地理编码失败", e);
            return Result.error("地理编码失败: " + e.getMessage());
        }
    }

    /**
     * 逆地理编码 - 坐标转地址
     *
     * @param request 逆地理编码请求
     * @return 位置信息
     */
    @PostMapping("/regeocoding")
    public Result<Location> reGeocoding(@Valid @RequestBody ReGeocodingRequest request) {
        try {
            Location location = mapService.reGeocoding(request);
            return Result.success(location);
        } catch (Exception e) {
            log.error("逆地理编码失败", e);
            return Result.error("逆地理编码失败: " + e.getMessage());
        }
    }

    /**
     * 驾车路径规划
     *
     * @param request 路径规划请求
     * @return 路径规划结果
     */
    @PostMapping("/direction/driving")
    public Result<Map<String, Object>> drivingDirection(@Valid @RequestBody DirectionRequest request) {
        try {
            Map<String, Object> result = mapService.drivingDirection(request);
            return Result.success(result);
        } catch (Exception e) {
            log.error("驾车路径规划失败", e);
            return Result.error("驾车路径规划失败: " + e.getMessage());
        }
    }

    /**
     * 步行路径规划
     *
     * @param request 路径规划请求
     * @return 路径规划结果
     */
    @PostMapping("/direction/walking")
    public Result<Map<String, Object>> walkingDirection(@Valid @RequestBody DirectionRequest request) {
        try {
            Map<String, Object> result = mapService.walkingDirection(request);
            return Result.success(result);
        } catch (Exception e) {
            log.error("步行路径规划失败", e);
            return Result.error("步行路径规划失败: " + e.getMessage());
        }
    }

    /**
     * 计算距离
     *
     * @param origins     起点坐标列表
     * @param destination 终点坐标
     * @param type        计算方式 (1:直线距离 3:驾车导航距离)
     * @return 距离信息
     */
    @PostMapping("/distance")
    public Result<Map<String, Object>> calculateDistance(
            @RequestBody @NotEmpty(message = "起点坐标不能为空") List<String> origins,
            @RequestParam @NotBlank(message = "终点坐标不能为空") String destination,
            @RequestParam(defaultValue = "1") Integer type) {
        try {
            Map<String, Object> result = mapService.calculateDistance(origins, destination, type);
            return Result.success(result);
        } catch (Exception e) {
            log.error("距离计算失败", e);
            return Result.error("距离计算失败: " + e.getMessage());
        }
    }

    /**
     * 搜索提示
     *
     * @param keywords 关键字
     * @param city     城市
     * @return 搜索提示结果
     */
    @GetMapping("/inputtips")
    public Result<Map<String, Object>> inputTips(
            @RequestParam @NotBlank(message = "关键字不能为空") String keywords,
            @RequestParam(required = false) String city) {
        try {
            Map<String, Object> result = mapService.inputTips(keywords, city);
            return Result.success(result);
        } catch (Exception e) {
            log.error("搜索提示失败", e);
            return Result.error("搜索提示失败: " + e.getMessage());
        }
    }

    /**
     * 关键字搜索POI
     *
     * @param keywords 关键字
     * @param city     城市
     * @param types    类型
     * @return POI搜索结果
     */
    @GetMapping("/poi/search")
    public Result<Map<String, Object>> searchPOI(
            @RequestParam @NotBlank(message = "关键字不能为空") String keywords,
            @RequestParam(required = false) String city,
            @RequestParam(required = false) String types) {
        try {
            Map<String, Object> result = mapService.searchPOI(keywords, city, types);
            return Result.success(result);
        } catch (Exception e) {
            log.error("POI搜索失败", e);
            return Result.error("POI搜索失败: " + e.getMessage());
        }
    }

    /**
     * 周边搜索POI
     *
     * @param keywords 关键字
     * @param location 中心点坐标
     * @param radius   搜索半径
     * @param types    类型
     * @return POI搜索结果
     */
    @GetMapping("/poi/around")
    public Result<Map<String, Object>> searchAroundPOI(
            @RequestParam @NotBlank(message = "关键字不能为空") String keywords,
            @RequestParam @NotBlank(message = "中心点坐标不能为空") String location,
            @RequestParam(required = false) Integer radius,
            @RequestParam(required = false) String types) {
        try {
            Map<String, Object> result = mapService.searchAroundPOI(keywords, location, radius, types);
            return Result.success(result);
        } catch (Exception e) {
            log.error("周边POI搜索失败", e);
            return Result.error("周边POI搜索失败: " + e.getMessage());
        }
    }

    /**
     * 获取天气信息
     *
     * @param city       城市
     * @param extensions 天气类型 (base:实况天气 all:预报天气)
     * @return 天气信息
     */
    @GetMapping("/weather")
    public Result<Map<String, Object>> getWeather(
            @RequestParam @NotBlank(message = "城市不能为空") String city,
            @RequestParam(defaultValue = "base") String extensions) {
        try {
            Map<String, Object> result = mapService.getWeather(city, extensions);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取天气信息失败", e);
            return Result.error("获取天气信息失败: " + e.getMessage());
        }
    }

    // ===== 便捷接口 =====

    /**
     * 地址转坐标（便捷接口）
     *
     * @param address 地址
     * @param city    城市（可选）
     * @return 位置信息
     */
    @GetMapping("/address-to-location")
    public Result<Location> addressToLocation(
            @RequestParam @NotBlank(message = "地址不能为空") String address,
            @RequestParam(required = false) String city) {
        try {
            Location location = mapService.addressToLocation(address, city);
            return Result.success(location);
        } catch (Exception e) {
            log.error("地址转坐标失败", e);
            return Result.error("地址转坐标失败: " + e.getMessage());
        }
    }

    /**
     * 坐标转地址（便捷接口）
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @return 位置信息
     */
    @GetMapping("/location-to-address")
    public Result<Location> locationToAddress(
            @RequestParam @NotNull(message = "经度不能为空") Double longitude,
            @RequestParam @NotNull(message = "纬度不能为空") Double latitude) {
        try {
            Location location = mapService.locationToAddress(longitude, latitude);
            return Result.success(location);
        } catch (Exception e) {
            log.error("坐标转地址失败", e);
            return Result.error("坐标转地址失败: " + e.getMessage());
        }
    }

    /**
     * 计算两点直线距离
     *
     * @param fromLng 起点经度
     * @param fromLat 起点纬度
     * @param toLng   终点经度
     * @param toLat   终点纬度
     * @return 距离（米）
     */
    @GetMapping("/distance/straight")
    public Result<Double> calculateStraightDistance(
            @RequestParam @NotNull(message = "起点经度不能为空") Double fromLng,
            @RequestParam @NotNull(message = "起点纬度不能为空") Double fromLat,
            @RequestParam @NotNull(message = "终点经度不能为空") Double toLng,
            @RequestParam @NotNull(message = "终点纬度不能为空") Double toLat) {
        try {
            Double distance = mapService.calculateStraightDistance(fromLng, fromLat, toLng, toLat);
            return Result.success(distance);
        } catch (Exception e) {
            log.error("计算直线距离失败", e);
            return Result.error("计算直线距离失败: " + e.getMessage());
        }
    }

    /**
     * 计算驾车距离
     *
     * @param fromLng 起点经度
     * @param fromLat 起点纬度
     * @param toLng   终点经度
     * @param toLat   终点纬度
     * @return 距离（米）
     */
    @GetMapping("/distance/driving")
    public Result<Double> calculateDrivingDistance(
            @RequestParam @NotNull(message = "起点经度不能为空") Double fromLng,
            @RequestParam @NotNull(message = "起点纬度不能为空") Double fromLat,
            @RequestParam @NotNull(message = "终点经度不能为空") Double toLng,
            @RequestParam @NotNull(message = "终点纬度不能为空") Double toLat) {
        try {
            Double distance = mapService.calculateDrivingDistance(fromLng, fromLat, toLng, toLat);
            return Result.success(distance);
        } catch (Exception e) {
            log.error("计算驾车距离失败", e);
            return Result.error("计算驾车距离失败: " + e.getMessage());
        }
    }

    /**
     * 获取最优驾车路径
     *
     * @param fromLng 起点经度
     * @param fromLat 起点纬度
     * @param toLng   终点经度
     * @param toLat   终点纬度
     * @return 路径信息
     */
    @GetMapping("/route/best")
    public Result<Map<String, Object>> getBestDrivingRoute(
            @RequestParam @NotNull(message = "起点经度不能为空") Double fromLng,
            @RequestParam @NotNull(message = "起点纬度不能为空") Double fromLat,
            @RequestParam @NotNull(message = "终点经度不能为空") Double toLng,
            @RequestParam @NotNull(message = "终点纬度不能为空") Double toLat) {
        try {
            Map<String, Object> route = mapService.getBestDrivingRoute(fromLng, fromLat, toLng, toLat);
            return Result.success(route);
        } catch (Exception e) {
            log.error("获取最优驾车路径失败", e);
            return Result.error("获取最优驾车路径失败: " + e.getMessage());
        }
    }

    /**
     * 批量地址转坐标
     *
     * @param addresses 地址列表
     * @return 位置信息列表
     */
    @PostMapping("/batch/address-to-location")
    public Result<List<Location>> batchAddressToLocation(
            @RequestBody @NotEmpty(message = "地址列表不能为空") List<String> addresses) {
        try {
            List<Location> locations = mapService.batchAddressToLocation(addresses);
            return Result.success(locations);
        } catch (Exception e) {
            log.error("批量地址转坐标失败", e);
            return Result.error("批量地址转坐标失败: " + e.getMessage());
        }
    }

    /**
     * 批量坐标转地址
     *
     * @param locations 坐标列表 (格式: 经度,纬度)
     * @return 位置信息列表
     */
    @PostMapping("/batch/location-to-address")
    public Result<List<Location>> batchLocationToAddress(
            @RequestBody @NotEmpty(message = "坐标列表不能为空") List<String> locations) {
        try {
            List<Location> result = mapService.batchLocationToAddress(locations);
            return Result.success(result);
        } catch (Exception e) {
            log.error("批量坐标转地址失败", e);
            return Result.error("批量坐标转地址失败: " + e.getMessage());
        }
    }
} 