server:
  port: 8080

spring:
  application:
    name: logistics-gateway
  main:
    web-application-type: reactive

  redis:
    host: localhost
    port: 6379
    password: redis123
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 200
        max-wait: -1ms
        max-idle: 10
        min-idle: 0

  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: public
        group: DEFAULT_GROUP
    gateway:
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
      routes:
        # 用户服务路由
        - id: logistics-user
          uri: lb://logistics-user
          predicates:
            - Path=/api/user/**
          filters:
            - StripPrefix=1
        # 订单服务路由
        - id: logistics-order
          uri: lb://logistics-order
          predicates:
            - Path=/api/order/**
          filters:
            - StripPrefix=1
        # 物流服务路由
        - id: logistics-logistics
          uri: lb://logistics-logistics
          predicates:
            - Path=/api/logistics/**
          filters:
            - StripPrefix=1
        # 配送服务路由
        - id: logistics-delivery
          uri: lb://logistics-delivery
          predicates:
            - Path=/api/delivery/**
          filters:
            - StripPrefix=1
        # 通知服务路由
        - id: logistics-notification
          uri: lb://logistics-notification
          predicates:
            - Path=/api/notification/**
          filters:
            - StripPrefix=1
        # 地图服务路由
        - id: logistics-map
          uri: lb://logistics-map
          predicates:
            - Path=/api/map/**
          filters:
            - StripPrefix=1

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always

# 日志配置
logging:
  level:
    org.springframework.cloud.gateway: DEBUG
    org.example.logisticsgateway: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"