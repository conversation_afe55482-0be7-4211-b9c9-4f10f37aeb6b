package org.example.logisticslogistics.repository;

import org.example.logisticslogistics.entity.TrackingInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 物流轨迹信息数据访问层
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Repository
public interface TrackingInfoRepository extends MongoRepository<TrackingInfo, String> {

    /**
     * 根据订单号查询轨迹信息
     */
    Optional<TrackingInfo> findByOrderNumber(String orderNumber);

    /**
     * 根据订单ID查询轨迹信息
     */
    Optional<TrackingInfo> findByOrderId(Long orderId);

    /**
     * 根据当前状态查询轨迹信息列表
     */
    List<TrackingInfo> findByCurrentStatus(String currentStatus);

    /**
     * 根据配送员ID查询轨迹信息列表
     */
    List<TrackingInfo> findByCourierId(Long courierId);

    /**
     * 根据配送员ID分页查询轨迹信息
     */
    Page<TrackingInfo> findByCourierId(Long courierId, Pageable pageable);

    /**
     * 查询异常轨迹信息
     */
    List<TrackingInfo> findByIsExceptionTrue();

    /**
     * 分页查询异常轨迹信息
     */
    Page<TrackingInfo> findByIsExceptionTrue(Pageable pageable);

    /**
     * 根据创建时间范围查询轨迹信息
     */
    List<TrackingInfo> findByCreateTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据最后更新时间范围查询轨迹信息
     */
    List<TrackingInfo> findByLastUpdateTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据预计到达时间范围查询轨迹信息
     */
    List<TrackingInfo> findByEstimatedDeliveryTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询指定状态且在指定时间范围内的轨迹信息
     */
    @Query("{'currentStatus': ?0, 'createTime': {'$gte': ?1, '$lte': ?2}}")
    List<TrackingInfo> findByStatusAndTimeRange(String status, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询配送中的订单（用于实时监控）
     */
    @Query("{'currentStatus': {'$in': ['OUT_FOR_DELIVERY', 'DELIVERY_ATTEMPT']}}")
    List<TrackingInfo> findDeliveryInProgress();

    /**
     * 根据城市查询轨迹信息
     */
    @Query("{'currentLocation.city': ?0}")
    List<TrackingInfo> findByCurrentCity(String city);

    /**
     * 查询指定配送员在指定日期的配送任务
     */
    @Query("{'courierId': ?0, 'createTime': {'$gte': ?1, '$lt': ?2}}")
    List<TrackingInfo> findByCourierAndDate(Long courierId, LocalDateTime startOfDay, LocalDateTime endOfDay);

    /**
     * 统计指定时间范围内的订单数量
     */
    @Query(value = "{'createTime': {'$gte': ?0, '$lte': ?1}}", count = true)
    long countByCreateTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计异常订单数量
     */
    long countByIsExceptionTrue();

    /**
     * 统计已完成订单数量
     */
    @Query(value = "{'currentStatus': {'$in': ['DELIVERED', 'SELF_PICKUP']}}", count = true)
    long countCompletedOrders();

    /**
     * 检查订单是否存在
     */
    boolean existsByOrderNumber(String orderNumber);

    /**
     * 检查订单ID是否存在
     */
    boolean existsByOrderId(Long orderId);
} 