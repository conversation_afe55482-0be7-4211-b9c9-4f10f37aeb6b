package com.logistics.common.result;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 结果码枚举
 * 
 * <AUTHOR>
 * @since 2024-12-25
 */
@Getter
@AllArgsConstructor
public enum ResultCode {

    // 成功
    SUCCESS(200, "操作成功"),

    // 失败
    ERROR(500, "操作失败"),

    // 参数错误
    PARAM_ERROR(400, "参数错误"),
    PARAM_MISSING(400, "缺少必要参数"),
    PARAM_INVALID(400, "参数格式不正确"),

    // 认证授权
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    TOKEN_INVALID(401, "Token无效"),
    TOKEN_EXPIRED(401, "Token已过期"),

    // 业务错误
    USER_NOT_FOUND(404, "用户不存在"),
    USER_DISABLED(403, "用户已被禁用"),
    USERNAME_EXISTS(400, "用户名已存在"),
    EMAIL_EXISTS(400, "邮箱已存在"),
    PHONE_EXISTS(400, "手机号已存在"),
    PASSWORD_ERROR(400, "密码错误"),

    // 订单相关
    ORDER_NOT_FOUND(404, "订单不存在"),
    ORDER_STATUS_ERROR(400, "订单状态错误"),
    ORDER_CANNOT_CANCEL(400, "订单无法取消"),

    // 配送相关
    COURIER_NOT_FOUND(404, "配送员不存在"),
    COURIER_BUSY(400, "配送员忙碌中"),
    DELIVERY_TASK_NOT_FOUND(404, "配送任务不存在"),

    // 系统错误
    SYSTEM_ERROR(500, "系统内部错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    DATABASE_ERROR(500, "数据库错误"),
    NETWORK_ERROR(500, "网络错误"),
    FILE_UPLOAD_ERROR(500, "文件上传失败"),

    // 限流熔断
    RATE_LIMIT(429, "请求过于频繁"),
    SERVICE_DEGRADED(503, "服务降级");

    /**
     * 响应码
     */
    private final Integer code;

    /**
     * 响应消息
     */
    private final String message;
} 