package org.example.logisticslogistics.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 物流轨迹状态枚举
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Getter
@AllArgsConstructor
public enum TrackingStatus {

    // 基础状态
    ORDER_CREATED("ORDER_CREATED", "订单已创建", "您的订单已创建，等待揽件"),

    // 揽件阶段
    PICKUP_ASSIGNED("PICKUP_ASSIGNED", "已分配揽件员", "揽件员已分配，即将上门取件"),
    PICKED_UP("PICKED_UP", "已揽件", "您的快件已被揽件员收取"),

    // 运输阶段
    DEPARTURE_ORIGIN("DEPARTURE_ORIGIN", "始发地发出", "您的快件已从始发地发出"),
    ARRIVAL_TRANSIT("ARRIVAL_TRANSIT", "到达中转站", "您的快件已到达中转站"),
    DEPARTURE_TRANSIT("DEPARTURE_TRANSIT", "中转站发出", "您的快件已从中转站发出"),
    ARRIVAL_DESTINATION("ARRIVAL_DESTINATION", "到达目的地", "您的快件已到达目的地分拣中心"),

    // 配送阶段
    OUT_FOR_DELIVERY("OUT_FOR_DELIVERY", "配送中", "您的快件正在配送途中"),
    DELIVERY_ATTEMPT("DELIVERY_ATTEMPT", "配送尝试", "配送员正在尝试投递"),

    // 完成状态
    DELIVERED("DELIVERED", "已签收", "您的快件已成功签收"),
    SELF_PICKUP("SELF_PICKUP", "自提完成", "您已在自提点取件"),

    // 异常状态
    DELIVERY_FAILED("DELIVERY_FAILED", "投递失败", "投递失败，等待重新投递"),
    RETURNED("RETURNED", "已退回", "您的快件已退回寄件人"),
    DAMAGED("DAMAGED", "包裹损坏", "包裹在运输过程中损坏"),
    LOST("LOST", "包裹丢失", "包裹在运输过程中丢失");

    private final String code;
    private final String title;
    private final String description;

    /**
     * 根据状态码获取枚举
     */
    public static TrackingStatus fromCode(String code) {
        for (TrackingStatus status : TrackingStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的轨迹状态: " + code);
    }

    /**
     * 判断是否为完成状态
     */
    public boolean isCompleted() {
        return this == DELIVERED || this == SELF_PICKUP || this == RETURNED;
    }

    /**
     * 判断是否为异常状态
     */
    public boolean isException() {
        return this == DELIVERY_FAILED || this == DAMAGED || this == LOST;
    }

    /**
     * 判断是否为运输中状态
     */
    public boolean isInTransit() {
        return this == DEPARTURE_ORIGIN || this == ARRIVAL_TRANSIT ||
                this == DEPARTURE_TRANSIT || this == ARRIVAL_DESTINATION;
    }
}