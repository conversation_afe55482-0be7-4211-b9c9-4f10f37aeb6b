<template>
  <div class="admin-order-detail" v-loading="loading">
    <div class="page-header">
      <el-button @click="$router.back()" :icon="ArrowLeft">返回</el-button>
      <h2>订单详情</h2>
      <div class="header-actions">
        <el-button @click="printOrder">
          <el-icon><Printer /></el-icon>
          打印
        </el-button>
        <el-button @click="exportOrder">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </div>
    </div>

    <div v-if="order" class="order-content">
      <!-- 订单基本信息 -->
      <el-card class="order-info-card">
        <template #header>
          <div class="card-header">
            <span>订单信息</span>
            <div class="order-actions">
              <el-button type="primary" @click="showStatusDialog = true">更新状态</el-button>
              <el-button @click="showAssignDialog = true">分配配送员</el-button>
            </div>
          </div>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-group">
              <h4>基本信息</h4>
              <div class="info-item">
                <label>订单号：</label>
                <span>{{ order.orderNumber }}</span>
              </div>
              <div class="info-item">
                <label>用户ID：</label>
                <span>{{ order.userId }}</span>
              </div>
              <div class="info-item">
                <label>订单状态：</label>
                <el-tag :type="getStatusTagType(order.orderStatus)">
                  {{ getStatusText(order.orderStatus) }}
                </el-tag>
              </div>
              <div class="info-item">
                <label>支付状态：</label>
                <el-tag :type="getPaymentTagType(order.paymentStatus)">
                  {{ getPaymentStatusText(order.paymentStatus) }}
                </el-tag>
              </div>
              <div class="info-item">
                <label>服务类型：</label>
                <span>{{ getServiceTypeText(order.serviceType) }}</span>
              </div>
              <div class="info-item">
                <label>创建时间：</label>
                <span>{{ formatTime(order.createTime) }}</span>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-group">
              <h4>费用信息</h4>
              <div class="info-item">
                <label>运费：</label>
                <span>¥{{ order.shippingFee.toFixed(2) }}</span>
              </div>
              <div class="info-item">
                <label>保险费：</label>
                <span>¥{{ order.insuranceFee.toFixed(2) }}</span>
              </div>
              <div class="info-item">
                <label>包装费：</label>
                <span>¥{{ order.packingFee.toFixed(2) }}</span>
              </div>
              <div class="info-item">
                <label>总金额：</label>
                <span class="total-amount">¥{{ order.totalFee.toFixed(2) }}</span>
              </div>
              <div class="info-item">
                <label>支付方式：</label>
                <span>{{ order.paymentMethod === 'ONLINE' ? '在线支付' : '货到付款' }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 地址信息 -->
      <el-card>
        <template #header>
          <span>地址信息</span>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="address-section">
              <h4>寄件信息</h4>
              <div class="address-content">
                <div class="contact">
                  <strong>{{ order.senderName }}</strong>
                  <span class="phone">{{ order.senderPhone }}</span>
                </div>
                <div class="address">
                  {{ order.senderProvince }} {{ order.senderCity }} {{ order.senderDistrict }}
                </div>
                <div class="address">{{ order.senderAddress }}</div>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="address-section">
              <h4>收件信息</h4>
              <div class="address-content">
                <div class="contact">
                  <strong>{{ order.receiverName }}</strong>
                  <span class="phone">{{ order.receiverPhone }}</span>
                </div>
                <div class="address">
                  {{ order.receiverProvince }} {{ order.receiverCity }} {{ order.receiverDistrict }}
                </div>
                <div class="address">{{ order.receiverAddress }}</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 物品信息 -->
      <el-card>
        <template #header>
          <span>物品信息</span>
        </template>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>物品名称：</label>
              <span>{{ order.itemName || '未填写' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>物品重量：</label>
              <span>{{ order.itemWeight || 0 }}kg</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>物品价值：</label>
              <span>¥{{ order.itemValue || 0 }}</span>
            </div>
          </el-col>
        </el-row>

        <div class="info-item" v-if="order.remarks">
          <label>备注：</label>
          <span>{{ order.remarks }}</span>
        </div>
      </el-card>

      <!-- 物流轨迹 -->
      <el-card>
        <template #header>
          <div class="card-header">
            <span>物流轨迹</span>
            <el-button type="text" @click="refreshTracking">刷新</el-button>
          </div>
        </template>

        <el-timeline v-if="trackingData.length > 0">
          <el-timeline-item
            v-for="(item, index) in trackingData"
            :key="index"
            :timestamp="formatTime(item.createTime)"
            :type="getTimelineType(item.status)"
          >
            <div class="timeline-content">
              <div class="timeline-title">{{ item.description }}</div>
              <div class="timeline-location" v-if="item.location">
                <el-icon><Location /></el-icon>
                {{ item.location }}
              </div>
              <div class="timeline-operator" v-if="item.operatorName">
                操作人：{{ item.operatorName }}
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>

        <el-empty v-else description="暂无物流信息" />
      </el-card>

      <!-- 操作日志 -->
      <el-card>
        <template #header>
          <span>操作日志</span>
        </template>

        <el-table :data="operationLogs" stripe>
          <el-table-column prop="operationType" label="操作类型" width="120" />
          <el-table-column prop="operatorName" label="操作人" width="120" />
          <el-table-column prop="description" label="操作描述" />
          <el-table-column prop="createTime" label="操作时间" width="180">
            <template #default="{ row }">
              {{ formatTime(row.createTime) }}
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 更新状态对话框 -->
    <el-dialog v-model="showStatusDialog" title="更新订单状态" width="500px">
      <el-form :model="statusForm" label-width="80px">
        <el-form-item label="当前状态">
          <el-tag>{{ getStatusText(order?.orderStatus || '') }}</el-tag>
        </el-form-item>
        <el-form-item label="新状态" required>
          <el-select v-model="statusForm.newStatus" placeholder="选择新状态" style="width: 100%">
            <el-option label="待发货" value="PENDING" />
            <el-option label="已支付" value="PAID" />
            <el-option label="已揽收" value="PICKED_UP" />
            <el-option label="运输中" value="IN_TRANSIT" />
            <el-option label="派送中" value="OUT_FOR_DELIVERY" />
            <el-option label="已送达" value="DELIVERED" />
            <el-option label="已取消" value="CANCELLED" />
          </el-select>
        </el-form-item>
        <el-form-item label="变更原因">
          <el-input v-model="statusForm.reason" type="textarea" placeholder="请输入变更原因" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showStatusDialog = false">取消</el-button>
        <el-button type="primary" @click="updateStatus" :loading="updating">确认更新</el-button>
      </template>
    </el-dialog>

    <!-- 分配配送员对话框 -->
    <el-dialog v-model="showAssignDialog" title="分配配送员" width="500px">
      <el-form :model="assignForm" label-width="80px">
        <el-form-item label="配送员" required>
          <el-select v-model="assignForm.courierId" placeholder="选择配送员" style="width: 100%">
            <el-option
              v-for="courier in courierList"
              :key="courier.id"
              :label="`${courier.name} (${courier.phone})`"
              :value="courier.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="assignForm.remarks" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAssignDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmAssign" :loading="assigning">确认分配</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Printer, Download, Location } from '@element-plus/icons-vue'
import { useRoute } from 'vue-router'
import { orderApi } from '@/api/order'
import type { Order, OrderStatus, ServiceType, PaymentStatus } from '@/types/order'
import dayjs from 'dayjs'

const route = useRoute()

// 状态
const loading = ref(false)
const updating = ref(false)
const assigning = ref(false)
const order = ref<Order | null>(null)
const trackingData = ref<any[]>([])
const operationLogs = ref<any[]>([])
const showStatusDialog = ref(false)
const showAssignDialog = ref(false)

// 表单
const statusForm = reactive({
  newStatus: '',
  reason: '',
})

const assignForm = reactive({
  courierId: '',
  remarks: '',
})

// 配送员列表
const courierList = ref([
  { id: 1, name: '张三', phone: '13800138001' },
  { id: 2, name: '李四', phone: '13800138002' },
  { id: 3, name: '王五', phone: '13800138003' },
])

// 加载订单详情
const loadOrderDetail = async () => {
  const orderId = route.params.id as string
  if (!orderId) return

  loading.value = true
  try {
    const response = await orderApi.getOrderById(Number(orderId))
    if (response.code === 200) {
      order.value = response.data
      loadTrackingData()
      loadOperationLogs()
    } else {
      ElMessage.error('订单不存在')
    }
  } catch (error) {
    console.error('加载订单详情失败:', error)
    ElMessage.error('加载订单详情失败')
  } finally {
    loading.value = false
  }
}

// 加载物流轨迹
const loadTrackingData = async () => {
  if (!order.value) return

  try {
    // TODO: 调用物流轨迹API
    // const response = await trackingApi.getTrackingByOrderNumber(order.value.orderNumber)
    // trackingData.value = response.data.data.trackingNodes

    // 模拟数据
    trackingData.value = [
      {
        status: 'CREATED',
        description: '订单已创建',
        location: '北京市',
        operatorName: '系统',
        createTime: order.value.createTime,
      },
    ]
  } catch (error) {
    console.error('加载物流轨迹失败:', error)
  }
}

// 加载操作日志
const loadOperationLogs = () => {
  // 模拟数据
  operationLogs.value = [
    {
      operationType: '创建订单',
      operatorName: '用户',
      description: '用户创建订单',
      createTime: order.value?.createTime,
    },
  ]
}

// 刷新物流轨迹
const refreshTracking = () => {
  loadTrackingData()
}

// 更新状态
const updateStatus = async () => {
  if (!statusForm.newStatus) {
    ElMessage.warning('请选择新状态')
    return
  }

  updating.value = true
  try {
    // TODO: 调用更新状态API
    ElMessage.success('状态更新成功')
    showStatusDialog.value = false
    loadOrderDetail()
  } catch (error) {
    console.error('更新状态失败:', error)
    ElMessage.error('更新失败')
  } finally {
    updating.value = false
  }
}

// 确认分配
const confirmAssign = async () => {
  if (!assignForm.courierId) {
    ElMessage.warning('请选择配送员')
    return
  }

  assigning.value = true
  try {
    // TODO: 调用分配配送员API
    ElMessage.success('分配成功')
    showAssignDialog.value = false
    loadOrderDetail()
  } catch (error) {
    console.error('分配配送员失败:', error)
    ElMessage.error('分配失败')
  } finally {
    assigning.value = false
  }
}

// 打印订单
const printOrder = () => {
  ElMessage.info('打印功能开发中...')
}

// 导出订单
const exportOrder = () => {
  ElMessage.info('导出功能开发中...')
}

// 工具函数
const getStatusTagType = (status: OrderStatus) => {
  const typeMap = {
    PENDING: '',
    PAID: 'success',
    PICKED_UP: 'info',
    IN_TRANSIT: 'warning',
    OUT_FOR_DELIVERY: 'warning',
    DELIVERED: 'success',
    CANCELLED: 'danger',
    RETURNED: 'danger',
  }
  return typeMap[status] || ''
}

const getStatusText = (status: OrderStatus) => {
  const textMap = {
    PENDING: '待发货',
    PAID: '已支付',
    PICKED_UP: '已揽收',
    IN_TRANSIT: '运输中',
    OUT_FOR_DELIVERY: '派送中',
    DELIVERED: '已送达',
    CANCELLED: '已取消',
    RETURNED: '已退回',
  }
  return textMap[status] || status
}

const getServiceTypeText = (serviceType: ServiceType) => {
  const textMap = {
    STANDARD: '标准快递',
    EXPRESS: '特快专递',
    URGENT: '加急服务',
  }
  return textMap[serviceType] || serviceType
}

const getPaymentStatusText = (paymentStatus: PaymentStatus) => {
  const textMap = {
    0: '未支付',
    1: '已支付',
    2: '已退款',
  }
  return textMap[paymentStatus] || '未知'
}

const getPaymentTagType = (paymentStatus: PaymentStatus) => {
  const typeMap = {
    0: 'warning',
    1: 'success',
    2: 'info',
  }
  return typeMap[paymentStatus] || ''
}

const getTimelineType = (status: string) => {
  if (status.includes('DELIVERED')) return 'success'
  if (status.includes('CANCELLED')) return 'danger'
  if (status.includes('TRANSIT')) return 'warning'
  return 'primary'
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm')
}

onMounted(() => {
  loadOrderDetail()
})
</script>

<style scoped>
.admin-order-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 0 15px;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.order-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-actions {
  display: flex;
  gap: 10px;
}

.info-group {
  margin-bottom: 20px;
}

.info-group h4 {
  margin: 0 0 15px 0;
  color: #333;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 10px;
}

.info-item {
  display: flex;
  margin-bottom: 10px;
}

.info-item label {
  min-width: 80px;
  color: #666;
  font-weight: 500;
}

.total-amount {
  font-weight: bold;
  color: #f56c6c;
  font-size: 16px;
}

.address-section h4 {
  margin: 0 0 15px 0;
  color: #333;
}

.address-content {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.contact {
  margin-bottom: 8px;
}

.contact strong {
  margin-right: 10px;
}

.phone {
  color: #666;
}

.address {
  color: #666;
  margin-bottom: 4px;
}

.timeline-content {
  padding-left: 10px;
}

.timeline-title {
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.timeline-location {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #666;
  font-size: 14px;
  margin-bottom: 2px;
}

.timeline-operator {
  color: #999;
  font-size: 12px;
}
</style>
