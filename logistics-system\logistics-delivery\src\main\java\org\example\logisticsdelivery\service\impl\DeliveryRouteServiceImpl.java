package org.example.logisticsdelivery.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.logisticsdelivery.entity.Courier;
import org.example.logisticsdelivery.entity.DeliveryRoute;
import org.example.logisticsdelivery.entity.DeliveryTask;
import org.example.logisticsdelivery.mapper.CourierMapper;
import org.example.logisticsdelivery.mapper.DeliveryRouteMapper;
import org.example.logisticsdelivery.mapper.DeliveryTaskMapper;
import org.example.logisticsdelivery.service.DeliveryRouteService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 配送路线服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeliveryRouteServiceImpl implements DeliveryRouteService {

    private final DeliveryRouteMapper routeMapper;
    private final CourierMapper courierMapper;
    private final DeliveryTaskMapper taskMapper;

    @Override
    @Transactional
    public DeliveryRoute createRoute(DeliveryRoute route) {
        try {
            // 参数验证
            validateRouteInfo(route);

            // 检查配送员当日是否已有路线
            if (route.getCourierId() != null && route.getRouteDate() != null) {
                DeliveryRoute existing = routeMapper.findByCourierAndDate(route.getCourierId(), route.getRouteDate());
                if (existing != null) {
                    throw new RuntimeException("配送员当日已存在路线");
                }
            }

            // 生成路线编号
            if (!StringUtils.hasText(route.getRouteNumber())) {
                route.setRouteNumber(generateRouteNumber());
            }

            // 设置默认值
            route.setRouteStatus("PLANNED");
            route.setTaskCount(0);
            route.setCompletedCount(0);
            route.setCreateTime(LocalDateTime.now());
            route.setUpdateTime(LocalDateTime.now());

            routeMapper.insert(route);
            log.info("创建配送路线成功，路线编号: {}", route.getRouteNumber());
            return route;
        } catch (Exception e) {
            log.error("创建配送路线失败", e);
            throw new RuntimeException("创建配送路线失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public DeliveryRoute updateRoute(DeliveryRoute route) {
        try {
            if (route.getId() == null) {
                throw new RuntimeException("路线ID不能为空");
            }

            DeliveryRoute existing = routeMapper.selectById(route.getId());
            if (existing == null) {
                throw new RuntimeException("配送路线不存在");
            }

            route.setUpdateTime(LocalDateTime.now());
            routeMapper.updateById(route);
            log.info("更新配送路线成功，ID: {}", route.getId());
            return routeMapper.selectById(route.getId());
        } catch (Exception e) {
            log.error("更新配送路线失败，ID: {}", route.getId(), e);
            throw new RuntimeException("更新配送路线失败: " + e.getMessage());
        }
    }

    @Override
    public DeliveryRoute getRouteById(Long id) {
        if (id == null) {
            throw new RuntimeException("路线ID不能为空");
        }
        return routeMapper.selectById(id);
    }

    @Override
    public List<DeliveryRoute> getRoutesByCourierId(Long courierId) {
        if (courierId == null) {
            throw new RuntimeException("配送员ID不能为空");
        }
        return routeMapper.findByCourierId(courierId);
    }

    @Override
    public DeliveryRoute getRouteByCourierAndDate(Long courierId, LocalDate routeDate) {
        if (courierId == null || routeDate == null) {
            throw new RuntimeException("参数不能为空");
        }
        return routeMapper.findByCourierAndDate(courierId, routeDate);
    }

    @Override
    public List<DeliveryRoute> getRoutesByStatus(String routeStatus) {
        if (!StringUtils.hasText(routeStatus)) {
            throw new RuntimeException("路线状态不能为空");
        }
        return routeMapper.findByRouteStatus(routeStatus);
    }

    @Override
    public List<DeliveryRoute> getRoutesByDate(LocalDate routeDate) {
        if (routeDate == null) {
            throw new RuntimeException("路线日期不能为空");
        }
        return routeMapper.findByRouteDate(routeDate);
    }

    @Override
    @Transactional
    public boolean updateRouteStatus(Long id, String routeStatus) {
        try {
            if (id == null || !StringUtils.hasText(routeStatus)) {
                throw new RuntimeException("参数不能为空");
            }
            
            int result = routeMapper.updateRouteStatus(id, routeStatus);
            if (result > 0) {
                log.info("更新路线状态成功，ID: {}, 状态: {}", id, routeStatus);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("更新路线状态失败，ID: {}", id, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean updateTaskCount(Long id, Integer taskCount, Integer completedCount) {
        try {
            if (id == null) {
                throw new RuntimeException("路线ID不能为空");
            }
            
            int result = routeMapper.updateTaskCount(id, taskCount, completedCount);
            if (result > 0) {
                log.info("更新路线任务统计成功，ID: {}, 总任务: {}, 完成任务: {}", id, taskCount, completedCount);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("更新路线任务统计失败，ID: {}", id, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean startRoute(Long routeId) {
        try {
            if (routeId == null) {
                throw new RuntimeException("路线ID不能为空");
            }
            
            DeliveryRoute route = routeMapper.selectById(routeId);
            if (route == null) {
                throw new RuntimeException("配送路线不存在");
            }
            
            route.setRouteStatus("IN_PROGRESS");
            route.setStartTime(LocalDateTime.now());
            route.setUpdateTime(LocalDateTime.now());
            
            int result = routeMapper.updateById(route);
            if (result > 0) {
                log.info("开始路线成功，路线ID: {}", routeId);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("开始路线失败，路线ID: {}", routeId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean completeRoute(Long routeId) {
        try {
            if (routeId == null) {
                throw new RuntimeException("路线ID不能为空");
            }
            
            DeliveryRoute route = routeMapper.selectById(routeId);
            if (route == null) {
                throw new RuntimeException("配送路线不存在");
            }
            
            route.setRouteStatus("COMPLETED");
            route.setEndTime(LocalDateTime.now());
            route.setUpdateTime(LocalDateTime.now());
            
            int result = routeMapper.updateById(route);
            if (result > 0) {
                log.info("完成路线成功，路线ID: {}", routeId);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("完成路线失败，路线ID: {}", routeId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean cancelRoute(Long routeId, String reason) {
        try {
            if (routeId == null) {
                throw new RuntimeException("路线ID不能为空");
            }
            
            DeliveryRoute route = routeMapper.selectById(routeId);
            if (route == null) {
                throw new RuntimeException("配送路线不存在");
            }
            
            route.setRouteStatus("CANCELLED");
            route.setRemarks(reason);
            route.setUpdateTime(LocalDateTime.now());
            
            int result = routeMapper.updateById(route);
            if (result > 0) {
                log.info("取消路线成功，路线ID: {}, 原因: {}", routeId, reason);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("取消路线失败，路线ID: {}", routeId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean deleteRoute(Long routeId) {
        try {
            if (routeId == null) {
                throw new RuntimeException("路线ID不能为空");
            }
            
            DeliveryRoute route = routeMapper.selectById(routeId);
            if (route == null) {
                throw new RuntimeException("配送路线不存在");
            }
            
            // 只能删除计划中或已取消的路线
            if (!"PLANNED".equals(route.getRouteStatus()) && !"CANCELLED".equals(route.getRouteStatus())) {
                throw new RuntimeException("只能删除计划中或已取消的路线");
            }
            
            int result = routeMapper.deleteById(routeId);
            if (result > 0) {
                log.info("删除路线成功，ID: {}", routeId);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("删除路线失败，ID: {}", routeId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public DeliveryRoute generateDailyRoute(Long courierId, LocalDate routeDate) {
        try {
            if (courierId == null || routeDate == null) {
                throw new RuntimeException("参数不能为空");
            }
            
            // 检查配送员是否存在
            Courier courier = courierMapper.selectById(courierId);
            if (courier == null) {
                throw new RuntimeException("配送员不存在");
            }
            
            // 检查当日是否已有路线
            DeliveryRoute existing = routeMapper.findByCourierAndDate(courierId, routeDate);
            if (existing != null) {
                throw new RuntimeException("配送员当日已存在路线");
            }
            
            // 创建新路线
            DeliveryRoute route = new DeliveryRoute();
            route.setRouteNumber(generateRouteNumber());
            route.setCourierId(courierId);
            route.setRouteName(courier.getCourierName() + "_" + routeDate);
            route.setRouteDate(routeDate);
            route.setRouteStatus("PLANNED");
            route.setTaskCount(0);
            route.setCompletedCount(0);
            route.setCreateTime(LocalDateTime.now());
            route.setUpdateTime(LocalDateTime.now());
            
            routeMapper.insert(route);
            log.info("生成当日路线成功，配送员ID: {}, 日期: {}", courierId, routeDate);
            return route;
        } catch (Exception e) {
            log.error("生成当日路线失败，配送员ID: {}, 日期: {}", courierId, routeDate, e);
            throw new RuntimeException("生成当日路线失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public DeliveryRoute optimizeRoute(Long routeId) {
        try {
            if (routeId == null) {
                throw new RuntimeException("路线ID不能为空");
            }
            
            DeliveryRoute route = routeMapper.selectById(routeId);
            if (route == null) {
                throw new RuntimeException("配送路线不存在");
            }
            
            // 简单的路线优化逻辑 - 实际项目中可以集成路径规划算法
            // 这里只是更新更新时间表示已优化
            route.setUpdateTime(LocalDateTime.now());
            routeMapper.updateById(route);
            
            log.info("路线优化完成，路线ID: {}", routeId);
            return route;
        } catch (Exception e) {
            log.error("路线优化失败，路线ID: {}", routeId, e);
            throw new RuntimeException("路线优化失败: " + e.getMessage());
        }
    }

    /**
     * 验证路线信息
     */
    private void validateRouteInfo(DeliveryRoute route) {
        if (route.getCourierId() == null) {
            throw new RuntimeException("配送员ID不能为空");
        }
        
        if (route.getRouteDate() == null) {
            throw new RuntimeException("路线日期不能为空");
        }
    }

    /**
     * 生成路线编号
     */
    private String generateRouteNumber() {
        return "ROUTE" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 4).toUpperCase();
    }
}