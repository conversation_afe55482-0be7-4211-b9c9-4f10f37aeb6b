# 物流网关服务 (logistics-gateway)

物流跟踪系统的API网关服务，提供统一入口、认证授权、限流熔断、监控统计等功能。

## 🚀 功能特性

### 核心功能
- **统一API入口** - 所有微服务的统一访问入口
- **JWT认证授权** - 完整的Token验证和用户信息传递
- **IP黑白名单** - 基于IP的访问控制
- **分布式限流** - 基于Redis的滑动窗口限流算法
- **请求监控** - 完整的API调用统计和性能监控
- **跨域支持** - CORS跨域请求处理
- **负载均衡** - 服务发现和负载均衡

### 技术特性
- **响应式架构** - 基于Spring Cloud Gateway的响应式编程
- **Redis缓存** - 分布式缓存和限流存储
- **实时监控** - 请求统计、慢查询监控、错误分析
- **健康检查** - 服务状态监控和故障诊断
- **动态配置** - 支持配置热更新
- **高性能** - 异步非阻塞处理，支持高并发

## 📦 技术栈

- **Spring Boot 2.7.8** - 微服务框架
- **Spring Cloud Gateway** - API网关
- **Spring Cloud Nacos** - 服务注册发现
- **Redis** - 缓存和限流存储
- **JWT** - 认证令牌
- **Micrometer** - 指标监控
- **Bucket4j** - 限流算法
- **Fastjson2** - JSON处理

## 🔧 配置说明

### 1. 基础配置

```yaml
server:
  port: 8080

spring:
  application:
    name: logistics-gateway
  main:
    web-application-type: reactive
  
  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: redis123
    database: 0
    timeout: 3000ms
  
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
    gateway:
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
```

### 2. 安全配置

```yaml
gateway:
  security:
    # JWT配置
    jwt:
      secret: your-jwt-secret-key-32-chars-min
      expiration: 7200000  # 2小时
    
    # 路径白名单
    whitelist:
      - "/api/user/register"
      - "/api/user/login"
      - "/api/*/test/**"
      - "/actuator/**"
    
    # IP黑名单
    blacklist:
      enabled: true
      ips: []
    
    # IP白名单（可选）
    ipWhitelist:
      enabled: false
      ips: []
```

### 3. 限流配置

```yaml
gateway:
  ratelimit:
    enabled: true
    default:
      replenishRate: 100    # 令牌桶每秒填充速率
      burstCapacity: 200    # 令牌桶总容量
      requestedTokens: 1    # 每次请求消耗令牌数
    
    # 特殊接口限流
    special:
      "/api/user/login":
        replenishRate: 10
        burstCapacity: 20
      "/api/user/register":
        replenishRate: 5
        burstCapacity: 10
```

### 4. 路由配置

```yaml
spring:
  cloud:
    gateway:
      routes:
        # 用户服务路由
        - id: logistics-user
          uri: lb://logistics-user
          predicates:
            - Path=/api/user/**
          filters:
            - StripPrefix=2
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 100
                redis-rate-limiter.burstCapacity: 200
```

## 📚 API接口文档

### 管理接口

#### 1. 健康检查

```http
GET /gateway/health
```

**响应示例：**
```json
{
  "status": "UP",
  "timestamp": 1703462400000,
  "service": "logistics-gateway",
  "version": "1.0.0",
  "redis": "UP"
}
```

#### 2. 统计信息

```http
GET /gateway/stats
```

**响应示例：**
```json
{
  "memory": {
    "totalRequests": 1000,
    "successRequests": 950,
    "errorRequests": 50
  },
  "today": {
    "total": "1000",
    "success": "950",
    "error": "50",
    "total_duration": "50000"
  },
  "avgResponseTime": 50,
  "successRate": 95.0
}
```

#### 3. 慢请求查询

```http
GET /gateway/slow-requests?start=0&end=10
```

#### 4. API使用统计

```http
GET /gateway/api-stats
```

#### 5. 网关配置查询

```http
GET /gateway/config
```

### JWT管理接口

#### 1. 生成测试Token

```http
POST /gateway/generate-token
Content-Type: application/x-www-form-urlencoded

userId=123&username=test&role=user
```

**响应示例：**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "userId": "123",
  "username": "test",
  "role": "user",
  "expiration": 7200000,
  "generateTime": 1703462400000
}
```

#### 2. 验证Token

```http
POST /gateway/validate-token
Content-Type: application/x-www-form-urlencoded

token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 3. 刷新Token

```http
POST /gateway/refresh-token
Content-Type: application/x-www-form-urlencoded

token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 业务接口路由

所有业务接口通过网关统一访问，路由规则：

- **用户服务**: `/api/user/**` → `logistics-user`
- **订单服务**: `/api/order/**` → `logistics-order`
- **物流服务**: `/api/logistics/**` → `logistics-logistics`
- **配送服务**: `/api/delivery/**` → `logistics-delivery`
- **通知服务**: `/api/notification/**` → `logistics-notification`
- **地图服务**: `/api/map/**` → `logistics-map`

## 🛠️ 使用指南

### 1. 客户端请求示例

```javascript
// 带Token的请求
const response = await fetch('http://localhost:8080/api/user/profile', {
    method: 'GET',
    headers: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/json'
    }
});

// 检查Token刷新提示
if (response.headers.get('X-Token-Refresh') === 'true') {
    const remaining = response.headers.get('X-Token-Remaining');
    console.log(`Token剩余时间：${remaining}秒`);
    // 执行Token刷新逻辑
}
```

### 2. 用户信息传递

网关验证JWT后，会在请求头中添加用户信息：

```http
X-User-Id: 123
X-Username: test
X-User-Role: user
X-Client-IP: *************
```

下游服务可直接从请求头获取用户信息，无需再次验证JWT。

### 3. 限流处理

当触发限流时，返回429状态码：

```json
{
  "code": 429,
  "message": "请求过于频繁，请稍后再试",
  "timestamp": 1703462400000,
  "path": "/api/user/login"
}
```

客户端应根据`X-Rate-Limit-Retry-After`响应头等待重试。

## 🔒 安全特性

### 1. JWT认证流程

1. 客户端登录获取JWT Token
2. 后续请求携带Token
3. 网关验证Token有效性
4. 提取用户信息传递给下游服务
5. Token即将过期时返回刷新提示

### 2. IP访问控制

- **黑名单模式**: 禁止指定IP访问
- **白名单模式**: 只允许指定IP访问
- **动态更新**: 支持配置热更新

### 3. 分布式限流

- **滑动窗口算法**: 精确控制访问频率
- **Redis存储**: 支持分布式部署
- **差异化限流**: 不同接口不同限制
- **优雅降级**: 限流异常时默认放行

## 📊 监控和运维

### 1. 监控指标

#### 请求统计
- 总请求数、成功数、失败数
- 平均响应时间、最大响应时间
- 慢请求记录（>1秒）
- API调用排行

#### 系统指标
- JVM内存使用情况
- 系统负载信息
- Redis连接状态
- 服务注册状态

### 2. 日志记录

```
# 请求日志格式
API请求开始 - Method: GET, Path: /api/user/profile, IP: *************
API请求完成 - Method: GET, Path: /api/user/profile, Status: 200, Duration: 50ms

# 异常日志格式
API请求异常 - Method: POST, Path: /api/user/login, IP: *************, Error: JWT验证失败

# 慢请求日志
发现慢请求 - Method: GET, Path: /api/order/list, Duration: 1500ms, Status: 200
```

### 3. 告警配置

建议配置以下告警：

- 错误率超过5%
- 平均响应时间超过500ms
- Redis连接失败
- 大量限流触发

## 🚨 故障排查

### 常见问题

#### 1. 认证失败
- 检查JWT密钥配置
- 确认Token格式正确
- 验证Token是否过期

#### 2. 限流触发
- 检查Redis连接状态
- 调整限流参数
- 查看慢请求日志

#### 3. 路由失败
- 确认下游服务注册状态
- 检查路由配置
- 验证负载均衡器状态

#### 4. 跨域问题
- 检查CORS配置
- 确认预检请求处理

### 调试工具

```bash
# 健康检查
curl http://localhost:8080/gateway/health

# 查看统计
curl http://localhost:8080/gateway/stats

# 生成测试Token
curl -X POST "http://localhost:8080/gateway/generate-token" \
     -d "userId=test&username=testuser&role=user"

# 验证Token
curl -X POST "http://localhost:8080/gateway/validate-token" \
     -d "token=YOUR_JWT_TOKEN"
```

## 📈 性能优化

### 1. JVM参数优化

```bash
java -jar logistics-gateway.jar \
  -Xms512m -Xmx1024m \
  -XX:+UseG1GC \
  -XX:MaxGCPauseMillis=200 \
  -XX:+HeapDumpOnOutOfMemoryError
```

### 2. Redis连接池优化

```yaml
spring:
  redis:
    lettuce:
      pool:
        max-active: 200
        max-wait: -1ms
        max-idle: 10
        min-idle: 0
```

### 3. 网关线程池优化

```yaml
spring:
  cloud:
    gateway:
      httpclient:
        pool:
          max-connections: 500
          max-idle-time: 30s
```

## 📄 许可证

本项目采用 MIT 许可证。

---

**版本**: 1.0.0  
**更新时间**: 2024-12-25  
**开发团队**: 物流系统开发组 