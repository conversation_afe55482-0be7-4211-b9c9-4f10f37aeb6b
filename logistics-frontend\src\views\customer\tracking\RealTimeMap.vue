<template>
  <div class="real-time-map">
    <div class="map-header">
      <div class="order-info">
        <h3>实时配送追踪</h3>
        <p v-if="orderInfo">订单号：{{ orderInfo.orderNumber }}</p>
        <p v-if="trackingInfo">当前状态：{{ getStatusText(trackingInfo.currentStatus) }}</p>
      </div>
      <div class="refresh-controls">
        <el-switch
          v-model="autoRefresh"
          active-text="自动刷新"
          @change="toggleAutoRefresh"
        />
        <el-button @click="refreshTracking" :loading="loading" :icon="Refresh">
          刷新位置
        </el-button>
      </div>
    </div>

    <!-- 地图容器 -->
    <div class="map-container">
      <div id="realtime-map" class="map-view"></div>
      
      <!-- 配送员信息卡片 -->
      <div v-if="courierInfo" class="courier-card">
        <div class="courier-header">
          <el-icon><User /></el-icon>
          <span>{{ courierInfo.name }}</span>
          <el-tag :type="getCourierStatusType(courierInfo.status)" size="small">
            {{ getCourierStatusText(courierInfo.status) }}
          </el-tag>
        </div>
        <div class="courier-contact">
          <el-button type="text" @click="callCourier" :icon="Phone">
            {{ courierInfo.phone }}
          </el-button>
        </div>
        <div class="delivery-info">
          <p v-if="estimatedTime">预计到达：{{ estimatedTime }}</p>
          <p v-if="distance">距离：{{ distance }}km</p>
        </div>
      </div>
    </div>

    <!-- 轨迹时间线 -->
    <div class="tracking-timeline">
      <h4>配送轨迹</h4>
      <el-timeline v-if="trackingNodes.length > 0">
        <el-timeline-item
          v-for="(node, index) in trackingNodes"
          :key="index"
          :timestamp="formatTime(node.createTime)"
          :type="getTimelineType(node.status)"
        >
          <div class="timeline-content">
            <div class="timeline-title">{{ node.description }}</div>
            <div class="timeline-location" v-if="node.location">
              <el-icon><Location /></el-icon>
              {{ node.location }}
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
      <el-empty v-else description="暂无轨迹信息" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  User,
  Phone,
  Location
} from '@element-plus/icons-vue'
import { orderApi } from '@/api/order'
import { trackingApi } from '@/api/tracking'
import { courierApi } from '@/api/courier'
import dayjs from 'dayjs'

// 引入高德地图
declare global {
  interface Window {
    AMap: any
  }
}

const route = useRoute()

// 状态
const loading = ref(false)
const autoRefresh = ref(true)
const orderInfo = ref<any>(null)
const trackingInfo = ref<any>(null)
const trackingNodes = ref<any[]>([])
const courierInfo = ref<any>(null)
const estimatedTime = ref('')
const distance = ref(0)

// 地图相关
let map: any = null
let courierMarker: any = null
let customerMarker: any = null
let routeLine: any = null
let refreshTimer: any = null

// 加载订单和追踪信息
const loadTrackingInfo = async () => {
  const orderNumber = route.params.orderNumber as string
  if (!orderNumber) {
    ElMessage.error('订单号不正确')
    return
  }

  loading.value = true
  try {
    // 并行加载订单信息和轨迹信息
    const [orderResponse, trackingResponse] = await Promise.allSettled([
      orderApi.getOrderByNumber(orderNumber),
      trackingApi.getTrackingByOrderNumber(orderNumber)
    ])

    // 处理订单信息
    if (orderResponse.status === 'fulfilled' && orderResponse.value.code === 200) {
      orderInfo.value = orderResponse.value.data
    }

    // 处理轨迹信息
    if (trackingResponse.status === 'fulfilled' && trackingResponse.value.code === 200) {
      trackingInfo.value = trackingResponse.value.data
      trackingNodes.value = trackingResponse.value.data.trackingNodes || []
      
      // 如果有配送员信息，加载配送员详情
      if (trackingInfo.value.courierId) {
        await loadCourierInfo(trackingInfo.value.courierId)
      }
      
      // 更新地图
      updateMap()
    } else {
      ElMessage.warning('暂无物流轨迹信息')
    }
  } catch (error) {
    console.error('加载追踪信息失败:', error)
    ElMessage.error('加载追踪信息失败')
  } finally {
    loading.value = false
  }
}

// 加载配送员信息
const loadCourierInfo = async (courierId: number) => {
  try {
    const response = await courierApi.getCourierById(courierId)
    if (response.code === 200) {
      courierInfo.value = response.data
    }
  } catch (error) {
    console.error('加载配送员信息失败:', error)
  }
}

// 初始化地图
const initMap = () => {
  if (!window.AMap) {
    console.error('高德地图未加载')
    return
  }

  map = new window.AMap.Map('realtime-map', {
    zoom: 13,
    center: [116.397428, 39.90923], // 默认北京
    mapStyle: 'amap://styles/normal'
  })

  // 添加地图控件
  map.addControl(new window.AMap.Scale())
  map.addControl(new window.AMap.ToolBar())
}

// 更新地图
const updateMap = () => {
  if (!map || !orderInfo.value) return

  // 清除之前的标记
  if (courierMarker) {
    map.remove(courierMarker)
  }
  if (customerMarker) {
    map.remove(customerMarker)
  }
  if (routeLine) {
    map.remove(routeLine)
  }

  try {
    // 解析地址，获取坐标
    const geocoder = new window.AMap.Geocoder()
    
    // 收货地址标记
    geocoder.getLocation(orderInfo.value.receiverAddress, (status: string, result: any) => {
      if (status === 'complete' && result.geocodes.length > 0) {
        const location = result.geocodes[0].location
        
        customerMarker = new window.AMap.Marker({
          position: location,
          title: '收货地址',
          icon: new window.AMap.Icon({
            image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png',
            size: new window.AMap.Size(19, 33)
          })
        })
        map.add(customerMarker)
        
        // 如果有配送员位置，添加配送员标记
        if (courierInfo.value && courierInfo.value.longitude && courierInfo.value.latitude) {
          const courierPosition = new window.AMap.LngLat(courierInfo.value.longitude, courierInfo.value.latitude)
          
          courierMarker = new window.AMap.Marker({
            position: courierPosition,
            title: '配送员位置',
            icon: new window.AMap.Icon({
              image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
              size: new window.AMap.Size(19, 33)
            })
          })
          map.add(courierMarker)
          
          // 绘制路线
          drawRoute(courierPosition, location)
          
          // 计算距离和预计时间
          calculateDistanceAndTime(courierPosition, location)
        }
        
        // 调整地图视野
        const bounds = new window.AMap.Bounds()
        bounds.extend(location)
        if (courierMarker) {
          bounds.extend(courierMarker.getPosition())
        }
        map.setBounds(bounds)
      }
    })
  } catch (error) {
    console.error('更新地图失败:', error)
  }
}

// 绘制路线
const drawRoute = (start: any, end: any) => {
  const driving = new window.AMap.Driving({
    map: map,
    hideMarkers: true
  })
  
  driving.search(start, end, (status: string, result: any) => {
    if (status === 'complete') {
      // 路线绘制成功
      console.log('路线规划成功')
    }
  })
}

// 计算距离和预计时间
const calculateDistanceAndTime = (start: any, end: any) => {
  const distance_m = start.distance(end)
  distance.value = Math.round(distance_m / 1000 * 10) / 10 // 保留一位小数
  
  // 简单估算时间（假设平均速度30km/h）
  const estimatedMinutes = Math.round(distance.value / 30 * 60)
  const now = dayjs()
  estimatedTime.value = now.add(estimatedMinutes, 'minute').format('HH:mm')
}

// 刷新追踪信息
const refreshTracking = () => {
  loadTrackingInfo()
}

// 切换自动刷新
const toggleAutoRefresh = (enabled: boolean) => {
  if (enabled) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

// 开始自动刷新
const startAutoRefresh = () => {
  stopAutoRefresh() // 先停止之前的定时器
  refreshTimer = setInterval(() => {
    loadTrackingInfo()
  }, 30000) // 30秒刷新一次
}

// 停止自动刷新
const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 拨打配送员电话
const callCourier = () => {
  if (courierInfo.value?.phone) {
    window.location.href = `tel:${courierInfo.value.phone}`
  }
}

// 工具函数
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    CREATED: '订单已创建',
    PAID: '已支付',
    PICKUP_ASSIGNED: '已分配配送员',
    PICKUP: '配送员已接单',
    IN_TRANSIT: '配送中',
    OUT_FOR_DELIVERY: '派送中',
    DELIVERED: '已送达',
    CANCELLED: '已取消'
  }
  return statusMap[status] || status
}

const getCourierStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    ONLINE: 'success',
    BUSY: 'warning',
    OFFLINE: 'info',
    REST: 'info'
  }
  return typeMap[status] || 'info'
}

const getCourierStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    ONLINE: '在线',
    BUSY: '配送中',
    OFFLINE: '离线',
    REST: '休息中'
  }
  return textMap[status] || status
}

const getTimelineType = (status: string) => {
  const typeMap: Record<string, string> = {
    CREATED: 'info',
    PAID: 'success',
    PICKUP_ASSIGNED: 'warning',
    PICKUP: 'primary',
    IN_TRANSIT: 'warning',
    DELIVERED: 'success',
    CANCELLED: 'danger'
  }
  return typeMap[status] || 'info'
}

const formatTime = (time: string) => {
  return dayjs(time).format('MM-DD HH:mm')
}

// 生命周期
onMounted(() => {
  initMap()
  loadTrackingInfo()
  if (autoRefresh.value) {
    startAutoRefresh()
  }
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.real-time-map {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.order-info h3 {
  margin: 0 0 5px 0;
  color: #333;
}

.order-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.refresh-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.map-container {
  position: relative;
  height: 500px;
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.map-view {
  width: 100%;
  height: 100%;
}

.courier-card {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 250px;
  background: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.courier-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
  font-weight: bold;
}

.courier-contact {
  margin-bottom: 10px;
}

.delivery-info p {
  margin: 5px 0;
  color: #666;
  font-size: 14px;
}

.tracking-timeline {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.tracking-timeline h4 {
  margin: 0 0 15px 0;
  color: #333;
}

.timeline-content {
  padding-left: 15px;
}

.timeline-title {
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.timeline-location {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #666;
  font-size: 14px;
}
</style> 