package org.example.logisticsmap.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.nacos.common.utils.StringUtils;
import com.logistics.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.example.logisticsmap.config.AmapConfig;
import org.example.logisticsmap.entity.Location;
import org.example.logisticsmap.service.MapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 前端兼容的地图API控制器
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Slf4j
@RestController
@RequestMapping("/map")
@CrossOrigin(origins = "*")
public class FrontendMapController {

    @Autowired
    private MapService mapService;

    @Autowired
    private AmapConfig amapConfig;

    /**
     * 获取前端地图配置
     */
    @GetMapping("/config")
    public Result<Map<String, Object>> getMapConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            config.put("jsApiKey", amapConfig.getJsApiKey());
            config.put("provider", "amap");
            config.put("defaultCenter", Map.of(
                    "lat", 39.915,
                    "lng", 116.404
            ));
            config.put("defaultZoom", 12);
            config.put("baseUrl", "https://webapi.amap.com/maps");

            return Result.success("获取地图配置成功", config);
        } catch (Exception e) {
            log.error("获取地图配置失败", e);
            return Result.error("获取地图配置失败: " + e.getMessage());
        }
    }

    /**
     * 地址转坐标（前端兼容接口）
     */
    @GetMapping("/geocode")
    public Result<Map<String, Object>> geocode(@RequestParam String address) {
        try {
            Location location = mapService.addressToLocation(address);
            if (location != null) {
                Map<String, Object> result = new HashMap<>();
                result.put("lat", location.getLatitude());
                result.put("lng", location.getLongitude());
                result.put("address", location.getAddress());
                result.put("province", location.getProvince());
                result.put("city", location.getCity());
                result.put("district", location.getDistrict());
                return Result.success("地址转坐标成功", result);
            } else {
                return Result.error("地址解析失败");
            }
        } catch (Exception e) {
            log.error("地址转坐标失败", e);
            return Result.error("地址转坐标失败: " + e.getMessage());
        }
    }

    /**
     * 坐标转地址（前端兼容接口）
     */
    @GetMapping("/reverse-geocode")
    public Result<Map<String, Object>> reverseGeocode(
            @RequestParam Double lat,
            @RequestParam Double lng) {
        try {
            Location location = mapService.locationToAddress(lng, lat);
            if (location != null) {
                Map<String, Object> result = new HashMap<>();
                result.put("address", location.getAddress());
                result.put("province", location.getProvince());
                result.put("city", location.getCity());
                result.put("district", location.getDistrict());
                result.put("street", location.getStreet());
                return Result.success("坐标转地址成功", result);
            } else {
                return Result.error("坐标解析失败");
            }
        } catch (Exception e) {
            log.error("坐标转地址失败", e);
            return Result.error("坐标转地址失败: " + e.getMessage());
        }
    }

    /**
     * 距离计算（前端兼容接口）
     */
    @PostMapping("/distance")
    public Result<Map<String, Object>> calculateDistance(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> from = (Map<String, Object>) request.get("from");
            @SuppressWarnings("unchecked")
            Map<String, Object> to = (Map<String, Object>) request.get("to");

            Double fromLng = Double.valueOf(from.get("lng").toString());
            Double fromLat = Double.valueOf(from.get("lat").toString());
            Double toLng = Double.valueOf(to.get("lng").toString());
            Double toLat = Double.valueOf(to.get("lat").toString());

            // 计算直线距离
            Double straightDistance = mapService.calculateStraightDistance(fromLng, fromLat, toLng, toLat);

            // 计算驾车距离
            Double drivingDistance = mapService.calculateDrivingDistance(fromLng, fromLat, toLng, toLat);

            Map<String, Object> result = new HashMap<>();
            result.put("straightDistance", straightDistance);
            result.put("drivingDistance", drivingDistance);

            return Result.success("距离计算成功", result);
        } catch (Exception e) {
            log.error("距离计算失败", e);
            return Result.error("距离计算失败: " + e.getMessage());
        }
    }

    /**
     * 路线规划（前端兼容接口）
     */
    @PostMapping("/route")
    public Result<Map<String, Object>> calculateRoute(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> origin = (Map<String, Object>) request.get("origin");
            @SuppressWarnings("unchecked")
            Map<String, Object> destination = (Map<String, Object>) request.get("destination");

            Double originLng = Double.valueOf(origin.get("lng").toString());
            Double originLat = Double.valueOf(origin.get("lat").toString());
            Double destLng = Double.valueOf(destination.get("lng").toString());
            Double destLat = Double.valueOf(destination.get("lat").toString());

            Map<String, Object> route = mapService.getBestDrivingRoute(originLng, originLat, destLng, destLat);

            return Result.success("路线规划成功", route);
        } catch (Exception e) {
            log.error("路线规划失败", e);
            return Result.error("路线规划失败: " + e.getMessage());
        }
    }

    /**
     * 获取省市区数据
     */
    @GetMapping("/regions")
    public Result<Object> getRegions(
            @RequestParam(required = false) String keywords,
            @RequestParam(required = false, defaultValue = "3") String subdistrict) {
        try {
            // 获取中国的省市区三级数据
            Object regions = mapService.getDistrictData("中国", "3");

            log.info("高德API返回数据: {}", regions);

            return Result.success("获取省市区数据成功", regions);
        } catch (Exception e) {
            log.error("获取省市区数据失败", e);
            // 返回静态数据作为兜底
            return Result.success("获取省市区数据失败");
        }
    }


    /**
     * 地址搜索提示
     */
    @GetMapping("/search")
    public Result<Map<String, Object>> searchAddress(
            @RequestParam String keyword,
            @RequestParam(required = false) String city) {
        try {
            Map<String, Object> result = mapService.inputTips(keyword, city);
            return Result.success("地址搜索成功", result);
        } catch (Exception e) {
            log.error("地址搜索失败", e);
            return Result.error("地址搜索失败: " + e.getMessage());
        }
    }

    /**
     * 批量地址转坐标
     */
    @PostMapping("/batch/geocode")
    public Result<List<Location>> batchGeocode(@RequestBody List<String> addresses) {
        try {
            List<Location> locations = mapService.batchAddressToLocation(addresses);
            return Result.success("批量地址转坐标成功", locations);
        } catch (Exception e) {
            log.error("批量地址转坐标失败", e);
            return Result.error("批量地址转坐标失败: " + e.getMessage());
        }
    }

    /**
     * 构建省市区数据
     */
    private Object buildRegionData() {
        String jsonData = "{\n" +
                "  \"provinces\": [\n" +
                "    {\n" +
                "      \"code\": \"110000\",\n" +
                "      \"name\": \"北京市\",\n" +
                "      \"children\": [\n" +
                "        {\n" +
                "          \"code\": \"110100\",\n" +
                "          \"name\": \"北京市\",\n" +
                "          \"children\": [\n" +
                "            {\"code\": \"110101\", \"name\": \"东城区\"},\n" +
                "            {\"code\": \"110102\", \"name\": \"西城区\"},\n" +
                "            {\"code\": \"110105\", \"name\": \"朝阳区\"},\n" +
                "            {\"code\": \"110106\", \"name\": \"丰台区\"},\n" +
                "            {\"code\": \"110107\", \"name\": \"石景山区\"},\n" +
                "            {\"code\": \"110108\", \"name\": \"海淀区\"},\n" +
                "            {\"code\": \"110109\", \"name\": \"门头沟区\"},\n" +
                "            {\"code\": \"110111\", \"name\": \"房山区\"},\n" +
                "            {\"code\": \"110112\", \"name\": \"通州区\"},\n" +
                "            {\"code\": \"110113\", \"name\": \"顺义区\"}\n" +
                "          ]\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"code\": \"310000\",\n" +
                "      \"name\": \"上海市\",\n" +
                "      \"children\": [\n" +
                "        {\n" +
                "          \"code\": \"310100\",\n" +
                "          \"name\": \"上海市\",\n" +
                "          \"children\": [\n" +
                "            {\"code\": \"310101\", \"name\": \"黄浦区\"},\n" +
                "            {\"code\": \"310104\", \"name\": \"徐汇区\"},\n" +
                "            {\"code\": \"310105\", \"name\": \"长宁区\"},\n" +
                "            {\"code\": \"310106\", \"name\": \"静安区\"},\n" +
                "            {\"code\": \"310107\", \"name\": \"普陀区\"},\n" +
                "            {\"code\": \"310109\", \"name\": \"虹口区\"},\n" +
                "            {\"code\": \"310110\", \"name\": \"杨浦区\"},\n" +
                "            {\"code\": \"310112\", \"name\": \"闵行区\"},\n" +
                "            {\"code\": \"310113\", \"name\": \"宝山区\"},\n" +
                "            {\"code\": \"310115\", \"name\": \"浦东新区\"}\n" +
                "          ]\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"code\": \"440000\",\n" +
                "      \"name\": \"广东省\",\n" +
                "      \"children\": [\n" +
                "        {\n" +
                "          \"code\": \"440100\",\n" +
                "          \"name\": \"广州市\",\n" +
                "          \"children\": [\n" +
                "            {\"code\": \"440103\", \"name\": \"荔湾区\"},\n" +
                "            {\"code\": \"440104\", \"name\": \"越秀区\"},\n" +
                "            {\"code\": \"440105\", \"name\": \"海珠区\"},\n" +
                "            {\"code\": \"440106\", \"name\": \"天河区\"},\n" +
                "            {\"code\": \"440111\", \"name\": \"白云区\"},\n" +
                "            {\"code\": \"440112\", \"name\": \"黄埔区\"},\n" +
                "            {\"code\": \"440113\", \"name\": \"番禺区\"},\n" +
                "            {\"code\": \"440114\", \"name\": \"花都区\"}\n" +
                "          ]\n" +
                "        },\n" +
                "        {\n" +
                "          \"code\": \"440300\",\n" +
                "          \"name\": \"深圳市\",\n" +
                "          \"children\": [\n" +
                "            {\"code\": \"440303\", \"name\": \"罗湖区\"},\n" +
                "            {\"code\": \"440304\", \"name\": \"福田区\"},\n" +
                "            {\"code\": \"440305\", \"name\": \"南山区\"},\n" +
                "            {\"code\": \"440306\", \"name\": \"宝安区\"},\n" +
                "            {\"code\": \"440307\", \"name\": \"龙岗区\"},\n" +
                "            {\"code\": \"440308\", \"name\": \"盐田区\"}\n" +
                "          ]\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"code\": \"320000\",\n" +
                "      \"name\": \"江苏省\",\n" +
                "      \"children\": [\n" +
                "        {\n" +
                "          \"code\": \"320100\",\n" +
                "          \"name\": \"南京市\",\n" +
                "          \"children\": [\n" +
                "            {\"code\": \"320102\", \"name\": \"玄武区\"},\n" +
                "            {\"code\": \"320104\", \"name\": \"秦淮区\"},\n" +
                "            {\"code\": \"320105\", \"name\": \"建邺区\"},\n" +
                "            {\"code\": \"320106\", \"name\": \"鼓楼区\"}\n" +
                "          ]\n" +
                "        },\n" +
                "        {\n" +
                "          \"code\": \"320500\",\n" +
                "          \"name\": \"苏州市\",\n" +
                "          \"children\": [\n" +
                "            {\"code\": \"320505\", \"name\": \"虎丘区\"},\n" +
                "            {\"code\": \"320506\", \"name\": \"吴中区\"},\n" +
                "            {\"code\": \"320507\", \"name\": \"相城区\"}\n" +
                "          ]\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"code\": \"330000\",\n" +
                "      \"name\": \"浙江省\",\n" +
                "      \"children\": [\n" +
                "        {\n" +
                "          \"code\": \"330100\",\n" +
                "          \"name\": \"杭州市\",\n" +
                "          \"children\": [\n" +
                "            {\"code\": \"330102\", \"name\": \"上城区\"},\n" +
                "            {\"code\": \"330105\", \"name\": \"拱墅区\"},\n" +
                "            {\"code\": \"330106\", \"name\": \"西湖区\"}\n" +
                "          ]\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}";

        return JSON.parseObject(jsonData, Map.class);
    }
}