package org.example.logisticslogistics.controller;

import com.logistics.common.result.Result;
import org.example.logisticslogistics.service.MonitorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 监控统计控制器
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@RestController
@RequestMapping("/logistics/monitor")
public class MonitorController {

    @Autowired
    private MonitorService monitorService;

    /**
     * 获取实时监控数据
     */
    @GetMapping("/realtime")
    public Result<Map<String, Object>> getRealTimeMonitorData() {
        try {
            Map<String, Object> data = monitorService.getRealTimeMonitorData();
            return Result.success(data);
        } catch (Exception e) {
            return Result.error("获取实时监控数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取状态统计
     */
    @GetMapping("/statistics/status")
    public Result<Map<String, Long>> getStatusStatistics() {
        try {
            Map<String, Long> statistics = monitorService.getStatusStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            return Result.error("获取状态统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取城市统计
     */
    @GetMapping("/statistics/city")
    public Result<Map<String, Long>> getCityStatistics() {
        try {
            Map<String, Long> statistics = monitorService.getCityStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            return Result.error("获取城市统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取配送员绩效数据
     */
    @GetMapping("/performance/courier/{courierId}")
    public Result<Map<String, Object>> getCourierPerformance(
            @PathVariable Long courierId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            Map<String, Object> performance = monitorService.getCourierPerformance(courierId, startTime, endTime);
            return Result.success(performance);
        } catch (Exception e) {
            return Result.error("获取配送员绩效数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取今日统计数据
     */
    @GetMapping("/statistics/today")
    public Result<Map<String, Object>> getTodayStatistics() {
        try {
            Map<String, Object> statistics = monitorService.getTodayStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            return Result.error("获取今日统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取本周统计数据
     */
    @GetMapping("/statistics/weekly")
    public Result<Map<String, Object>> getWeeklyStatistics() {
        try {
            Map<String, Object> statistics = monitorService.getWeeklyStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            return Result.error("获取本周统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取本月统计数据
     */
    @GetMapping("/statistics/monthly")
    public Result<Map<String, Object>> getMonthlyStatistics() {
        try {
            Map<String, Object> statistics = monitorService.getMonthlyStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            return Result.error("获取本月统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取异常订单统计
     */
    @GetMapping("/statistics/exceptions")
    public Result<Map<String, Object>> getExceptionStatistics() {
        try {
            Map<String, Object> statistics = monitorService.getExceptionStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            return Result.error("获取异常订单统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取配送效率统计
     */
    @GetMapping("/statistics/delivery-efficiency")
    public Result<Map<String, Object>> getDeliveryEfficiencyStats() {
        try {
            Map<String, Object> statistics = monitorService.getDeliveryEfficiencyStats();
            return Result.success(statistics);
        } catch (Exception e) {
            return Result.error("获取配送效率统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取热门配送区域统计
     */
    @GetMapping("/statistics/popular-areas")
    public Result<Map<String, Object>> getPopularDeliveryAreas() {
        try {
            Map<String, Object> statistics = monitorService.getPopularDeliveryAreas();
            return Result.success(statistics);
        } catch (Exception e) {
            return Result.error("获取热门配送区域统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取配送时间分布统计
     */
    @GetMapping("/statistics/time-distribution")
    public Result<Map<String, Object>> getDeliveryTimeDistribution() {
        try {
            Map<String, Object> statistics = monitorService.getDeliveryTimeDistribution();
            return Result.success(statistics);
        } catch (Exception e) {
            return Result.error("获取配送时间分布统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统健康状态
     */
    @GetMapping("/health")
    public Result<Map<String, Object>> getSystemHealthStatus() {
        try {
            Map<String, Object> health = monitorService.getSystemHealthStatus();
            return Result.success(health);
        } catch (Exception e) {
            return Result.error("获取系统健康状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取自定义时间范围的统计数据
     */
    @GetMapping("/statistics/custom-range")
    public Result<Map<String, Object>> getCustomRangeStatistics(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            // 这里可以调用一个新的服务方法来获取自定义时间范围的统计
            // 暂时返回今日统计作为示例
            Map<String, Object> statistics = monitorService.getTodayStatistics();
            statistics.put("customRange", Map.of(
                    "startTime", startTime,
                    "endTime", endTime,
                    "note", "自定义时间范围统计功能待完善"
            ));
            return Result.success(statistics);
        } catch (Exception e) {
            return Result.error("获取自定义时间范围统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取配送员排行榜
     */
    @GetMapping("/leaderboard/couriers")
    public Result<Map<String, Object>> getCourierLeaderboard(
            @RequestParam(defaultValue = "completed") String sortBy,
            @RequestParam(defaultValue = "10") int limit) {
        try {
            // 暂时返回模拟数据，实际项目中需要实现具体的排行榜逻辑
            Map<String, Object> leaderboard = new java.util.HashMap<>();
            leaderboard.put("sortBy", sortBy);
            leaderboard.put("limit", limit);
            leaderboard.put("note", "配送员排行榜功能待完善");
            leaderboard.put("sampleData", java.util.List.of(
                    Map.of("courierId", 1L, "courierName", "张三", "completedOrders", 150),
                    Map.of("courierId", 2L, "courierName", "李四", "completedOrders", 120),
                    Map.of("courierId", 3L, "courierName", "王五", "completedOrders", 100)
            ));
            
            return Result.success(leaderboard);
        } catch (Exception e) {
            return Result.error("获取配送员排行榜失败: " + e.getMessage());
        }
    }

    /**
     * 获取实时告警信息
     */
    @GetMapping("/alerts")
    public Result<Map<String, Object>> getRealTimeAlerts() {
        try {
            Map<String, Object> alerts = new java.util.HashMap<>();
            
            // 获取异常统计
            Map<String, Object> exceptionStats = monitorService.getExceptionStatistics();
            
            // 模拟告警规则
            java.util.List<Map<String, Object>> alertList = new java.util.ArrayList<>();
            
            Long exceptionCount = (Long) exceptionStats.get("totalExceptions");
            if (exceptionCount != null && exceptionCount > 10) {
                alertList.add(Map.of(
                        "level", "WARNING",
                        "message", "异常订单数量过多: " + exceptionCount,
                        "timestamp", LocalDateTime.now()
                ));
            }
            
            alerts.put("alerts", alertList);
            alerts.put("totalAlerts", alertList.size());
            alerts.put("lastUpdateTime", LocalDateTime.now());
            
            return Result.success(alerts);
        } catch (Exception e) {
            return Result.error("获取实时告警信息失败: " + e.getMessage());
        }
    }

    /**
     * 导出统计报表
     */
    @GetMapping("/export/report")
    public Result<Map<String, Object>> exportStatisticsReport(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @RequestParam(defaultValue = "excel") String format) {
        try {
            Map<String, Object> reportInfo = new java.util.HashMap<>();
            reportInfo.put("startTime", startTime);
            reportInfo.put("endTime", endTime);
            reportInfo.put("format", format);
            reportInfo.put("note", "报表导出功能待完善，实际项目中需要生成具体的文件");
            reportInfo.put("downloadUrl", "/api/download/report_" + System.currentTimeMillis() + "." + format);
            
            return Result.success(reportInfo);
        } catch (Exception e) {
            return Result.error("导出统计报表失败: " + e.getMessage());
        }
    }
} 