package org.example.logisticsnotification.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通知状态枚举
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Getter
@AllArgsConstructor
public enum NotificationStatus {

    PENDING(0, "待发送", "通知待发送状态"),
    SENDING(1, "发送中", "通知发送中状态"),
    SUCCESS(2, "发送成功", "通知发送成功状态"),
    FAILED(3, "发送失败", "通知发送失败状态"),
    CANCELLED(4, "已取消", "通知已取消状态");

    private final Integer code;
    private final String name;
    private final String description;

    /**
     * 根据状态码获取枚举
     */
    public static NotificationStatus fromCode(Integer code) {
        for (NotificationStatus status : NotificationStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的通知状态: " + code);
    }

    /**
     * 根据状态码获取名称
     */
    public static String getNameByCode(Integer code) {
        for (NotificationStatus status : NotificationStatus.values()) {
            if (status.getCode().equals(code)) {
                return status.getName();
            }
        }
        return "未知状态";
    }

    /**
     * 判断是否为最终状态
     */
    public boolean isFinalStatus() {
        return this == SUCCESS || this == FAILED || this == CANCELLED;
    }
}