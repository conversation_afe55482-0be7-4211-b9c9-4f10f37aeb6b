package org.example.logisticsorder.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.logistics.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("orders")
public class Order extends BaseEntity {

    /**
     * 订单�?
     */
    private String orderNumber;

    /**
     * 下单用户ID
     */
    private Long userId;

    /**
     * 寄件人姓名
     */
    private String senderName;

    /**
     * 寄件人电话
     */
    private String senderPhone;

    /**
     * 寄件人地址
     */
    private String senderAddress;

    /**
     * 寄件人省份
     */
    private String senderProvince;

    /**
     * 寄件人城市
     */
    private String senderCity;

    /**
     * 寄件人区域
     */
    private String senderDistrict;

    /**
     * 寄件人经度
     */
    private BigDecimal senderLongitude;

    /**
     * 寄件人纬度
     */
    private BigDecimal senderLatitude;

    /**
     * 收件人姓名
     */
    private String receiverName;

    /**
     * 收件人电话
     */
    private String receiverPhone;

    /**
     * 收件人地址
     */
    private String receiverAddress;

    /**
     * 收件人省份
     */
    private String receiverProvince;

    /**
     * 收件人城市
     */
    private String receiverCity;

    /**
     * 收件人区域
     */
    private String receiverDistrict;

    /**
     * 收件人经度
     */
    private BigDecimal receiverLongitude;

    /**
     * 收件人纬度
     */
    private BigDecimal receiverLatitude;

    /**
     * 物品名称
     */
    private String itemName;

    /**
     * 物品类型
     */
    private String itemType;

    /**
     * 物品重量(kg)
     */
    private BigDecimal itemWeight;

    /**
     * 物品体积(m³)
     */
    private BigDecimal itemVolume;

    /**
     * 物品长度(cm)
     */
    private BigDecimal itemLength;

    /**
     * 物品宽度(cm)
     */
    private BigDecimal itemWidth;

    /**
     * 物品高度(cm)
     */
    private BigDecimal itemHeight;

    /**
     * 物品价值
     */
    private BigDecimal itemValue;

    /**
     * 是否易碎品：0-否，1-�?
     */
    private Boolean isFragile;

    /**
     * 服务类型：STANDARD-标准，EXPRESS-快递，URGENT-加急
     */
    private String serviceType;

    /**
     * 运费
     */
    private BigDecimal shippingFee;

    /**
     * 保险费
     */
    private BigDecimal insuranceFee;

    /**
     * 包装费
     */
    private BigDecimal packingFee;

    /**
     * 总费用
     */
    private BigDecimal totalFee;

    /**
     * 支付方式：ONLINE-在线支付，COD-货到付款
     */
    private String paymentMethod;

    /**
     * 支付状态：0-未支付，1-已支付，2-已退款
     */
    private Integer paymentStatus;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 揽件时间
     */
    private LocalDateTime pickupTime;

    /**
     * 配送时间
     */
    private LocalDateTime deliveryTime;

    /**
     * 签收时间
     */
    private LocalDateTime signTime;

    /**
     * 预计送达时间
     */
    private LocalDateTime estimatedDeliveryTime;

    /**
     * 备注
     */
    private String remarks;
}
