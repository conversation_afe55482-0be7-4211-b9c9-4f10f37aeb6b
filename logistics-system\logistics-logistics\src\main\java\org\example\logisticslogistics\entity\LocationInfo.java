package org.example.logisticslogistics.entity;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 位置信息实体
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
public class LocationInfo {

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String district;

    /**
     * 邮政编码
     */
    private String postalCode;
}
