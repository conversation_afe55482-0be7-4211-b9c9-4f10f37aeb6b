<template>
  <div class="pickup-confirm">
    <div class="page-header">
      <el-button @click="$router.back()" :icon="ArrowLeft">返回</el-button>
      <h2>揽件确认</h2>
    </div>

    <div v-if="taskInfo" class="pickup-content">
      <!-- 订单信息 -->
      <el-card class="order-info">
        <template #header>
          <div class="card-header">
            <el-icon><Document /></el-icon>
            <span>订单信息</span>
          </div>
        </template>

        <div class="order-details">
          <div class="info-row">
            <span class="label">订单号：</span>
            <span class="value">{{ taskInfo.orderNumber }}</span>
          </div>
          
          <div class="address-section">
            <h4>寄件人信息</h4>
            <div class="address-info">
              <p><strong>{{ taskInfo.senderName }}</strong> {{ taskInfo.senderPhone }}</p>
              <p>{{ taskInfo.senderAddress }}</p>
            </div>
          </div>

          <div class="address-section">
            <h4>收件人信息</h4>
            <div class="address-info">
              <p><strong>{{ taskInfo.receiverName }}</strong> {{ taskInfo.receiverPhone }}</p>
              <p>{{ taskInfo.receiverAddress }}</p>
            </div>
          </div>

          <div class="item-section">
            <h4>物品信息</h4>
            <div class="item-info">
              <p>物品名称：{{ taskInfo.itemName || '包裹' }}</p>
              <p>预估重量：{{ taskInfo.estimatedWeight || 1 }}kg</p>
              <p>特殊要求：{{ taskInfo.specialRequirements || '无' }}</p>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 揽件确认表单 -->
      <el-card class="pickup-form">
        <template #header>
          <div class="card-header">
            <el-icon><Box /></el-icon>
            <span>揽件确认</span>
          </div>
        </template>

        <el-form :model="pickupForm" :rules="pickupRules" ref="pickupFormRef" label-width="100px">
          <el-form-item label="实际重量" prop="actualWeight">
            <el-input-number
              v-model="pickupForm.actualWeight"
              :min="0.1"
              :step="0.1"
              :precision="1"
              style="width: 200px"
            />
            <span style="margin-left: 10px">kg</span>
          </el-form-item>

          <el-form-item label="实际体积" prop="actualVolume">
            <div class="volume-inputs">
              <el-input-number
                v-model="pickupForm.length"
                :min="1"
                placeholder="长"
                style="width: 80px"
                @change="calculateVolume"
              />
              <span>×</span>
              <el-input-number
                v-model="pickupForm.width"
                :min="1"
                placeholder="宽"
                style="width: 80px"
                @change="calculateVolume"
              />
              <span>×</span>
              <el-input-number
                v-model="pickupForm.height"
                :min="1"
                placeholder="高"
                style="width: 80px"
                @change="calculateVolume"
              />
              <span style="margin-left: 10px">cm</span>
            </div>
            <div style="margin-top: 5px; color: #666; font-size: 14px">
              体积：{{ (pickupForm.actualVolume || 0).toFixed(3) }}m³
            </div>
          </el-form-item>

          <el-form-item label="包装状态" prop="packagingStatus">
            <el-radio-group v-model="pickupForm.packagingStatus">
              <el-radio value="GOOD">包装完好</el-radio>
              <el-radio value="DAMAGED">包装破损</el-radio>
              <el-radio value="REPACK">需要重新包装</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="货物状态" prop="goodsStatus">
            <el-checkbox-group v-model="pickupForm.goodsStatus">
              <el-checkbox value="FRAGILE">易碎品</el-checkbox>
              <el-checkbox value="LIQUID">液体</el-checkbox>
              <el-checkbox value="VALUABLE">贵重物品</el-checkbox>
              <el-checkbox value="DOCUMENTS">文件资料</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="拍照取证" prop="photos">
            <div class="photo-upload">
              <el-upload
                v-model:file-list="photoList"
                action="#"
                list-type="picture-card"
                :auto-upload="false"
                :limit="5"
                accept="image/*"
                @change="handlePhotoChange"
              >
                <el-icon><Plus /></el-icon>
              </el-upload>
              <div class="upload-tip">
                请拍摄货物照片（最多5张），包括包装外观、标签等
              </div>
            </div>
          </el-form-item>

          <el-form-item label="异常情况">
            <el-input
              v-model="pickupForm.exceptionNote"
              type="textarea"
              :rows="3"
              placeholder="如有异常情况请详细描述"
            />
          </el-form-item>

          <el-form-item label="备注">
            <el-input
              v-model="pickupForm.remarks"
              type="textarea"
              :rows="2"
              placeholder="其他备注信息"
            />
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button size="large" @click="reportException">上报异常</el-button>
        <el-button type="primary" size="large" :loading="confirming" @click="confirmPickup">
          确认揽收
        </el-button>
      </div>
    </div>

    <!-- 异常上报对话框 -->
    <el-dialog v-model="showExceptionDialog" title="异常上报" width="500px">
      <el-form :model="exceptionForm" label-width="100px">
        <el-form-item label="异常类型" required>
          <el-select v-model="exceptionForm.type" placeholder="请选择异常类型">
            <el-option label="客户不在" value="CUSTOMER_ABSENT" />
            <el-option label="地址错误" value="WRONG_ADDRESS" />
            <el-option label="货物问题" value="GOODS_ISSUE" />
            <el-option label="客户拒绝" value="CUSTOMER_REFUSED" />
            <el-option label="其他" value="OTHER" />
          </el-select>
        </el-form-item>
        <el-form-item label="详细描述" required>
          <el-input
            v-model="exceptionForm.description"
            type="textarea"
            :rows="4"
            placeholder="请详细描述异常情况"
          />
        </el-form-item>
        <el-form-item label="现场照片">
          <el-upload
            v-model:file-list="exceptionPhotoList"
            action="#"
            list-type="picture-card"
            :auto-upload="false"
            :limit="3"
            accept="image/*"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showExceptionDialog = false">取消</el-button>
        <el-button type="primary" @click="submitException" :loading="submittingException">
          提交异常
        </el-button>
      </template>
    </el-dialog>

    <!-- 确认对话框 -->
    <el-dialog v-model="showConfirmDialog" title="确认揽收" width="400px">
      <div class="confirm-content">
        <p>请确认以下信息：</p>
        <ul>
          <li>实际重量：{{ pickupForm.actualWeight }}kg</li>
          <li>实际体积：{{ pickupForm.actualVolume?.toFixed(3) }}m³</li>
          <li>包装状态：{{ getPackagingStatusText(pickupForm.packagingStatus) }}</li>
          <li>已拍摄照片：{{ photoList.length }}张</li>
        </ul>
        <p style="color: #f56c6c; font-size: 14px;">
          确认后将无法修改，请仔细核对信息
        </p>
      </div>
      <template #footer>
        <el-button @click="showConfirmDialog = false">取消</el-button>
        <el-button type="primary" @click="submitPickupConfirm" :loading="confirming">
          确认揽收
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import {
  ArrowLeft,
  Document,
  Box,
  Plus
} from '@element-plus/icons-vue'
import { courierApi } from '@/api/courier'

const route = useRoute()
const router = useRouter()

// 表单引用
const pickupFormRef = ref<FormInstance>()

// 状态
const confirming = ref(false)
const submittingException = ref(false)
const showExceptionDialog = ref(false)
const showConfirmDialog = ref(false)
const taskInfo = ref<any>(null)
const photoList = ref<any[]>([])
const exceptionPhotoList = ref<any[]>([])

// 揽件表单
const pickupForm = reactive({
  actualWeight: 1,
  length: 10,
  width: 10,
  height: 10,
  actualVolume: 0.001,
  packagingStatus: 'GOOD',
  goodsStatus: [] as string[],
  exceptionNote: '',
  remarks: '',
  photos: [] as string[]
})

// 异常表单
const exceptionForm = reactive({
  type: '',
  description: '',
  photos: [] as string[]
})

// 表单验证规则
const pickupRules: FormRules = {
  actualWeight: [{ required: true, message: '请输入实际重量', trigger: 'blur' }],
  packagingStatus: [{ required: true, message: '请选择包装状态', trigger: 'change' }]
}

// 加载任务信息
const loadTaskInfo = async () => {
  const taskId = route.params.id as string
  if (!taskId) {
    ElMessage.error('任务ID不正确')
    router.back()
    return
  }

  try {
    // 这里应该调用获取任务详情的API
    // const response = await courierApi.getTaskById(Number(taskId))
    // 模拟数据
    taskInfo.value = {
      id: taskId,
      orderNumber: 'LP' + Date.now().toString().slice(-8),
      senderName: '张三',
      senderPhone: '13800138001',
      senderAddress: '北京市朝阳区某某街道123号',
      receiverName: '李四',
      receiverPhone: '13800138002',
      receiverAddress: '上海市浦东新区某某路456号',
      itemName: '电子产品',
      estimatedWeight: 2.5,
      specialRequirements: '轻拿轻放'
    }
  } catch (error) {
    console.error('加载任务信息失败:', error)
    ElMessage.error('加载任务信息失败')
    router.back()
  }
}

// 计算体积
const calculateVolume = () => {
  if (pickupForm.length && pickupForm.width && pickupForm.height) {
    pickupForm.actualVolume = (pickupForm.length * pickupForm.width * pickupForm.height) / 1000000
  }
}

// 处理照片变化
const handlePhotoChange = (uploadFile: any, uploadFiles: any[]) => {
  photoList.value = uploadFiles
}

// 确认揽收
const confirmPickup = async () => {
  if (!pickupFormRef.value) return

  try {
    await pickupFormRef.value.validate()
    
    if (photoList.value.length === 0) {
      ElMessage.warning('请至少上传一张货物照片')
      return
    }

    showConfirmDialog.value = true
  } catch (error) {
    console.log('表单验证失败:', error)
  }
}

// 提交揽收确认
const submitPickupConfirm = async () => {
  confirming.value = true
  
  try {
    // 这里应该调用确认揽收的API
    const pickupData = {
      taskId: taskInfo.value.id,
      actualWeight: pickupForm.actualWeight,
      actualVolume: pickupForm.actualVolume,
      packagingStatus: pickupForm.packagingStatus,
      goodsStatus: pickupForm.goodsStatus,
      photos: pickupForm.photos,
      exceptionNote: pickupForm.exceptionNote,
      remarks: pickupForm.remarks
    }
    
    console.log('揽收数据:', pickupData)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('揽收确认成功')
    showConfirmDialog.value = false
    router.push('/courier/dashboard')
  } catch (error) {
    console.error('确认揽收失败:', error)
    ElMessage.error('确认揽收失败')
  } finally {
    confirming.value = false
  }
}

// 上报异常
const reportException = () => {
  showExceptionDialog.value = true
}

// 提交异常
const submitException = async () => {
  if (!exceptionForm.type || !exceptionForm.description) {
    ElMessage.warning('请填写异常类型和详细描述')
    return
  }

  submittingException.value = true
  
  try {
    // 这里应该调用异常上报的API
    const exceptionData = {
      taskId: taskInfo.value.id,
      type: exceptionForm.type,
      description: exceptionForm.description,
      photos: exceptionForm.photos
    }
    
    console.log('异常数据:', exceptionData)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    ElMessage.success('异常上报成功')
    showExceptionDialog.value = false
    router.push('/courier/dashboard')
  } catch (error) {
    console.error('异常上报失败:', error)
    ElMessage.error('异常上报失败')
  } finally {
    submittingException.value = false
  }
}

// 获取包装状态文本
const getPackagingStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    GOOD: '包装完好',
    DAMAGED: '包装破损',
    REPACK: '需要重新包装'
  }
  return statusMap[status] || status
}

onMounted(() => {
  loadTaskInfo()
  calculateVolume()
})
</script>

<style scoped>
.pickup-confirm {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.pickup-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.order-details {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.info-row {
  display: flex;
  gap: 10px;
}

.info-row .label {
  color: #666;
  min-width: 80px;
}

.info-row .value {
  font-weight: bold;
  color: #333;
}

.address-section, .item-section {
  padding: 15px;
  background: #f5f7fa;
  border-radius: 8px;
}

.address-section h4, .item-section h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 16px;
}

.address-info p, .item-info p {
  margin: 5px 0;
  color: #666;
}

.volume-inputs {
  display: flex;
  align-items: center;
  gap: 10px;
}

.photo-upload {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.upload-tip {
  color: #666;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  padding: 20px;
}

.confirm-content ul {
  margin: 15px 0;
  padding-left: 20px;
}

.confirm-content li {
  margin: 5px 0;
  color: #333;
}
</style> 