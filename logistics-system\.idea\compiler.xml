<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
      </profile>
      <profile name="Annotation profile for 物流跟踪系统" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$MAVEN_REPOSITORY$/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar" />
          <entry name="$MAVEN_REPOSITORY$/org/mapstruct/mapstruct-processor/1.5.3.Final/mapstruct-processor-1.5.3.Final.jar" />
          <entry name="$MAVEN_REPOSITORY$/org/mapstruct/mapstruct/1.5.3.Final/mapstruct-1.5.3.Final.jar" />
        </processorPath>
        <module name="logistics-email" />
        <module name="logistics-delivery" />
        <module name="logistics-notification" />
        <module name="logistics-order" />
        <module name="logistics-logistics" />
        <module name="logistics-common" />
        <module name="logistics-map" />
        <module name="logistics-gateway" />
        <module name="logistics-user" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="logistics-delivery" options="" />
      <module name="logistics-email" options="" />
      <module name="logistics-gateway" options="" />
      <module name="logistics-logistics" options="" />
      <module name="logistics-map" options="" />
      <module name="logistics-notification" options="" />
      <module name="logistics-order" options="" />
      <module name="logistics-user" options="" />
    </option>
  </component>
</project>