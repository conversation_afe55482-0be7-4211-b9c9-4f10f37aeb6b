<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑地址' : '新增地址'"
    width="600px"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="联系人" prop="contactName">
        <el-input v-model="form.contactName" placeholder="请输入联系人姓名" />
      </el-form-item>

      <el-form-item label="联系电话" prop="contactPhone">
        <el-input v-model="form.contactPhone" placeholder="请输入联系电话" />
      </el-form-item>

      <el-form-item label="所在地区" prop="region">
        <el-cascader
          v-model="form.region"
          :options="regionOptions"
          :props="cascaderProps"
          placeholder="请选择省市区"
          style="width: 100%"
          @change="handleRegionChange"
        />
      </el-form-item>

      <el-form-item label="详细地址" prop="detailedAddress">
        <el-input
          v-model="form.detailedAddress"
          type="textarea"
          :rows="3"
          placeholder="请输入详细地址"
        />
      </el-form-item>

      <el-form-item label="邮政编码" prop="postalCode">
        <el-input v-model="form.postalCode" placeholder="请输入邮政编码" />
      </el-form-item>

      <el-form-item label="地址类型" prop="addressType">
        <el-radio-group v-model="form.addressType">
          <el-radio :label="1">收货地址</el-radio>
          <el-radio :label="2">发货地址</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item>
        <el-checkbox v-model="form.isDefault">设为默认地址</el-checkbox>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { mapApi } from '@/api/map'
import { addressApi, type Address } from '@/api/address'

interface Props {
  modelValue: boolean
  addressData?: Address
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

const formRef = ref<FormInstance>()
const loading = ref(false)
const regionOptions = ref<any[]>([])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const isEdit = computed(() => !!props.addressData?.id)

const form = reactive({
  id: null as number | null,
  contactName: '',
  contactPhone: '',
  region: [] as string[],
  province: '',
  city: '',
  district: '',
  detailedAddress: '',
  postalCode: '',
  addressType: 1,
  isDefault: false,
})

const rules: FormRules = {
  contactName: [{ required: true, message: '请输入联系人姓名', trigger: 'blur' }],
  contactPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
  ],
  region: [{ required: true, message: '请选择所在地区', trigger: 'change' }],
  detailedAddress: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
}

const cascaderProps = {
  value: 'code',
  label: 'name',
  children: 'children',
  emitPath: true,
  checkStrictly: false,
}

// 加载省市区数据
const loadRegionData = async () => {
  try {
    const response = await addressApi.getRegionData()
    console.log('获取到的省市区数据:', response)
    if (response.code === 200) {
      regionOptions.value = response.data || []
      console.log('设置的regionOptions:', regionOptions.value)
    }
  } catch (error) {
    console.error('加载省市区数据失败:', error)
    ElMessage.error('加载省市区数据失败')
  }
}

// 处理地区选择变化
const handleRegionChange = (values: string[]) => {
  console.log('选择的地区codes:', values)
  if (values && values.length >= 1) {
    // 根据选择的code查找对应的省市区名称
    const findRegionNames = (codes: string[]) => {
      let province = '',
        city = '',
        district = ''

      // 查找省份
      const provinceItem = regionOptions.value.find((p) => p.code === codes[0])
      if (provinceItem) {
        province = provinceItem.name

        // 查找城市
        if (codes.length > 1 && provinceItem.children) {
          const cityItem = provinceItem.children.find((c: any) => c.code === codes[1])
          if (cityItem) {
            city = cityItem.name

            // 查找区县
            if (codes.length > 2 && cityItem.children) {
              const districtItem = cityItem.children.find((d: any) => d.code === codes[2])
              if (districtItem) {
                district = districtItem.name
              }
            }
          }
        }
      }

      return { province, city, district }
    }

    const { province, city, district } = findRegionNames(values)
    form.province = province
    form.city = city
    form.district = district

    console.log('设置的省市区:', { province, city, district })
  }
}

// 初始化表单数据
const initForm = () => {
  if (props.addressData) {
    Object.assign(form, {
      ...props.addressData,
      region: [], // 需要根据省市区名称找到对应的adcode
    })

    // 如果有省市区信息，需要设置region数组
    if (props.addressData.province) {
      // 这里需要根据省市区名称反向查找adcode
      // 简化处理，实际应该实现完整的反向查找
      form.region = []
    }
  } else {
    // 重置表单
    Object.assign(form, {
      id: null,
      contactName: '',
      contactPhone: '',
      region: [],
      province: '',
      city: '',
      district: '',
      detailedAddress: '',
      postalCode: '',
      addressType: props.addressType || 1,
      isDefault: false,
    })
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    const submitData = {
      contactName: form.contactName,
      contactPhone: form.contactPhone,
      province: form.province,
      city: form.city,
      district: form.district,
      detailedAddress: form.detailedAddress,
      postalCode: form.postalCode,
      addressType: form.addressType,
      isDefault: form.isDefault,
    }

    if (isEdit.value && form.id) {
      await addressApi.updateAddress(form.id, submitData)
      ElMessage.success('地址更新成功')
    } else {
      await addressApi.createAddress(submitData)
      ElMessage.success('地址添加成功')
    }

    emit('success')
    handleClose()
  } catch (error) {
    console.error('保存地址失败:', error)
    ElMessage.error('保存地址失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  formRef.value?.clearValidate()
}

// 监听对话框显示状态
watch(visible, (newVal) => {
  if (newVal) {
    loadRegionData()
    initForm()
  }
})
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
