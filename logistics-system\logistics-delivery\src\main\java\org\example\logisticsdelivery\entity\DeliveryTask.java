package org.example.logisticsdelivery.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.logistics.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 配送任务实体
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("delivery_tasks")
public class DeliveryTask extends BaseEntity {

    /**
     * 任务ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 任务编号
     */
    @TableField("task_number")
    private String taskNumber;

    /**
     * 订单ID
     */
    @TableField("order_id")
    private Long orderId;

    /**
     * 订单号
     */
    @TableField("order_number")
    private String orderNumber;

    /**
     * 配送员ID
     */
    @TableField("courier_id")
    private Long courierId;

    /**
     * 任务类型：PICKUP-揽件，DELIVERY-派送
     */
    @TableField("task_type")
    private String taskType;

    /**
     * 优先级：1-低，2-中，3-高，4-紧急
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 重量(kg)
     */
    @TableField("weight")
    private BigDecimal weight;

    /**
     * 体积(m³)
     */
    @TableField("volume")
    private BigDecimal volume;

    /**
     * 货物描述
     */
    @TableField("goods_description")
    private String goodsDescription;

    /**
     * 特殊要求
     */
    @TableField("special_requirements")
    private String specialRequirements;

    /**
     * 配送费
     */
    @TableField("delivery_fee")
    private BigDecimal deliveryFee;

    /**
     * 预计完成时间
     */
    @TableField("estimated_time")
    private LocalDateTime estimatedTime;

    /**
     * 实际开始时间
     */
    @TableField("actual_start_time")
    private LocalDateTime actualStartTime;

    /**
     * 实际完成时间
     */
    @TableField("actual_end_time")
    private LocalDateTime actualEndTime;

    /**
     * 任务状态：ASSIGNED-已分配，ACCEPTED-已接受，IN_PROGRESS-进行中，COMPLETED-已完成，CANCELLED-已取消
     */
    @TableField("task_status")
    private String taskStatus;

    /**
     * 发件人姓名
     */
    @TableField("sender_name")
    private String senderName;

    /**
     * 发件人电话
     */
    @TableField("sender_phone")
    private String senderPhone;

    /**
     * 发件人地址
     */
    @TableField("sender_address")
    private String senderAddress;

    /**
     * 收件人姓名
     */
    @TableField("receiver_name")
    private String receiverName;

    /**
     * 收件人电话
     */
    @TableField("receiver_phone")
    private String receiverPhone;

    /**
     * 收件人地址
     */
    @TableField("receiver_address")
    private String receiverAddress;

    /**
     * 实际完成时间
     */
    @TableField("actual_time")
    private LocalDateTime actualTime;

    /**
     * 取件地址
     */
    @TableField("pickup_address")
    private String pickupAddress;

    /**
     * 送达地址
     */
    @TableField("delivery_address")
    private String deliveryAddress;

    /**
     * 联系人姓名
     */
    @TableField("contact_name")
    private String contactName;

    /**
     * 联系人电话
     */
    @TableField("contact_phone")
    private String contactPhone;

    /**
     * 特殊说明
     */
    @TableField("special_instructions")
    private String specialInstructions;

    /**
     * 完成凭证(签收照片等)
     */
    @TableField("completion_proof")
    private String completionProof;

    /**
     * 失败原因
     */
    @TableField("failure_reason")
    private String failureReason;

    /**
     * 重试次数
     */
    @TableField("retry_count")
    private Integer retryCount;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;
}