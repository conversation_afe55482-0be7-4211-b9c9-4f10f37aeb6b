<template>
  <el-dialog
    :model-value="modelValue"
    :title="title"
    width="600px"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <div class="address-select">
      <div class="dialog-header">
        <el-button type="primary" @click="showAddressForm = true">
          <el-icon><Plus /></el-icon>
          新增地址
        </el-button>
      </div>

      <div class="address-list" v-loading="loading">
        <div
          v-for="address in addressList"
          :key="address.id"
          class="address-item"
          :class="{ active: selectedAddressId === address.id }"
          @click="selectAddress(address)"
        >
          <div class="address-info">
            <div class="contact-info">
              <span class="name">{{ address.contactName }}</span>
              <span class="phone">{{ address.contactPhone }}</span>
              <el-tag v-if="address.isDefault" type="success" size="small">默认</el-tag>
            </div>
            <div class="address-detail">
              {{ address.province }} {{ address.city }} {{ address.district }}
              {{ address.detailedAddress }}
            </div>
          </div>
          <div class="address-actions">
            <el-button type="text" @click.stop="editAddress(address)">编辑</el-button>
            <el-button type="text" @click.stop="deleteAddress(address.id)">删除</el-button>
          </div>
        </div>

        <div v-if="addressList.length === 0" class="empty-state">
          <el-empty description="暂无地址，请先添加地址" />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:modelValue', false)">取消</el-button>
        <el-button type="primary" :disabled="!selectedAddressId" @click="confirmSelect">
          确认选择
        </el-button>
      </div>
    </template>

    <!-- 地址表单对话框 -->
    <AddressFormDialog
      v-model="showAddressForm"
      :address="editingAddress"
      :address-type="addressType"
      @success="onAddressFormSuccess"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { addressApi } from '@/api/address'
import type { Address } from '@/types/address'
import AddressFormDialog from './AddressFormDialog.vue'

interface Props {
  modelValue: boolean
  title: string
  addressType: 1 | 2 // 1-收货地址，2-发货地址
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'select', address: Address): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 状态
const loading = ref(false)
const addressList = ref<Address[]>([])
const selectedAddressId = ref<number | null>(null)
const showAddressForm = ref(false)
const editingAddress = ref<Address | null>(null)

// 加载地址列表
const loadAddressList = async () => {
  loading.value = true
  try {
    console.log('加载地址列表，类型:', props.addressType)
    const response = await addressApi.getAddressList(props.addressType)
    console.log('地址列表响应:', response)

    if (response.code === 200) {
      // 修改这里
      addressList.value = response.data || []
      console.log('加载的地址列表:', addressList.value)
    } else {
      ElMessage.error(response.message || '加载地址列表失败')
    }
  } catch (error) {
    console.error('加载地址列表失败:', error)
    ElMessage.error('加载地址列表失败')
  } finally {
    loading.value = false
  }
}

// 选择地址
const selectAddress = (address: Address) => {
  selectedAddressId.value = address.id
}

// 确认选择
const confirmSelect = () => {
  const selectedAddress = addressList.value.find((addr) => addr.id === selectedAddressId.value)
  if (selectedAddress) {
    emit('select', selectedAddress)
    emit('update:modelValue', false)
  }
}

// 编辑地址
const editAddress = (address: Address) => {
  editingAddress.value = address
  showAddressForm.value = true
}

// 删除地址
const deleteAddress = async (addressId: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个地址吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const response = await addressApi.deleteAddress(addressId)
    if (response.code === 200) {
      // 修改这里
      ElMessage.success('删除成功')
      loadAddressList()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除地址失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 地址表单成功回调
const onAddressFormSuccess = () => {
  loadAddressList()
  editingAddress.value = null
}

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (visible) => {
    if (visible) {
      loadAddressList()
      selectedAddressId.value = null
    }
  },
)

// 监听地址表单关闭
watch(
  () => showAddressForm.value,
  (visible) => {
    if (!visible) {
      editingAddress.value = null
    }
  },
)
</script>

<style scoped>
.address-select {
  max-height: 500px;
}

.dialog-header {
  margin-bottom: 20px;
  text-align: right;
}

.address-list {
  max-height: 400px;
  overflow-y: auto;
}

.address-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s;
}

.address-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.address-item.active {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.address-info {
  flex: 1;
}

.contact-info {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.name {
  font-weight: bold;
  color: #333;
}

.phone {
  color: #666;
  font-size: 14px;
}

.address-detail {
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

.address-actions {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.dialog-footer {
  text-align: right;
}
</style>
