<template>
  <div class="payment-page">
    <div class="page-header">
      <h2>订单支付</h2>
      <p>请选择支付方式完成订单</p>
    </div>

    <div v-if="orderInfo" class="payment-content">
      <!-- 订单信息 -->
      <el-card class="order-summary">
        <template #header>
          <span>订单信息</span>
        </template>

        <div class="order-details">
          <p><strong>订单号：</strong>{{ orderInfo.orderNumber }}</p>
          <p><strong>寄件人：</strong>{{ orderInfo.senderName }} {{ orderInfo.senderPhone }}</p>
          <p><strong>收件人：</strong>{{ orderInfo.receiverName }} {{ orderInfo.receiverPhone }}</p>
          <p><strong>物品：</strong>{{ orderInfo.itemName || '包裹' }}</p>
          <p><strong>总金额：</strong>¥{{ (orderInfo.totalFee || 0).toFixed(2) }}</p>
        </div>
      </el-card>

      <!-- 支付方式选择 -->
      <el-card class="payment-methods">
        <template #header>
          <span>选择支付方式</span>
        </template>

        <el-radio-group v-model="selectedPaymentMethod">
          <el-radio value="ALIPAY" label="支付宝支付" />
          <el-radio value="WECHAT" label="微信支付" />
          <el-radio value="COD" label="货到付款" />
        </el-radio-group>
      </el-card>

      <!-- 支付操作 -->
      <div class="payment-actions">
        <el-button size="large" @click="cancelPayment">取消支付</el-button>
        <el-button 
          type="primary" 
          size="large" 
          :loading="paying"
          @click="handlePayment"
          :disabled="!selectedPaymentMethod"
        >
          {{ selectedPaymentMethod === 'COD' ? '确认订单' : '立即支付' }}
        </el-button>
      </div>
    </div>

    <!-- 支付结果对话框 -->
    <el-dialog v-model="showPaymentResult" title="支付结果" width="400px">
      <div class="payment-result">
        <div v-if="paymentSuccess" class="success-result">
          <h3>支付成功</h3>
          <p>您的订单已支付完成，我们将尽快为您安排配送</p>
        </div>
        <div v-else class="failed-result">
          <h3>支付失败</h3>
          <p>{{ paymentErrorMessage }}</p>
        </div>
      </div>
      <template #footer>
        <el-button v-if="!paymentSuccess" @click="showPaymentResult = false">重新支付</el-button>
        <el-button v-if="paymentSuccess" type="primary" @click="goToOrderDetail">查看订单</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { orderApi } from '@/api/order'

const route = useRoute()
const router = useRouter()

// 状态
const paying = ref(false)
const showPaymentResult = ref(false)
const paymentSuccess = ref(false)
const paymentErrorMessage = ref('')
const orderInfo = ref<any>(null)
const selectedPaymentMethod = ref('ALIPAY')

// 加载订单信息
const loadOrderInfo = async () => {
  const orderId = route.params.id as string
  if (!orderId) {
    ElMessage.error('订单ID不正确')
    router.back()
    return
  }

  try {
    const response = await orderApi.getOrderById(Number(orderId))
    if (response.code === 200) {
      orderInfo.value = response.data
    } else {
      ElMessage.error('订单不存在')
      router.back()
    }
  } catch (error) {
    console.error('加载订单信息失败:', error)
    ElMessage.error('加载订单信息失败')
    router.back()
  }
}

// 处理支付
const handlePayment = async () => {
  if (!orderInfo.value) return

  paying.value = true

  try {
    // 模拟支付过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 模拟支付成功
    paymentSuccess.value = true
    showPaymentResult.value = true
    
    ElMessage.success('支付成功')
  } catch (error) {
    console.error('支付失败:', error)
    paymentSuccess.value = false
    paymentErrorMessage.value = '支付失败，请重试'
    showPaymentResult.value = true
  } finally {
    paying.value = false
  }
}

// 取消支付
const cancelPayment = async () => {
  try {
    await ElMessageBox.confirm('确定要取消支付吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    router.back()
  } catch {
    // 用户取消
  }
}

// 跳转到订单详情
const goToOrderDetail = () => {
  if (orderInfo.value) {
    router.push(`/customer/order/detail/${orderInfo.value.id}`)
  }
}

onMounted(() => {
  loadOrderInfo()
})
</script>

<style scoped>
.payment-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 10px 0;
  color: #333;
}

.payment-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.order-details p {
  margin: 10px 0;
}

.payment-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  padding: 20px;
}

.payment-result {
  text-align: center;
  padding: 20px;
}

.payment-result h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.success-result h3 {
  color: #67c23a;
}

.failed-result h3 {
  color: #f56c6c;
}
</style> 