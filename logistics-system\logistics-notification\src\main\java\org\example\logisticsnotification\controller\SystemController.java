package org.example.logisticsnotification.controller;

import com.logistics.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.example.logisticsnotification.service.NotificationService;
import org.example.logisticsnotification.service.NotificationTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 系统监控控制器
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Slf4j
@RestController
@RequestMapping("/api/system")
public class SystemController {

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private NotificationTemplateService notificationTemplateService;

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Result<Map<String, Object>> healthCheck() {
        try {
            Map<String, Object> health = new HashMap<>();
            health.put("status", "UP");
            health.put("service", "notification-service");
            health.put("timestamp", LocalDateTime.now());
            health.put("version", "1.0.0");

            // 检查基础功能
            try {
                // 检查模板统计功能
                Map<String, Object> templateStats = notificationTemplateService.getTemplateUsageStatistics();
                health.put("templateServiceStatus", "UP");
                health.put("templateCount", templateStats.get("totalCount"));
            } catch (Exception e) {
                health.put("templateServiceStatus", "DOWN");
                health.put("templateServiceError", e.getMessage());
            }

            try {
                // 检查通知统计功能
                Map<String, Object> notificationStats = notificationService.getNotificationStatistics(null, null, null);
                health.put("notificationServiceStatus", "UP");
                health.put("notificationCount", notificationStats.get("totalCount"));
            } catch (Exception e) {
                health.put("notificationServiceStatus", "DOWN");
                health.put("notificationServiceError", e.getMessage());
            }

            return Result.success(health);
        } catch (Exception e) {
            log.error("健康检查异常", e);
            Map<String, Object> health = new HashMap<>();
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
            health.put("timestamp", LocalDateTime.now());
            // 修复：错误情况下也返回数据，但状态设为DOWN
            return Result.success(health);
        }
    }

    /**
     * 系统信息
     */
    @GetMapping("/info")
    public Result<Map<String, Object>> systemInfo() {
        try {
            Map<String, Object> info = new HashMap<>();
            info.put("serviceName", "通知服务");
            info.put("description", "物流系统通知服务，负责短信、邮件、推送等通知的发送和管理");
            info.put("version", "1.0.0");
            info.put("author", "logistics");
            info.put("contact", "<EMAIL>");

            // 运行时信息
            Runtime runtime = Runtime.getRuntime();
            Map<String, Object> jvm = new HashMap<>();
            jvm.put("maxMemory", runtime.maxMemory() / 1024 / 1024 + "MB");
            jvm.put("totalMemory", runtime.totalMemory() / 1024 / 1024 + "MB");
            jvm.put("freeMemory", runtime.freeMemory() / 1024 / 1024 + "MB");
            jvm.put("usedMemory", (runtime.totalMemory() - runtime.freeMemory()) / 1024 / 1024 + "MB");
            jvm.put("processors", runtime.availableProcessors());
            info.put("jvm", jvm);

            // 系统信息
            Map<String, Object> system = new HashMap<>();
            system.put("osName", System.getProperty("os.name"));
            system.put("osVersion", System.getProperty("os.version"));
            system.put("javaVersion", System.getProperty("java.version"));
            system.put("javaVendor", System.getProperty("java.vendor"));
            info.put("system", system);

            return Result.success(info);
        } catch (Exception e) {
            log.error("获取系统信息异常", e);
            return Result.error("获取系统信息异常：" + e.getMessage());
        }
    }

    /**
     * 处理通知发送队列
     */
    @PostMapping("/process-sending")
    public Result<String> processNotificationSending() {
        try {
            notificationService.processNotificationSending();
            return Result.success("通知发送处理完成");
        } catch (Exception e) {
            log.error("处理通知发送异常", e);
            return Result.error("处理通知发送异常：" + e.getMessage());
        }
    }

    /**
     * 处理通知重试队列
     */
    @PostMapping("/process-retry")
    public Result<String> processNotificationRetry() {
        try {
            notificationService.processNotificationRetry();
            return Result.success("通知重试处理完成");
        } catch (Exception e) {
            log.error("处理通知重试异常", e);
            return Result.error("处理通知重试异常：" + e.getMessage());
        }
    }

    /**
     * 系统统计概览
     */
    @GetMapping("/overview")
    public Result<Map<String, Object>> systemOverview() {
        try {
            Map<String, Object> overview = new HashMap<>();

            // 通知统计
            Map<String, Object> notificationStats = notificationService.getNotificationStatistics(null, null, null);
            overview.put("notificationStats", notificationStats);

            // 模板统计
            Map<String, Object> templateStats = notificationTemplateService.getTemplateUsageStatistics();
            overview.put("templateStats", templateStats);

            // 系统状态
            overview.put("systemStatus", "RUNNING");
            overview.put("lastUpdateTime", LocalDateTime.now());

            return Result.success(overview);
        } catch (Exception e) {
            log.error("获取系统概览异常", e);
            return Result.error("获取系统概览异常：" + e.getMessage());
        }
    }

    /**
     * 清理系统数据
     */
    @PostMapping("/cleanup")
    public Result<Map<String, Object>> cleanupSystemData(
            @RequestParam(required = false, defaultValue = "30") Integer expireDays) {
        try {
            LocalDateTime expireTime = LocalDateTime.now().minusDays(expireDays);

            // 清理过期通知
            int cleanedNotifications = notificationService.cleanExpiredNotifications(expireTime);

            Map<String, Object> result = new HashMap<>();
            result.put("cleanedNotifications", cleanedNotifications);
            result.put("expireTime", expireTime);
            result.put("expireDays", expireDays);
            result.put("cleanupTime", LocalDateTime.now());

            return Result.success(result);
        } catch (Exception e) {
            log.error("清理系统数据异常", e);
            return Result.error("清理系统数据异常：" + e.getMessage());
        }
    }

    /**
     * 系统重启状态检查
     */
    @GetMapping("/startup-check")
    public Result<Map<String, Object>> startupCheck() {
        try {
            Map<String, Object> status = new HashMap<>();

            // 检查关键服务
            boolean allServicesReady = true;

            try {
                notificationService.getNotificationStatistics(null, null, null);
                status.put("notificationService", "READY");
            } catch (Exception e) {
                status.put("notificationService", "FAILED");
                status.put("notificationServiceError", e.getMessage());
                allServicesReady = false;
            }

            try {
                notificationTemplateService.getTemplateUsageStatistics();
                status.put("templateService", "READY");
            } catch (Exception e) {
                status.put("templateService", "FAILED");
                status.put("templateServiceError", e.getMessage());
                allServicesReady = false;
            }

            status.put("systemReady", allServicesReady);
            status.put("checkTime", LocalDateTime.now());

            // 修复：统一返回数据，通过systemReady字段判断状态
            return Result.success(status);
        } catch (Exception e) {
            log.error("启动检查异常", e);
            return Result.error("启动检查异常：" + e.getMessage());
        }
    }

    /**
     * 测试通知发送
     */
    @PostMapping("/test-notification")
    public Result<String> testNotification(
            @RequestParam String recipient,
            @RequestParam(required = false, defaultValue = "TEST") String templateCode) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("testMessage", "这是一条测试通知");
            params.put("timestamp", LocalDateTime.now().toString());

            boolean success = notificationService.sendBusinessNotification(
                    "SYSTEM",
                    null,
                    templateCode,
                    recipient,
                    params
            );

            return success ? Result.success("测试通知发送成功") : Result.error("测试通知发送失败");
        } catch (Exception e) {
            log.error("测试通知发送异常", e);
            return Result.error("测试通知发送异常：" + e.getMessage());
        }
    }
}