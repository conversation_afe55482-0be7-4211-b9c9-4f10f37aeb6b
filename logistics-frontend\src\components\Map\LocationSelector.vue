<template>
  <div class="location-selector">
    <el-dialog
      v-model="visible"
      title="选择地点"
      width="800px"
      :before-close="handleClose"
      class="location-dialog"
    >
      <div class="selector-content">
        <!-- 搜索栏 -->
        <div class="search-section">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索地点、地标、网点..."
            class="search-input"
            @input="handleSearch"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          
          <el-button 
            type="primary" 
            @click="getCurrentLocation"
            :loading="gettingLocation"
          >
            <el-icon><Location /></el-icon>
            获取当前位置
          </el-button>
        </div>

        <!-- 快速选择标签 -->
        <div class="quick-tabs">
          <el-tabs v-model="activeTab" @tab-change="handleTabChange">
            <el-tab-pane label="搜索结果" name="search" v-if="searchResults.length > 0">
              <div class="location-list">
                <div
                  v-for="location in searchResults"
                  :key="location.id"
                  class="location-item"
                  :class="{ active: selectedLocation?.id === location.id }"
                  @click="selectLocation(location)"
                >
                  <div class="location-icon">
                    <el-icon><MapPin /></el-icon>
                  </div>
                  <div class="location-info">
                    <h4 class="location-name">{{ location.name }}</h4>
                    <p class="location-address">{{ location.address }}</p>
                    <div class="location-meta">
                      <span class="distance" v-if="location.distance">
                        距离: {{ location.distance }}km
                      </span>
                      <span class="type">{{ getLocationTypeText(location.type) }}</span>
                    </div>
                  </div>
                  <div class="location-actions">
                    <el-button 
                      size="small" 
                      type="text"
                      @click.stop="addToFrequent(location)"
                    >
                      <el-icon><Star /></el-icon>
                    </el-button>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="常用地点" name="frequent">
              <div class="location-list">
                <div
                  v-for="location in frequentLocations"
                  :key="location.id"
                  class="location-item"
                  :class="{ active: selectedLocation?.id === location.id }"
                  @click="selectLocation(location)"
                >
                  <div class="location-icon frequent">
                    <el-icon><StarFilled /></el-icon>
                  </div>
                  <div class="location-info">
                    <h4 class="location-name">{{ location.name }}</h4>
                    <p class="location-address">{{ location.address }}</p>
                    <div class="location-meta">
                      <span class="type">{{ getLocationTypeText(location.type) }}</span>
                      <span class="usage-count">使用 {{ location.usageCount }} 次</span>
                    </div>
                  </div>
                  <div class="location-actions">
                    <el-button 
                      size="small" 
                      type="text" 
                      @click.stop="removeFromFrequent(location)"
                    >
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="网点" name="stations" v-if="userRole !== 'CUSTOMER'">
              <div class="location-list">
                <div
                  v-for="station in availableStations"
                  :key="station.id"
                  class="location-item"
                  :class="{ active: selectedLocation?.id === station.id }"
                  @click="selectLocation(station)"
                >
                  <div class="location-icon station">
                    <el-icon><OfficeBuilding /></el-icon>
                  </div>
                  <div class="location-info">
                    <h4 class="location-name">{{ station.name }}</h4>
                    <p class="location-address">{{ station.address }}</p>
                    <div class="location-meta">
                      <span class="station-type">{{ getStationTypeText(station.stationType) }}</span>
                      <span class="status" :class="station.status">
                        {{ station.status === 'ACTIVE' ? '营业中' : '暂停服务' }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="地图选择" name="map">
              <div class="map-container">
                <div id="location-map" class="map-view"></div>
                <div class="map-controls">
                  <el-button @click="centerToCurrentLocation">
                    <el-icon><Aim /></el-icon>
                    定位到当前位置
                  </el-button>
                  <el-button @click="confirmMapSelection" type="primary">
                    确认选择此位置
                  </el-button>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>

        <!-- 选中的地点信息 -->
        <div class="selected-location" v-if="selectedLocation">
          <h3>已选择地点</h3>
          <div class="selected-info">
            <div class="selected-icon">
              <el-icon><MapPin /></el-icon>
            </div>
            <div class="selected-details">
              <h4>{{ selectedLocation.name }}</h4>
              <p>{{ selectedLocation.address }}</p>
              <div class="coordinates">
                经度: {{ selectedLocation.longitude?.toFixed(6) }}, 
                纬度: {{ selectedLocation.latitude?.toFixed(6) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button 
            type="primary" 
            @click="confirmSelection"
            :disabled="!selectedLocation"
          >
            确认选择
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Search, Location, MapPin, Star, StarFilled, Delete,
  OfficeBuilding, Aim
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { locationApi } from '@/api/location'

// Props
interface Props {
  modelValue: boolean
  userRole?: string
  currentLocation?: {
    longitude: number
    latitude: number
    address?: string
  }
}

const props = withDefaults(defineProps<Props>(), {
  userRole: 'CUSTOMER'
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'location-selected': [location: any]
}>()

// 响应式数据
const visible = ref(false)
const activeTab = ref('search')
const searchKeyword = ref('')
const gettingLocation = ref(false)
const selectedLocation = ref(null)

const searchResults = ref([])
const frequentLocations = ref([])
const availableStations = ref([])

let map: any = null
let marker: any = null
let searchTimeout: NodeJS.Timeout | null = null

// 用户信息
const userStore = useUserStore()

// 监听显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    initializeData()
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 方法
const initializeData = async () => {
  try {
    // 加载常用地点
    await loadFrequentLocations()
    
    // 如果不是客户，加载可用网点
    if (props.userRole !== 'CUSTOMER') {
      await loadAvailableStations()
    }
    
    // 如果有当前位置，设置为默认选择
    if (props.currentLocation) {
      selectedLocation.value = {
        id: 'current',
        name: '当前位置',
        address: props.currentLocation.address || '当前所在位置',
        longitude: props.currentLocation.longitude,
        latitude: props.currentLocation.latitude,
        type: 'CURRENT'
      }
    }
  } catch (error) {
    console.error('初始化数据失败:', error)
  }
}

const handleSearch = () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  
  searchTimeout = setTimeout(async () => {
    if (searchKeyword.value.trim()) {
      try {
        const response = await locationApi.searchLocations({
          keyword: searchKeyword.value,
          city: userStore.userInfo?.city || '北京市',
          limit: 20
        })
        searchResults.value = response.data || []
        activeTab.value = 'search'
      } catch (error) {
        ElMessage.error('搜索地点失败')
      }
    } else {
      searchResults.value = []
    }
  }, 500)
}

const getCurrentLocation = () => {
  gettingLocation.value = true
  
  if (navigator.geolocation) {
    navigator.geolocation.getCurrentPosition(
      async (position) => {
        try {
          const { longitude, latitude } = position.coords
          const response = await locationApi.reverseGeocode(longitude, latitude)
          
          const currentLoc = {
            id: 'current-gps',
            name: '当前GPS位置',
            address: response.data.address,
            longitude,
            latitude,
            type: 'GPS'
          }
          
          selectedLocation.value = currentLoc
          ElMessage.success('获取当前位置成功')
        } catch (error) {
          ElMessage.error('解析位置信息失败')
        } finally {
          gettingLocation.value = false
        }
      },
      (error) => {
        ElMessage.error('获取位置失败，请检查定位权限')
        gettingLocation.value = false
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000
      }
    )
  } else {
    ElMessage.error('浏览器不支持定位功能')
    gettingLocation.value = false
  }
}

const selectLocation = (location: any) => {
  selectedLocation.value = { ...location }
}

const addToFrequent = async (location: any) => {
  try {
    await locationApi.saveFrequentLocation({
      locationName: location.name,
      address: location.address,
      longitude: location.longitude,
      latitude: location.latitude,
      locationType: location.type
    })
    ElMessage.success('已添加到常用地点')
    await loadFrequentLocations()
  } catch (error) {
    ElMessage.error('添加常用地点失败')
  }
}

const removeFromFrequent = async (location: any) => {
  try {
    await locationApi.removeFrequentLocation(location.id)
    ElMessage.success('已从常用地点移除')
    await loadFrequentLocations()
  } catch (error) {
    ElMessage.error('移除常用地点失败')
  }
}

const loadFrequentLocations = async () => {
  try {
    const response = await locationApi.getFrequentLocations()
    frequentLocations.value = response.data || []
  } catch (error) {
    console.error('加载常用地点失败:', error)
  }
}

const loadAvailableStations = async () => {
  try {
    const response = await locationApi.getAvailableStations()
    availableStations.value = response.data || []
  } catch (error) {
    console.error('加载可用网点失败:', error)
  }
}

const handleTabChange = (tabName: string) => {
  if (tabName === 'map') {
    nextTick(() => {
      initMap()
    })
  }
}

const initMap = () => {
  // 初始化高德地图
  if (window.AMap) {
    map = new window.AMap.Map('location-map', {
      zoom: 15,
      center: props.currentLocation ? 
        [props.currentLocation.longitude, props.currentLocation.latitude] : 
        [116.397428, 39.90923]
    })
    
    // 添加点击事件
    map.on('click', (e: any) => {
      const { lng, lat } = e.lnglat
      updateMapMarker(lng, lat)
      reverseGeocodeMapLocation(lng, lat)
    })
    
    // 如果有当前位置，添加标记
    if (props.currentLocation) {
      updateMapMarker(props.currentLocation.longitude, props.currentLocation.latitude)
    }
  }
}

const updateMapMarker = (lng: number, lat: number) => {
  if (marker) {
    marker.setPosition([lng, lat])
  } else {
    marker = new window.AMap.Marker({
      position: [lng, lat],
      map: map
    })
  }
}

const reverseGeocodeMapLocation = async (lng: number, lat: number) => {
  try {
    const response = await locationApi.reverseGeocode(lng, lat)
    selectedLocation.value = {
      id: 'map-selected',
      name: '地图选择位置',
      address: response.data.address,
      longitude: lng,
      latitude: lat,
      type: 'MAP'
    }
  } catch (error) {
    console.error('解析地图位置失败:', error)
  }
}

const centerToCurrentLocation = () => {
  if (props.currentLocation && map) {
    map.setCenter([props.currentLocation.longitude, props.currentLocation.latitude])
    updateMapMarker(props.currentLocation.longitude, props.currentLocation.latitude)
  } else {
    getCurrentLocation()
  }
}

const confirmMapSelection = () => {
  if (selectedLocation.value) {
    ElMessage.success('已选择地图位置')
  } else {
    ElMessage.warning('请先在地图上点击选择位置')
  }
}

const confirmSelection = () => {
  if (selectedLocation.value) {
    emit('location-selected', selectedLocation.value)
    handleClose()
  }
}

const handleClose = () => {
  visible.value = false
  selectedLocation.value = null
  searchKeyword.value = ''
  searchResults.value = []
  activeTab.value = 'search'
}

const getLocationTypeText = (type: string) => {
  const typeMap = {
    'CURRENT': '当前位置',
    'GPS': 'GPS定位',
    'MAP': '地图选择',
    'SEARCH': '搜索结果',
    'FREQUENT': '常用地点',
    'STATION': '网点',
    'LANDMARK': '地标'
  }
  return typeMap[type] || type
}

const getStationTypeText = (type: string) => {
  const typeMap = {
    'PICKUP': '揽件点',
    'TRANSIT': '中转站',
    'DELIVERY': '派送点'
  }
  return typeMap[type] || type
}

onMounted(() => {
  // 动态加载高德地图API
  if (!window.AMap) {
    const script = document.createElement('script')
    script.src = `https://webapi.amap.com/maps?v=1.4.15&key=YOUR_AMAP_KEY&callback=initAMap`
    document.head.appendChild(script)
  }
})
</script>
