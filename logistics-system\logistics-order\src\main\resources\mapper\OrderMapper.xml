<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.example.logisticsorder.mapper.OrderMapper">

    <!-- 订单基础结果映射 -->
    <resultMap id="BaseResultMap" type="org.example.logisticsorder.entity.Order">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="order_number" property="orderNumber" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="sender_name" property="senderName" jdbcType="VARCHAR"/>
        <result column="sender_phone" property="senderPhone" jdbcType="VARCHAR"/>
        <result column="sender_address" property="senderAddress" jdbcType="VARCHAR"/>
        <result column="sender_province" property="senderProvince" jdbcType="VARCHAR"/>
        <result column="sender_city" property="senderCity" jdbcType="VARCHAR"/>
        <result column="sender_district" property="senderDistrict" jdbcType="VARCHAR"/>
        <result column="sender_longitude" property="senderLongitude" jdbcType="DECIMAL"/>
        <result column="sender_latitude" property="senderLatitude" jdbcType="DECIMAL"/>
        <result column="receiver_name" property="receiverName" jdbcType="VARCHAR"/>
        <result column="receiver_phone" property="receiverPhone" jdbcType="VARCHAR"/>
        <result column="receiver_address" property="receiverAddress" jdbcType="VARCHAR"/>
        <result column="receiver_province" property="receiverProvince" jdbcType="VARCHAR"/>
        <result column="receiver_city" property="receiverCity" jdbcType="VARCHAR"/>
        <result column="receiver_district" property="receiverDistrict" jdbcType="VARCHAR"/>
        <result column="receiver_longitude" property="receiverLongitude" jdbcType="DECIMAL"/>
        <result column="receiver_latitude" property="receiverLatitude" jdbcType="DECIMAL"/>
        <result column="item_name" property="itemName" jdbcType="VARCHAR"/>
        <result column="item_type" property="itemType" jdbcType="VARCHAR"/>
        <result column="item_weight" property="itemWeight" jdbcType="DECIMAL"/>
        <result column="item_volume" property="itemVolume" jdbcType="DECIMAL"/>
        <result column="item_length" property="itemLength" jdbcType="DECIMAL"/>
        <result column="item_width" property="itemWidth" jdbcType="DECIMAL"/>
        <result column="item_height" property="itemHeight" jdbcType="DECIMAL"/>
        <result column="item_value" property="itemValue" jdbcType="DECIMAL"/>
        <result column="is_fragile" property="isFragile" jdbcType="TINYINT"/>
        <result column="service_type" property="serviceType" jdbcType="VARCHAR"/>
        <result column="shipping_fee" property="shippingFee" jdbcType="DECIMAL"/>
        <result column="insurance_fee" property="insuranceFee" jdbcType="DECIMAL"/>
        <result column="packing_fee" property="packingFee" jdbcType="DECIMAL"/>
        <result column="total_fee" property="totalFee" jdbcType="DECIMAL"/>
        <result column="payment_method" property="paymentMethod" jdbcType="VARCHAR"/>
        <result column="payment_status" property="paymentStatus" jdbcType="TINYINT"/>
        <result column="order_status" property="orderStatus" jdbcType="VARCHAR"/>
        <result column="pickup_time" property="pickupTime" jdbcType="TIMESTAMP"/>
        <result column="delivery_time" property="deliveryTime" jdbcType="TIMESTAMP"/>
        <result column="sign_time" property="signTime" jdbcType="TIMESTAMP"/>
        <result column="estimated_delivery_time" property="estimatedDeliveryTime" jdbcType="TIMESTAMP"/>
        <result column="remarks" property="remarks" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 订单列表VO结果映射 -->
    <resultMap id="OrderListVOMap" type="org.example.logisticsorder.vo.OrderListVO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="order_number" property="orderNumber" jdbcType="VARCHAR"/>
        <result column="sender_name" property="senderName" jdbcType="VARCHAR"/>
        <result column="sender_phone" property="senderPhone" jdbcType="VARCHAR"/>
        <result column="sender_city" property="senderCity" jdbcType="VARCHAR"/>
        <result column="receiver_name" property="receiverName" jdbcType="VARCHAR"/>
        <result column="receiver_phone" property="receiverPhone" jdbcType="VARCHAR"/>
        <result column="receiver_city" property="receiverCity" jdbcType="VARCHAR"/>
        <result column="item_name" property="itemName" jdbcType="VARCHAR"/>
        <result column="item_weight" property="itemWeight" jdbcType="DECIMAL"/>
        <result column="service_type" property="serviceType" jdbcType="VARCHAR"/>
        <result column="total_fee" property="totalFee" jdbcType="DECIMAL"/>
        <result column="payment_status" property="paymentStatus" jdbcType="TINYINT"/>
        <result column="order_status" property="orderStatus" jdbcType="VARCHAR"/>
        <result column="estimated_delivery_time" property="estimatedDeliveryTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        id, order_number, user_id, sender_name, sender_phone, sender_address, sender_province,
        sender_city, sender_district, sender_longitude, sender_latitude, receiver_name,
        receiver_phone, receiver_address, receiver_province, receiver_city, receiver_district,
        receiver_longitude, receiver_latitude, item_name, item_type, item_weight, item_volume,
        item_length, item_width, item_height, item_value, is_fragile, service_type,
        shipping_fee, insurance_fee, packing_fee, total_fee, payment_method, payment_status,
        order_status, pickup_time, delivery_time, sign_time, estimated_delivery_time,
        remarks, create_time, update_time
    </sql>

    <!-- 列表查询字段 -->
    <sql id="List_Column_List">
        id, order_number, sender_name, sender_phone, sender_city, receiver_name,
        receiver_phone, receiver_city, item_name, item_weight, service_type,
        total_fee, payment_status, order_status, estimated_delivery_time, create_time
    </sql>

    <!-- 根据订单号查询订单 -->
    <select id="selectByOrderNumber" parameterType="string" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM orders
        WHERE order_number = #{orderNumber}
    </select>

    <!-- 分页查询订单列表 -->
    <select id="selectOrderPage" resultMap="OrderListVOMap">
        SELECT <include refid="List_Column_List"/>
        FROM orders
        <where>
            <if test="query.orderNumber != null and query.orderNumber != ''">
                AND order_number = #{query.orderNumber}
            </if>
            <if test="query.userId != null">
                AND user_id = #{query.userId}
            </if>
            <if test="query.orderStatus != null and query.orderStatus != ''">
                AND order_status = #{query.orderStatus}
            </if>
            <if test="query.paymentStatus != null">
                AND payment_status = #{query.paymentStatus}
            </if>
            <if test="query.senderPhone != null and query.senderPhone != ''">
                AND sender_phone = #{query.senderPhone}
            </if>
            <if test="query.receiverPhone != null and query.receiverPhone != ''">
                AND receiver_phone = #{query.receiverPhone}
            </if>
            <if test="query.receiverCity != null and query.receiverCity != ''">
                AND receiver_city = #{query.receiverCity}
            </if>
            <if test="query.serviceType != null and query.serviceType != ''">
                AND service_type = #{query.serviceType}
            </if>
            <if test="query.startTime != null and query.endTime != null">
                AND create_time BETWEEN #{query.startTime} AND #{query.endTime}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 根据用户ID查询订单列表 -->
    <select id="selectByUserId" resultMap="OrderListVOMap">
        SELECT <include refid="List_Column_List"/>
        FROM orders
        WHERE user_id = #{userId}
        ORDER BY create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据收件人电话查询订单 -->
    <select id="selectByReceiverPhone" parameterType="string" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM orders
        WHERE receiver_phone = #{receiverPhone}
        ORDER BY create_time DESC
    </select>

    <!-- 根据寄件人电话查询订单 -->
    <select id="selectBySenderPhone" parameterType="string" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM orders
        WHERE sender_phone = #{senderPhone}
        ORDER BY create_time DESC
    </select>

    <!-- 根据订单状态查询订单数量 -->
    <select id="countByStatus" parameterType="string" resultType="long">
        SELECT COUNT(*)
        FROM orders
        WHERE order_status = #{orderStatus}
    </select>

    <!-- 根据用户ID和状态查询订单数量 -->
    <select id="countByUserIdAndStatus" resultType="long">
        SELECT COUNT(*)
        FROM orders
        WHERE user_id = #{userId} AND order_status = #{orderStatus}
    </select>

    <!-- 查询待支付订单（超时自动取消） -->
    <select id="selectPendingPaymentOrders" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM orders
        WHERE order_status = 'PENDING'
        AND payment_status = 0
        AND create_time &lt;= DATE_SUB(NOW(), INTERVAL #{timeoutMinutes} MINUTE)
    </select>

    <select id="countByStatusAndTimeRange" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM orders
        WHERE 1=1
        <if test="status != null and status != ''">
            AND order_status = #{status}
        </if>
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
    </select>

    <!-- 根据时间范围统计订单金额 -->
    <select id="sumAmountByTimeRange" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(total_fee), 0)
        FROM orders
        WHERE order_status = 'DELIVERED'
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
    </select>

    <!-- 批量更新订单状态 -->
    <update id="batchUpdateStatus">
        UPDATE orders
        SET order_status = #{newStatus}, update_time = NOW()
        WHERE id IN
        <foreach collection="orderIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 更新订单支付状态 -->
    <update id="updatePaymentStatus">
        UPDATE orders
        SET payment_status = #{paymentStatus}, update_time = NOW()
        WHERE id = #{orderId}
    </update>

    <!-- 更新订单时间字段 -->
    <update id="updateOrderTime">
        UPDATE orders
        SET 
        <choose>
            <when test="timeType == 'pickup'">pickup_time = #{timeValue}</when>
            <when test="timeType == 'delivery'">delivery_time = #{timeValue}</when>
            <when test="timeType == 'sign'">sign_time = #{timeValue}</when>
            <when test="timeType == 'estimated_delivery'">estimated_delivery_time = #{timeValue}</when>
        </choose>,
        update_time = NOW()
        WHERE id = #{orderId}
    </update>

</mapper>