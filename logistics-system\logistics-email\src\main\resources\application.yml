server:
  port: 8087

spring:
  application:
    name: logistics-email
  
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************
    username: root
    password: 123456
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
  
  # 邮件配置
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>  # 替换为你的QQ邮箱
    password: your-auth-code     # 替换为你的QQ邮箱授权码
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
          ssl:
            trust: smtp.qq.com
    default-encoding: UTF-8
  
  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms
  
  # RabbitMQ配置
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    virtual-host: /
    listener:
      simple:
        acknowledge-mode: manual
        retry:
          enabled: true
          max-attempts: 3
          initial-interval: 1000ms
  
  # Thymeleaf配置
  thymeleaf:
    mode: HTML
    encoding: UTF-8
    cache: false
    prefix: classpath:/templates/
    suffix: .html

# Nacos配置
cloud:
  nacos:
    discovery:
      server-addr: localhost:8848
      namespace: public

# 邮件服务配置
email:
  # 发件人信息
  from:
    address: <EMAIL>  # 替换为你的QQ邮箱
    name: 智慧物流系统
  
  # 模板配置
  template:
    cache-enabled: true
    cache-duration: 3600  # 模板缓存时间（秒）
  
  # 发送配置
  send:
    async: true           # 异步发送
    retry-times: 3        # 重试次数
    retry-interval: 5000  # 重试间隔（毫秒）
  
  # 限流配置
  rate-limit:
    enabled: true
    max-per-minute: 60    # 每分钟最大发送数量
    max-per-hour: 1000    # 每小时最大发送数量

# 日志配置
logging:
  level:
    org.example.logisticsemail: DEBUG
    org.springframework.mail: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
