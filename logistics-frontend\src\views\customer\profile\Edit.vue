<template>
  <div class="profile-edit">
    <div class="page-header">
      <h2>编辑个人资料</h2>
      <p>修改您的个人信息</p>
    </div>

    <el-card>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        @submit.prevent="handleSubmit"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="真实姓名" prop="realName">
              <el-input v-model="form.realName" placeholder="请输入真实姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户名" prop="username">
              <el-input v-model="form.username" placeholder="请输入用户名" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入手机号码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="电子邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入电子邮箱" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="身份证号" prop="idCard">
              <el-input v-model="form.idCard" placeholder="请输入身份证号（可选）" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-radio-group v-model="form.gender">
                <el-radio :label="1">男</el-radio>
                <el-radio :label="2">女</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="出生日期" prop="birthday">
              <el-date-picker
                v-model="form.birthday"
                type="date"
                placeholder="请选择出生日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="头像">
              <el-upload
                class="avatar-uploader"
                :show-file-list="false"
                :on-success="handleAvatarSuccess"
                :on-error="handleAvatarError"
                :before-upload="beforeAvatarUpload"
                :http-request="customUpload"
                :disabled="uploading"
              >
                <img v-if="form.avatar" :src="form.avatar" class="avatar" />
                <el-icon v-else class="avatar-uploader-icon">
                  <Plus v-if="!uploading" />
                  <Loading v-else />
                </el-icon>
              </el-upload>
              <div class="upload-tip">支持JPG、PNG格式，文件大小不超过2MB</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="loading"> 保存修改 </el-button>
          <el-button @click="handleCancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import {
  ElMessage,
  type FormInstance,
  type FormRules,
  type UploadRequestOptions,
  type UploadRawFile,
} from 'element-plus'
import { Plus, Loading } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { userApi, type AvatarUploadResponse } from '@/api/user'
import type { ApiResponse } from '@/types/api'

const router = useRouter()
const authStore = useAuthStore()
const formRef = ref<FormInstance>()
const loading = ref(false)
const uploading = ref(false)

// 表单数据
const form = reactive({
  username: '',
  realName: '',
  phone: '',
  email: '',
  idCard: '',
  gender: undefined as number | undefined,
  birthday: '',
  avatar: '',
})

// 验证规则
const rules: FormRules = {
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' },
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入正确的手机号码',
      trigger: 'blur',
    },
  ],
  email: [
    { required: true, message: '请输入电子邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
  ],
  idCard: [
    {
      pattern:
        /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
      message: '请输入正确的身份证号码',
      trigger: 'blur',
    },
  ],
}

// 初始化表单数据
const initForm = async () => {
  try {
    loading.value = true
    const response = await userApi.getUserEditInfo()

    if (response.code === 200) {
      const userInfo = response.data
      form.realName = userInfo.realName || ''
      form.phone = userInfo.phone || ''
      form.email = userInfo.email || ''
      form.idCard = userInfo.idCard || ''
      form.gender = userInfo.gender
      form.birthday = userInfo.birthday || ''
      form.avatar = userInfo.avatar || ''

      // 设置用户名（从store获取，不可编辑）
      form.username = authStore.userInfo?.username || ''
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败')
  } finally {
    loading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    const updateData = {
      realName: form.realName,
      phone: form.phone,
      email: form.email,
      idCard: form.idCard || undefined,
      gender: form.gender,
      birthday: form.birthday || undefined,
      avatar: form.avatar || undefined,
    }

    const response = await userApi.updateUserInfo(updateData)

    if (response.code === 200) {
      ElMessage.success('个人资料更新成功')
      // 更新store中的用户信息
      await authStore.fetchUserInfo()
      router.back()
    } else {
      ElMessage.error(response.message || '更新失败')
    }
  } catch (error) {
    console.error('更新失败:', error)
    ElMessage.error('更新失败，请重试')
  } finally {
    loading.value = false
  }
}

// 自定义上传函数
const customUpload = async (options: UploadRequestOptions) => {
  try {
    uploading.value = true
    console.log('开始上传头像:', options.file.name)

    const response = await userApi.uploadAvatar(options.file as File)
    console.log('上传响应:', response)

    // 调用成功回调
    if (options.onSuccess && response) {
      options.onSuccess(response)
    }
  } catch (error) {
    console.error('头像上传失败:', error)
    // 调用错误回调
    if (options.onError) {
      options.onError(error as Error)
    }
  } finally {
    uploading.value = false
  }
}

// 头像上传成功回调
const handleAvatarSuccess = (response: any) => {
  console.log('头像上传成功回调:', response)

  // 根据后端实际返回的格式处理
  if (response && response.code === 200 && response.data && response.data.url) {
    form.avatar = response.data.url
    ElMessage.success('头像上传成功')
  } else {
    console.error('上传响应格式错误:', response)
    ElMessage.error('头像上传失败')
  }
}

// 头像上传失败回调
const handleAvatarError = (error: Error) => {
  console.error('头像上传失败回调:', error)
  ElMessage.error('头像上传失败，请重试')
}

// 头像上传前检查
const beforeAvatarUpload = (rawFile: UploadRawFile) => {
  const isJPG = rawFile.type === 'image/jpeg' || rawFile.type === 'image/png'
  const isLt2M = rawFile.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('头像图片只能是 JPG/PNG 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('头像图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 取消编辑
const handleCancel = () => {
  router.back()
}

onMounted(() => {
  initForm()
})
</script>

<style scoped>
.profile-edit {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 10px 0;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
}

.avatar-uploader .avatar {
  width: 80px;
  height: 80px;
  display: block;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-uploader :deep(.el-upload) {
  border: 1px dashed #d9d9d9;
  border-radius: 50%;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: border-color 0.3s;
}

.avatar-uploader :deep(.el-upload:hover) {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}

.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

@media (max-width: 768px) {
  .profile-edit {
    padding: 15px;
  }
}
</style>
