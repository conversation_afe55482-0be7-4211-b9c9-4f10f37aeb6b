<template>
  <div class="base-map">
    <div ref="mapContainer" class="map-container"></div>
    <div v-if="loading" class="map-loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>地图加载中...</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import { mapApi, type MapConfig } from '@/api/map'

interface Props {
  center?: { lat: number; lng: number }
  zoom?: number
  markers?: Array<{
    id: string
    position: { lat: number; lng: number }
    title?: string
    content?: string
  }>
  polylines?: Array<{
    id: string
    path: Array<{ lat: number; lng: number }>
    strokeColor?: string
    strokeWeight?: number
  }>
}

const props = withDefaults(defineProps<Props>(), {
  zoom: 12,
  markers: () => [],
  polylines: () => [],
})

const emit = defineEmits<{
  mapClick: [event: { lat: number; lng: number }]
  markerClick: [marker: { id: string; position: { lat: number; lng: number } }]
}>()

const mapContainer = ref<HTMLDivElement>()
const loading = ref(true)

let map: any = null
let AMap: any = null
let mapConfig: MapConfig | null = null

// 初始化地图
const initMap = async () => {
  try {
    loading.value = true

    // 获取地图配置
    const configResponse = await mapApi.getConfig()
    if (configResponse.code !== 200) {
      throw new Error('获取地图配置失败')
    }

    mapConfig = configResponse.data

    // 动态加载高德地图API
    await loadAmapAPI(mapConfig.jsApiKey)

    // 创建地图实例
    map = new AMap.Map(mapContainer.value, {
      zoom: props.zoom,
      center: [
        props.center?.lng || mapConfig.defaultCenter.lng,
        props.center?.lat || mapConfig.defaultCenter.lat,
      ],
      mapStyle: 'amap://styles/normal',
      viewMode: '2D',
      resizeEnable: true,
    })

    // 添加地图事件
    map.on('click', (e: any) => {
      emit('mapClick', {
        lat: e.lnglat.lat,
        lng: e.lnglat.lng,
      })
    })

    // 初始化标记和路线
    updateMarkers()
    updatePolylines()

    loading.value = false
  } catch (error) {
    console.error('地图初始化失败:', error)
    ElMessage.error('地图初始化失败')
    loading.value = false
  }
}

// 动态加载高德地图API
const loadAmapAPI = (key: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (window.AMap) {
      AMap = window.AMap
      resolve()
      return
    }

    const script = document.createElement('script')
    script.src = `https://webapi.amap.com/maps?v=2.0&key=${key}&plugin=AMap.Geocoder,AMap.PlaceSearch,AMap.Autocomplete`
    script.async = true

    script.onload = () => {
      AMap = window.AMap
      resolve()
    }

    script.onerror = () => {
      reject(new Error('地图API加载失败'))
    }

    document.head.appendChild(script)
  })
}

// 更新标记
const updateMarkers = () => {
  if (!map || !AMap) return

  // 清除现有标记
  map.clearMap()

  // 添加新标记
  props.markers.forEach((marker) => {
    const amapMarker = new AMap.Marker({
      position: [marker.position.lng, marker.position.lat],
      title: marker.title,
      content: marker.content,
    })

    amapMarker.on('click', () => {
      emit('markerClick', marker)
    })

    map.add(amapMarker)
  })
}

// 更新路线
const updatePolylines = () => {
  if (!map || !AMap) return

  props.polylines.forEach((polyline) => {
    const amapPolyline = new AMap.Polyline({
      path: polyline.path.map((point) => [point.lng, point.lat]),
      strokeColor: polyline.strokeColor || '#3366FF',
      strokeWeight: polyline.strokeWeight || 5,
      strokeOpacity: 0.8,
    })

    map.add(amapPolyline)
  })
}

// 设置地图中心
const setCenter = (center: { lat: number; lng: number }) => {
  if (map) {
    map.setCenter([center.lng, center.lat])
  }
}

// 设置地图缩放级别
const setZoom = (zoom: number) => {
  if (map) {
    map.setZoom(zoom)
  }
}

// 地址转坐标
const geocode = async (address: string) => {
  try {
    const response = await mapApi.geocode(address)
    if (response.code === 200) {
      return response.data
    }
    throw new Error(respons.message || '地址解析失败')
  } catch (error) {
    console.error('地址解析失败:', error)
    ElMessage.error('地址解析失败')
    return null
  }
}

// 坐标转地址
const reverseGeocode = async (lat: number, lng: number) => {
  try {
    const response = await mapApi.reverseGeocode(lat, lng)
    if (response.code === 200) {
      return response.data.data
    }
    throw new Error(response.message || '坐标解析失败')
  } catch (error) {
    console.error('坐标解析失败:', error)
    ElMessage.error('坐标解析失败')
    return null
  }
}

// 监听属性变化
watch(
  () => props.center,
  (newCenter) => {
    if (newCenter) {
      setCenter(newCenter)
    }
  },
)

watch(
  () => props.zoom,
  (newZoom) => {
    setZoom(newZoom)
  },
)

watch(
  () => props.markers,
  () => {
    updateMarkers()
  },
  { deep: true },
)

watch(
  () => props.polylines,
  () => {
    updatePolylines()
  },
  { deep: true },
)

// 暴露方法给父组件
defineExpose({
  setCenter,
  setZoom,
  geocode,
  reverseGeocode,
})

onMounted(() => {
  initMap()
})

onUnmounted(() => {
  if (map) {
    map.destroy()
  }
})
</script>

<style scoped>
.base-map {
  position: relative;
  width: 100%;
  height: 100%;
}

.map-container {
  width: 100%;
  height: 100%;
}

.map-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.9);
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}
</style>
