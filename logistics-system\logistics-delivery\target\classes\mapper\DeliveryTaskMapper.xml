<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.logisticsdelivery.mapper.DeliveryTaskMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="org.example.logisticsdelivery.entity.DeliveryTask">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="task_number" jdbcType="VARCHAR" property="taskNumber"/>
        <result column="order_number" jdbcType="VARCHAR" property="orderNumber"/>
        <result column="courier_id" jdbcType="BIGINT" property="courierId"/>
        <result column="task_type" jdbcType="VARCHAR" property="taskType"/>
        <result column="task_status" jdbcType="VARCHAR" property="taskStatus"/>
        <result column="sender_name" jdbcType="VARCHAR" property="senderName"/>
        <result column="sender_phone" jdbcType="VARCHAR" property="senderPhone"/>
        <result column="sender_address" jdbcType="VARCHAR" property="senderAddress"/>
        <result column="receiver_name" jdbcType="VARCHAR" property="receiverName"/>
        <result column="receiver_phone" jdbcType="VARCHAR" property="receiverPhone"/>
        <result column="receiver_address" jdbcType="VARCHAR" property="receiverAddress"/>
        <result column="estimated_time" jdbcType="TIMESTAMP" property="estimatedTime"/>
        <result column="actual_time" jdbcType="TIMESTAMP" property="actualTime"/>
        <result column="priority" jdbcType="INTEGER" property="priority"/>
        <result column="weight" jdbcType="DECIMAL" property="weight"/>
        <result column="volume" jdbcType="DECIMAL" property="volume"/>
        <result column="goods_description" jdbcType="VARCHAR" property="goodsDescription"/>
        <result column="special_requirements" jdbcType="VARCHAR" property="specialRequirements"/>
        <result column="delivery_fee" jdbcType="DECIMAL" property="deliveryFee"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        id, task_number, order_number, courier_id, task_type, task_status,
        sender_name, sender_phone, sender_address, receiver_name, receiver_phone, receiver_address,
        estimated_time, actual_time, priority, weight, volume, goods_description,
        special_requirements, delivery_fee, remarks, create_time, update_time
    </sql>

    <!-- 根据配送员ID查询任务列表 -->
    <select id="findByCourierId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM delivery_tasks
        WHERE courier_id = #{courierId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据任务状态查询任务列表 -->
    <select id="findByTaskStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM delivery_tasks
        WHERE task_status = #{taskStatus}
        ORDER BY priority DESC, create_time ASC
    </select>

    <!-- 根据订单号查询任务 -->
    <select id="findByOrderNumber" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM delivery_tasks
        WHERE order_number = #{orderNumber}
    </select>

    <!-- 根据任务编号查询任务 -->
    <select id="findByTaskNumber" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM delivery_tasks
        WHERE task_number = #{taskNumber}
    </select>

    <!-- 查询配送员当日任务 -->
    <select id="findCourierDailyTasks" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM delivery_tasks
        WHERE courier_id = #{courierId}
        AND DATE(create_time) = #{taskDate}
        ORDER BY priority DESC, create_time ASC
    </select>

    <!-- 查询配送员进行中的任务 -->
    <select id="findCourierActiveTasks" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM delivery_tasks
        WHERE courier_id = #{courierId}
        AND task_status IN ('ASSIGNED', 'PICKED_UP', 'IN_TRANSIT')
        ORDER BY priority DESC, create_time ASC
    </select>

    <!-- 查询待分配的任务 -->
    <select id="findPendingTasks" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM delivery_tasks dt
        WHERE dt.task_status = 'PENDING'
        <if test="taskType != null and taskType != ''">
            AND dt.task_type = #{taskType}
        </if>
        <if test="workArea != null and workArea != ''">
            AND (dt.sender_address LIKE CONCAT('%', #{workArea}, '%') 
            OR dt.receiver_address LIKE CONCAT('%', #{workArea}, '%'))
        </if>
        ORDER BY dt.priority DESC, dt.create_time ASC
    </select>

    <!-- 查询超时的任务 -->
    <select id="findTimeoutTasks" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM delivery_tasks
        WHERE task_status IN ('ASSIGNED', 'PICKED_UP', 'IN_TRANSIT')
        AND estimated_time &lt; DATE_SUB(NOW(), INTERVAL #{timeoutMinutes} MINUTE)
        ORDER BY estimated_time ASC
    </select>

    <!-- 批量更新任务状态 -->
    <update id="batchUpdateTaskStatus">
        UPDATE delivery_tasks
        SET task_status = #{taskStatus},
            update_time = NOW()
        WHERE id IN
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </update>

    <!-- 分页查询任务 -->
    <select id="findTasksPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM delivery_tasks
        WHERE 1=1
        <if test="courierId != null">
            AND courier_id = #{courierId}
        </if>
        <if test="taskStatus != null and taskStatus != ''">
            AND task_status = #{taskStatus}
        </if>
        <if test="taskType != null and taskType != ''">
            AND task_type = #{taskType}
        </if>
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 统计任务数据 -->
    <select id="getTaskStatistics" resultType="java.util.Map">
        SELECT 
            COUNT(*) as totalTasks,
            COUNT(CASE WHEN task_status = 'COMPLETED' THEN 1 END) as completedTasks,
            COUNT(CASE WHEN task_status = 'CANCELLED' THEN 1 END) as cancelledTasks,
            COUNT(CASE WHEN task_status IN ('ASSIGNED', 'PICKED_UP', 'IN_TRANSIT') THEN 1 END) as activeTasks,
            ROUND(AVG(delivery_fee), 2) as avgDeliveryFee,
            SUM(delivery_fee) as totalDeliveryFee
        FROM delivery_tasks
        WHERE 1=1
        <if test="courierId != null">
            AND courier_id = #{courierId}
        </if>
        <if test="startDate != null">
            AND DATE(create_time) >= #{startDate}
        </if>
        <if test="endDate != null">
            AND DATE(create_time) &lt;= #{endDate}
        </if>
    </select>

    <!-- 统计各状态任务数量 -->
    <select id="countByTaskStatus" resultType="java.util.Map">
        SELECT 
            task_status,
            COUNT(*) as count
        FROM delivery_tasks
        GROUP BY task_status
    </select>

</mapper> 