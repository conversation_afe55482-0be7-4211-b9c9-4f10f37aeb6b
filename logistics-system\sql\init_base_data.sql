-- =============================================
-- 物流系统基础数据初始化脚本
-- 执行时间：2024-12-30
-- 说明：插入真实的基础数据，支持完整的业务流程测试
-- =============================================

-- 清理现有数据（可选，谨慎使用）
-- DELETE FROM delivery_tasks WHERE id > 0;
-- DELETE FROM logistics_tracking WHERE id > 0;
-- DELETE FROM orders WHERE id > 0;
-- DELETE FROM users WHERE id > 1;

-- =============================================
-- 1. 插入网点数据
-- =============================================
INSERT INTO `logistics_stations` (`station_code`, `station_name`, `station_type`, `province`, `city`, `district`, `detailed_address`, `longitude`, `latitude`, `contact_person`, `contact_phone`, `business_hours`, `service_scope`, `capacity`, `status`, `create_time`, `update_time`) VALUES
-- 北京网点
('BJ001', '北京朝阳分拣中心', 'TRANSIT', '北京市', '北京市', '朝阳区', '朝阳区建国路88号', 116.4074, 39.9042, '张经理', '010-12345678', '06:00-22:00', '朝阳区全区', 5000, 1, NOW(), NOW()),
('BJ002', '北京海淀揽件点', 'PICKUP', '北京市', '北京市', '海淀区', '海淀区中关村大街1号', 116.3017, 39.9656, '李主管', '010-87654321', '08:00-20:00', '海淀区中关村地区', 1000, 1, NOW(), NOW()),
('BJ003', '北京丰台派送点', 'DELIVERY', '北京市', '北京市', '丰台区', '丰台区南三环西路16号', 116.2868, 39.8583, '王队长', '010-11223344', '07:00-21:00', '丰台区全区', 800, 1, NOW(), NOW()),

-- 上海网点
('SH001', '上海浦东分拣中心', 'TRANSIT', '上海市', '上海市', '浦东新区', '浦东新区张江高科技园区', 121.6024, 31.2077, '陈经理', '021-12345678', '06:00-22:00', '浦东新区全区', 6000, 1, NOW(), NOW()),
('SH002', '上海徐汇揽件点', 'PICKUP', '上海市', '上海市', '徐汇区', '徐汇区淮海中路999号', 121.4491, 31.2077, '刘主管', '021-87654321', '08:00-20:00', '徐汇区全区', 1200, 1, NOW(), NOW()),
('SH003', '上海静安派送点', 'DELIVERY', '上海市', '上海市', '静安区', '静安区南京西路1788号', 121.4648, 31.2297, '赵队长', '021-11223344', '07:00-21:00', '静安区全区', 900, 1, NOW(), NOW()),

-- 广州网点
('GZ001', '广州天河分拣中心', 'TRANSIT', '广东省', '广州市', '天河区', '天河区珠江新城花城大道85号', 113.3890, 23.1167, '黄经理', '020-12345678', '06:00-22:00', '天河区全区', 5500, 1, NOW(), NOW()),
('GZ002', '广州越秀揽件点', 'PICKUP', '广东省', '广州市', '越秀区', '越秀区中山五路68号', 113.2644, 23.1291, '林主管', '020-87654321', '08:00-20:00', '越秀区全区', 1100, 1, NOW(), NOW()),
('GZ003', '广州番禺派送点', 'DELIVERY', '广东省', '广州市', '番禺区', '番禺区市桥街道', 113.3849, 22.9489, '吴队长', '020-11223344', '07:00-21:00', '番禺区全区', 850, 1, NOW(), NOW()),

-- 深圳网点
('SZ001', '深圳南山分拣中心', 'TRANSIT', '广东省', '深圳市', '南山区', '南山区科技园南区', 113.9547, 22.5311, '郑经理', '0755-12345678', '06:00-22:00', '南山区全区', 4800, 1, NOW(), NOW()),
('SZ002', '深圳福田揽件点', 'PICKUP', '广东省', '深圳市', '福田区', '福田区华强北路1002号', 114.0579, 22.5455, '何主管', '0755-87654321', '08:00-20:00', '福田区全区', 1000, 1, NOW(), NOW());

-- =============================================
-- 2. 插入用户数据（包含各种角色）
-- =============================================

-- 管理员用户
INSERT INTO `users` (`username`, `password`, `email`, `phone`, `real_name`, `role`, `status`, `avatar`, `create_time`, `update_time`) VALUES
('admin', '$2a$10$7JB720yubVSOfvVaMWye2.bDnhZiQjjt07MM9tKlT3WferLjC1lWq', '<EMAIL>', '13800000001', '系统管理员', 'ADMIN', 1, NULL, NOW(), NOW()),
('operator001', '$2a$10$7JB720yubVSOfvVaMWye2.bDnhZiQjjt07MM9tKlT3WferLjC1lWq', '<EMAIL>', '13800000002', '张操作员', 'OPERATOR', 1, NULL, NOW(), NOW()),
('operator002', '$2a$10$7JB720yubVSOfvVaMWye2.bDnhZiQjjt07MM9tKlT3WferLjC1lWq', '<EMAIL>', '13800000003', '李操作员', 'OPERATOR', 1, NULL, NOW(), NOW());

-- 配送员用户
INSERT INTO `users` (`username`, `password`, `email`, `phone`, `real_name`, `role`, `status`, `avatar`, `create_time`, `update_time`) VALUES
('courier001', '$2a$10$7JB720yubVSOfvVaMWye2.bDnhZiQjjt07MM9tKlT3WferLjC1lWq', '<EMAIL>', '13800001001', '王配送员', 'COURIER', 1, NULL, NOW(), NOW()),
('courier002', '$2a$10$7JB720yubVSOfvVaMWye2.bDnhZiQjjt07MM9tKlT3WferLjC1lWq', '<EMAIL>', '13800001002', '刘配送员', 'COURIER', 1, NULL, NOW(), NOW()),
('courier003', '$2a$10$7JB720yubVSOfvVaMWye2.bDnhZiQjjt07MM9tKlT3WferLjC1lWq', '<EMAIL>', '13800001003', '陈配送员', 'COURIER', 1, NULL, NOW(), NOW()),
('courier004', '$2a$10$7JB720yubVSOfvVaMWye2.bDnhZiQjjt07MM9tKlT3WferLjC1lWq', '<EMAIL>', '13800001004', '赵配送员', 'COURIER', 1, NULL, NOW(), NOW()),
('courier005', '$2a$10$7JB720yubVSOfvVaMWye2.bDnhZiQjjt07MM9tKlT3WferLjC1lWq', '<EMAIL>', '13800001005', '孙配送员', 'COURIER', 1, NULL, NOW(), NOW());

-- 客户用户
INSERT INTO `users` (`username`, `password`, `email`, `phone`, `real_name`, `role`, `status`, `avatar`, `create_time`, `update_time`) VALUES
('customer001', '$2a$10$7JB720yubVSOfvVaMWye2.bDnhZiQjjt07MM9tKlT3WferLjC1lWq', '<EMAIL>', '***********', '张三', 'CUSTOMER', 1, NULL, NOW(), NOW()),
('customer002', '$2a$10$7JB720yubVSOfvVaMWye2.bDnhZiQjjt07MM9tKlT3WferLjC1lWq', '<EMAIL>', '***********', '李四', 'CUSTOMER', 1, NULL, NOW(), NOW()),
('customer003', '$2a$10$7JB720yubVSOfvVaMWye2.bDnhZiQjjt07MM9tKlT3WferLjC1lWq', '<EMAIL>', '***********', '王五', 'CUSTOMER', 1, NULL, NOW(), NOW()),
('customer004', '$2a$10$7JB720yubVSOfvVaMWye2.bDnhZiQjjt07MM9tKlT3WferLjC1lWq', '<EMAIL>', '***********', '赵六', 'CUSTOMER', 1, NULL, NOW(), NOW()),
('customer005', '$2a$10$7JB720yubVSOfvVaMWye2.bDnhZiQjjt07MM9tKlT3WferLjC1lWq', '<EMAIL>', '13900001005', '钱七', 'CUSTOMER', 1, NULL, NOW(), NOW());

-- =============================================
-- 3. 插入配送员详细信息
-- =============================================
INSERT INTO `couriers` (`user_id`, `courier_name`, `phone`, `email`, `id_card`, `vehicle_type`, `vehicle_number`, `service_area`, `status`, `rating`, `total_orders`, `current_location`, `create_time`, `update_time`) VALUES
-- 北京配送员
((SELECT id FROM users WHERE username = 'courier001'), '王配送员', '13800001001', '<EMAIL>', '110101199001011234', 'ELECTRIC_BIKE', '京A12345', '北京市朝阳区', 'AVAILABLE', 4.8, 156, '北京市朝阳区建国路88号', NOW(), NOW()),
((SELECT id FROM users WHERE username = 'courier002'), '刘配送员', '13800001002', '<EMAIL>', '110101199002021234', 'MOTORCYCLE', '京B67890', '北京市海淀区', 'AVAILABLE', 4.9, 203, '北京市海淀区中关村大街1号', NOW(), NOW()),

-- 上海配送员
((SELECT id FROM users WHERE username = 'courier003'), '陈配送员', '13800001003', '<EMAIL>', '310101199003031234', 'VAN', '沪C11111', '上海市浦东新区', 'AVAILABLE', 4.7, 178, '上海市浦东新区张江高科技园区', NOW(), NOW()),
((SELECT id FROM users WHERE username = 'courier004'), '赵配送员', '13800001004', '<EMAIL>', '310101199004041234', 'ELECTRIC_BIKE', '沪D22222', '上海市徐汇区', 'BUSY', 4.6, 134, '上海市徐汇区淮海中路999号', NOW(), NOW()),

-- 广州配送员
((SELECT id FROM users WHERE username = 'courier005'), '孙配送员', '13800001005', '<EMAIL>', '440101199005051234', 'MOTORCYCLE', '粤A33333', '广州市天河区', 'AVAILABLE', 4.8, 189, '广州市天河区珠江新城花城大道85号', NOW(), NOW());

-- =============================================
-- 4. 插入常用地址数据（用于Redis缓存测试）
-- =============================================
INSERT INTO `addresses` (`user_id`, `contact_name`, `contact_phone`, `province`, `city`, `district`, `detailed_address`, `postal_code`, `longitude`, `latitude`, `is_default`, `address_type`, `create_time`, `update_time`) VALUES
-- 张三的地址
((SELECT id FROM users WHERE username = 'customer001'), '张三', '***********', '北京市', '北京市', '朝阳区', '朝阳区建国路99号国贸大厦A座1001室', '100020', 116.4074, 39.9042, 1, 'HOME', NOW(), NOW()),
((SELECT id FROM users WHERE username = 'customer001'), '张三', '***********', '北京市', '北京市', '海淀区', '海淀区中关村大街27号中关村大厦B座808室', '100080', 116.3017, 39.9656, 0, 'OFFICE', NOW(), NOW()),

-- 李四的地址
((SELECT id FROM users WHERE username = 'customer002'), '李四', '***********', '上海市', '上海市', '浦东新区', '浦东新区陆家嘴环路1000号恒生银行大厦20楼', '200120', 121.6024, 31.2077, 1, 'HOME', NOW(), NOW()),
((SELECT id FROM users WHERE username = 'customer002'), '李四', '***********', '上海市', '上海市', '徐汇区', '徐汇区淮海中路1045号淮海国际广场15楼', '200030', 121.4491, 31.2077, 0, 'OFFICE', NOW(), NOW()),

-- 王五的地址
((SELECT id FROM users WHERE username = 'customer003'), '王五', '***********', '广东省', '广州市', '天河区', '天河区珠江新城花城大道68号环球都会广场T1栋3001室', '510623', 113.3890, 23.1167, 1, 'HOME', NOW(), NOW()),
((SELECT id FROM users WHERE username = 'customer003'), '王五', '***********', '广东省', '广州市', '越秀区', '越秀区中山五路219号中旅商业城12楼', '510030', 113.2644, 23.1291, 0, 'OFFICE', NOW(), NOW()),

-- 赵六的地址
((SELECT id FROM users WHERE username = 'customer004'), '赵六', '***********', '广东省', '深圳市', '南山区', '南山区科技园南区深圳湾科技生态园10栋A座2001室', '518057', 113.9547, 22.5311, 1, 'HOME', NOW(), NOW()),
((SELECT id FROM users WHERE username = 'customer004'), '赵六', '***********', '广东省', '深圳市', '福田区', '福田区华强北路1002号赛格广场45楼', '518031', 114.0579, 22.5455, 0, 'OFFICE', NOW(), NOW());

-- =============================================
-- 5. 插入邮件模板数据（用于notification模块）
-- =============================================

-- 先创建邮件模板表（如果不存在）
CREATE TABLE IF NOT EXISTS `email_templates` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `template_code` varchar(50) NOT NULL COMMENT '模板编码',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `subject` varchar(200) NOT NULL COMMENT '邮件主题',
  `content` text NOT NULL COMMENT '邮件内容',
  `template_type` varchar(20) DEFAULT 'BUSINESS' COMMENT '模板类型',
  `is_enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_code` (`template_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邮件模板表';

INSERT INTO `email_templates` (`template_code`, `template_name`, `subject`, `content`, `template_type`, `is_enabled`, `create_time`, `update_time`) VALUES
('ORDER_CREATED', '订单创建通知', '【智慧物流】订单创建成功 - {{orderNumber}}', 
'<!DOCTYPE html>
<html>
<head><meta charset="UTF-8"><title>订单创建成功</title></head>
<body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #1890ff, #722ed1); color: white; padding: 20px; border-radius: 8px 8px 0 0;">
        <h1 style="margin: 0; font-size: 24px;">📦 订单创建成功</h1>
        <p style="margin: 5px 0 0 0; opacity: 0.9;">您的包裹即将开始配送之旅</p>
    </div>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px;">
        <h2 style="color: #2c3e50; margin-top: 0;">尊敬的 {{customerName}}，您好！</h2>
        <p>您的订单已成功创建，详情如下：</p>
        <table style="width: 100%; border-collapse: collapse; margin: 15px 0; background: white; border-radius: 6px; overflow: hidden;">
            <tr><td style="padding: 12px; background: #e9ecef; font-weight: bold;">订单号</td><td style="padding: 12px;">{{orderNumber}}</td></tr>
            <tr><td style="padding: 12px; background: #e9ecef; font-weight: bold;">订单金额</td><td style="padding: 12px; color: #e74c3c; font-weight: bold;">¥{{totalFee}}</td></tr>
            <tr><td style="padding: 12px; background: #e9ecef; font-weight: bold;">创建时间</td><td style="padding: 12px;">{{createTime}}</td></tr>
            <tr><td style="padding: 12px; background: #e9ecef; font-weight: bold;">寄件地址</td><td style="padding: 12px;">{{senderAddress}}</td></tr>
            <tr><td style="padding: 12px; background: #e9ecef; font-weight: bold;">收件地址</td><td style="padding: 12px;">{{receiverAddress}}</td></tr>
        </table>
        <p style="color: #34495e;">请及时完成支付，我们将在24小时内安排揽件员上门取件。</p>
        <div style="text-align: center; margin: 20px 0;">
            <a href="{{paymentUrl}}" style="background: #1890ff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">立即支付</a>
        </div>
    </div>
    <div style="text-align: center; margin-top: 20px; color: #95a5a6; font-size: 12px;">
        <p>此邮件由系统自动发送，请勿回复 | 智慧物流系统 © 2024</p>
    </div>
</body>
</html>', 'BUSINESS', 1, NOW(), NOW()),

('PAYMENT_SUCCESS', '支付成功通知', '【智慧物流】支付成功，订单处理中 - {{orderNumber}}',
'<!DOCTYPE html>
<html>
<head><meta charset="UTF-8"><title>支付成功</title></head>
<body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #52c41a, #389e0d); color: white; padding: 20px; border-radius: 8px 8px 0 0;">
        <h1 style="margin: 0; font-size: 24px;">✅ 支付成功</h1>
        <p style="margin: 5px 0 0 0; opacity: 0.9;">您的订单支付已完成，正在安排揽件</p>
    </div>
    <div style="background: #f6ffed; padding: 20px; border-radius: 0 0 8px 8px; border: 1px solid #b7eb8f;">
        <h2 style="color: #2c3e50; margin-top: 0;">尊敬的 {{customerName}}，您好！</h2>
        <p>您的订单支付已成功完成，我们将尽快为您安排揽件服务。</p>
        <table style="width: 100%; border-collapse: collapse; margin: 15px 0; background: white; border-radius: 6px; overflow: hidden;">
            <tr><td style="padding: 12px; background: #e9ecef; font-weight: bold;">订单号</td><td style="padding: 12px;">{{orderNumber}}</td></tr>
            <tr><td style="padding: 12px; background: #e9ecef; font-weight: bold;">支付金额</td><td style="padding: 12px; color: #52c41a; font-weight: bold;">¥{{paymentAmount}}</td></tr>
            <tr><td style="padding: 12px; background: #e9ecef; font-weight: bold;">支付时间</td><td style="padding: 12px;">{{paymentTime}}</td></tr>
            <tr><td style="padding: 12px; background: #e9ecef; font-weight: bold;">支付方式</td><td style="padding: 12px;">{{paymentMethod}}</td></tr>
        </table>
        <p style="color: #34495e;">我们将在24小时内安排揽件员上门取件，请保持电话畅通。</p>
    </div>
    <div style="text-align: center; margin-top: 20px; color: #95a5a6; font-size: 12px;">
        <p>此邮件由系统自动发送，请勿回复 | 智慧物流系统 © 2024</p>
    </div>
</body>
</html>', 'BUSINESS', 1, NOW(), NOW());

-- =============================================
-- 6. 插入示例订单数据
-- =============================================
INSERT INTO `orders` (`order_number`, `sender_name`, `sender_phone`, `sender_address`, `receiver_name`, `receiver_phone`, `receiver_address`, `item_name`, `item_weight`, `item_value`, `service_type`, `total_fee`, `payment_status`, `order_status`, `create_time`, `update_time`) VALUES
('LO202412300001', '张三', '***********', '北京市朝阳区建国路99号国贸大厦A座1001室', '李四', '***********', '上海市浦东新区陆家嘴环路1000号恒生银行大厦20楼', '重要文件', 0.5, 1000.00, 'EXPRESS', 25.00, 1, 'PAID', NOW() - INTERVAL 2 HOUR, NOW()),
('LO202412300002', '王五', '***********', '广州市天河区珠江新城花城大道68号环球都会广场T1栋3001室', '赵六', '***********', '深圳市南山区科技园南区深圳湾科技生态园10栋A座2001室', '电子产品', 2.5, 5000.00, 'STANDARD', 18.00, 1, 'PICKUP_ASSIGNED', NOW() - INTERVAL 1 HOUR, NOW()),
('LO202412300003', '钱七', '13900001005', '北京市海淀区中关村大街27号中关村大厦B座808室', '张三', '***********', '北京市朝阳区建国路99号国贸大厦A座1001室', '书籍资料', 1.2, 200.00, 'STANDARD', 12.00, 0, 'PENDING', NOW() - INTERVAL 30 MINUTE, NOW());

-- =============================================
-- 7. 插入更多真实订单数据（用于测试完整流程）
-- =============================================
INSERT INTO `orders` (`order_number`, `sender_name`, `sender_phone`, `sender_address`, `receiver_name`, `receiver_phone`, `receiver_address`, `item_name`, `item_weight`, `item_value`, `service_type`, `total_fee`, `payment_status`, `order_status`, `create_time`, `update_time`) VALUES
-- 已支付，等待分配揽件员
('LO202412300004', '李四', '***********', '上海市徐汇区淮海中路1045号淮海国际广场15楼', '张三', '***********', '北京市朝阳区建国路99号国贸大厦A座1001室', '合同文件', 0.3, 500.00, 'EXPRESS', 28.00, 1, 'PAID', NOW() - INTERVAL 3 HOUR, NOW()),

-- 已分配揽件员
('LO202412300005', '赵六', '***********', '深圳市福田区华强北路1002号赛格广场45楼', '王五', '***********', '广州市天河区珠江新城花城大道68号环球都会广场T1栋3001室', '电子设备', 3.2, 8000.00, 'STANDARD', 22.00, 1, 'PICKUP_ASSIGNED', NOW() - INTERVAL 2 HOUR, NOW()),

-- 已揽收，运输中
('LO202412300006', '钱七', '13900001005', '北京市海淀区中关村大街27号中关村大厦B座808室', '李四', '***********', '上海市浦东新区陆家嘴环路1000号恒生银行大厦20楼', '样品包装', 1.8, 1200.00, 'STANDARD', 16.00, 1, 'PICKUP', NOW() - INTERVAL 4 HOUR, NOW()),

-- 分拣中
('LO202412300007', '张三', '***********', '北京市朝阳区建国路99号国贸大厦A座1001室', '赵六', '***********', '深圳市南山区科技园南区深圳湾科技生态园10栋A座2001室', '技术资料', 0.8, 300.00, 'EXPRESS', 35.00, 1, 'SORTING', NOW() - INTERVAL 6 HOUR, NOW()),

-- 发车中
('LO202412300008', '王五', '***********', '广州市越秀区中山五路219号中旅商业城12楼', '钱七', '13900001005', '北京市海淀区中关村大街27号中关村大厦B座808室', '产品手册', 1.5, 800.00, 'STANDARD', 20.00, 1, 'DISPATCHING', NOW() - INTERVAL 8 HOUR, NOW()),

-- 运输中
('LO202412300009', '李四', '***********', '上海市浦东新区陆家嘴环路1000号恒生银行大厦20楼', '张三', '***********', '北京市朝阳区建国路99号国贸大厦A座1001室', '重要合同', 0.4, 2000.00, 'EXPRESS', 30.00, 1, 'TRANSIT', NOW() - INTERVAL 10 HOUR, NOW()),

-- 到达目的地
('LO202412300010', '赵六', '***********', '深圳市南山区科技园南区深圳湾科技生态园10栋A座2001室', '王五', '***********', '广州市天河区珠江新城花城大道68号环球都会广场T1栋3001室', '设计图纸', 0.6, 1500.00, 'STANDARD', 15.00, 1, 'ARRIVED', NOW() - INTERVAL 12 HOUR, NOW()),

-- 派送中
('LO202412300011', '钱七', '13900001005', '北京市海淀区中关村大街27号中关村大厦B座808室', '李四', '***********', '上海市徐汇区淮海中路1045号淮海国际广场15楼', '测试设备', 2.8, 6000.00, 'EXPRESS', 32.00, 1, 'DELIVERING', NOW() - INTERVAL 14 HOUR, NOW()),

-- 已签收
('LO202412300012', '张三', '***********', '北京市朝阳区建国路99号国贸大厦A座1001室', '赵六', '***********', '深圳市福田区华强北路1002号赛格广场45楼', '商务礼品', 1.0, 500.00, 'STANDARD', 18.00, 1, 'SIGNED', NOW() - INTERVAL 1 DAY, NOW()),

-- 异常订单
('LO202412300013', '王五', '***********', '广州市天河区珠江新城花城大道68号环球都会广场T1栋3001室', '钱七', '13900001005', '北京市海淀区中关村大街27号中关村大厦B座808室', '易碎物品', 2.0, 3000.00, 'EXPRESS', 28.00, 1, 'EXCEPTION', NOW() - INTERVAL 16 HOUR, NOW());

-- =============================================
-- 8. 插入配送任务数据
-- =============================================
INSERT INTO `delivery_tasks` (`order_number`, `order_id`, `courier_id`, `task_type`, `pickup_address`, `delivery_address`, `contact_name`, `contact_phone`, `estimated_time`, `actual_start_time`, `actual_end_time`, `task_status`, `priority`, `remarks`, `create_time`, `update_time`) VALUES
-- 揽件任务
((SELECT order_number FROM orders WHERE order_number = 'LO202412300002'), (SELECT id FROM orders WHERE order_number = 'LO202412300002'), (SELECT id FROM users WHERE username = 'courier001'), 'PICKUP', '广州市天河区珠江新城花城大道68号环球都会广场T1栋3001室', NULL, '王五', '***********', NOW() + INTERVAL 2 HOUR, NULL, NULL, 'ASSIGNED', 2, '客户要求上午取件', NOW() - INTERVAL 1 HOUR, NOW()),

-- 派送任务
((SELECT order_number FROM orders WHERE order_number = 'LO202412300011'), (SELECT id FROM orders WHERE order_number = 'LO202412300011'), (SELECT id FROM users WHERE username = 'courier002'), 'DELIVERY', NULL, '上海市徐汇区淮海中路1045号淮海国际广场15楼', '李四', '***********', NOW() + INTERVAL 1 HOUR, NOW() - INTERVAL 30 MINUTE, NULL, 'IN_PROGRESS', 1, '客户要求当日送达', NOW() - INTERVAL 2 HOUR, NOW());

-- =============================================
-- 9. 插入物流轨迹数据
-- =============================================
INSERT INTO `logistics_tracking` (`order_number`, `order_id`, `tracking_status`, `description`, `location`, `longitude`, `latitude`, `operator_name`, `operator_type`, `tracking_time`, `create_time`, `update_time`) VALUES
-- LO202412300001 的轨迹
('LO202412300001', (SELECT id FROM orders WHERE order_number = 'LO202412300001'), 'ORDER_CREATED', '订单已创建', '北京市朝阳区建国路99号', 116.4074, 39.9042, '系统', 'SYSTEM', NOW() - INTERVAL 2 HOUR, NOW() - INTERVAL 2 HOUR, NOW()),
('LO202412300001', (SELECT id FROM orders WHERE order_number = 'LO202412300001'), 'PAYMENT_SUCCESS', '支付成功', '北京市朝阳区建国路99号', 116.4074, 39.9042, '系统', 'SYSTEM', NOW() - INTERVAL 2 HOUR + INTERVAL 5 MINUTE, NOW() - INTERVAL 2 HOUR + INTERVAL 5 MINUTE, NOW()),

-- LO202412300006 的轨迹
('LO202412300006', (SELECT id FROM orders WHERE order_number = 'LO202412300006'), 'ORDER_CREATED', '订单已创建', '北京市海淀区中关村大街27号', 116.3017, 39.9656, '系统', 'SYSTEM', NOW() - INTERVAL 4 HOUR, NOW() - INTERVAL 4 HOUR, NOW()),
('LO202412300006', (SELECT id FROM orders WHERE order_number = 'LO202412300006'), 'PAYMENT_SUCCESS', '支付成功', '北京市海淀区中关村大街27号', 116.3017, 39.9656, '系统', 'SYSTEM', NOW() - INTERVAL 4 HOUR + INTERVAL 5 MINUTE, NOW() - INTERVAL 4 HOUR + INTERVAL 5 MINUTE, NOW()),
('LO202412300006', (SELECT id FROM orders WHERE order_number = 'LO202412300006'), 'PICKUP_ASSIGNED', '已分配揽件员', '北京市海淀区中关村大街27号', 116.3017, 39.9656, '张操作员', 'OPERATOR', NOW() - INTERVAL 3 HOUR + INTERVAL 30 MINUTE, NOW() - INTERVAL 3 HOUR + INTERVAL 30 MINUTE, NOW()),
('LO202412300006', (SELECT id FROM orders WHERE order_number = 'LO202412300006'), 'PICKUP', '包裹已揽收', '北京市海淀区中关村大街27号', 116.3017, 39.9656, '王配送员', 'COURIER', NOW() - INTERVAL 3 HOUR, NOW() - INTERVAL 3 HOUR, NOW());

-- =============================================
-- 10. 插入系统配置数据
-- =============================================
INSERT INTO `system_config` (`config_key`, `config_value`, `config_type`, `description`, `create_time`, `update_time`) VALUES
('email.enabled', 'true', 'BOOLEAN', '是否启用邮件通知', NOW(), NOW()),
('email.from.address', '<EMAIL>', 'STRING', '邮件发送地址', NOW(), NOW()),
('email.from.name', '智慧物流系统', 'STRING', '邮件发送者名称', NOW(), NOW()),
('sms.enabled', 'true', 'BOOLEAN', '是否启用短信通知', NOW(), NOW()),
('map.api.key', 'YOUR_AMAP_KEY', 'STRING', '高德地图API密钥', NOW(), NOW()),
('delivery.timeout.hours', '24', 'NUMBER', '配送超时时间（小时）', NOW(), NOW()),
('order.auto.cancel.hours', '72', 'NUMBER', '订单自动取消时间（小时）', NOW(), NOW()),
('courier.max.tasks', '10', 'NUMBER', '配送员最大任务数', NOW(), NOW());

-- 查询插入结果
SELECT '=== 网点数据 ===' as info;
SELECT station_code, station_name, station_type, city, status FROM logistics_stations;

SELECT '=== 用户数据 ===' as info;
SELECT username, real_name, role, phone, email FROM users WHERE role != 'ADMIN';

SELECT '=== 配送员数据 ===' as info;
SELECT c.courier_name, c.phone, c.vehicle_type, c.service_area, c.status FROM couriers c;

SELECT '=== 地址数据 ===' as info;
SELECT a.contact_name, a.city, a.district, a.detailed_address, a.address_type FROM addresses a LIMIT 10;

SELECT '=== 订单数据 ===' as info;
SELECT order_number, sender_name, receiver_name, order_status, total_fee FROM orders;

SELECT '=== 配送任务数据 ===' as info;
SELECT dt.order_number, dt.task_type, dt.task_status, c.courier_name FROM delivery_tasks dt LEFT JOIN couriers c ON dt.courier_id = c.user_id;

SELECT '=== 物流轨迹数据 ===' as info;
SELECT order_number, tracking_status, description, operator_name FROM logistics_tracking ORDER BY tracking_time DESC LIMIT 10;
