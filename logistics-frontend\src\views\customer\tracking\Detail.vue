<template>
  <div class="tracking-detail" v-loading="loading">
    <div class="page-header">
      <el-button @click="$router.back()" :icon="ArrowLeft">返回</el-button>
      <h2>物流详情</h2>
    </div>

    <div v-if="trackingInfo" class="tracking-content">
      <!-- 订单基本信息 -->
      <el-card class="order-info-card">
        <div class="order-summary">
          <div class="order-number">
            <h3>{{ trackingInfo.orderNumber }}</h3>
            <el-tag :type="getStatusTagType(trackingInfo.currentStatus)">
              {{ getStatusText(trackingInfo.currentStatus) }}
            </el-tag>
          </div>
          <div v-if="trackingInfo.estimatedDeliveryTime" class="estimated-time">
            <el-icon><Clock /></el-icon>
            <span>预计送达：{{ formatTime(trackingInfo.estimatedDeliveryTime) }}</span>
          </div>
        </div>
      </el-card>

      <!-- 地址信息 -->
      <el-card class="address-card">
        <div class="address-container">
          <div class="address-section">
            <h4>寄件信息</h4>
            <div class="address-info">
              <p>
                <strong>{{ trackingInfo.orderInfo.senderName }}</strong>
              </p>
              <p>{{ trackingInfo.orderInfo.senderAddress }}</p>
            </div>
          </div>

          <div class="address-arrow">
            <el-icon size="24"><Right /></el-icon>
          </div>

          <div class="address-section">
            <h4>收件信息</h4>
            <div class="address-info">
              <p>
                <strong>{{ trackingInfo.orderInfo.receiverName }}</strong>
              </p>
              <p>{{ trackingInfo.orderInfo.receiverAddress }}</p>
            </div>
          </div>
        </div>

        <div class="item-info">
          <el-icon><Box /></el-icon>
          <span>{{ trackingInfo.orderInfo.itemName || '货物' }}</span>
          <span class="service-type">{{ trackingInfo.orderInfo.serviceType }}</span>
        </div>
      </el-card>

      <!-- 物流轨迹 -->
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><Van /></el-icon>
            <span>物流轨迹</span>
            <el-button type="text" @click="refreshTracking" :loading="refreshing"> 刷新 </el-button>
          </div>
        </template>

        <el-timeline class="tracking-timeline">
          <el-timeline-item
            v-for="(node, index) in trackingInfo.trackingNodes"
            :key="node.id"
            :timestamp="formatTime(node.createTime)"
            :type="getTimelineType(node.status, index === 0)"
            :size="index === 0 ? 'large' : 'normal'"
          >
            <div class="timeline-content">
              <div class="timeline-header">
                <span class="timeline-title">{{ node.description }}</span>
                <el-tag v-if="index === 0" type="success" size="small">最新</el-tag>
              </div>
              <div class="timeline-details">
                <span v-if="node.location" class="timeline-location">
                  <el-icon><LocationInformation /></el-icon>
                  {{ node.location }}
                </span>
                <span v-if="node.operatorName" class="timeline-operator">
                  <el-icon><User /></el-icon>
                  {{ node.operatorName }}
                </span>
              </div>
              <div v-if="node.remarks" class="timeline-remarks">
                {{ node.remarks }}
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>

        <div v-if="trackingInfo.trackingNodes.length === 0" class="empty-tracking">
          <el-empty description="暂无物流信息" />
        </div>
      </el-card>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button @click="subscribeNotification">
          <el-icon><Bell /></el-icon>
          订阅通知
        </el-button>
        <el-button @click="viewOrderDetail">
          <el-icon><Document /></el-icon>
          查看订单
        </el-button>
        <el-button @click="contactService">
          <el-icon><Service /></el-icon>
          联系客服
        </el-button>
      </div>
    </div>

    <!-- 查询表单（如果没有数据） -->
    <el-card v-if="!trackingInfo && !loading" class="search-card">
      <template #header>
        <div class="card-header">
          <el-icon><Search /></el-icon>
          <span>查询物流信息</span>
        </div>
      </template>

      <el-form :model="queryForm" @submit.prevent="handleQuery">
        <el-form-item label="订单号" required>
          <el-input v-model="queryForm.orderNumber" placeholder="请输入订单号" size="large" />
        </el-form-item>
        <el-form-item label="手机号">
          <el-input v-model="queryForm.phone" placeholder="请输入手机号（选填）" size="large" />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            @click="handleQuery"
            :loading="loading"
            style="width: 100%"
          >
            查询
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 订阅通知对话框 -->
    <el-dialog v-model="showSubscribeDialog" title="订阅物流通知" width="400px">
      <el-form :model="subscribeForm" label-width="80px">
        <el-form-item label="手机号" required>
          <el-input v-model="subscribeForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input v-model="subscribeForm.email" placeholder="请输入邮箱（选填）" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showSubscribeDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmSubscribe" :loading="subscribing">
          确认订阅
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowLeft,
  Clock,
  Right,
  Box,
  Van,
  LocationInformation,
  User,
  Bell,
  Document,
  Service,
  Search,
} from '@element-plus/icons-vue'
import { trackingApi } from '@/api/tracking'
import type { TrackingResponse, TrackingStatus } from '@/types/tracking'
import dayjs from 'dayjs'

const route = useRoute()
const router = useRouter()

// 状态
const loading = ref(false)
const refreshing = ref(false)
const subscribing = ref(false)
const showSubscribeDialog = ref(false)
const trackingInfo = ref<TrackingResponse | null>(null)

// 查询表单
const queryForm = reactive({
  orderNumber: '',
  phone: '',
})

// 订阅表单
const subscribeForm = reactive({
  phone: '',
  email: '',
})

// 查询物流信息
const queryTracking = async (orderNumber: string, phone?: string) => {
  loading.value = true
  try {
    const response = await trackingApi.getTrackingInfo({
      orderNumber,
      phone,
    })

    console.log('轨迹查询API响应:', response)

    // HTTP拦截器已经处理了响应格式，直接检查code
    if (response.code === 200) {
      trackingInfo.value = response.data
    } else {
      ElMessage.error(response.message || '查询失败')
    }
  } catch (error: any) {
    console.error('查询物流信息失败:', error)
    // 如果是404错误或包含"未找到"，说明轨迹信息不存在
    if (error.message && (error.message.includes('未找到') || error.message.includes('404'))) {
      ElMessage.warning('该订单暂无物流轨迹信息，可能订单刚创建或尚未开始配送')
    } else {
      ElMessage.error(error.message || '查询失败，请稍后重试')
    }
    trackingInfo.value = null
  } finally {
    loading.value = false
  }
}

// 处理查询
const handleQuery = () => {
  if (!queryForm.orderNumber.trim()) {
    ElMessage.warning('请输入订单号')
    return
  }

  queryTracking(queryForm.orderNumber, queryForm.phone)
}

// 刷新物流信息
const refreshTracking = () => {
  if (trackingInfo.value) {
    refreshing.value = true
    queryTracking(trackingInfo.value.orderNumber).finally(() => {
      refreshing.value = false
    })
  }
}

// 订阅通知
const subscribeNotification = () => {
  if (trackingInfo.value) {
    subscribeForm.orderNumber = trackingInfo.value.orderNumber
    showSubscribeDialog.value = true
  }
}

// 确认订阅
const confirmSubscribe = async () => {
  if (!subscribeForm.phone.trim()) {
    ElMessage.warning('请输入手机号')
    return
  }

  if (!trackingInfo.value) return

  subscribing.value = true
  try {
    const response = await trackingApi.subscribeTracking(
      trackingInfo.value.orderNumber,
      subscribeForm.phone,
      subscribeForm.email || undefined,
    )

    if (response.data.code === 200) {
      ElMessage.success('订阅成功')
      showSubscribeDialog.value = false
    } else {
      ElMessage.error(response.data.message || '订阅失败')
    }
  } catch (error) {
    console.error('订阅失败:', error)
    ElMessage.error('订阅失败，请稍后重试')
  } finally {
    subscribing.value = false
  }
}

// 查看订单详情
const viewOrderDetail = () => {
  if (trackingInfo.value) {
    // 通过订单号查找订单ID，这里简化处理
    router.push(`/customer/order/list?search=${trackingInfo.value.orderNumber}`)
  }
}

// 联系客服
const contactService = () => {
  ElMessage.info('客服功能开发中...')
}

// 工具函数
const getStatusTagType = (status: TrackingStatus) => {
  const typeMap = {
    CREATED: '',
    PICKED_UP: 'info',
    IN_TRANSIT: 'warning',
    ARRIVED: 'warning',
    OUT_FOR_DELIVERY: 'warning',
    DELIVERED: 'success',
    EXCEPTION: 'danger',
    RETURNED: 'danger',
  }
  return typeMap[status] || ''
}

const getStatusText = (status: TrackingStatus) => {
  const textMap = {
    CREATED: '订单创建',
    PICKED_UP: '已揽收',
    IN_TRANSIT: '运输中',
    ARRIVED: '到达网点',
    OUT_FOR_DELIVERY: '派送中',
    DELIVERED: '已送达',
    EXCEPTION: '异常',
    RETURNED: '已退回',
  }
  return textMap[status] || status
}

const getTimelineType = (status: TrackingStatus, isLatest: boolean) => {
  if (isLatest) return 'primary'

  const typeMap = {
    CREATED: 'info',
    PICKED_UP: 'success',
    IN_TRANSIT: 'warning',
    ARRIVED: 'info',
    OUT_FOR_DELIVERY: 'warning',
    DELIVERED: 'success',
    EXCEPTION: 'danger',
    RETURNED: 'danger',
  }
  return typeMap[status] || 'info'
}

const formatTime = (time: string) => {
  return dayjs(time).format('MM-DD HH:mm')
}

onMounted(() => {
  const orderNumber = route.query.orderNumber as string
  if (orderNumber) {
    queryForm.orderNumber = orderNumber
    queryTracking(orderNumber)
  }
})
</script>

<style scoped>
.tracking-detail {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.tracking-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.order-info-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.order-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-number {
  display: flex;
  align-items: center;
  gap: 15px;
}

.order-number h3 {
  margin: 0;
  font-size: 20px;
}

.estimated-time {
  display: flex;
  align-items: center;
  gap: 5px;
  opacity: 0.9;
}

.address-container {
  display: flex;
  align-items: center;
  gap: 30px;
  margin-bottom: 20px;
}

.address-section {
  flex: 1;
}

.address-section h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.address-info p {
  margin: 5px 0;
  color: #666;
}

.address-arrow {
  color: #409eff;
}

.item-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
  color: #666;
}

.service-type {
  background: #f5f7fa;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.tracking-timeline {
  margin-top: 20px;
}

.timeline-content {
  padding-left: 10px;
}

.timeline-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}
</style>
