version: '3.8'
services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: logistics-mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: logistics
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql/logistics_mysql.sql:/docker-entrypoint-initdb.d/logistics_mysql.sql
    command: --default-authentication-plugin=mysql_native_password

  # MongoDB数据库  
  mongodb:
    image: mongo:4.4
    container_name: logistics-mongodb
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: admin123
    volumes:
      - mongodb_data:/data/db

  # Redis缓存
  redis:
    image: redis:6.2-alpine
    container_name: logistics-redis
    ports:
      - "6379:6379"
    command: redis-server --requirepass redis123
    volumes:
      - redis_data:/data

  # RabbitMQ消息队列
  rabbitmq:
    image: rabbitmq:3.8-management
    container_name: logistics-rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin123
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq

  # Nacos注册中心
  nacos:
    image: nacos/nacos-server:v2.2.0
    container_name: logistics-nacos
    ports:
      - "8848:8848"
      - "9848:9848"
    environment:
      MODE: standalone
      PREFER_HOST_MODE: hostname
    volumes:
      - nacos_data:/home/<USER>/data

volumes:
  mysql_data:
  mongodb_data:
  redis_data:
  rabbitmq_data:
  nacos_data: 