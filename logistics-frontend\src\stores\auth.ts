import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authApi } from '@/api/auth'
import type { UserInfo, LoginRequest, RegisterRequest } from '@/types/user'
import { ElMessage } from 'element-plus'

interface ApiError {
  response?: {
    data?: {
      message?: string
    }
  }
  message?: string
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string>(localStorage.getItem('token') || '')
  const userInfo = ref<UserInfo | null>(null)
  const loading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value)
  const userRole = computed(() => userInfo.value?.userType || '')
  const permissions = computed(() => userInfo.value?.permissions || [])

  // 登录
  const login = async (loginData: LoginRequest): Promise<boolean> => {
    try {
      loading.value = true
      const response = await authApi.login(loginData)

      console.log('登录响应:', response)

      // 修复：正确处理API响应结构
      if (response.code === 200 && response.data) {
        token.value = response.data.token
        userInfo.value = response.data.userInfo

        // 保存到本地存储
        localStorage.setItem('token', response.data.token)
        localStorage.setItem('userInfo', JSON.stringify(response.data.userInfo))

        ElMessage.success('登录成功')

        // 动态导入 router 避免循环依赖
        const { default: router } = await import('@/router')
        const redirectPath = getRedirectPath(response.data.userInfo.userType)
        await router.push(redirectPath)

        return true
      } else {
        ElMessage.error(response.message || '登录失败')
        return false
      }
    } catch (error: unknown) {
      console.error('登录失败:', error)
      const apiError = error as ApiError
      const errorMessage = apiError?.response?.data?.message || apiError?.message || '登录失败'
      ElMessage.error(errorMessage)
      return false
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (registerData: RegisterRequest): Promise<boolean> => {
    try {
      loading.value = true
      const response = await authApi.register(registerData)

      if (response.code === 200) {
        ElMessage.success('注册成功，请登录')
        const { default: router } = await import('@/router')
        await router.push('/login')
        return true
      } else {
        ElMessage.error(response.message || '注册失败')
        return false
      }
    } catch (error: unknown) {
      console.error('注册失败:', error)
      const apiError = error as ApiError
      const errorMessage = apiError?.response?.data?.message || apiError?.message || '注册失败'
      ElMessage.error(errorMessage)
      return false
    } finally {
      loading.value = false
    }
  }

  // 获取用户信息
  const fetchUserInfo = async (): Promise<void> => {
    try {
      if (!token.value) {
        throw new Error('未找到token')
      }

      const response = await authApi.getUserInfo()
      if (response.code === 200 && response.data) {
        userInfo.value = response.data
        localStorage.setItem('userInfo', JSON.stringify(response.data))
      } else {
        throw new Error(response.message || '获取用户信息失败')
      }
    } catch (error: unknown) {
      console.error('获取用户信息失败:', error)
      // 清除无效的token和用户信息
      logout()
    }
  }

  // 登出
  const logout = async (): Promise<void> => {
    token.value = ''
    userInfo.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')

    const { default: router } = await import('@/router')
    await router.push('/login')
    ElMessage.success('已退出登录')
  }

  // 初始化用户信息
  const initUserInfo = (): void => {
    const savedToken = localStorage.getItem('token')
    const savedUserInfo = localStorage.getItem('userInfo')

    if (savedToken && savedUserInfo) {
      try {
        token.value = savedToken
        userInfo.value = JSON.parse(savedUserInfo) as UserInfo

        // 不自动验证token，避免登录后立即过期的问题
        // 只在实际API调用时才会验证token有效性
      } catch (error) {
        console.error('解析用户信息失败:', error)
        logout()
      }
    }
  }

  // 根据用户角色获取重定向路径
  const getRedirectPath = (userType: string): string => {
    switch (userType) {
      case 'ADMIN':
        return '/admin/dashboard'
      case 'OPERATOR':
        return '/operator/dashboard'
      case 'COURIER':
        return '/courier/dashboard'
      case 'CUSTOMER':
      default:
        return '/customer/dashboard'
    }
  }

  // 检查权限
  const hasPermission = (permission: string): boolean => {
    return permissions.value.includes(permission)
  }

  // 检查角色
  const hasRole = (role: string): boolean => {
    return userRole.value === role
  }

  return {
    // 状态
    token,
    userInfo,
    loading,
    // 计算属性
    isLoggedIn,
    userRole,
    permissions,
    // 方法
    login,
    register,
    logout,
    fetchUserInfo,
    initUserInfo,
    hasPermission,
    hasRole,
  }
})
