package org.example.logisticsnotification.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.example.logisticsnotification.constants.NotificationStatus;
import org.example.logisticsnotification.constants.NotificationType;
import org.example.logisticsnotification.entity.Notification;
import org.example.logisticsnotification.mapper.NotificationMapper;
import org.example.logisticsnotification.service.NotificationService;
import org.example.logisticsnotification.service.NotificationTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 通知服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Slf4j
@Service
public class NotificationServiceImpl extends ServiceImpl<NotificationMapper, Notification> 
        implements NotificationService {

    @Autowired
    private NotificationTemplateService notificationTemplateService;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Notification createNotification(Notification notification) {
        // 设置默认值
        if (notification.getSendStatus() == null) {
            notification.setSendStatus(NotificationStatus.PENDING.getCode());
        }
        if (notification.getRetryCount() == null) {
            notification.setRetryCount(0);
        }
        if (notification.getMaxRetryCount() == null) {
            notification.setMaxRetryCount(3);
        }
        if (notification.getPriority() == null) {
            notification.setPriority(2); // 默认中等优先级
        }
        if (notification.getIsRead() == null) {
            notification.setIsRead(0);
        }

        return save(notification) ? notification : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchCreateNotifications(List<Notification> notifications) {
        if (CollectionUtils.isEmpty(notifications)) {
            return false;
        }

        // 设置默认值
        for (Notification notification : notifications) {
            if (notification.getSendStatus() == null) {
                notification.setSendStatus(NotificationStatus.PENDING.getCode());
            }
            if (notification.getRetryCount() == null) {
                notification.setRetryCount(0);
            }
            if (notification.getMaxRetryCount() == null) {
                notification.setMaxRetryCount(3);
            }
            if (notification.getPriority() == null) {
                notification.setPriority(2);
            }
            if (notification.getIsRead() == null) {
                notification.setIsRead(0);
            }
        }

        return saveBatch(notifications);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean sendNotification(Long notificationId) {
        Notification notification = getById(notificationId);
        if (notification == null) {
            log.warn("通知不存在: {}", notificationId);
            return false;
        }

        if (!NotificationStatus.PENDING.getCode().equals(notification.getSendStatus()) &&
            !NotificationStatus.FAILED.getCode().equals(notification.getSendStatus())) {
            log.warn("通知状态不允许发送: {}", notification.getSendStatus());
            return false;
        }

        // 更新状态为发送中
        notification.setSendStatus(NotificationStatus.SENDING.getCode());
        updateById(notification);

        try {
            // 根据通知类型进行实际发送
            boolean sendResult = doSendNotification(notification);
            
            if (sendResult) {
                // 发送成功
                notification.setSendStatus(NotificationStatus.SUCCESS.getCode());
                notification.setSendTime(LocalDateTime.now());
                notification.setFailureReason(null);
            } else {
                // 发送失败
                notification.setSendStatus(NotificationStatus.FAILED.getCode());
                notification.setRetryCount(notification.getRetryCount() + 1);
                
                // 设置下次重试时间
                if (notification.getRetryCount() < notification.getMaxRetryCount()) {
                    notification.setNextRetryTime(calculateNextRetryTime(notification.getRetryCount()));
                }
            }
            
            updateById(notification);
            return sendResult;
        } catch (Exception e) {
            log.error("发送通知异常: {}", notificationId, e);
            
            // 更新为失败状态
            notification.setSendStatus(NotificationStatus.FAILED.getCode());
            notification.setFailureReason(e.getMessage());
            notification.setRetryCount(notification.getRetryCount() + 1);
            
            if (notification.getRetryCount() < notification.getMaxRetryCount()) {
                notification.setNextRetryTime(calculateNextRetryTime(notification.getRetryCount()));
            }
            
            updateById(notification);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchSendNotifications(List<Long> notificationIds) {
        if (CollectionUtils.isEmpty(notificationIds)) {
            return 0;
        }

        int successCount = 0;
        for (Long notificationId : notificationIds) {
            if (sendNotification(notificationId)) {
                successCount++;
            }
        }
        return successCount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Notification createAndSendNotification(Notification notification) {
        Notification created = createNotification(notification);
        if (created != null) {
            sendNotification(created.getId());
        }
        return created;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean sendBusinessNotification(String businessType, Long businessId, String templateCode,
                                          String recipient, Map<String, Object> params) {
        try {
            // 生成通知内容
            Map<String, String> content = notificationTemplateService.generateNotificationContent(templateCode, params);
            if (content == null) {
                log.warn("模板不存在或生成内容失败: {}", templateCode);
                return false;
            }

            // 创建通知
            Notification notification = new Notification();
            notification.setBusinessType(businessType);
            notification.setBusinessId(businessId);
            notification.setTemplateCode(templateCode);
            notification.setRecipient(recipient);
            notification.setTitle(content.get("title"));
            notification.setContent(content.get("content"));
            notification.setNotificationType(NotificationType.SMS.getCode()); // 默认短信
            
            // 转换参数为JSON
            if (params != null && !params.isEmpty()) {
                notification.setTemplateParams(objectMapper.writeValueAsString(params));
            }

            return createAndSendNotification(notification) != null;
        } catch (Exception e) {
            log.error("发送业务通知异常", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean sendSystemNotification(Long userId, String templateCode, Map<String, Object> params) {
        return sendBusinessNotification("SYSTEM", null, templateCode, userId.toString(), params);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateNotification(Notification notification) {
        return updateById(notification);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateNotificationStatus(Long notificationId, Integer status, String failureReason) {
        List<Long> ids = new ArrayList<>();
        ids.add(notificationId);
        return baseMapper.batchUpdateStatus(ids, status, failureReason) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateNotificationStatus(List<Long> notificationIds, Integer status, String failureReason) {
        if (CollectionUtils.isEmpty(notificationIds)) {
            return 0;
        }
        return baseMapper.batchUpdateStatus(notificationIds, status, failureReason);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRetryInfo(Long notificationId, Integer retryCount, LocalDateTime nextRetryTime, String failureReason) {
        return baseMapper.updateRetryInfo(notificationId, retryCount, nextRetryTime, failureReason) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markAsRead(List<Long> notificationIds, Long userId) {
        if (CollectionUtils.isEmpty(notificationIds)) {
            return false;
        }
        return baseMapper.markAsRead(notificationIds, userId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markAllAsRead(Long userId) {
        LambdaQueryWrapper<Notification> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Notification::getUserId, userId)
               .eq(Notification::getIsRead, 0);
        
        List<Notification> unreadNotifications = list(wrapper);
        if (CollectionUtils.isEmpty(unreadNotifications)) {
            return true;
        }
        
        List<Long> notificationIds = unreadNotifications.stream()
                .map(Notification::getId)
                .collect(Collectors.toList());
        
        return markAsRead(notificationIds, userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteNotification(Long notificationId) {
        return removeById(notificationId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteNotifications(List<Long> notificationIds) {
        if (CollectionUtils.isEmpty(notificationIds)) {
            return false;
        }
        return removeByIds(notificationIds);
    }

    @Override
    public Notification getNotificationById(Long notificationId) {
        return getById(notificationId);
    }

    @Override
    public List<Notification> getNotificationsByUserId(Long userId, Integer isRead) {
        return baseMapper.findByUserId(userId, isRead);
    }

    @Override
    public List<Notification> getNotificationsByBusiness(String businessType, Long businessId) {
        return baseMapper.findByBusiness(businessType, businessId);
    }

    @Override
    public List<Notification> getNotificationsByType(String notificationType, Integer sendStatus) {
        return baseMapper.findByNotificationType(notificationType, sendStatus);
    }

    @Override
    public List<Notification> getPendingNotifications(String notificationType, Integer limit) {
        return baseMapper.findPendingNotifications(notificationType, limit);
    }

    @Override
    public List<Notification> getRetryNotifications(Integer limit) {
        return baseMapper.findRetryNotifications(LocalDateTime.now(), limit);
    }

    @Override
    public List<Notification> getFailedNotifications(LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.findFailedNotifications(startTime, endTime);
    }

    @Override
    public IPage<Notification> getNotificationsPage(Page<Notification> page, Long userId, String notificationType,
                                                  Integer sendStatus, String businessType,
                                                  LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.findNotificationsPage(page, userId, notificationType, sendStatus, businessType, startTime, endTime);
    }

    @Override
    public Map<String, Object> getNotificationStatistics(Long userId, LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.getNotificationStatistics(userId, startTime, endTime);
    }

    @Override
    public List<Map<String, Object>> countNotificationsByStatus(String notificationType) {
        return baseMapper.countByStatus(notificationType);
    }

    @Override
    public List<Map<String, Object>> countNotificationsByType(LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.countByType(startTime, endTime);
    }

    @Override
    public Long countUnreadNotifications(Long userId) {
        return baseMapper.countUnreadByUserId(userId);
    }

    @Override
    public void processNotificationSending() {
        List<Notification> pendingNotifications = getPendingNotifications(null, 100);
        for (Notification notification : pendingNotifications) {
            try {
                sendNotification(notification.getId());
            } catch (Exception e) {
                log.error("处理通知发送异常: {}", notification.getId(), e);
            }
        }
    }

    @Override
    public void processNotificationRetry() {
        List<Notification> retryNotifications = getRetryNotifications(50);
        for (Notification notification : retryNotifications) {
            try {
                sendNotification(notification.getId());
            } catch (Exception e) {
                log.error("处理通知重试异常: {}", notification.getId(), e);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanExpiredNotifications(LocalDateTime expireTime) {
        return baseMapper.deleteExpiredNotifications(expireTime);
    }

    @Override
    public boolean checkNotificationStatus(Long notificationId) {
        Notification notification = getById(notificationId);
        return notification != null && NotificationStatus.SUCCESS.getCode().equals(notification.getSendStatus());
    }

    @Override
    public Map<String, Object> getSendingStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        return getNotificationStatistics(null, startTime, endTime);
    }

    /**
     * 实际发送通知的方法
     */
    private boolean doSendNotification(Notification notification) {
        String notificationType = notification.getNotificationType();
        
        if (NotificationType.SMS.getCode().equals(notificationType)) {
            return sendSMS(notification);
        } else if (NotificationType.EMAIL.getCode().equals(notificationType)) {
            return sendEmail(notification);
        } else if (NotificationType.PUSH.getCode().equals(notificationType)) {
            return sendPush(notification);
        } else if (NotificationType.WECHAT.getCode().equals(notificationType)) {
            return sendWechat(notification);
        }
        
        log.warn("不支持的通知类型: {}", notificationType);
        return false;
    }

    /**
     * 发送短信
     */
    private boolean sendSMS(Notification notification) {
        log.info("发送短信通知: {} -> {}", notification.getRecipient(), notification.getContent());
        // TODO: 集成短信服务商SDK
        return true; // 模拟发送成功
    }

    /**
     * 发送邮件
     */
    private boolean sendEmail(Notification notification) {
        log.info("发送邮件通知: {} -> {}", notification.getRecipient(), notification.getTitle());
        // TODO: 集成邮件服务
        return true; // 模拟发送成功
    }

    /**
     * 发送推送
     */
    private boolean sendPush(Notification notification) {
        log.info("发送推送通知: {} -> {}", notification.getRecipient(), notification.getTitle());
        // TODO: 集成推送服务
        return true; // 模拟发送成功
    }

    /**
     * 发送微信通知
     */
    private boolean sendWechat(Notification notification) {
        log.info("发送微信通知: {} -> {}", notification.getRecipient(), notification.getTitle());
        // TODO: 集成微信服务
        return true; // 模拟发送成功
    }

    /**
     * 计算下次重试时间
     */
    private LocalDateTime calculateNextRetryTime(Integer retryCount) {
        // 使用指数退避算法：2^retryCount 分钟
        long delayMinutes = (long) Math.pow(2, retryCount);
        return LocalDateTime.now().plusMinutes(delayMinutes);
    }
} 