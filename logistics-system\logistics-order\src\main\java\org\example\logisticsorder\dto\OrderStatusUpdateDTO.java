package org.example.logisticsorder.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 订单状态更新请求DTO
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
public class OrderStatusUpdateDTO {

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    /**
     * 新状态
     */
    @NotBlank(message = "订单状态不能为空")
    private String newStatus;

    /**
     * 操作员ID
     */
    private Long operatorId;

    /**
     * 操作员姓名
     */
    private String operatorName;

    /**
     * 操作员类型
     */
    private String operatorType;

    /**
     * 变更原因
     */
    private String changeReason;

    /**
     * 操作地点
     */
    private String location;

    /**
     * 备注
     */
    private String remarks;
}