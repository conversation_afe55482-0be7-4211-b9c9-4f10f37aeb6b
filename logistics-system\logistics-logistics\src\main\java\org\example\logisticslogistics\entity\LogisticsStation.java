package org.example.logisticslogistics.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.logistics.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


import java.math.BigDecimal;

/**
 * 物流网点实体
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("logistics_stations")
public class LogisticsStation extends BaseEntity {

    /**
     * 网点ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 网点编码
     */
    @TableField("station_code")
    private String stationCode;

    /**
     * 网点名称
     */
    @TableField("station_name")
    private String stationName;

    /**
     * 网点类型：PICKUP-揽件点，TRANSIT-中转站，DELIVERY-派送点
     */
    @TableField("station_type")
    private String stationType;

    /**
     * 省份
     */
    @TableField("province")
    private String province;

    /**
     * 城市
     */
    @TableField("city")
    private String city;

    /**
     * 区域
     */
    @TableField("district")
    private String district;

    /**
     * 详细地址
     */
    @TableField("detailed_address")
    private String detailedAddress;

    /**
     * 经度
     */
    @TableField("longitude")
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @TableField("latitude")
    private BigDecimal latitude;

    /**
     * 联系人
     */
    @TableField("contact_person")
    private String contactPerson;

    /**
     * 联系电话
     */
    @TableField("contact_phone")
    private String contactPhone;

    /**
     * 营业时间
     */
    @TableField("business_hours")
    private String businessHours;

    /**
     * 服务范围
     */
    @TableField("service_scope")
    private String serviceScope;

    /**
     * 处理能力(件/天)
     */
    @TableField("capacity")
    private Integer capacity;

    /**
     * 状态：0-停用，1-启用
     */
    @TableField("status")
    private Integer status;

    /**
     * 获取完整地址
     */
    public String getFullAddress() {
        StringBuilder sb = new StringBuilder();
        if (province != null) sb.append(province);
        if (city != null) sb.append(city);
        if (district != null) sb.append(district);
        if (detailedAddress != null) sb.append(detailedAddress);
        return sb.toString();
    }

    /**
     * 判断是否为启用状态
     */
    public boolean isActive() {
        return status != null && status == 1;
    }

    /**
     * 判断是否为中转站
     */
    public boolean isTransitStation() {
        return "TRANSIT".equals(stationType);
    }

    /**
     * 判断是否为揽件点
     */
    public boolean isPickupStation() {
        return "PICKUP".equals(stationType);
    }

    /**
     * 判断是否为派送点
     */
    public boolean isDeliveryStation() {
        return "DELIVERY".equals(stationType);
    }
}
