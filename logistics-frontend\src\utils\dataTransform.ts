/**
 * 数据转换工具
 * 处理前后端数据格式转换，确保数据一致性
 */

import type { Order, TrackingInfo, DeliveryTask, User, Station, Address } from '@/types'

// ========== 订单数据转换 ==========

/**
 * 转换后端订单数据为前端格式
 */
export function transformOrderData(backendOrder: any): Order {
  return {
    id: backendOrder.id,
    orderNumber: backendOrder.orderNumber || backendOrder.order_number,
    senderName: backendOrder.senderName || backendOrder.sender_name,
    senderPhone: backendOrder.senderPhone || backendOrder.sender_phone,
    senderAddress: backendOrder.senderAddress || backendOrder.sender_address,
    receiverName: backendOrder.receiverName || backendOrder.receiver_name,
    receiverPhone: backendOrder.receiverPhone || backendOrder.receiver_phone,
    receiverAddress: backendOrder.receiverAddress || backendOrder.receiver_address,
    itemName: backendOrder.itemName || backendOrder.item_name,
    itemWeight: backendOrder.itemWeight || backendOrder.item_weight,
    itemValue: backendOrder.itemValue || backendOrder.item_value,
    serviceType: backendOrder.serviceType || backendOrder.service_type,
    totalFee: backendOrder.totalFee || backendOrder.total_fee,
    paymentStatus: backendOrder.paymentStatus || backendOrder.payment_status,
    orderStatus: backendOrder.orderStatus || backendOrder.order_status,
    createTime: backendOrder.createTime || backendOrder.create_time,
    updateTime: backendOrder.updateTime || backendOrder.update_time,
    remarks: backendOrder.remarks
  }
}

/**
 * 转换前端订单数据为后端格式
 */
export function transformOrderForBackend(frontendOrder: Partial<Order>): any {
  return {
    sender_name: frontendOrder.senderName,
    sender_phone: frontendOrder.senderPhone,
    sender_address: frontendOrder.senderAddress,
    receiver_name: frontendOrder.receiverName,
    receiver_phone: frontendOrder.receiverPhone,
    receiver_address: frontendOrder.receiverAddress,
    item_name: frontendOrder.itemName,
    item_weight: frontendOrder.itemWeight,
    item_value: frontendOrder.itemValue,
    service_type: frontendOrder.serviceType,
    remarks: frontendOrder.remarks
  }
}

// ========== 物流轨迹数据转换 ==========

/**
 * 转换后端轨迹数据为前端格式
 */
export function transformTrackingData(backendTracking: any): TrackingInfo {
  return {
    id: backendTracking.id,
    orderNumber: backendTracking.orderNumber || backendTracking.order_number,
    trackingStatus: backendTracking.trackingStatus || backendTracking.tracking_status,
    description: backendTracking.description,
    location: backendTracking.location,
    longitude: backendTracking.longitude,
    latitude: backendTracking.latitude,
    operatorName: backendTracking.operatorName || backendTracking.operator_name,
    operatorType: backendTracking.operatorType || backendTracking.operator_type,
    trackingTime: backendTracking.trackingTime || backendTracking.tracking_time,
    createTime: backendTracking.createTime || backendTracking.create_time,
    images: backendTracking.images || [],
    remarks: backendTracking.remarks
  }
}

/**
 * 转换前端轨迹数据为后端格式
 */
export function transformTrackingForBackend(frontendTracking: Partial<TrackingInfo>): any {
  return {
    order_number: frontendTracking.orderNumber,
    tracking_status: frontendTracking.trackingStatus,
    description: frontendTracking.description,
    location: frontendTracking.location,
    longitude: frontendTracking.longitude,
    latitude: frontendTracking.latitude,
    operator_name: frontendTracking.operatorName,
    operator_type: frontendTracking.operatorType,
    images: frontendTracking.images,
    remarks: frontendTracking.remarks
  }
}

// ========== 配送任务数据转换 ==========

/**
 * 转换后端配送任务数据为前端格式
 */
export function transformDeliveryTaskData(backendTask: any): DeliveryTask {
  return {
    id: backendTask.id,
    orderNumber: backendTask.orderNumber || backendTask.order_number,
    orderId: backendTask.orderId || backendTask.order_id,
    courierId: backendTask.courierId || backendTask.courier_id,
    courierName: backendTask.courierName || backendTask.courier_name,
    taskType: backendTask.taskType || backendTask.task_type,
    pickupAddress: backendTask.pickupAddress || backendTask.pickup_address,
    deliveryAddress: backendTask.deliveryAddress || backendTask.delivery_address,
    contactName: backendTask.contactName || backendTask.contact_name,
    contactPhone: backendTask.contactPhone || backendTask.contact_phone,
    estimatedTime: backendTask.estimatedTime || backendTask.estimated_time,
    actualStartTime: backendTask.actualStartTime || backendTask.actual_start_time,
    actualEndTime: backendTask.actualEndTime || backendTask.actual_end_time,
    taskStatus: backendTask.taskStatus || backendTask.task_status,
    priority: backendTask.priority,
    remarks: backendTask.remarks,
    createTime: backendTask.createTime || backendTask.create_time,
    updateTime: backendTask.updateTime || backendTask.update_time
  }
}

// ========== 用户数据转换 ==========

/**
 * 转换后端用户数据为前端格式
 */
export function transformUserData(backendUser: any): User {
  return {
    id: backendUser.id,
    username: backendUser.username,
    email: backendUser.email,
    phone: backendUser.phone,
    realName: backendUser.realName || backendUser.real_name,
    role: backendUser.role,
    status: backendUser.status,
    avatar: backendUser.avatar,
    createTime: backendUser.createTime || backendUser.create_time,
    updateTime: backendUser.updateTime || backendUser.update_time,
    // 配送员特有字段
    vehicleType: backendUser.vehicleType || backendUser.vehicle_type,
    vehicleNumber: backendUser.vehicleNumber || backendUser.vehicle_number,
    serviceArea: backendUser.serviceArea || backendUser.service_area,
    rating: backendUser.rating,
    totalOrders: backendUser.totalOrders || backendUser.total_orders,
    currentLocation: backendUser.currentLocation || backendUser.current_location
  }
}

// ========== 网点数据转换 ==========

/**
 * 转换后端网点数据为前端格式
 */
export function transformStationData(backendStation: any): Station {
  return {
    id: backendStation.id,
    stationCode: backendStation.stationCode || backendStation.station_code,
    stationName: backendStation.stationName || backendStation.station_name,
    stationType: backendStation.stationType || backendStation.station_type,
    province: backendStation.province,
    city: backendStation.city,
    district: backendStation.district,
    detailedAddress: backendStation.detailedAddress || backendStation.detailed_address,
    longitude: backendStation.longitude,
    latitude: backendStation.latitude,
    contactPerson: backendStation.contactPerson || backendStation.contact_person,
    contactPhone: backendStation.contactPhone || backendStation.contact_phone,
    businessHours: backendStation.businessHours || backendStation.business_hours,
    serviceScope: backendStation.serviceScope || backendStation.service_scope,
    capacity: backendStation.capacity,
    status: backendStation.status,
    createTime: backendStation.createTime || backendStation.create_time,
    updateTime: backendStation.updateTime || backendStation.update_time
  }
}

// ========== 地址数据转换 ==========

/**
 * 转换后端地址数据为前端格式
 */
export function transformAddressData(backendAddress: any): Address {
  return {
    id: backendAddress.id,
    userId: backendAddress.userId || backendAddress.user_id,
    contactName: backendAddress.contactName || backendAddress.contact_name,
    contactPhone: backendAddress.contactPhone || backendAddress.contact_phone,
    province: backendAddress.province,
    city: backendAddress.city,
    district: backendAddress.district,
    detailedAddress: backendAddress.detailedAddress || backendAddress.detailed_address,
    postalCode: backendAddress.postalCode || backendAddress.postal_code,
    longitude: backendAddress.longitude,
    latitude: backendAddress.latitude,
    isDefault: backendAddress.isDefault || backendAddress.is_default,
    addressType: backendAddress.addressType || backendAddress.address_type,
    createTime: backendAddress.createTime || backendAddress.create_time,
    updateTime: backendAddress.updateTime || backendAddress.update_time
  }
}

/**
 * 转换前端地址数据为后端格式
 */
export function transformAddressForBackend(frontendAddress: Partial<Address>): any {
  return {
    contact_name: frontendAddress.contactName,
    contact_phone: frontendAddress.contactPhone,
    province: frontendAddress.province,
    city: frontendAddress.city,
    district: frontendAddress.district,
    detailed_address: frontendAddress.detailedAddress,
    postal_code: frontendAddress.postalCode,
    longitude: frontendAddress.longitude,
    latitude: frontendAddress.latitude,
    is_default: frontendAddress.isDefault,
    address_type: frontendAddress.addressType
  }
}

// ========== 批量数据转换 ==========

/**
 * 批量转换订单数据
 */
export function transformOrderList(backendOrders: any[]): Order[] {
  return backendOrders.map(transformOrderData)
}

/**
 * 批量转换轨迹数据
 */
export function transformTrackingList(backendTrackings: any[]): TrackingInfo[] {
  return backendTrackings.map(transformTrackingData)
}

/**
 * 批量转换配送任务数据
 */
export function transformDeliveryTaskList(backendTasks: any[]): DeliveryTask[] {
  return backendTasks.map(transformDeliveryTaskData)
}

/**
 * 批量转换用户数据
 */
export function transformUserList(backendUsers: any[]): User[] {
  return backendUsers.map(transformUserData)
}

/**
 * 批量转换网点数据
 */
export function transformStationList(backendStations: any[]): Station[] {
  return backendStations.map(transformStationData)
}

/**
 * 批量转换地址数据
 */
export function transformAddressList(backendAddresses: any[]): Address[] {
  return backendAddresses.map(transformAddressData)
}

// ========== 状态转换 ==========

/**
 * 订单状态显示文本
 */
export function getOrderStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    'PENDING': '待支付',
    'PAID': '已支付',
    'PICKUP_ASSIGNED': '已分配揽件员',
    'PICKUP': '已揽收',
    'SORTING': '分拣中',
    'DISPATCHING': '发车中',
    'TRANSFERRING': '运输中',
    'ARRIVED': '已到达',
    'DELIVERING': '派送中',
    'SIGNED': '已签收',
    'CANCELLED': '已取消',
    'EXCEPTION': '异常'
  }
  return statusMap[status] || status
}

/**
 * 订单状态颜色
 */
export function getOrderStatusColor(status: string): string {
  const colorMap: Record<string, string> = {
    'PENDING': '#faad14',
    'PAID': '#1890ff',
    'PICKUP_ASSIGNED': '#722ed1',
    'PICKUP': '#52c41a',
    'SORTING': '#fa8c16',
    'DISPATCHING': '#722ed1',
    'TRANSFERRING': '#13c2c2',
    'ARRIVED': '#1890ff',
    'DELIVERING': '#722ed1',
    'SIGNED': '#52c41a',
    'CANCELLED': '#f5222d',
    'EXCEPTION': '#f5222d'
  }
  return colorMap[status] || '#666666'
}

/**
 * 配送任务状态显示文本
 */
export function getTaskStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    'ASSIGNED': '已分配',
    'ACCEPTED': '已接受',
    'IN_PROGRESS': '进行中',
    'COMPLETED': '已完成',
    'CANCELLED': '已取消',
    'EXCEPTION': '异常'
  }
  return statusMap[status] || status
}

/**
 * 用户角色显示文本
 */
export function getUserRoleText(role: string): string {
  const roleMap: Record<string, string> = {
    'ADMIN': '管理员',
    'OPERATOR': '操作员',
    'COURIER': '配送员',
    'CUSTOMER': '客户'
  }
  return roleMap[role] || role
}

/**
 * 网点类型显示文本
 */
export function getStationTypeText(type: string): string {
  const typeMap: Record<string, string> = {
    'PICKUP': '揽件点',
    'TRANSIT': '中转站',
    'DELIVERY': '派送点'
  }
  return typeMap[type] || type
}

// ========== 数据验证 ==========

/**
 * 验证订单数据完整性
 */
export function validateOrderData(orderData: Partial<Order>): string[] {
  const errors: string[] = []
  
  if (!orderData.senderName) errors.push('寄件人姓名不能为空')
  if (!orderData.senderPhone) errors.push('寄件人电话不能为空')
  if (!orderData.senderAddress) errors.push('寄件人地址不能为空')
  if (!orderData.receiverName) errors.push('收件人姓名不能为空')
  if (!orderData.receiverPhone) errors.push('收件人电话不能为空')
  if (!orderData.receiverAddress) errors.push('收件人地址不能为空')
  if (!orderData.itemName) errors.push('物品名称不能为空')
  if (!orderData.itemWeight || orderData.itemWeight <= 0) errors.push('物品重量必须大于0')
  if (!orderData.serviceType) errors.push('服务类型不能为空')
  
  return errors
}

/**
 * 验证地址数据完整性
 */
export function validateAddressData(addressData: Partial<Address>): string[] {
  const errors: string[] = []
  
  if (!addressData.contactName) errors.push('联系人姓名不能为空')
  if (!addressData.contactPhone) errors.push('联系人电话不能为空')
  if (!addressData.province) errors.push('省份不能为空')
  if (!addressData.city) errors.push('城市不能为空')
  if (!addressData.district) errors.push('区县不能为空')
  if (!addressData.detailedAddress) errors.push('详细地址不能为空')
  
  return errors
}
