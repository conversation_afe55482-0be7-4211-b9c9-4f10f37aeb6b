package org.example.logisticsuser.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;

@Data
public class UserLoginDTO {

    @NotBlank(message = "用户名不能为空")
    private String username;

    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 登录类型: USERNAME, PHONE, EMAIL
     */
    private String loginType = "USERNAME";

    /**
     * 记住我
     */
    private Boolean rememberMe = false;
} 