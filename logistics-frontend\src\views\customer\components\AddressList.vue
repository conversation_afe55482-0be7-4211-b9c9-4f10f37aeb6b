<template>
  <div class="address-list" v-loading="loading">
    <div v-for="address in addresses" :key="address.id" class="address-item">
      <div class="address-content">
        <div class="address-header">
          <div class="contact-info">
            <span class="contact-name">{{ address.contactName }}</span>
            <span class="contact-phone">{{ address.contactPhone }}</span>
            <el-tag v-if="address.isDefault" type="success" size="small">默认</el-tag>
          </div>
          <div class="address-actions">
            <el-button type="text" @click="$emit('edit', address)">编辑</el-button>
            <el-button type="text" @click="$emit('delete', address.id)">删除</el-button>
            <el-button
              v-if="!address.isDefault"
              type="text"
              @click="$emit('set-default', address.id)"
            >
              设为默认
            </el-button>
          </div>
        </div>

        <div class="address-detail">
          {{ address.province }} {{ address.city }} {{ address.district }}
          {{ address.detailedAddress }}
        </div>

        <div class="address-extra" v-if="address.postalCode">邮编：{{ address.postalCode }}</div>
      </div>
    </div>

    <div v-if="addresses.length === 0 && !loading" class="empty-state">
      <el-empty :description="`暂无${addressType === 1 ? '收货' : '发货'}地址`">
        <el-button type="primary" @click="$emit('add')"> 添加地址 </el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { AddressVO } from '@/api/address'

interface Props {
  addresses: AddressVO[]
  loading: boolean
  addressType: 1 | 2
}

interface Emits {
  (e: 'edit', address: AddressVO): void
  (e: 'delete', addressId: number): void
  (e: 'set-default', addressId: number): void
  (e: 'add'): void
}

defineProps<Props>()
defineEmits<Emits>()
</script>

<style scoped>
.address-list {
  min-height: 200px;
}

.address-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 15px;
  transition: all 0.3s;
}

.address-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.address-content {
  padding: 20px;
}

.address-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.contact-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.contact-name {
  font-weight: bold;
  color: #333;
}

.contact-phone {
  color: #666;
}

.address-actions {
  display: flex;
  gap: 5px;
}

.address-detail {
  color: #666;
  line-height: 1.5;
  margin-bottom: 8px;
}

.address-extra {
  color: #999;
  font-size: 14px;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}

@media (max-width: 768px) {
  .address-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .address-actions {
    align-self: flex-end;
  }
}
</style>
