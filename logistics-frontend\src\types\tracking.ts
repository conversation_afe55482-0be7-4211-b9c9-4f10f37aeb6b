// 物流轨迹状态
export type TrackingStatus =
  | 'CREATED' // 订单创建
  | 'PICKED_UP' // 已揽收
  | 'IN_TRANSIT' // 运输中
  | 'ARRIVED' // 到达网点
  | 'OUT_FOR_DELIVERY' // 派送中
  | 'DELIVERED' // 已送达
  | 'EXCEPTION' // 异常
  | 'RETURNED' // 已退回

// 物流轨迹节点
export interface TrackingNode {
  id: number
  orderNumber: string
  status: TrackingStatus
  description: string
  location?: string
  operatorName?: string
  operatorType?: string
  createTime: string
  remarks?: string
}

// 物流轨迹查询响应
export interface TrackingResponse {
  orderNumber: string
  currentStatus: TrackingStatus
  estimatedDeliveryTime?: string
  trackingNodes: TrackingNode[]
  orderInfo: {
    senderName: string
    senderAddress: string
    receiverName: string
    receiverAddress: string
    itemName?: string
    serviceType: string
  }
}

// 物流轨迹查询请求
export interface TrackingQueryRequest {
  orderNumber: string
  phone?: string
}
