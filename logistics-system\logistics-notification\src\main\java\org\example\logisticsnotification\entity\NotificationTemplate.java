package org.example.logisticsnotification.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.logistics.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通知模板实体
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("notification_templates")
public class NotificationTemplate extends BaseEntity {

    /**
     * 模板ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 模板编码
     */
    @TableField("template_code")
    private String templateCode;

    /**
     * 模板名称
     */
    @TableField("template_name")
    private String templateName;

    /**
     * 模板类型
     */
    @TableField("template_type")
    private String templateType;

    /**
     * 通知类型：SMS、EMAIL、PUSH、WECHAT
     */
    @TableField("notification_type")
    private String notificationType;

    /**
     * 模板标题
     */
    @TableField("title")
    private String title;

    /**
     * 模板内容
     */
    @TableField("content")
    private String content;

    /**
     * 模板参数说明（JSON格式）
     */
    @TableField("params_desc")
    private String paramsDesc;

    /**
     * 模板示例
     */
    @TableField("example")
    private String example;

    /**
     * 是否启用：0-禁用，1-启用
     */
    @TableField("is_enabled")
    private Integer isEnabled;

    /**
     * 排序
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;
}
