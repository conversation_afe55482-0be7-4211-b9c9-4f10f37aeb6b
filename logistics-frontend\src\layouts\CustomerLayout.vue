<template>
  <div class="layout-container">
    <el-container>
      <!-- 顶部导航 -->
      <el-header class="layout-header">
        <div class="header-left">
          <h1>物流跟踪系统</h1>
        </div>
        <div class="header-right">
          <el-dropdown @command="handleCommand">
            <span class="user-info">
              <el-avatar :size="32" :src="userInfo?.avatar">
                {{ userInfo?.realName?.charAt(0) }}
              </el-avatar>
              <span class="username">{{ userInfo?.realName }}</span>
              <el-icon class="el-icon--right"><arrow-down /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                <el-dropdown-item command="settings">设置</el-dropdown-item>
                <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <el-container>
        <!-- 侧边栏 -->
        <el-aside width="200px" class="layout-aside">
          <el-menu :default-active="activeMenu" class="sidebar-menu" router :collapse="false">
            <el-menu-item index="/customer/dashboard">
              <el-icon><house /></el-icon>
              <span>客户中心</span>
            </el-menu-item>

            <el-sub-menu index="order">
              <template #title>
                <el-icon><box /></el-icon>
                <span>订单管理</span>
              </template>
              <el-menu-item index="/customer/order/create">创建订单</el-menu-item>
              <el-menu-item index="/customer/order/list">订单列表</el-menu-item>
            </el-sub-menu>

            <el-sub-menu index="tracking">
              <template #title>
                <el-icon><van /></el-icon>
                <span>物流追踪</span>
              </template>
              <el-menu-item index="/customer/tracking/index">追踪查询</el-menu-item>
            </el-sub-menu>

            <el-sub-menu index="profile">
              <template #title>
                <el-icon><user /></el-icon>
                <span>个人中心</span>
              </template>
              <el-menu-item index="/customer/profile/index">基本信息</el-menu-item>
              <el-menu-item index="/customer/profile/address">地址管理</el-menu-item>
            </el-sub-menu>
          </el-menu>
        </el-aside>

        <!-- 主内容区 -->
        <el-main class="layout-main">
          <div class="page-content">
            <router-view />
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { House, Box, Van, User, ArrowDown } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

const userInfo = computed(() => authStore.userInfo)
const activeMenu = computed(() => route.path)

const handleCommand = (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/customer/profile/index')
      break
    case 'settings':
      // TODO: 跳转到设置页面
      break
    case 'logout':
      authStore.logout()
      break
  }
}
</script>

<style scoped>
.layout-container {
  height: 100vh;
}

.layout-header {
  background: #fff;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.header-left h1 {
  margin: 0;
  color: #409eff;
  font-size: 20px;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.username {
  margin: 0 8px;
  color: #333;
}

.layout-aside {
  background: #fff;
  border-right: 1px solid #e6e6e6;
}

.sidebar-menu {
  border-right: none;
  height: 100%;
}

.layout-main {
  background: #f5f5f5;
  padding: 0;
}

.page-content {
  padding: 20px;
  min-height: calc(100vh - 60px);
}
</style>
