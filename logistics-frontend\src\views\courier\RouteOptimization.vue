<template>
  <div class="route-optimization">
    <div class="page-header">
      <h2>路线规划</h2>
      <div class="header-actions">
        <el-button @click="refreshTasks" :icon="Refresh">刷新</el-button>
        <el-button @click="optimizeRoute" type="primary" :disabled="selectedTasks.length < 2">
          <el-icon><Guide /></el-icon>
          优化路线
        </el-button>
      </div>
    </div>

    <el-row :gutter="20">
      <!-- 左侧：任务列表 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>待配送任务</span>
              <span class="task-count">共 {{ pendingTasks.length }} 个任务</span>
            </div>
          </template>

          <div class="task-selection">
            <el-checkbox 
              v-model="selectAll" 
              @change="handleSelectAll"
              :indeterminate="isIndeterminate"
            >
              全选
            </el-checkbox>
            <span class="selected-info">已选择 {{ selectedTasks.length }} 个任务</span>
          </div>

          <div class="task-list">
            <div 
              v-for="task in pendingTasks" 
              :key="task.id" 
              class="task-item"
              :class="{ selected: selectedTasks.includes(task.id) }"
            >
              <el-checkbox 
                v-model="selectedTasks" 
                :label="task.id"
                @change="handleTaskSelect"
              />
              
              <div class="task-content" @click="toggleTaskSelect(task.id)">
                <div class="task-header">
                  <span class="task-number">{{ task.taskNumber }}</span>
                  <el-tag :type="getTaskTypeTag(task.taskType)" size="small">
                    {{ getTaskTypeText(task.taskType) }}
                  </el-tag>
                  <el-tag :type="getPriorityTag(task.priority)" size="small">
                    {{ getPriorityText(task.priority) }}
                  </el-tag>
                </div>

                <div class="task-address">
                  <div class="address-item">
                    <el-icon><Position /></el-icon>
                    <span>{{ task.senderAddress }}</span>
                  </div>
                  <div class="address-item">
                    <el-icon><LocationFilled /></el-icon>
                    <span>{{ task.receiverAddress }}</span>
                  </div>
                </div>

                <div class="task-info">
                  <span>{{ task.receiverName }} - {{ task.receiverPhone }}</span>
                  <span v-if="task.estimatedTime" class="estimated-time">
                    {{ formatTime(task.estimatedTime) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：路线结果 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>优化路线</span>
              <div v-if="routeResult" class="route-stats">
                <span>总距离：{{ routeResult.totalDistance }}km</span>
                <span>预计时间：{{ routeResult.estimatedTime }}分钟</span>
              </div>
            </div>
          </template>

          <div v-if="!routeResult" class="empty-route">
            <el-empty description="请选择至少2个任务进行路线优化" />
          </div>

          <div v-else class="route-content">
            <!-- 路线统计 -->
            <div class="route-summary">
              <el-row :gutter="20">
                <el-col :span="8">
                  <div class="stat-item">
                    <div class="stat-number">{{ routeResult.optimizedRoute.length }}</div>
                    <div class="stat-label">任务数量</div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="stat-item">
                    <div class="stat-number">{{ routeResult.totalDistance }}</div>
                    <div class="stat-label">总距离(km)</div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="stat-item">
                    <div class="stat-number">{{ routeResult.estimatedTime }}</div>
                    <div class="stat-label">预计时间(分钟)</div>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 优化后的路线 -->
            <div class="optimized-route">
              <h4>推荐路线</h4>
              <el-timeline>
                <el-timeline-item
                  v-for="(task, index) in routeResult.optimizedRoute"
                  :key="task.id"
                  :timestamp="`第${index + 1}站`"
                  placement="top"
                >
                  <div class="route-step">
                    <div class="step-header">
                      <span class="task-number">{{ task.taskNumber }}</span>
                      <el-tag :type="getTaskTypeTag(task.taskType)" size="small">
                        {{ getTaskTypeText(task.taskType) }}
                      </el-tag>
                    </div>
                    
                    <div class="step-address">
                      <el-icon><LocationFilled /></el-icon>
                      <span>{{ task.taskType === 'PICKUP' ? task.senderAddress : task.receiverAddress }}</span>
                    </div>
                    
                    <div class="step-contact">
                      <span>{{ task.taskType === 'PICKUP' ? task.senderName : task.receiverName }}</span>
                      <span>{{ task.taskType === 'PICKUP' ? task.senderPhone : task.receiverPhone }}</span>
                    </div>

                    <div class="step-actions">
                      <el-button size="small" @click="callContact(task)">
                        <el-icon><Phone /></el-icon>
                        联系
                      </el-button>
                      <el-button size="small" @click="openNavigation(task)">
                        <el-icon><Guide /></el-icon>
                        导航
                      </el-button>
                    </div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </div>

            <!-- 路线操作 -->
            <div class="route-actions">
              <el-button type="primary" @click="startRouteExecution">
                <el-icon><VideoPlay /></el-icon>
                开始执行路线
              </el-button>
              <el-button @click="saveRoute">
                <el-icon><Download /></el-icon>
                保存路线
              </el-button>
              <el-button @click="shareRoute">
                <el-icon><Share /></el-icon>
                分享路线
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 地图显示对话框 -->
    <el-dialog v-model="showMapDialog" title="路线地图" width="80%" top="5vh">
      <div class="map-container">
        <div id="route-map" style="width: 100%; height: 500px;"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  Guide,
  Position,
  LocationFilled,
  Phone,
  VideoPlay,
  Download,
  Share,
} from '@element-plus/icons-vue'
import { courierApi, type DeliveryTask } from '@/api/courier'
import dayjs from 'dayjs'

// 状态
const loading = ref(false)
const optimizing = ref(false)
const pendingTasks = ref<DeliveryTask[]>([])
const selectedTasks = ref<number[]>([])
const routeResult = ref<{
  optimizedRoute: DeliveryTask[]
  totalDistance: number
  estimatedTime: number
} | null>(null)
const showMapDialog = ref(false)

// 计算属性
const selectAll = computed({
  get: () => selectedTasks.value.length === pendingTasks.value.length && pendingTasks.value.length > 0,
  set: (value: boolean) => {
    selectedTasks.value = value ? pendingTasks.value.map(t => t.id) : []
  }
})

const isIndeterminate = computed(() => {
  return selectedTasks.value.length > 0 && selectedTasks.value.length < pendingTasks.value.length
})

// 加载待配送任务
const loadPendingTasks = async () => {
  loading.value = true
  try {
    const response = await courierApi.getActiveTasks()
    if (response.data.code === 200) {
      pendingTasks.value = response.data.data
    }
  } catch (error) {
    console.error('加载任务失败:', error)
    // 使用模拟数据
    pendingTasks.value = [
      {
        id: 1,
        taskNumber: 'T202401010001',
        orderNumber: 'O202401010001',
        courierId: 1,
        taskType: 'DELIVERY',
        taskStatus: 'ASSIGNED',
        priority: 2,
        weight: 2.5,
        volume: 0.1,
        goodsDescription: '文件',
        deliveryFee: 15.00,
        estimatedTime: '2024-01-01 14:00:00',
        senderName: '李四',
        senderPhone: '13800138002',
        senderAddress: '北京市朝阳区建国门外大街1号',
        receiverName: '王五',
        receiverPhone: '13800138003',
        receiverAddress: '北京市朝阳区三里屯路2号',
        retryCount: 0,
        createTime: '2024-01-01 10:00:00',
        updateTime: '2024-01-01 10:00:00',
      },
      {
        id: 2,
        taskNumber: 'T202401010002',
        orderNumber: 'O202401010002',
        courierId: 1,
        taskType: 'PICKUP',
        taskStatus: 'ASSIGNED',
        priority: 1,
        weight: 1.0,
        volume: 0.05,
        goodsDescription: '包裹',
        deliveryFee: 12.00,
        estimatedTime: '2024-01-01 16:00:00',
        senderName: '张三',
        senderPhone: '13800138001',
        senderAddress: '北京市朝阳区望京SOHO',
        receiverName: '赵六',
        receiverPhone: '13800138004',
        receiverAddress: '北京市朝阳区国贸CBD',
        retryCount: 0,
        createTime: '2024-01-01 11:00:00',
        updateTime: '2024-01-01 11:00:00',
      },
    ]
  } finally {
    loading.value = false
  }
}

// 刷新任务
const refreshTasks = () => {
  loadPendingTasks()
}

// 全选处理
const handleSelectAll = (value: boolean) => {
  selectedTasks.value = value ? pendingTasks.value.map(t => t.id) : []
}

// 任务选择处理
const handleTaskSelect = () => {
  // 选择变化时清除路线结果
  routeResult.value = null
}

// 切换任务选择
const toggleTaskSelect = (taskId: number) => {
  const index = selectedTasks.value.indexOf(taskId)
  if (index > -1) {
    selectedTasks.value.splice(index, 1)
  } else {
    selectedTasks.value.push(taskId)
  }
  routeResult.value = null
}

// 优化路线
const optimizeRoute = async () => {
  if (selectedTasks.value.length < 2) {
    ElMessage.warning('请选择至少2个任务进行路线优化')
    return
  }

  optimizing.value = true
  try {
    const response = await courierApi.getRouteOptimization(selectedTasks.value)
    if (response.data.code === 200) {
      routeResult.value = response.data.data
      ElMessage.success('路线优化完成')
    }
  } catch (error) {
    console.error('路线优化失败:', error)
    // 使用模拟数据
    const selectedTaskData = pendingTasks.value.filter(t => selectedTasks.value.includes(t.id))
    routeResult.value = {
      optimizedRoute: selectedTaskData,
      totalDistance: 15.8,
      estimatedTime: 45,
    }
    ElMessage.success('路线优化完成')
  } finally {
    optimizing.value = false
  }
}

// 开始执行路线
const startRouteExecution = () => {
  if (!routeResult.value) return
  
  ElMessage.success('路线执行已开始，请按顺序完成任务')
  // TODO: 实现路线执行逻辑
}

// 保存路线
const saveRoute = () => {
  if (!routeResult.value) return
  
  // TODO: 实现保存路线逻辑
  ElMessage.success('路线已保存')
}

// 分享路线
const shareRoute = () => {
  if (!routeResult.value) return
  
  // TODO: 实现分享路线逻辑
  ElMessage.success('路线分享链接已复制到剪贴板')
}

// 联系客户
const callContact = (task: DeliveryTask) => {
  const phone = task.taskType === 'PICKUP' ? task.senderPhone : task.receiverPhone
  if (phone) {
    window.open(`tel:${phone}`)
  } else {
    ElMessage.warning('联系电话不存在')
  }
}

// 打开导航
const openNavigation = (task: DeliveryTask) => {
  const address = task.taskType === 'PICKUP' ? task.senderAddress : task.receiverAddress
  const encodedAddress = encodeURIComponent(address)
  const mapUrl = `https://uri.amap.com/navigation?to=${encodedAddress}&mode=car&policy=1&src=myapp&coordinate=gaode&callnative=0`
  
  window.open(mapUrl, '_blank')
}

// 工具函数
const getTaskTypeText = (type: string) => {
  const textMap = {
    PICKUP: '揽件',
    DELIVERY: '派送',
  }
  return textMap[type] || type
}

const getTaskTypeTag = (type: string) => {
  const tagMap = {
    PICKUP: 'warning',
    DELIVERY: 'success',
  }
  return tagMap[type] || ''
}

const getPriorityText = (priority: number) => {
  const textMap = {
    1: '低',
    2: '中',
    3: '高',
    4: '紧急',
  }
  return textMap[priority] || '普通'
}

const getPriorityTag = (priority: number) => {
  const tagMap = {
    1: 'info',
    2: '',
    3: 'warning',
    4: 'danger',
  }
  return tagMap[priority] || ''
}

const formatTime = (time: string) => {
  return dayjs(time).format('MM-DD HH:mm')
}

onMounted(() => {
  loadPendingTasks()
})
</script>

<style scoped>
.route-optimization {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-count {
  color: #666;
  font-size: 14px;
}

.route-stats {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #666;
}

.task-selection {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.selected-info {
  color: #666;
  font-size: 14px;
}

.task-list {
  max-height: 600px;
  overflow-y: auto;
}

.task-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 12px;
  transition: all 0.3s;
}

.task-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.task-item.selected {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.task-content {
  flex: 1;
  cursor: pointer;
}

.task-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.task-number {
  font-weight: bold;
  color: #333;
}

.task-address {
  margin-bottom: 8px;
}

.address-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.task-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.estimated-time {
  color: #f56c6c;
}

.empty-route {
  text-align: center;
  padding: 60px 0;
}

.route-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.route-summary {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

.optimized-route h4 {
  margin: 0 0 16px 0;
  color: #333;
}

.route-step {
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.step-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.step-address {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 6px;
  font-size: 14px;
  color: #666;
}

.step-contact {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}

.step-actions {
  display: flex;
  gap: 8px;
}

.route-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.map-container {
  width: 100%;
  height: 500px;
  border-radius: 8px;
  overflow: hidden;
}
</style> 