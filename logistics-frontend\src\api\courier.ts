import http from '@/utils/http'
import type { ApiResponse, PageResponse } from '@/types/api'

// 配送员信息
export interface CourierInfo {
  id: number
  userId?: number
  courierCode: string
  courierName: string
  phone: string
  idCard?: string
  workNumber?: string
  vehicleType?: string
  vehicleNumber?: string
  workArea?: string
  maxWeight?: number
  maxVolume?: number
  status: number
  longitude?: number
  latitude?: number
  currentAddress?: string
  rating: number
  deliveryCount: number
  createTime: string
  updateTime: string
}

// 配送任务
export interface DeliveryTask {
  id: number
  taskNumber: string
  orderId: number
  orderNumber: string
  courierId?: number
  taskType: string
  taskStatus: string
  priority: number
  pickupAddress: string
  deliveryAddress: string
  contactName: string
  contactPhone: string
  goodsDescription?: string
  weight?: number
  volume?: number
  specialRequirements?: string
  estimatedPickupTime?: string
  estimatedDeliveryTime?: string
  actualPickupTime?: string
  actualDeliveryTime?: string
  completionProof?: string
  failureReason?: string
  retryCount: number
  remark?: string
  createTime: string
  updateTime: string
}

// 配送员统计
export interface CourierStats {
  todayTasks: number
  completedTasks: number
  pendingTasks: number
  todayEarnings: number
  totalEarnings: number
  rating: number
  onlineHours: number
  thisMonthTasks: number
  thisMonthEarnings: number
}

export const courierApi = {
  // 获取配送员信息 - 根据用户ID查询
  getCourierInfo(): Promise<ApiResponse<CourierInfo>> {
    return http.get('/user/profile').then((response) => {
      if (response.data.code === 200) {
        const user = response.data.data
        // 如果是配送员，获取配送员详细信息
        if (user.userType === 'COURIER') {
          return http.get(`/delivery/courier/user/${user.id}`)
        } else {
          // 返回基本用户信息转换为配送员格式
          return {
            data: {
              code: 200,
              message: '获取配送员信息成功',
              data: {
                id: user.id,
                courierCode: user.username,
                courierName: user.realName,
                phone: user.phone,
                status: user.status || 1,
                rating: 5.0,
                deliveryCount: 0,
                createTime: user.createTime,
                updateTime: user.updateTime,
              },
            },
          } as any
        }
      }
      throw new Error('获取配送员信息失败')
    })
  },

  // 获取配送员统计
  getCourierStats(): Promise<ApiResponse<CourierStats>> {
    return http.get('/delivery/task/statistics').then((response) => {
      if (response.data.code === 200) {
        const stats = response.data.data
        return {
          data: {
            code: 200,
            message: '获取统计数据成功',
            data: {
              todayTasks: stats.todayTasks || 0,
              completedTasks: stats.completedTasks || 0,
              pendingTasks: stats.pendingTasks || 0,
              todayEarnings: stats.todayEarnings || 0,
              totalEarnings: stats.totalEarnings || 0,
              rating: stats.avgRating || 5.0,
              onlineHours: 8,
              thisMonthTasks: stats.monthlyTasks || 0,
              thisMonthEarnings: stats.monthlyEarnings || 0,
            },
          },
        } as any
      }
      throw new Error('获取统计数据失败')
    })
  },

  // 获取配送任务列表（修复：使用正确的接口路径）
  getDeliveryTasks(params?: {
    status?: string
    taskType?: string
    page?: number
    size?: number
    courierId?: number
  }): Promise<ApiResponse<PageResponse<DeliveryTask>>> {
    const userId = localStorage.getItem('userId')
    const queryParams = {
      pageNum: params?.page || 1,
      pageSize: params?.size || 10,
      taskStatus: params?.status,
      taskType: params?.taskType,
      courierId: params?.courierId || userId, // 默认使用当前用户ID
    }
    return http.get('/delivery/task/page', { params: queryParams })
  },

  // 获取进行中的任务（修复：使用正确的接口路径）
  getActiveTasks(): Promise<ApiResponse<DeliveryTask[]>> {
    const userId = localStorage.getItem('userId')
    if (!userId) {
      return Promise.reject(new Error('未找到用户ID'))
    }
    
    // 使用正确的后端接口路径
    return http.get('/delivery/task/courier/active', {
      params: { courierId: userId }
    })
  },

  // 获取配送员当日任务（修复）
  getTodayTasks(): Promise<ApiResponse<DeliveryTask[]>> {
    const userId = localStorage.getItem('userId')
    if (!userId) {
      return Promise.reject(new Error('未找到用户ID'))
    }
    
    return http.get('/delivery/task/courier/daily', {
      params: { courierId: userId }
    })
  },

  // 获取任务统计（修复：使用正确的接口路径）
  getTaskStatistics(): Promise<ApiResponse<CourierStats>> {
    const userId = localStorage.getItem('userId')
    if (!userId) {
      return Promise.reject(new Error('未找到用户ID'))
    }
    
    return http.get('/delivery/task/courier/statistics', {
      params: { courierId: userId }
    }).then((response) => {
      if (response.code === 200) {
        const stats = response.data
        return {
          code: 200,
          message: '获取统计数据成功',
          data: {
            todayTasks: stats.totalTasks || 0,
            completedTasks: stats.completedTasks || 0,
            pendingTasks: stats.activeTasks || 0,
            todayCompleted: stats.completedTasks || 0,
            avgRating: 5.0, // 暂时固定值，等待后端接口
            todayEarnings: stats.totalDeliveryFee || 0,
            totalEarnings: stats.totalDeliveryFee || 0,
            onlineHours: 8, // 暂时固定值
            thisMonthTasks: stats.totalTasks || 0,
            thisMonthEarnings: stats.totalDeliveryFee || 0,
          },
        } as ApiResponse<CourierStats>
      }
      throw new Error('获取统计数据失败')
    })
  },

  // 开始任务
  startTask(taskId: number): Promise<ApiResponse<boolean>> {
    return http.put(`/delivery/task/${taskId}/start`)
  },

  // 完成任务
  completeTask(data: {
    taskId: number
    completionProof: string
    remarks?: string
  }): Promise<ApiResponse<boolean>> {
    return http.put(`/delivery/task/${data.taskId}/complete`, {
      completionProof: data.completionProof,
      remarks: data.remarks,
    })
  },

  // 获取所有配送员（管理员专用）
  getAllCouriers(params?: {
    page?: number
    size?: number
    status?: number
    workArea?: string
    keyword?: string
  }): Promise<ApiResponse<PageResponse<CourierInfo>>> {
    const queryParams = {
      pageNum: params?.page || 1,
      pageSize: params?.size || 10,
      status: params?.status,
      workArea: params?.workArea,
      courierName: params?.keyword,
    }
    return http.get('/delivery/courier/page', { params: queryParams })
  },

  // 根据ID获取配送员
  getCourierById(id: number): Promise<ApiResponse<CourierInfo>> {
    return http.get(`/delivery/courier/${id}`)
  },

  // 更新配送员位置
  updateCourierLocation(
    courierId: number,
    data: {
      longitude: number
      latitude: number
      address?: string
    },
  ): Promise<ApiResponse<boolean>> {
    return http.put(`/delivery/courier/${courierId}/location`, null, {
      params: {
        longitude: data.longitude,
        latitude: data.latitude,
        address: data.address,
      },
    })
  },

  // 更新配送员状态
  updateCourierStatus(courierId: number, status: number): Promise<ApiResponse<boolean>> {
    return http.put(`/delivery/courier/${courierId}/status`, null, {
      params: { status },
    })
  },

  // 获取配送员位置
  getCourierLocation(courierId: number): Promise<
    ApiResponse<{
      latitude: number
      longitude: number
      address?: string
      updateTime: string
    }>
  > {
    return http.get(`/delivery/courier/${courierId}`).then((response) => {
      if (response.data.code === 200) {
        const courier = response.data.data
        return {
          data: {
            code: 200,
            message: '获取配送员位置成功',
            data: {
              latitude: courier.latitude || 39.9042,
              longitude: courier.longitude || 116.4074,
              address: courier.currentAddress || '',
              updateTime: courier.updateTime || new Date().toISOString(),
            },
          },
        } as any
      }
      throw new Error('获取配送员位置失败')
    })
  },

  // 获取可用配送员
  getAvailableCouriers(workArea?: string): Promise<ApiResponse<CourierInfo[]>> {
    return http.get('/delivery/courier/available', {
      params: workArea ? { workArea } : {},
    })
  },

  // 自动分配最佳配送员
  assignBestCourier(data: {
    workArea: string
    longitude?: number
    latitude?: number
  }): Promise<ApiResponse<CourierInfo>> {
    return http.post('/delivery/courier/assign-best', null, {
      params: data,
    })
  },

  // 获取配送员状态统计
  getCourierStatusStatistics(): Promise<ApiResponse<Record<string, number>>> {
    return http.get('/delivery/courier/statistics/status')
  },

  // 创建配送员
  createCourier(data: Partial<CourierInfo>): Promise<ApiResponse<CourierInfo>> {
    return http.post('/delivery/courier', data)
  },

  // 更新配送员信息
  updateCourier(id: number, data: Partial<CourierInfo>): Promise<ApiResponse<CourierInfo>> {
    return http.put(`/delivery/courier/${id}`, data)
  },

  // 删除配送员
  deleteCourier(id: number): Promise<ApiResponse<boolean>> {
    return http.delete(`/delivery/courier/${id}`)
  },

  // 批量更新配送员状态
  batchUpdateCourierStatus(data: {
    courierIds: number[]
    status: number
  }): Promise<ApiResponse<boolean>> {
    return http.put('/delivery/courier/batch/status', data)
  },

  // 更新配送员工作状态
  updateStatus(status: number): Promise<ApiResponse<boolean>> {
    return http.put('/delivery/courier/status', null, {
      params: { status },
    })
  },

  // 签到签退
  clockInOut(
    action: 'IN' | 'OUT',
    location: {
      longitude: number
      latitude: number
      address?: string
    },
  ): Promise<ApiResponse<boolean>> {
    return http.post('/delivery/courier/clock', {
      action,
      longitude: location.longitude,
      latitude: location.latitude,
      address: location.address,
    })
  },

  // 取消任务
  cancelTask(taskId: number, reason: string): Promise<ApiResponse<boolean>> {
    return http.put(`/delivery/task/${taskId}/cancel`, {
      reason,
    })
  },

  // 接受任务
  acceptTask(taskId: number): Promise<ApiResponse<boolean>> {
    return http.put(`/delivery/task/${taskId}/accept`)
  },

  // 拒绝任务
  rejectTask(taskId: number, reason: string): Promise<ApiResponse<boolean>> {
    return http.put(`/delivery/task/${taskId}/reject`, {
      reason,
    })
  },

  // 上报位置
  reportLocation(data: {
    longitude: number
    latitude: number
    address?: string
  }): Promise<ApiResponse<boolean>> {
    return http.post('/delivery/courier/location', data)
  },

  // 获取配送历史
  getDeliveryHistory(params?: {
    page?: number
    size?: number
    startDate?: string
    endDate?: string
  }): Promise<ApiResponse<PageResponse<DeliveryTask>>> {
    const queryParams = {
      pageNum: params?.page || 1,
      pageSize: params?.size || 10,
      startDate: params?.startDate,
      endDate: params?.endDate,
      taskStatus: 'COMPLETED',
    }
    return http.get('/delivery/task/history', { params: queryParams })
  },
}
