import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      redirect: '/login',
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/auth/Login.vue'),
      meta: { requiresAuth: false, title: '用户登录' },
    },
    {
      path: '/register',
      name: 'Register',
      component: () => import('@/views/auth/Register.vue'),
      meta: { requiresAuth: false, title: '用户注册' },
    },
    {
      path: '/customer',
      component: () => import('@/layouts/CustomerLayout.vue'),
      meta: { requiresAuth: true, roles: ['CUSTOMER'] },
      redirect: '/customer/dashboard', // 添加默认重定向
      children: [
        {
          path: 'dashboard',
          name: 'CustomerDashboard',
          component: () => import('@/views/customer/Dashboard.vue'),
          meta: { title: '客户中心' },
        },
        // 订单管理
        {
          path: 'order',
          redirect: '/customer/order/list', // 添加默认重定向
          children: [
            {
              path: 'create',
              name: 'OrderCreate',
              component: () => import('@/views/customer/order/Create.vue'),
              meta: { title: '创建订单' },
            },
            {
              path: 'list',
              name: 'OrderList',
              component: () => import('@/views/customer/order/List.vue'),
              meta: { title: '订单列表' },
            },
            {
              path: 'detail/:id',
              name: 'OrderDetail',
              component: () => import('@/views/customer/order/Detail.vue'),
              meta: { title: '订单详情' },
            },
          ],
        },
        // 物流追踪
        {
          path: 'tracking',
          redirect: '/customer/tracking/index', // 添加默认重定向
          children: [
            {
              path: 'index',
              name: 'TrackingIndex',
              component: () => import('@/views/customer/tracking/Index.vue'),
              meta: { title: '物流追踪' },
            },
            {
              path: 'detail',
              name: 'TrackingDetail',
              component: () => import('@/views/customer/tracking/Detail.vue'),
              meta: { title: '追踪详情' },
            },
          ],
        },
        // 个人中心
        {
          path: 'profile',
          redirect: '/customer/profile/index', // 添加默认重定向
          children: [
            {
              path: 'index',
              name: 'Profile',
              component: () => import('@/views/customer/profile/Index.vue'),
              meta: { title: '个人中心' },
            },
            {
              path: 'edit',
              name: 'ProfileEdit',
              component: () => import('@/views/customer/profile/Edit.vue'),
              meta: { title: '编辑资料' },
            },
            {
              path: 'address',
              name: 'ProfileAddress',
              component: () => import('@/views/customer/profile/Address.vue'),
              meta: { title: '地址管理' },
            },
            {
              path: 'security',
              name: 'ProfileSecurity',
              component: () => import('@/views/customer/profile/Security.vue'),
              meta: { title: '账号安全' },
            },
          ],
        },
      ],
    },
    {
      path: '/admin',
      component: () => import('@/layouts/AdminLayout.vue'),
      meta: { requiresAuth: true, roles: ['ADMIN'] },
      redirect: '/admin/dashboard',
      children: [
        {
          path: 'dashboard',
          name: 'AdminDashboard',
          component: () => import('@/views/admin/Dashboard.vue'),
          meta: { title: '管理后台' },
        },
        // 订单管理
        {
          path: 'order',
          redirect: '/admin/order/list',
          children: [
            {
              path: 'list',
              name: 'AdminOrderList',
              component: () => import('@/views/admin/order/List.vue'),
              meta: { title: '订单管理' },
            },
            {
              path: 'detail/:id',
              name: 'AdminOrderDetail',
              component: () => import('@/views/admin/order/Detail.vue'),
              meta: { title: '订单详情' },
            },
          ],
        },
        // 用户管理
        {
          path: 'user',
          redirect: '/admin/user/list',
          children: [
            {
              path: 'list',
              name: 'AdminUserList',
              component: () => import('@/views/admin/user/List.vue'),
              meta: { title: '用户管理' },
            },
          ],
        },
      ],
    },
    {
      path: '/operator',
      component: () => import('@/layouts/OperatorLayout.vue'),
      meta: { requiresAuth: true, roles: ['OPERATOR'] },
      redirect: '/operator/dashboard',
      children: [
        {
          path: 'dashboard',
          name: 'OperatorDashboard',
          component: () => import('@/views/operator/Dashboard.vue'),
          meta: { title: '操作员工作台' },
        },
        // 订单调度
        {
          path: 'orders',
          name: 'OperatorOrders',
          component: () => import('@/views/operator/OrderManagement.vue'),
          meta: { title: '订单调度' },
        },
        // 订单详情
        {
          path: 'orders/:id',
          name: 'OperatorOrderDetail',
          component: () => import('@/views/operator/OrderDetail.vue'),
          meta: { title: '订单详情' },
        },
        // 配送员管理
        {
          path: 'couriers',
          name: 'OperatorCouriers',
          component: () => import('@/views/operator/CourierManagement.vue'),
          meta: { title: '配送员管理' },
        },
        // 客服工单
        {
          path: 'tickets',
          name: 'OperatorTickets',
          component: () => import('@/views/operator/ServiceTickets.vue'),
          meta: { title: '客服工单' },
        },
        // 实时地图
        {
          path: 'map',
          name: 'OperatorMap',
          component: () => import('@/views/operator/DeliveryMap.vue'),
          meta: { title: '实时地图' },
        },
        // 调度记录
        {
          path: 'dispatch',
          name: 'OperatorDispatch',
          component: () => import('@/views/operator/DispatchRecords.vue'),
          meta: { title: '调度记录' },
        },
      ],
    },
    {
      path: '/courier',
      component: () => import('@/layouts/CourierLayout.vue'),
      meta: { requiresAuth: true, roles: ['COURIER'] },
      redirect: '/courier/dashboard',
      children: [
        {
          path: 'dashboard',
          name: 'CourierDashboard',
          component: () => import('@/views/courier/Dashboard.vue'),
          meta: { title: '配送员工作台' },
        },
        // 任务管理
        {
          path: 'tasks',
          name: 'CourierTasks',
          component: () => import('@/views/courier/TaskList.vue'),
          meta: { title: '我的任务' },
        },
        {
          path: 'task/detail/:id',
          name: 'CourierTaskDetail',
          component: () => import('@/views/courier/TaskDetail.vue'),
          meta: { title: '任务详情' },
        },
        {
          path: 'task/report/:id',
          name: 'CourierTaskReport',
          component: () => import('@/views/courier/TaskReport.vue'),
          meta: { title: '报告问题' },
        },
        // 揽件确认
        {
          path: 'pickup/:id',
          name: 'CourierPickupConfirm',
          component: () => import('@/views/courier/PickupConfirm.vue'),
          meta: { title: '揽件确认' },
        },
        // 签收确认
        {
          path: 'delivery/:id',
          name: 'CourierDeliveryConfirm',
          component: () => import('@/views/courier/DeliveryConfirm.vue'),
          meta: { title: '签收确认' },
        },
        // 路线规划
        {
          path: 'route',
          name: 'CourierRoute',
          component: () => import('@/views/courier/RouteOptimization.vue'),
          meta: { title: '路线规划' },
        },
        // 配送地图
        {
          path: 'delivery-map',
          name: 'CourierDeliveryMap',
          component: () => import('@/views/courier/DeliveryMap.vue'),
          meta: { title: '配送地图' },
        },
        // 配送历史
        {
          path: 'history',
          name: 'CourierHistory',
          component: () => import('@/views/courier/DeliveryHistory.vue'),
          meta: { title: '配送历史' },
        },
        // 个人信息
        {
          path: 'profile',
          name: 'CourierProfile',
          component: () => import('@/views/courier/Profile.vue'),
          meta: { title: '个人信息' },
        },
      ],
    },
    // 通用消息中心
    {
      path: '/messages',
      name: 'MessageCenter',
      component: () => import('@/views/Common/MessageCenter.vue'),
      meta: { requiresAuth: true, title: '消息中心' },
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/error/NotFound.vue'),
      meta: { title: '页面不存在' },
    },
  ],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  const token = localStorage.getItem('token')
  const userInfo = authStore.userInfo

  // 如果需要认证但没有token，跳转到登录页
  if (to.meta.requiresAuth && !token) {
    next('/login')
    return
  }

  // 如果已登录但访问登录/注册页，跳转到对应的dashboard
  if ((to.path === '/login' || to.path === '/register') && token && userInfo) {
    const redirectPath = getRedirectPath(userInfo.userType)
    next(redirectPath)
    return
  }

  // 角色权限检查
  if (to.meta.roles && Array.isArray(to.meta.roles) && userInfo) {
    const hasPermission = to.meta.roles.includes(userInfo.userType)
    if (!hasPermission) {
      next('/404')
      return
    }
  }

  next()
})

// 根据用户角色获取重定向路径
function getRedirectPath(userType: string): string {
  switch (userType) {
    case 'ADMIN':
      return '/admin/dashboard'
    case 'OPERATOR':
      return '/operator/dashboard'
    case 'COURIER':
      return '/courier/dashboard'
    case 'CUSTOMER':
    default:
      return '/customer/dashboard'
  }
}

export default router
