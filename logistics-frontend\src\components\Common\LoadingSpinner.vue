<template>
  <div class="loading-container" :class="containerClass">
    <div class="loading-spinner" :class="spinnerClass">
      <div class="spinner-ring">
        <div class="ring-segment"></div>
        <div class="ring-segment"></div>
        <div class="ring-segment"></div>
        <div class="ring-segment"></div>
      </div>
      
      <div class="loading-content">
        <div class="loading-icon">
          <el-icon :size="iconSize">
            <component :is="icon" />
          </el-icon>
        </div>
        
        <div class="loading-text" v-if="text">
          <p class="main-text">{{ text }}</p>
          <p class="sub-text" v-if="subText">{{ subText }}</p>
        </div>
        
        <div class="loading-progress" v-if="showProgress">
          <el-progress 
            :percentage="progress" 
            :color="progressColor"
            :stroke-width="4"
            :show-text="false"
            class="progress-bar"
          />
          <span class="progress-text">{{ progress }}%</span>
        </div>
      </div>
    </div>
    
    <!-- 背景遮罩 -->
    <div v-if="overlay" class="loading-overlay"></div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Van } from '@element-plus/icons-vue'

// Props
interface Props {
  size?: 'small' | 'medium' | 'large'
  type?: 'primary' | 'success' | 'warning' | 'danger'
  text?: string
  subText?: string
  icon?: string
  overlay?: boolean
  showProgress?: boolean
  progress?: number
  fullscreen?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  type: 'primary',
  icon: 'Van',
  overlay: false,
  showProgress: false,
  progress: 0,
  fullscreen: false
})

// 计算属性
const containerClass = computed(() => {
  return {
    'fullscreen': props.fullscreen,
    'with-overlay': props.overlay
  }
})

const spinnerClass = computed(() => {
  return {
    [`size-${props.size}`]: true,
    [`type-${props.type}`]: true
  }
})

const iconSize = computed(() => {
  const sizes = {
    small: 24,
    medium: 32,
    large: 48
  }
  return sizes[props.size]
})

const progressColor = computed(() => {
  const colors = {
    primary: '#1890ff',
    success: '#52c41a',
    warning: '#faad14',
    danger: '#f5222d'
  }
  return colors[props.type]
})
</script>

<style scoped>
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.loading-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: var(--z-modal);
}

.loading-container.with-overlay {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
  position: relative;
}

/* ========== 旋转环 ========== */
.spinner-ring {
  position: relative;
  width: 80px;
  height: 80px;
}

.ring-segment {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-radius: 50%;
  animation: spin 1.5s linear infinite;
}

.ring-segment:nth-child(1) {
  border-top-color: var(--primary-color);
  animation-delay: 0s;
}

.ring-segment:nth-child(2) {
  border-right-color: var(--primary-color);
  animation-delay: 0.3s;
  opacity: 0.8;
}

.ring-segment:nth-child(3) {
  border-bottom-color: var(--primary-color);
  animation-delay: 0.6s;
  opacity: 0.6;
}

.ring-segment:nth-child(4) {
  border-left-color: var(--primary-color);
  animation-delay: 0.9s;
  opacity: 0.4;
}

/* ========== 尺寸变体 ========== */
.size-small .spinner-ring {
  width: 40px;
  height: 40px;
}

.size-small .ring-segment {
  border-width: 2px;
}

.size-large .spinner-ring {
  width: 120px;
  height: 120px;
}

.size-large .ring-segment {
  border-width: 4px;
}

/* ========== 类型变体 ========== */
.type-success .ring-segment:nth-child(1) { border-top-color: var(--success-color); }
.type-success .ring-segment:nth-child(2) { border-right-color: var(--success-color); }
.type-success .ring-segment:nth-child(3) { border-bottom-color: var(--success-color); }
.type-success .ring-segment:nth-child(4) { border-left-color: var(--success-color); }

.type-warning .ring-segment:nth-child(1) { border-top-color: var(--warning-color); }
.type-warning .ring-segment:nth-child(2) { border-right-color: var(--warning-color); }
.type-warning .ring-segment:nth-child(3) { border-bottom-color: var(--warning-color); }
.type-warning .ring-segment:nth-child(4) { border-left-color: var(--warning-color); }

.type-danger .ring-segment:nth-child(1) { border-top-color: var(--error-color); }
.type-danger .ring-segment:nth-child(2) { border-right-color: var(--error-color); }
.type-danger .ring-segment:nth-child(3) { border-bottom-color: var(--error-color); }
.type-danger .ring-segment:nth-child(4) { border-left-color: var(--error-color); }

/* ========== 加载内容 ========== */
.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

.loading-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--primary-color);
  animation: pulse 2s ease-in-out infinite;
}

.type-success .loading-icon { color: var(--success-color); }
.type-warning .loading-icon { color: var(--warning-color); }
.type-danger .loading-icon { color: var(--error-color); }

.loading-text {
  text-align: center;
  margin-top: 60px;
}

.size-small .loading-text {
  margin-top: 30px;
}

.size-large .loading-text {
  margin-top: 90px;
}

.main-text {
  margin: 0;
  font-size: var(--font-md);
  font-weight: 500;
  color: var(--text-primary);
}

.sub-text {
  margin: var(--spacing-xs) 0 0 0;
  font-size: var(--font-sm);
  color: var(--text-tertiary);
}

/* ========== 进度条 ========== */
.loading-progress {
  width: 200px;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.progress-bar {
  margin-bottom: 0;
}

.progress-text {
  text-align: center;
  font-size: var(--font-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

/* ========== 动画效果 ========== */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.7;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

/* ========== 响应式设计 ========== */
@media (max-width: 768px) {
  .loading-progress {
    width: 150px;
  }
  
  .size-large .spinner-ring {
    width: 80px;
    height: 80px;
  }
  
  .size-large .loading-text {
    margin-top: 60px;
  }
}
</style>
