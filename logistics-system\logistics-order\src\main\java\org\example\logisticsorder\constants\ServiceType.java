package org.example.logisticsorder.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 服务类型枚举
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Getter
@AllArgsConstructor
public enum ServiceType {

    STANDARD("STANDARD", "标准快递", 1.0),
    EXPRESS("EXPRESS", "特快专递", 1.5),
    URGENT("URGENT", "加急配送", 2.0),
    SAME_DAY("SAME_DAY", "当日达", 2.5);

    private final String code;
    private final String description;
    private final Double priceMultiplier; // 价格倍数

    /**
     * 根据服务类型码获取枚举
     */
    public static ServiceType fromCode(String code) {
        for (ServiceType type : ServiceType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的服务类型: " + code);
    }
}
