package org.example.logisticslogistics.service;

import org.example.logisticslogistics.entity.LocationInfo;
import org.example.logisticslogistics.entity.TrackingInfo;

import java.util.List;
import java.util.Map;

/**
 * 位置管理服务接口
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
public interface LocationService {

    /**
     * 根据经纬度范围查询轨迹信息
     */
    List<TrackingInfo> findByLocationRange(double minLng, double maxLng, double minLat, double maxLat);

    /**
     * 查询指定坐标点附近的轨迹信息
     */
    List<TrackingInfo> findNearby(double longitude, double latitude, double radiusKm);

    /**
     * 根据城市查询轨迹信息
     */
    List<TrackingInfo> findByCity(String city);

    /**
     * 计算两点间距离（公里）
     */
    Double calculateDistance(LocationInfo from, LocationInfo to);

    /**
     * 验证经纬度坐标是否有效
     */
    boolean isValidCoordinate(double longitude, double latitude);

    /**
     * 解析地址信息
     */
    LocationInfo parseAddress(String address);

    /**
     * 格式化地址显示
     */
    String formatAddress(LocationInfo location);

    /**
     * 获取城市列表统计
     */
    List<String> getAllCities();

    /**
     * 根据省份获取城市列表
     */
    List<String> getCitiesByProvince(String province);

    /**
     * 获取热力图数据
     */
    List<Map<String, Object>> getLocationHeatMapData(String city);
} 