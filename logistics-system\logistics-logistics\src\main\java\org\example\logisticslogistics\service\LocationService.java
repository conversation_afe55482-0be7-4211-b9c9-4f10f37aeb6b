package org.example.logisticslogistics.service;

import org.example.logisticslogistics.entity.LocationInfo;
import org.example.logisticslogistics.entity.TrackingInfo;

import java.util.List;
import java.util.Map;

/**
 * 位置管理服务接口
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
public interface LocationService {

    /**
     * 根据经纬度范围查询轨迹信息
     */
    List<TrackingInfo> findByLocationRange(double minLng, double maxLng, double minLat, double maxLat);

    /**
     * 查询指定坐标点附近的轨迹信息
     */
    List<TrackingInfo> findNearby(double longitude, double latitude, double radiusKm);

    /**
     * 根据城市查询轨迹信息
     */
    List<TrackingInfo> findByCity(String city);

    /**
     * 计算两点间距离（公里）
     */
    Double calculateDistance(LocationInfo from, LocationInfo to);

    /**
     * 验证经纬度坐标是否有效
     */
    boolean isValidCoordinate(double longitude, double latitude);

    /**
     * 解析地址信息
     */
    LocationInfo parseAddress(String address);

    /**
     * 格式化地址显示
     */
    String formatAddress(LocationInfo location);

    /**
     * 获取城市列表统计
     */
    List<String> getAllCities();

    /**
     * 根据省份获取城市列表
     */
    List<String> getCitiesByProvince(String province);

    /**
     * 获取热力图数据
     */
    List<Map<String, Object>> getLocationHeatMapData(String city);

    // ========== 新增地点选择功能 ==========

    /**
     * 根据关键词搜索地点（集成高德地图API）
     */
    List<Map<String, Object>> searchLocationsByKeyword(String keyword, String city, Integer limit);

    /**
     * 逆地理编码 - 根据坐标获取地址信息
     */
    Map<String, Object> reverseGeocode(Double longitude, Double latitude);

    /**
     * 地理编码 - 根据地址获取坐标信息
     */
    Map<String, Object> geocode(String address);

    /**
     * 获取配送员可选择的地点列表
     * 包括：当前位置、网点位置、常用地点、附近地标
     */
    List<Map<String, Object>> getCourierAvailableLocations(Long courierId);

    /**
     * 获取操作员可选择的网点列表
     */
    List<Map<String, Object>> getOperatorAvailableStations(Long operatorId);

    /**
     * 保存用户选择的地点到常用地点
     */
    boolean saveFrequentLocation(Long userId, String locationName, String address,
                                Double longitude, Double latitude, String locationType);

    /**
     * 获取用户常用地点列表
     */
    List<Map<String, Object>> getUserFrequentLocations(Long userId);

    /**
     * 验证地点是否在配送范围内
     */
    boolean isLocationInDeliveryRange(Double longitude, Double latitude, String serviceArea);

    /**
     * 获取网点附近的地标和重要地点
     */
    List<Map<String, Object>> getNearbyLandmarks(String stationCode, Integer radiusKm);

    /**
     * 更新配送员当前位置（支持手动选择）
     */
    boolean updateCourierLocation(Long courierId, Double longitude, Double latitude,
                                 String address, String updateType);

    /**
     * 获取两点间的路径规划
     */
    Map<String, Object> getRoutePlanning(Double fromLng, Double fromLat,
                                        Double toLng, Double toLat, String strategy);
}