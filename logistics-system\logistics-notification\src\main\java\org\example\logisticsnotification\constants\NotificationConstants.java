package org.example.logisticsnotification.constants;

/**
 * 通知常量
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
public class NotificationConstants {

    // 队列名称
    public static final String ORDER_NOTIFICATION_QUEUE = "order.notification.queue";
    public static final String DELIVERY_NOTIFICATION_QUEUE = "delivery.notification.queue";
    public static final String SMS_NOTIFICATION_QUEUE = "sms.notification.queue";
    public static final String EMAIL_NOTIFICATION_QUEUE = "email.notification.queue";
    public static final String PUSH_NOTIFICATION_QUEUE = "push.notification.queue";

    // 交换机名称
    public static final String NOTIFICATION_EXCHANGE = "notification.exchange";

    // 路由键
    public static final String ORDER_ROUTING_KEY = "order.notification";
    public static final String DELIVERY_ROUTING_KEY = "delivery.notification";
    public static final String SMS_ROUTING_KEY = "sms.notification";
    public static final String EMAIL_ROUTING_KEY = "email.notification";
    public static final String PUSH_ROUTING_KEY = "push.notification";

    // Redis键前缀
    public static final String NOTIFICATION_CACHE_PREFIX = "notification:";
    public static final String TEMPLATE_CACHE_PREFIX = "template:";
    public static final String SMS_LIMIT_PREFIX = "sms:limit:";
    public static final String EMAIL_LIMIT_PREFIX = "email:limit:";

    // 默认重试次数
    public static final int DEFAULT_RETRY_COUNT = 3;

    // 限流配置
    public static final int SMS_DAILY_LIMIT = 100; // 每日短信限制
    public static final int EMAIL_DAILY_LIMIT = 500; // 每日邮件限制

    // 模板变量占位符
    public static final String TEMPLATE_VAR_PREFIX = "#{";
    public static final String TEMPLATE_VAR_SUFFIX = "}";
}