import http from '@/utils/http'
import type { ApiResponse } from '@/types/api'

// 操作员统计数据
export interface OperatorStats {
  pendingOrders: number
  todayProcessed: number
  onlineCouriers: number
  exceptionOrders: number
  todayRevenue: number
  customerSatisfaction: number
}

// 订单调度信息
export interface OrderDispatch {
  id: number
  orderNumber: string
  customerName: string
  customerPhone: string
  pickupAddress: string
  deliveryAddress: string
  status: string
  priority: 'normal' | 'urgent' | 'emergency'
  estimatedTime: string
  weight: number
  value: number
  courierId?: number
  courierName?: string
  createTime: string
  updateTime: string
  notes?: string
}

// 配送员信息
export interface CourierInfo {
  id: number
  name: string
  phone: string
  status: 'online' | 'busy' | 'offline' | 'rest'
  location: string
  currentOrders: number
  todayCompleted: number
  rating: number
  workingHours: string
  lastActiveTime: string
}

// 客户服务工单
export interface ServiceTicket {
  id: number
  ticketNumber: string
  orderNumber: string
  customerName: string
  customerPhone: string
  type: 'complaint' | 'inquiry' | 'lost' | 'damage' | 'delay'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  status: 'open' | 'processing' | 'resolved' | 'closed'
  description: string
  response?: string
  assignedTo?: string
  createTime: string
  updateTime: string
  resolveTime?: string
}

// 调度记录
export interface DispatchRecord {
  id: number
  orderNumber: string
  fromCourier?: string
  toCourier: string
  reason: string
  operatorName: string
  createTime: string
  status: 'success' | 'failed' | 'pending'
}

// 操作员API - 使用真实的后端接口
export const operatorApi = {
  // 获取统计数据 - 简化响应处理
  async getStats(): Promise<ApiResponse<OperatorStats>> {
    try {
      // 使用操作员统计接口
      const operatorStatsResponse = await http.get('/order/operator/statistics')

      // 获取配送员状态统计
      const courierStatsResponse = await http.get('/delivery/courier/statistics/status')

      if (operatorStatsResponse.code === 200) {
        const operatorStats = operatorStatsResponse.data
        const courierStats = courierStatsResponse.code === 200 ? courierStatsResponse.data : {}

        const stats: OperatorStats = {
          pendingOrders: operatorStats.pendingCount || 0,
          todayProcessed: operatorStats.todayProcessed || 0,
          onlineCouriers: Object.values(courierStats).reduce(
            (sum: number, count: any) => sum + (Number(count) || 0),
            0,
          ),
          exceptionOrders: operatorStats.exceptionCount || 0,
          todayRevenue: operatorStats.todayRevenue || 0,
          customerSatisfaction: 95, // 暂时固定值
        }

        return {
          code: 200,
          message: '获取统计数据成功',
          data: stats,
        } as any
      }

      throw new Error('获取统计数据失败')
    } catch (error) {
      console.error('获取统计数据失败:', error)
      throw error
    }
  },

  // 获取待处理订单 - 使用真实的订单接口
  getPendingOrders(params?: {
    page?: number
    size?: number
    priority?: string
    status?: string
    keyword?: string
  }): Promise<ApiResponse<{ list: OrderDispatch[]; total: number }>> {
    return http
      .get('/order/page', {
        params: {
          pageNum: params?.page || 1,
          pageSize: params?.size || 10,
          orderStatus: params?.status || 'PENDING', // 只获取待处理订单
          ...params,
        },
      })
      .then((response) => {
        if (response.code === 200) {
          const page = response.data
          return {
            data: {
              code: 200,
              message: '获取待处理订单成功',
              data: {
                list: page.records.map((order: any) => ({
                  id: order.id,
                  orderNumber: order.orderNumber,
                  customerName: order.receiverName || '未知',
                  customerPhone: order.receiverPhone || '',
                  pickupAddress: order.senderAddress || '',
                  deliveryAddress: order.receiverAddress || '',
                  status: order.orderStatus,
                  priority: order.priority || 'normal',
                  estimatedTime: order.estimatedDeliveryTime || '',
                  weight: order.weight || 0,
                  value: order.totalAmount || 0,
                  courierId: order.courierId,
                  courierName: order.courierName,
                  createTime: order.createTime,
                  updateTime: order.updateTime,
                  notes: order.remark,
                })),
                total: page.total,
              },
            },
          } as any
        }
        throw new Error('获取订单失败')
      })
  },

  // 获取配送员列表 - 使用真实的配送员接口
  getCouriers(params?: {
    status?: number
    workArea?: string
    name?: string
    phone?: string
  }): Promise<ApiResponse<CourierInfo[]>> {
    return http
      .get('/delivery/courier/available', {
        params: {
          workArea: params?.workArea,
          status: params?.status || 1, // 确保查询在线配送员
        },
      })
      .then((response) => {
        // 修复响应格式检查
        if (response.code === 200) {
          return response
        }
        throw new Error('获取配送员列表失败')
      })
  },

  // 获取可分配的配送员 - 使用真实接口
  getAvailableCouriers(location?: string): Promise<ApiResponse<CourierInfo[]>> {
    return http
      .get('/delivery/courier/available', {
        params: { workArea: location },
      })
      .then((response) => {
        if (response.code === 200) {
          const couriers = response.data
          return {
            data: {
              code: 200,
              message: '获取可用配送员成功',
              data: couriers.map((courier: any) => ({
                id: courier.id,
                name: courier.courierName || courier.realName,
                phone: courier.phone,
                status: 'online',
                location: courier.workArea || '',
                currentOrders: courier.currentOrderCount || 0,
                todayCompleted: courier.todayCompletedCount || 0,
                rating: courier.rating || 5.0,
                workingHours: courier.workingHours || '09:00-18:00',
                lastActiveTime: courier.lastActiveTime || courier.updateTime,
              })),
            },
          } as any
        }
        throw new Error('获取可用配送员失败')
      })
  },

  // 分配订单给配送员（修复：使用正确的参数格式）
  assignOrder(orderId: number, courierId: number, notes?: string): Promise<ApiResponse<boolean>> {
    console.log(`🚀 开始分配订单 ${orderId} 给配送员 ${courierId}`)
    
    return http.put(`/order/${orderId}/assign-delivery`, null, {
      params: { 
        courierId: courierId
      }
    }).then((response) => {
      console.log('✅ 分配响应:', response)
      
      // 检查响应格式 - 兼容不同的响应格式
      if (response && (response.code === 200 || response.status === 200)) {
        console.log('✅ 订单分配成功')
        return {
          code: 200,
          message: '订单分配成功',
          data: true
        } as ApiResponse<boolean>
      } else {
        console.error('❌ 分配失败：响应格式异常', response)
        throw new Error('分配失败：响应格式异常')
      }
    }).catch((error) => {
      console.error('❌ 分配失败:', error)
      console.error('❌ 错误详情:', {
        orderId,
        courierId,
        url: `/order/${orderId}/assign-delivery`,
        params: { courierId },
        error: error.message,
        response: error.response?.data
      })
      throw error
    })
  },

  // 重新分配订单
  reassignOrder(orderId: number, newCourierId: number, reason: string): Promise<ApiResponse<void>> {
    // 先取消当前分配，再重新分配
    return http.put(`/order/${orderId}/assign-delivery`, null, {
      params: { courierId: newCourierId },
    })
  },

  // 更新订单优先级
  updateOrderPriority(orderId: number, priority: string): Promise<ApiResponse<void>> {
    // 这个功能需要在订单服务中添加，暂时返回成功
    return Promise.resolve({
      data: {
        code: 200,
        message: '更新优先级成功',
        data: null,
      },
    } as any)
  },

  // 更新配送员状态 - 使用真实接口
  updateCourierStatus(courierId: number, status: string): Promise<ApiResponse<void>> {
    const statusMap: Record<string, number> = {
      online: 1,
      offline: 0,
      busy: 2,
      rest: 3,
    }

    return http.put(`/delivery/courier/${courierId}/status`, null, {
      params: { status: statusMap[status] || 0 },
    })
  },

  // 获取配送员详细信息
  getCourierDetail(courierId: number): Promise<
    ApiResponse<
      CourierInfo & {
        todayOrders: OrderDispatch[]
        recentLocations: Array<{ time: string; location: string }>
      }
    >
  > {
    return http.get(`/delivery/courier/${courierId}`).then((response) => {
      if (response.code === 200) {
        const courier = response.data
        return {
          data: {
            code: 200,
            message: '获取配送员详情成功',
            data: {
              id: courier.id,
              name: courier.courierName || courier.realName,
              phone: courier.phone,
              status: courier.status === 1 ? 'online' : 'offline',
              location: courier.workArea || '',
              currentOrders: courier.currentOrderCount || 0,
              todayCompleted: courier.todayCompletedCount || 0,
              rating: courier.rating || 5.0,
              workingHours: courier.workingHours || '09:00-18:00',
              lastActiveTime: courier.lastActiveTime || courier.updateTime,
              todayOrders: [], // 需要额外查询
              recentLocations: [], // 需要额外查询
            },
          },
        } as any
      }
      throw new Error('获取配送员详情失败')
    })
  },

  // 获取实时配送地图数据
  getDeliveryMapData(): Promise<
    ApiResponse<{
      couriers: Array<{
        id: number
        name: string
        lat: number
        lng: number
        status: string
        currentOrder?: string
      }>
      orders: Array<{
        id: number
        orderNumber: string
        pickupLat: number
        pickupLng: number
        deliveryLat: number
        deliveryLng: number
        status: string
      }>
    }>
  > {
    // 这个需要组合多个接口的数据
    return Promise.all([
      http.get('/delivery/courier/available'),
      http.get('/order/page', { params: { pageNum: 1, pageSize: 50, orderStatus: 'PROCESSING' } }),
    ]).then(([courierResponse, orderResponse]) => {
      const couriers = courierResponse.code === 200 ? courierResponse.data : []
      const orders = orderResponse.code === 200 ? orderResponse.data.records : []

      return {
        data: {
          code: 200,
          message: '获取地图数据成功',
          data: {
            couriers: couriers.map((courier: any) => ({
              id: courier.id,
              name: courier.courierName || courier.realName,
              lat: courier.currentLatitude || 39.9042,
              lng: courier.currentLongitude || 116.4074,
              status: courier.status === 1 ? 'online' : 'offline',
              currentOrder: courier.currentOrderNumber,
            })),
            orders: orders.map((order: any) => ({
              id: order.id,
              orderNumber: order.orderNumber,
              pickupLat: order.senderLatitude || 39.9042,
              pickupLng: order.senderLongitude || 116.4074,
              deliveryLat: order.receiverLatitude || 39.9042,
              deliveryLng: order.receiverLongitude || 116.4074,
              status: order.orderStatus,
            })),
          },
        },
      } as any
    })
  },
  // 获取系统告警
  getSystemAlerts(): Promise<ApiResponse<any[]>> {
    return Promise.resolve({
      code: 200,
      message: '获取系统告警成功',
      data: [],
    } as any)
  },

  // 解决告警
  resolveAlert(alertId: number): Promise<ApiResponse<void>> {
    return Promise.resolve({
      code: 200,
      message: '告警已解决',
      data: null,
    } as any)
  },

  // 紧急调度
  emergencyDispatch(params: {
    reason: string
    priority: 'high' | 'urgent'
    orderIds: number[]
  }): Promise<ApiResponse<void>> {
    return Promise.resolve({
      code: 200,
      message: '紧急调度成功',
      data: null,
    } as any)
  },

  // 创建服务工单
  createServiceTicket(ticket: Partial<ServiceTicket>): Promise<ApiResponse<ServiceTicket>> {
    return Promise.resolve({
      code: 200,
      message: '创建工单成功',
      data: {
        id: Date.now(),
        ticketNumber: `TK${Date.now()}`,
        ...ticket,
        status: 'open',
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString(),
      } as ServiceTicket,
    } as any)
  },

  // 处理服务工单
  handleServiceTicket(
    ticketId: number,
    params: {
      status: string
      response: string
    },
  ): Promise<ApiResponse<void>> {
    return Promise.resolve({
      code: 200,
      message: '工单处理成功',
      data: null,
    } as any)
  },
  // 批量分配订单（修复：改用逐个分配方式，兼容现有后端）
  async batchAssignOrders(assignments: Array<{ orderId: number; courierId: number }>): Promise<ApiResponse<any>> {
    console.log('🚀 开始批量分配:', assignments)

    let successCount = 0
    let failCount = 0
    const results = []

    // 逐个分配，确保每个订单都正确处理
    for (const assignment of assignments) {
      try {
        console.log(`📝 分配订单 ${assignment.orderId} 给配送员 ${assignment.courierId}`)
        
        // 直接调用HTTP接口
        const response = await http.put(`/order/${assignment.orderId}/assign-delivery`, null, {
          params: { courierId: assignment.courierId }
        })
        
        console.log(`✅ 订单 ${assignment.orderId} 分配成功:`, response)
        results.push({ success: true, orderId: assignment.orderId, response })
        successCount++
        
      } catch (error) {
        console.error(`❌ 订单 ${assignment.orderId} 分配失败:`, error)
        results.push({ success: false, orderId: assignment.orderId, error })
        failCount++
      }
    }

    console.log(`🎯 批量分配结果: 成功 ${successCount} 个，失败 ${failCount} 个`)

    if (successCount > 0) {
      return {
        code: 200,
        message: failCount > 0
          ? `部分分配成功：成功 ${successCount} 个，失败 ${failCount} 个`
          : `批量分配成功：共 ${successCount} 个订单`,
        data: { successCount, failCount, results },
      } as ApiResponse<any>
    } else {
      throw new Error('所有订单分配都失败了')
    }
  },

  // 获取服务工单列表 - 调用真实的后端接口
  getServiceTickets(params?: {
    page?: number
    size?: number
    status?: string
    type?: string
    priority?: string
    keyword?: string
  }): Promise<ApiResponse<{ list: ServiceTicket[]; total: number }>> {
    return http.get('/notification/service-tickets', {
      params: {
        pageNum: params?.page || 1,
        pageSize: params?.size || 20,
        status: params?.status,
        type: params?.type,
        priority: params?.priority,
        keyword: params?.keyword,
      },
    }).then((response) => {
      if (response.code === 200) {
        const data = response.data
        return {
          code: 200,
          message: '获取工单列表成功',
          data: {
            list: (data.records || data.list || []).map((ticket: any) => ({
              id: ticket.id,
              ticketNumber: ticket.ticketNumber,
              orderNumber: ticket.orderNumber,
              customerName: ticket.customerName,
              customerPhone: ticket.customerPhone,
              type: ticket.type,
              priority: ticket.priority,
              status: ticket.status,
              description: ticket.description,
              response: ticket.response,
              assignedTo: ticket.assignedTo,
              createTime: ticket.createTime,
              updateTime: ticket.updateTime,
              resolveTime: ticket.resolveTime,
            })),
            total: data.total || 0,
          },
        } as any
      }
      throw new Error(response.message || '获取工单列表失败')
    })
  },

  // 获取调度记录列表 - 调用真实的后端接口
  getDispatchRecords(params?: {
    page?: number
    size?: number
    orderNumber?: string
    courier?: string
    operator?: string
    status?: string
    startTime?: string
    endTime?: string
  }): Promise<ApiResponse<{ list: DispatchRecord[]; total: number }>> {
    return http.get('/order/dispatch-records', {
      params: {
        pageNum: params?.page || 1,
        pageSize: params?.size || 20,
        orderNumber: params?.orderNumber,
        courier: params?.courier,
        operator: params?.operator,
        status: params?.status,
        startTime: params?.startTime,
        endTime: params?.endTime,
      },
    }).then((response) => {
      if (response.code === 200) {
        const data = response.data
        return {
          code: 200,
          message: '获取调度记录成功',
          data: {
            list: (data.records || data.list || []).map((record: any) => ({
              id: record.id,
              orderNumber: record.orderNumber,
              fromCourier: record.fromCourier,
              toCourier: record.toCourier,
              reason: record.reason,
              operatorName: record.operatorName,
              createTime: record.createTime,
              status: record.status,
            })),
            total: data.total || 0,
          },
        } as any
      }
      throw new Error(response.message || '获取调度记录失败')
    })
  },
}
// 辅助函数：数字状态转字符串
function getStatusText(status: number): 'online' | 'busy' | 'offline' | 'rest' {
  const statusMap: Record<number, 'online' | 'busy' | 'offline' | 'rest'> = {
    0: 'offline',
    1: 'online',
    2: 'busy',
    3: 'rest',
  }
  return statusMap[status] || 'offline'
}
