<template>
  <div class="order-create">
    <div class="page-header">
      <h2>创建订单</h2>
      <p>请填写订单信息，我们将为您安排快递服务</p>
    </div>

    <el-form ref="orderFormRef" :model="orderForm" :rules="orderRules" label-width="100px">
      <!-- 寄件人信息 -->
      <el-card class="form-section">
        <template #header>
          <div class="section-header">
            <el-icon><User /></el-icon>
            <span>寄件人信息</span>
            <el-button type="text" @click="showSenderAddressDialog = true">
              从地址簿选择
            </el-button>
          </div>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="寄件人" prop="senderName">
              <el-input v-model="orderForm.senderName" placeholder="请输入寄件人姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="senderPhone">
              <el-input v-model="orderForm.senderPhone" placeholder="请输入手机号" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="省份" prop="senderProvince">
              <el-select
                v-model="orderForm.senderProvince"
                placeholder="请选择省份"
                @change="onSenderProvinceChange"
              >
                <el-option
                  v-for="province in provinces"
                  :key="province.code"
                  :label="province.name"
                  :value="province.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="城市" prop="senderCity">
              <el-select
                v-model="orderForm.senderCity"
                placeholder="请选择城市"
                @change="onSenderCityChange"
              >
                <el-option
                  v-for="city in senderCities"
                  :key="city.code"
                  :label="city.name"
                  :value="city.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="区/县" prop="senderDistrict">
              <el-select v-model="orderForm.senderDistrict" placeholder="请选择区/县">
                <el-option
                  v-for="district in senderDistricts"
                  :key="district.code"
                  :label="district.name"
                  :value="district.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="详细地址" prop="senderAddress">
          <el-input v-model="orderForm.senderAddress" placeholder="请输入详细地址" />
        </el-form-item>
      </el-card>

      <!-- 收件人信息 -->
      <el-card class="form-section">
        <template #header>
          <div class="section-header">
            <el-icon><LocationInformation /></el-icon>
            <span>收件人信息</span>
            <el-button type="text" @click="showReceiverAddressDialog = true">
              从地址簿选择
            </el-button>
          </div>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="收件人" prop="receiverName">
              <el-input v-model="orderForm.receiverName" placeholder="请输入收件人姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="receiverPhone">
              <el-input v-model="orderForm.receiverPhone" placeholder="请输入手机号" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="省份" prop="receiverProvince">
              <el-select
                v-model="orderForm.receiverProvince"
                placeholder="请选择省份"
                @change="onReceiverProvinceChange"
              >
                <el-option
                  v-for="province in provinces"
                  :key="province.code"
                  :label="province.name"
                  :value="province.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="城市" prop="receiverCity">
              <el-select
                v-model="orderForm.receiverCity"
                placeholder="请选择城市"
                @change="onReceiverCityChange"
              >
                <el-option
                  v-for="city in receiverCities"
                  :key="city.code"
                  :label="city.name"
                  :value="city.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="区/县" prop="receiverDistrict">
              <el-select v-model="orderForm.receiverDistrict" placeholder="请选择区/县">
                <el-option
                  v-for="district in receiverDistricts"
                  :key="district.code"
                  :label="district.name"
                  :value="district.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="详细地址" prop="receiverAddress">
          <el-input v-model="orderForm.receiverAddress" placeholder="请输入详细地址" />
        </el-form-item>
      </el-card>

      <!-- 物品信息 -->
      <el-card class="form-section">
        <template #header>
          <div class="section-header">
            <el-icon><Box /></el-icon>
            <span>物品信息</span>
          </div>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="物品名称" prop="itemName">
              <el-input v-model="orderForm.itemName" placeholder="请输入物品名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物品类型" prop="itemType">
              <el-select v-model="orderForm.itemType" placeholder="请选择物品类型">
                <el-option label="文件" value="文件" />
                <el-option label="数码产品" value="数码产品" />
                <el-option label="服装" value="服装" />
                <el-option label="食品" value="食品" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="重量(kg)" prop="itemWeight">
              <el-input-number
                v-model="orderForm.itemWeight"
                :min="0.1"
                :step="0.1"
                :precision="1"
                style="width: 100%"
                @change="calculateFee"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="长(cm)" prop="itemLength">
              <el-input-number
                v-model="orderForm.itemLength"
                :min="1"
                style="width: 100%"
                @change="calculateVolume"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="宽(cm)" prop="itemWidth">
              <el-input-number
                v-model="orderForm.itemWidth"
                :min="1"
                style="width: 100%"
                @change="calculateVolume"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="高(cm)" prop="itemHeight">
              <el-input-number
                v-model="orderForm.itemHeight"
                :min="1"
                style="width: 100%"
                @change="calculateVolume"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="价值(元)" prop="itemValue">
              <el-input-number
                v-model="orderForm.itemValue"
                :min="0"
                :step="10"
                style="width: 100%"
                @change="calculateFee"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="易碎品">
              <el-switch v-model="orderForm.isFragile" @change="calculateFee" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 服务选择 -->
      <el-card class="form-section">
        <template #header>
          <div class="section-header">
            <el-icon><Van /></el-icon>
            <span>服务选择</span>
          </div>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="服务类型" prop="serviceType">
              <el-radio-group v-model="orderForm.serviceType" @change="calculateFee">
                <el-radio value="STANDARD">标准快递</el-radio>
                <el-radio value="EXPRESS">特快专递</el-radio>
                <el-radio value="URGENT">加急服务</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="支付方式" prop="paymentMethod">
              <el-radio-group v-model="orderForm.paymentMethod">
                <el-radio value="ONLINE">在线支付</el-radio>
                <el-radio value="COD">货到付款</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注">
          <el-input
            v-model="orderForm.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（选填）"
          />
        </el-form-item>
      </el-card>

      <!-- 费用信息 -->
      <el-card class="form-section">
        <template #header>
          <div class="section-header">
            <el-icon><Money /></el-icon>
            <span>费用信息</span>
            <el-button type="text" @click="calculateFee" :loading="feeCalculating">
              重新计算
            </el-button>
          </div>
        </template>

        <div class="fee-info">
          <div class="fee-item">
            <span>运费：</span>
            <span class="fee-value">¥{{ feeInfo.shippingFee.toFixed(2) }}</span>
          </div>
          <div class="fee-item">
            <span>保险费：</span>
            <span class="fee-value">¥{{ feeInfo.insuranceFee.toFixed(2) }}</span>
          </div>
          <div class="fee-item">
            <span>包装费：</span>
            <span class="fee-value">¥{{ feeInfo.packingFee.toFixed(2) }}</span>
          </div>
          <div class="fee-item total">
            <span>总计：</span>
            <span class="fee-value">¥{{ feeInfo.totalFee.toFixed(2) }}</span>
          </div>
          <div class="estimated-time" v-if="feeInfo.estimatedDeliveryTime">
            <span>预计送达：{{ feeInfo.estimatedDeliveryTime }}</span>
          </div>
        </div>
      </el-card>

      <!-- 提交按钮 -->
      <div class="form-actions">
        <el-button size="large" @click="$router.back()">取消</el-button>
        <el-button type="primary" size="large" :loading="submitting" @click="submitOrder">
          创建订单
        </el-button>
      </div>
    </el-form>

    <!-- 地址选择对话框 -->
    <AddressSelectDialog
      v-model="showSenderAddressDialog"
      title="选择寄件地址"
      :address-type="2"
      @select="onSenderAddressSelect"
    />

    <AddressSelectDialog
      v-model="showReceiverAddressDialog"
      title="选择收件地址"
      :address-type="1"
      @select="onReceiverAddressSelect"
    />
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { User, LocationInformation, Box, Van, Money } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { orderApi, type CalculateFeeResponse } from '@/api/order'
import { addressApi } from '@/api/address'
import type { RegionData, Address } from '@/types/address'
import AddressSelectDialog from '../components/AddressSelectDialog.vue'
import { OrderLifecycleManager } from '@/utils/orderLifecycle'

const router = useRouter()

// 表单引用
const orderFormRef = ref<FormInstance>()

// 状态
const submitting = ref(false)
const feeCalculating = ref(false)
const showSenderAddressDialog = ref(false)
const showReceiverAddressDialog = ref(false)

// 省市区数据
const provinces = ref<RegionData[]>([])
const senderCities = ref<RegionData[]>([])
const senderDistricts = ref<RegionData[]>([])
const receiverCities = ref<RegionData[]>([])
const receiverDistricts = ref<RegionData[]>([])

// 表单数据 - 使用any类型避免类型错误
const orderForm = reactive<any>({
  senderName: '',
  senderPhone: '',
  senderAddress: '',
  senderProvince: '',
  senderCity: '',
  senderDistrict: '',
  receiverName: '',
  receiverPhone: '',
  receiverAddress: '',
  receiverProvince: '',
  receiverCity: '',
  receiverDistrict: '',
  itemName: '',
  itemType: '其他',
  itemWeight: 1,
  itemLength: 10,
  itemWidth: 10,
  itemHeight: 10,
  itemVolume: 0.001,
  itemValue: 100,
  isFragile: false,
  serviceType: 'STANDARD',
  paymentMethod: 'ONLINE',
  remarks: '',
})

// 费用信息
const feeInfo = ref<CalculateFeeResponse>({
  shippingFee: 0,
  insuranceFee: 0,
  packingFee: 0,
  totalFee: 0,
  estimatedDeliveryTime: '',
})

// 表单验证规则
const orderRules: FormRules = {
  senderName: [{ required: true, message: '请输入寄件人姓名', trigger: 'blur' }],
  senderPhone: [
    { required: true, message: '请输入寄件人手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
  ],
  senderProvince: [{ required: true, message: '请选择省份', trigger: 'change' }],
  senderCity: [{ required: true, message: '请选择城市', trigger: 'change' }],
  senderDistrict: [{ required: true, message: '请选择区域', trigger: 'change' }],
  senderAddress: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
  receiverName: [{ required: true, message: '请输入收件人姓名', trigger: 'blur' }],
  receiverPhone: [
    { required: true, message: '请输入收件人手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
  ],
  receiverProvince: [{ required: true, message: '请选择省份', trigger: 'change' }],
  receiverCity: [{ required: true, message: '请选择城市', trigger: 'change' }],
  receiverDistrict: [{ required: true, message: '请选择区域', trigger: 'change' }],
  receiverAddress: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
  itemName: [{ required: true, message: '请输入物品名称', trigger: 'blur' }],
  itemType: [{ required: true, message: '请选择物品类型', trigger: 'change' }],
  itemWeight: [{ required: true, message: '请输入物品重量', trigger: 'blur' }],
  serviceType: [{ required: true, message: '请选择服务类型', trigger: 'change' }],
  paymentMethod: [{ required: true, message: '请选择支付方式', trigger: 'change' }],
}

// 计算体积
const calculateVolume = () => {
  if (orderForm.itemLength && orderForm.itemWidth && orderForm.itemHeight) {
    orderForm.itemVolume =
      (orderForm.itemLength * orderForm.itemWidth * orderForm.itemHeight) / 1000000 // 转换为立方米
    calculateFee()
  }
}

// 计算费用
const calculateFee = async () => {
  // 确保必要的信息都已填写
  if (
    !orderForm.senderCity ||
    !orderForm.receiverCity ||
    !orderForm.senderName ||
    !orderForm.senderPhone ||
    !orderForm.receiverName ||
    !orderForm.receiverPhone ||
    !orderForm.itemName
  ) {
    console.log('必要信息不完整，跳过费用计算')
    return
  }

  feeCalculating.value = true
  try {
    console.log('开始计算费用，使用完整表单数据')

    const response = await orderApi.calculateFee({
      // 寄件人信息 - 使用真实表单数据
      senderName: orderForm.senderName,
      senderPhone: orderForm.senderPhone,
      senderProvince: orderForm.senderProvince,
      senderCity: orderForm.senderCity,
      senderDistrict: orderForm.senderDistrict,
      senderAddress: orderForm.senderAddress,

      // 收件人信息 - 使用真实表单数据
      receiverName: orderForm.receiverName,
      receiverPhone: orderForm.receiverPhone,
      receiverProvince: orderForm.receiverProvince,
      receiverCity: orderForm.receiverCity,
      receiverDistrict: orderForm.receiverDistrict,
      receiverAddress: orderForm.receiverAddress,

      // 物品信息 - 使用真实表单数据
      itemName: orderForm.itemName,
      itemType: orderForm.itemType,
      itemWeight: orderForm.itemWeight,
      itemVolume: orderForm.itemVolume,
      itemValue: orderForm.itemValue,
      isFragile: orderForm.isFragile,

      // 服务信息 - 使用真实表单数据
      serviceType: orderForm.serviceType,
      paymentMethod: orderForm.paymentMethod,
    })

    console.log('费用计算响应:', response)
    if (response.data.code === 200) {
      feeInfo.value = response.data.data
    } else {
      console.error('费用计算失败:', response.data.message)
      ElMessage.error(response.data.message || '费用计算失败')
    }
  } catch (error) {
    console.error('费用计算失败:', error)
    ElMessage.error('费用计算失败，请检查表单信息是否完整')
  } finally {
    feeCalculating.value = false
  }
}

// 省份变化处理 - 支持预设城市值
const onSenderProvinceChange = (province: string, presetCity?: string) => {
  if (!presetCity) {
    orderForm.senderCity = ''
    orderForm.senderDistrict = ''
  }
  const selectedProvince = provinces.value.find((p) => p.name === province)
  senderCities.value = selectedProvince?.children || []
  if (!presetCity) {
    senderDistricts.value = []
  }
}

const onSenderCityChange = (city: string, presetDistrict?: string) => {
  if (!presetDistrict) {
    orderForm.senderDistrict = ''
  }
  const selectedCity = senderCities.value.find((c) => c.name === city)
  senderDistricts.value = selectedCity?.children || []
  calculateFee()
}

// 对receiver也做同样的修改
const onReceiverProvinceChange = (province: string, presetCity?: string) => {
  if (!presetCity) {
    orderForm.receiverCity = ''
    orderForm.receiverDistrict = ''
  }
  const selectedProvince = provinces.value.find((p) => p.name === province)
  receiverCities.value = selectedProvince?.children || []
  if (!presetCity) {
    receiverDistricts.value = []
  }
}

const onReceiverCityChange = (city: string, presetDistrict?: string) => {
  if (!presetDistrict) {
    orderForm.receiverDistrict = ''
  }
  const selectedCity = receiverCities.value.find((c) => c.name === city)
  receiverDistricts.value = selectedCity?.children || []
  calculateFee()
}

// 然后修改地址选择处理函数
const onSenderAddressSelect = (address: Address) => {
  orderForm.senderName = address.contactName
  orderForm.senderPhone = address.contactPhone
  orderForm.senderProvince = address.province
  orderForm.senderCity = address.city
  orderForm.senderDistrict = address.district
  orderForm.senderAddress = address.detailedAddress

  // 使用预设值更新选项
  onSenderProvinceChange(address.province, address.city)
  onSenderCityChange(address.city, address.district)

  showSenderAddressDialog.value = false
}

const onReceiverAddressSelect = (address: Address) => {
  orderForm.receiverName = address.contactName
  orderForm.receiverPhone = address.contactPhone
  orderForm.receiverProvince = address.province
  orderForm.receiverCity = address.city
  orderForm.receiverDistrict = address.district
  orderForm.receiverAddress = address.detailedAddress

  // 使用预设值更新选项
  onReceiverProvinceChange(address.province, address.city)
  onReceiverCityChange(address.city, address.district)

  showReceiverAddressDialog.value = false
}

// 提交订单（修复：使用订单生命周期管理器）
const submitOrder = async () => {
  if (!orderFormRef.value) return

  await orderFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        console.log('开始创建订单并初始化轨迹...')
        
        // 使用订单生命周期管理器创建订单
        const order = await OrderLifecycleManager.createOrderWithTracking(orderForm)
        
        ElMessage.success('订单创建成功')
        console.log('订单创建成功，订单ID:', order.id, '订单号:', order.orderNumber)
        
        // 跳转到订单详情页面
        router.push(`/customer/order/detail/${order.id}`)
      } catch (error: any) {
        console.error('订单创建失败:', error)
        // 显示更详细的错误信息
        const errorMsg =
          error.response?.data?.message || error.message || '订单创建失败，请稍后重试'
        ElMessage.error(errorMsg)
      } finally {
        submitting.value = false
      }
    }
  })
}

// 加载省市区数据
const loadRegionData = async () => {
  try {
    const response = await addressApi.getRegionData()
    if (response.code === 200) {
      provinces.value = response.data
    }
  } catch (error) {
    console.error('加载省市区数据失败:', error)
    ElMessage.error('加载省市区数据失败')
  }
}

onMounted(() => {
  loadRegionData()
  calculateVolume()
})
</script>

<style scoped>
.order-create {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 10px 0;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
}

.form-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.fee-info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 6px;
}

.fee-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 14px;
}

.fee-item.total {
  font-size: 16px;
  font-weight: bold;
  border-top: 1px solid #e4e7ed;
  padding-top: 10px;
  margin-top: 10px;
}

.fee-value {
  color: #f56c6c;
  font-weight: bold;
}

.estimated-time {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #e4e7ed;
  text-align: center;
  color: #67c23a;
  font-size: 14px;
}

.form-actions {
  text-align: center;
  padding: 30px 0;
  border-top: 1px solid #e4e7ed;
  margin-top: 30px;
}

.form-actions .el-button {
  min-width: 120px;
  margin: 0 10px;
}
</style>
