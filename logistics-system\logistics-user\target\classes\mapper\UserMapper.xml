<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.example.logisticsuser.mapper.UserMapper">

    <!-- 用户基础结果映射 -->
    <resultMap id="BaseResultMap" type="org.example.logisticsuser.entity.User">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="real_name" property="realName" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="id_card" property="idCard" jdbcType="VARCHAR"/>
        <result column="user_type" property="userType" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="avatar" property="avatar" jdbcType="VARCHAR"/>
        <result column="last_login_time" property="lastLoginTime" jdbcType="TIMESTAMP"/>
        <result column="last_login_ip" property="lastLoginIp" jdbcType="VARCHAR"/>
        <result column="gender" property="gender" jdbcType="TINYINT"/>
        <result column="birthday" property="birthday" jdbcType="DATE"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>

    </resultMap>

    <!-- 根据用户名查询用户 -->
    <select id="selectByUsername" parameterType="string" resultMap="BaseResultMap">
        SELECT * FROM users
        WHERE username = #{username}
    </select>

    <!-- 根据手机号查询用户 -->
    <select id="selectByPhone" parameterType="string" resultMap="BaseResultMap">
        SELECT * FROM users
        WHERE phone = #{phone}
    </select>

    <!-- 根据邮箱查询用户 -->
    <select id="selectByEmail" parameterType="string" resultMap="BaseResultMap">
        SELECT * FROM users
        WHERE email = #{email}
    </select>

    <!-- 根据身份证号查询用户 -->
    <select id="selectByIdCard" parameterType="string" resultMap="BaseResultMap">
        SELECT * FROM users
        WHERE id_card = #{idCard}
    </select>

    <!-- 根据用户ID查询用户角色 -->
    <select id="selectRolesByUserId" parameterType="long" resultType="string">
        SELECT r.role_code
        FROM roles r
                 INNER JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND r.status = 'ACTIVE'
    </select>

    <!-- 根据用户ID查询用户权限 -->
    <select id="selectPermissionsByUserId" parameterType="long" resultType="string">
        SELECT DISTINCT p.permission_code
        FROM permissions p
                 INNER JOIN role_permissions rp ON p.id = rp.permission_id
                 INNER JOIN user_roles ur ON rp.role_id = ur.role_id
                 INNER JOIN roles r ON ur.role_id = r.id
        WHERE ur.user_id = #{userId}
          AND p.status = 'ACTIVE' AND r.status = 'ACTIVE'
    </select>

    <!-- 更新用户最后登录信息 -->
    <update id="updateLastLoginInfo">
        UPDATE users
        SET last_login_time = #{loginTime},
            last_login_ip = #{loginIp},
            update_time = NOW()
        WHERE id = #{userId}
    </update>

    <!-- 根据用户类型查询用户列表 -->
    <select id="selectByUserType" parameterType="string" resultMap="BaseResultMap">
        SELECT * FROM users
        WHERE user_type = #{userType}
        ORDER BY create_time DESC
    </select>

</mapper>