<template>
  <div class="admin-order-list">
    <div class="page-header">
      <h2>订单管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="exportOrders">
          <el-icon><Download /></el-icon>
          导出订单
        </el-button>
        <el-button @click="refreshList">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="订单号">
          <el-input
            v-model="searchForm.orderNumber"
            placeholder="输入订单号"
            style="width: 200px"
          />
        </el-form-item>

        <el-form-item label="订单状态">
          <el-select v-model="searchForm.orderStatus" placeholder="全部状态" clearable>
            <el-option label="待发货" value="PENDING" />
            <el-option label="已支付" value="PAID" />
            <el-option label="已揽收" value="PICKED_UP" />
            <el-option label="运输中" value="IN_TRANSIT" />
            <el-option label="派送中" value="OUT_FOR_DELIVERY" />
            <el-option label="已送达" value="DELIVERED" />
            <el-option label="已取消" value="CANCELLED" />
          </el-select>
        </el-form-item>

        <el-form-item label="支付状态">
          <el-select v-model="searchForm.paymentStatus" placeholder="全部" clearable>
            <el-option label="未支付" :value="0" />
            <el-option label="已支付" :value="1" />
            <el-option label="已退款" :value="2" />
          </el-select>
        </el-form-item>

        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <el-form-item label="用户ID">
          <el-input
            v-model="searchForm.userId"
            placeholder="用户ID"
            style="width: 120px"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 批量操作 -->
    <el-card class="batch-actions" v-if="selectedOrders.length > 0">
      <div class="batch-info">
        <span>已选择 {{ selectedOrders.length }} 个订单</span>
        <div class="batch-buttons">
          <el-button @click="batchUpdateStatus('PICKED_UP')">批量设为已揽收</el-button>
          <el-button @click="batchUpdateStatus('IN_TRANSIT')">批量设为运输中</el-button>
          <el-button @click="batchUpdateStatus('OUT_FOR_DELIVERY')">批量设为派送中</el-button>
          <el-button type="danger" @click="batchCancel">批量取消</el-button>
        </div>
      </div>
    </el-card>

    <!-- 订单列表 -->
    <el-card>
      <el-table
        :data="orderList"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="orderNumber" label="订单号" width="180">
          <template #default="{ row }">
            <el-link type="primary" @click="viewOrderDetail(row.id)">
              {{ row.orderNumber }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column label="用户信息" width="150">
          <template #default="{ row }">
            <div>
              <div>ID: {{ row.userId }}</div>
              <div class="text-gray">{{ row.senderName }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="收件信息" width="200">
          <template #default="{ row }">
            <div>
              <div>{{ row.receiverName }}</div>
              <div class="text-gray">{{ row.receiverPhone }}</div>
              <div class="text-gray">{{ row.receiverCity }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="itemName" label="物品" width="120" />

        <el-table-column label="服务类型" width="100">
          <template #default="{ row }">
            <el-tag size="small">{{ getServiceTypeText(row.serviceType) }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="金额" width="100">
          <template #default="{ row }">
            <div class="amount">¥{{ row.totalFee.toFixed(2) }}</div>
          </template>
        </el-table-column>

        <el-table-column label="支付状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getPaymentTagType(row.paymentStatus)" size="small">
              {{ getPaymentStatusText(row.paymentStatus) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="订单状态" width="120">
          <template #default="{ row }">
            <el-select
              v-model="row.orderStatus"
              size="small"
              @change="updateOrderStatus(row)"
              style="width: 100%"
            >
              <el-option label="待发货" value="PENDING" />
              <el-option label="已支付" value="PAID" />
              <el-option label="已揽收" value="PICKED_UP" />
              <el-option label="运输中" value="IN_TRANSIT" />
              <el-option label="派送中" value="OUT_FOR_DELIVERY" />
              <el-option label="已送达" value="DELIVERED" />
              <el-option label="已取消" value="CANCELLED" />
            </el-select>
          </template>
        </el-table-column>

        <el-table-column label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatTime(row.createTime) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" @click="viewOrderDetail(row.id)">详情</el-button>
            <el-button type="text" @click="viewTracking(row.orderNumber)">跟踪</el-button>
            <el-button type="text" @click="assignCourier(row)">分配配送员</el-button>
            <el-dropdown>
              <el-button type="text">
                更多<el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="editOrder(row)">编辑</el-dropdown-item>
                  <el-dropdown-item @click="printOrder(row)">打印</el-dropdown-item>
                  <el-dropdown-item @click="refundOrder(row)" divided>退款</el-dropdown-item>
                  <el-dropdown-item @click="deleteOrder(row)" class="danger">删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 分配配送员对话框 -->
    <el-dialog v-model="showAssignDialog" title="分配配送员" width="500px">
      <el-form :model="assignForm" label-width="80px">
        <el-form-item label="订单号">
          <el-input v-model="assignForm.orderNumber" readonly />
        </el-form-item>
        <el-form-item label="配送员" required>
          <el-select v-model="assignForm.courierId" placeholder="选择配送员" style="width: 100%">
            <el-option
              v-for="courier in courierList"
              :key="courier.id"
              :label="`${courier.name} (${courier.phone})`"
              :value="courier.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="assignForm.remarks" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAssignDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmAssign" :loading="assigning">确认分配</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Download,
  Refresh,
  ArrowDown,
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { orderApi } from '@/api/order'
import http from '@/utils/http'
import type {
  Order,
  OrderQueryParams,
  OrderStatus,
  ServiceType,
  PaymentStatus,
} from '@/types/order'
import dayjs from 'dayjs'

const router = useRouter()

// 状态
const loading = ref(false)
const assigning = ref(false)
const orderList = ref<Order[]>([])
const selectedOrders = ref<Order[]>([])
const total = ref(0)
const showAssignDialog = ref(false)

// 搜索表单
const searchForm = reactive({
  orderNumber: '',
  orderStatus: '',
  paymentStatus: '',
  userId: '',
})

// 日期范围
const dateRange = ref<[string, string] | null>(null)

// 分页
const pagination = reactive({
  current: 1,
  size: 20,
})

// 分配表单
const assignForm = reactive({
  orderNumber: '',
  orderId: 0,
  courierId: '',
  remarks: '',
})

// 配送员列表
const courierList = ref<any[]>([])

// 加载配送员列表
const loadCourierList = async () => {
  try {
    const response = await http.get('/delivery/couriers')
    if (response && response.code === 200) {
      courierList.value = response.data || []
    } else {
      console.error('获取配送员列表失败:', response?.message)
    }
  } catch (error) {
    console.error('获取配送员列表失败:', error)
    // 如果API失败，使用基础的配送员数据
    courierList.value = []
  }
}

// 计算搜索参数
const searchParams = computed((): OrderQueryParams => {
  const params: OrderQueryParams = {
    page: pagination.current,
    size: pagination.size,
  }

  if (searchForm.orderNumber) params.keyword = searchForm.orderNumber
  if (searchForm.orderStatus) params.orderStatus = searchForm.orderStatus as OrderStatus
  if (searchForm.paymentStatus !== '') params.paymentStatus = searchForm.paymentStatus
  if (searchForm.userId) params.userId = Number(searchForm.userId)

  if (dateRange.value) {
    params.startDate = dateRange.value[0]
    params.endDate = dateRange.value[1]
  }

  return params
})

// 加载订单列表
const loadOrderList = async () => {
  loading.value = true
  try {
    const response = await orderApi.getOrderList(searchParams.value)
    if (response.data.code === 200) {
      const data = response.data.data
      orderList.value = data.records
      total.value = data.total
      pagination.current = data.current
      pagination.size = data.size
    }
  } catch (error) {
    console.error('加载订单列表失败:', error)
    ElMessage.error('加载订单列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadOrderList()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    orderNumber: '',
    orderStatus: '',
    paymentStatus: '',
    userId: '',
  })
  dateRange.value = null
  pagination.current = 1
  loadOrderList()
}

// 刷新列表
const refreshList = () => {
  loadOrderList()
}

// 分页变化
const handleSizeChange = () => {
  pagination.current = 1
  loadOrderList()
}

const handleCurrentChange = () => {
  loadOrderList()
}

// 选择变化
const handleSelectionChange = (selection: Order[]) => {
  selectedOrders.value = selection
}

// 更新订单状态
const updateOrderStatus = async (order: Order) => {
  try {
    // TODO: 调用更新状态API
    ElMessage.success('订单状态更新成功')
    loadOrderList()
  } catch (error) {
    console.error('更新订单状态失败:', error)
    ElMessage.error('更新失败')
  }
}

// 批量更新状态
const batchUpdateStatus = async (status: string) => {
  if (selectedOrders.value.length === 0) {
    ElMessage.warning('请选择要操作的订单')
    return
  }

  try {
    await ElMessageBox.confirm(`确定要将选中的 ${selectedOrders.value.length} 个订单状态更新为"${status}"吗？`, '批量操作确认')
    
    // TODO: 调用批量更新API
    ElMessage.success('批量更新成功')
    selectedOrders.value = []
    loadOrderList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量更新失败:', error)
      ElMessage.error('批量更新失败')
    }
  }
}

// 批量取消
const batchCancel = async () => {
  if (selectedOrders.value.length === 0) {
    ElMessage.warning('请选择要取消的订单')
    return
  }

  try {
    await ElMessageBox.confirm(`确定要取消选中的 ${selectedOrders.value.length} 个订单吗？`, '批量取消确认', {
      type: 'warning'
    })
    
    // TODO: 调用批量取消API
    ElMessage.success('批量取消成功')
    selectedOrders.value = []
    loadOrderList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量取消失败:', error)
      ElMessage.error('批量取消失败')
    }
  }
}

// 查看订单详情
const viewOrderDetail = (orderId: number) => {
  router.push(`/admin/order/detail/${orderId}`)
}

// 查看物流跟踪
const viewTracking = (orderNumber: string) => {
  router.push(`/admin/tracking/detail?orderNumber=${orderNumber}`)
}

// 分配配送员
const assignCourier = (order: Order) => {
  assignForm.orderNumber = order.orderNumber
  assignForm.orderId = order.id
  assignForm.courierId = ''
  assignForm.remarks = ''
  showAssignDialog.value = true
}

// 确认分配
const confirmAssign = async () => {
  if (!assignForm.courierId || !assignForm.orderId) {
    ElMessage.warning('请选择配送员')
    return
  }

  assigning.value = true
  try {
    console.log(`开始分配订单 ${assignForm.orderId} 给配送员 ${assignForm.courierId}`)

    // 调用分配配送员API
    const response = await http.put(`/order/${assignForm.orderId}/assign-delivery`, null, {
      params: {
        courierId: Number(assignForm.courierId)
      }
    })

    console.log('分配响应:', response)

    if (response && response.code === 200) {
      ElMessage.success('分配成功')
      showAssignDialog.value = false

      // 重置表单
      assignForm.courierId = ''
      assignForm.orderId = 0
      assignForm.orderNumber = ''
      assignForm.remarks = ''

      // 刷新列表
      await loadOrderList()
    } else {
      throw new Error(response?.message || '分配失败')
    }
  } catch (error: any) {
    console.error('分配配送员失败:', error)
    ElMessage.error(error.message || '分配失败')
  } finally {
    assigning.value = false
  }
}

// 其他操作
const editOrder = (order: Order) => {
  router.push(`/admin/order/edit/${order.id}`)
}

const printOrder = (order: Order) => {
  ElMessage.info('打印功能开发中...')
}

const refundOrder = async (order: Order) => {
  try {
    await ElMessageBox.confirm('确定要退款这个订单吗？', '退款确认', {
      type: 'warning'
    })
    
    // TODO: 调用退款API
    ElMessage.success('退款成功')
    loadOrderList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('退款失败:', error)
      ElMessage.error('退款失败')
    }
  }
}

const deleteOrder = async (order: Order) => {
  try {
    await ElMessageBox.confirm('确定要删除这个订单吗？此操作不可恢复！', '删除确认', {
      type: 'error'
    })
    
    // TODO: 调用删除API
    ElMessage.success('删除成功')
    loadOrderList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除订单失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 导出订单
const exportOrders = () => {
  ElMessage.info('导出功能开发中...')
}

// 工具函数
const getServiceTypeText = (serviceType: ServiceType) => {
  const textMap = {
    STANDARD: '标准',
    EXPRESS: '特快',
    URGENT: '加急',
  }
  return textMap[serviceType] || serviceType
}

const getPaymentStatusText = (paymentStatus: PaymentStatus) => {
  const textMap = {
    0: '未支付',
    1: '已支付',
    2: '已退款',
  }
  return textMap[paymentStatus] || '未知'
}

const getPaymentTagType = (paymentStatus: PaymentStatus) => {
  const typeMap = {
    0: 'warning',
    1: 'success',
    2: 'info',
  }
  return typeMap[paymentStatus] || ''
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm')
}

onMounted(() => {
  loadOrderList()
  loadCourierList()
})
</script>

<style scoped>
.admin-order-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-card {
  margin-bottom: 20px;
}

.batch-actions {
  margin-bottom: 20px;
  background: #f8f9fa;
}

.batch-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.batch-buttons {
  display: flex;
  gap: 10px;
}

.text-gray {
  color: #666;
  font-size: 12px;
}

.amount {
  font-weight: bold;
  color: #f56c6c;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.danger {
  color: #f56c6c;
}
</style> 