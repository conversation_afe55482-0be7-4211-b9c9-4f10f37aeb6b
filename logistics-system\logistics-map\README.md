# 地图服务模块 (logistics-map)

物流跟踪系统的地图服务模块，提供地址解析、路径规划、距离计算等地理位置相关功能。

## 🚀 功能特性

### 核心功能
- **地理编码** - 地址转坐标，支持批量处理
- **逆地理编码** - 坐标转地址，获取详细地址信息
- **路径规划** - 驾车/步行路径规划，支持多种策略
- **距离计算** - 直线距离/驾车距离计算
- **POI搜索** - 关键字搜索/周边搜索
- **搜索提示** - 地址输入智能提示
- **天气查询** - 获取城市天气信息

### 技术特性
- **高德地图API集成** - 使用高德地图Web服务API
- **智能缓存** - Redis缓存热点数据，提升响应速度
- **批量处理** - 支持批量地址解析和坐标转换
- **参数验证** - 完整的输入参数校验
- **异常处理** - 优雅的异常处理和错误提示
- **性能优化** - 连接池、超时控制、重试机制

## 📦 技术栈

- **Spring Boot 2.7.8** - 微服务框架
- **Spring Cloud** - 微服务治理
- **Nacos** - 服务注册发现
- **Redis** - 数据缓存
- **高德地图API** - 地图服务提供商
- **Apache HttpClient** - HTTP客户端
- **Fastjson2** - JSON处理
- **Hutool** - 工具类库

## 🔧 配置说明

### 1. 申请高德地图API密钥

访问 [高德开放平台](https://lbs.amap.com/) 申请API密钥：

1. 注册账号并完成认证
2. 创建应用，选择Web服务API
3. 获取API Key

### 2. 配置application.yml

```yaml
# 高德地图API配置
amap:
  # 替换为你的高德地图API密钥
  web-api-key: YOUR_AMAP_WEB_API_KEY
  js-api-key: YOUR_AMAP_JS_API_KEY
  base-url: https://restapi.amap.com
  
  # 默认配置
  default:
    city: "北京"
    output: "json"
    extensions: "all"
    radius: 3000
    offset: 20

# 地图服务配置
map:
  # 缓存配置
  cache:
    enabled: true
    ttl: 3600
    max-size: 10000
    
  # 限流配置
  rate-limit:
    enabled: true
    requests-per-second: 100
    burst-capacity: 200
    
  # 超时配置
  timeout:
    connect: 5000
    read: 10000
```

### 3. 启动服务

```bash
# 启动地图服务
mvn spring-boot:run

# 或者
java -jar logistics-map-1.0-SNAPSHOT.jar
```

服务启动后访问：
- 健康检查: http://localhost:8006/test/hello
- 服务状态: http://localhost:8006/test/status
- 配置检查: http://localhost:8006/test/config

## 📚 API接口文档

### 基础接口

#### 1. 地理编码 - 地址转坐标

```http
POST /api/map/geocoding
Content-Type: application/json

{
  "address": "北京市朝阳区望京SOHO",
  "city": "北京"
}
```

#### 2. 逆地理编码 - 坐标转地址

```http
POST /api/map/regeocoding
Content-Type: application/json

{
  "location": "116.480881,39.989410",
  "radius": 1000,
  "extensions": "all"
}
```

#### 3. 驾车路径规划

```http
POST /api/map/direction/driving
Content-Type: application/json

{
  "origin": "116.481028,39.989643",
  "destination": "116.434446,39.90816",
  "strategy": 0
}
```

### 便捷接口

#### 1. 地址转坐标（GET）

```http
GET /api/map/address-to-location?address=北京市朝阳区望京SOHO&city=北京
```

#### 2. 坐标转地址（GET）

```http
GET /api/map/location-to-address?longitude=116.480881&latitude=39.989410
```

#### 3. 计算直线距离

```http
GET /api/map/distance/straight?fromLng=116.481028&fromLat=39.989643&toLng=116.434446&toLat=39.90816
```

#### 4. 计算驾车距离

```http
GET /api/map/distance/driving?fromLng=116.481028&fromLat=39.989643&toLng=116.434446&toLat=39.90816
```

### 批量接口

#### 1. 批量地址转坐标

```http
POST /api/map/batch/address-to-location
Content-Type: application/json

[
  "北京市朝阳区望京SOHO",
  "上海市浦东新区陆家嘴金融中心",
  "广州市天河区珠江新城"
]
```

#### 2. 批量坐标转地址

```http
POST /api/map/batch/location-to-address
Content-Type: application/json

[
  "116.480881,39.989410",
  "121.499763,31.239580",
  "113.324520,23.138270"
]
```

### POI搜索接口

#### 1. 关键字搜索

```http
GET /api/map/poi/search?keywords=银行&city=北京&types=150000
```

#### 2. 周边搜索

```http
GET /api/map/poi/around?keywords=餐厅&location=116.480881,39.989410&radius=1000
```

### 其他接口

#### 1. 搜索提示

```http
GET /api/map/inputtips?keywords=北京朝阳&city=北京
```

#### 2. 天气查询

```http
GET /api/map/weather?city=北京&extensions=all
```

## 🛠️ 使用示例

### Java代码示例

```java
@RestController
public class DeliveryController {
    
    @Autowired
    private MapService mapService;
    
    /**
     * 计算配送距离和时间
     */
    @PostMapping("/delivery/calculate")
    public Result<DeliveryInfo> calculateDelivery(@RequestBody DeliveryRequest request) {
        // 1. 地址转坐标
        Location fromLocation = mapService.addressToLocation(request.getFromAddress());
        Location toLocation = mapService.addressToLocation(request.getToAddress());
        
        // 2. 计算驾车距离
        Double distance = mapService.calculateDrivingDistance(
            fromLocation.getLongitude(), fromLocation.getLatitude(),
            toLocation.getLongitude(), toLocation.getLatitude()
        );
        
        // 3. 获取最优路径
        Map<String, Object> route = mapService.getBestDrivingRoute(
            fromLocation.getLongitude(), fromLocation.getLatitude(),
            toLocation.getLongitude(), toLocation.getLatitude()
        );
        
        // 4. 构建返回结果
        DeliveryInfo deliveryInfo = new DeliveryInfo();
        deliveryInfo.setDistance(distance);
        deliveryInfo.setRoute(route);
        
        return Result.success(deliveryInfo);
    }
}
```

### 前端JavaScript示例

```javascript
// 地址转坐标
async function addressToLocation(address) {
    const response = await fetch(`/api/map/address-to-location?address=${encodeURIComponent(address)}`);
    const result = await response.json();
    return result.data;
}

// 计算两点距离
async function calculateDistance(fromLng, fromLat, toLng, toLat) {
    const response = await fetch(`/api/map/distance/driving?fromLng=${fromLng}&fromLat=${fromLat}&toLng=${toLng}&toLat=${toLat}`);
    const result = await response.json();
    return result.data;
}

// 路径规划
async function planRoute(origin, destination) {
    const response = await fetch('/api/map/direction/driving', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({origin, destination, strategy: 0})
    });
    const result = await response.json();
    return result.data;
}
```

## 🔒 安全配置

### 1. API密钥保护
- 不要在客户端代码中暴露API密钥
- 使用环境变量或配置中心管理密钥
- 定期更换API密钥

### 2. 访问限制
- 配置IP白名单（生产环境）
- 设置请求频率限制
- 启用API调用监控

### 3. 数据缓存
- 合理设置缓存过期时间
- 避免缓存敏感位置信息
- 定期清理过期缓存

## 📊 监控和日志

### 1. 健康检查
```http
GET /test/status
```

### 2. 配置检查
```http
GET /test/config
```

### 3. 日志配置
```yaml
logging:
  level:
    org.example.logisticsmap: DEBUG
    org.apache.http: INFO
```

## ⚠️ 注意事项

1. **API配额限制**: 高德地图API有日调用量限制，超出需要付费
2. **坐标系统**: 高德地图使用GCJ02坐标系，GPS原始坐标为WGS84
3. **缓存策略**: 合理使用缓存，避免数据过期问题
4. **异常处理**: 网络异常时的降级策略
5. **性能优化**: 批量请求优于单个请求

## 🆘 故障排查

### 常见问题

1. **API密钥错误**
   - 检查密钥是否正确配置
   - 确认密钥对应的服务类型

2. **网络连接超时**
   - 检查网络连接
   - 调整超时时间配置

3. **缓存不生效**
   - 检查Redis连接
   - 确认缓存配置

4. **坐标转换异常**
   - 检查输入坐标格式
   - 确认是否在服务范围内

### 联系支持
- 项目文档: 查看详细设计文档
- 技术支持: 提交Issue或联系开发团队

---

**版本**: 1.0.0  
**更新时间**: 2024-12-25  
**开发团队**: 物流系统开发组 