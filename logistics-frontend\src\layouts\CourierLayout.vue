<template>
  <div class="layout-container">
    <div class="layout-header">
      <h1>配送员工作台 - {{ userInfo?.realName }}</h1>
      <el-button @click="handleLogout">退出登录</el-button>
    </div>
    <div class="layout-content">
      <router-view />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()
const userInfo = computed(() => authStore.userInfo)

const handleLogout = () => {
  authStore.logout()
}
</script>

<style scoped>
.layout-container {
  min-height: 100vh;
  background: #f5f5f5;
}

.layout-header {
  background: white;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.layout-content {
  padding: 20px;
}
</style>
