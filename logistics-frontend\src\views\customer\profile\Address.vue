<template>
  <div class="address-management">
    <div class="page-header">
      <el-button @click="$router.back()" :icon="ArrowLeft">返回</el-button>
      <h2>地址管理</h2>
      <el-button type="primary" @click="addAddress">
        <el-icon><Plus /></el-icon>
        新增地址
      </el-button>
    </div>

    <div class="address-tabs">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="收货地址" name="1">
          <AddressList
            :address-type="1"
            :addresses="receivingAddresses"
            :loading="loading"
            @edit="editAddress"
            @delete="deleteAddress"
            @set-default="setDefaultAddress"
          />
        </el-tab-pane>
        <el-tab-pane label="发货地址" name="2">
          <AddressList
            :address-type="2"
            :addresses="sendingAddresses"
            :loading="loading"
            @edit="editAddress"
            @delete="deleteAddress"
            @set-default="setDefaultAddress"
          />
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 地址表单对话框 -->
    <AddressFormDialog
      v-model="showAddressForm"
      :address="editingAddress"
      :address-type="Number(activeTab)"
      @success="onAddressFormSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Plus } from '@element-plus/icons-vue'
import { addressApi } from '@/api/address'
import type { Address } from '@/types/address'
import AddressList from '../components/AddressList.vue'
import AddressFormDialog from '../components/AddressFormDialog.vue'

// 状态
const loading = ref(false)
const activeTab = ref('1')
const showAddressForm = ref(false)
const editingAddress = ref<Address | null>(null)
const allAddresses = ref<Address[]>([])

// 计算属性
const receivingAddresses = computed(() =>
  allAddresses.value.filter((addr) => addr.addressType === 1),
)

const sendingAddresses = computed(() => allAddresses.value.filter((addr) => addr.addressType === 2))

// 加载地址列表
const loadAddresses = async () => {
  loading.value = true
  try {
    console.log('开始加载地址列表...')
    const response = await addressApi.getAddressList()
    console.log('地址列表API响应:', response)

    if (response.code === 200) {
      allAddresses.value = response.data || []
      console.log('加载的地址数据:', allAddresses.value)
    } else {
      console.error('API返回错误:', response.message)
      ElMessage.error(response.message || '加载地址列表失败')
    }
  } catch (error) {
    console.error('加载地址列表失败:', error)
    ElMessage.error('加载地址列表失败')
  } finally {
    loading.value = false
  }
}

// 切换标签页
const handleTabChange = () => {
  // 可以在这里做一些特殊处理
}

// 新增地址
const addAddress = () => {
  editingAddress.value = null
  showAddressForm.value = true
}

// 编辑地址
const editAddress = (address: Address) => {
  editingAddress.value = address
  showAddressForm.value = true
}

// 删除地址
const deleteAddress = async (addressId: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个地址吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const response = await addressApi.deleteAddress(addressId)
    if (response.code === 200) {
      // 修改这里
      ElMessage.success('删除成功')
      loadAddresses()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除地址失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 设置默认地址
const setDefaultAddress = async (addressId: number) => {
  try {
    const response = await addressApi.setDefaultAddress(addressId)
    if (response.code === 200) {
      // 修改这里
      ElMessage.success('设置成功')
      loadAddresses()
    } else {
      ElMessage.error(response.message || '设置失败')
    }
  } catch (error) {
    console.error('设置默认地址失败:', error)
    ElMessage.error('设置失败')
  }
}

// 地址表单成功回调
const onAddressFormSuccess = () => {
  loadAddresses()
  editingAddress.value = null
}

onMounted(() => {
  loadAddresses()
})
</script>

<style scoped>
.address-management {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.address-tabs {
  background: white;
  border-radius: 8px;
}
</style>
