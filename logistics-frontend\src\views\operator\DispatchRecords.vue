<template>
  <div class="dispatch-records">
    <div class="page-header">
      <h1>调度记录</h1>
      <div class="header-actions">
        <el-button @click="exportData">
          <el-icon><Download /></el-icon>
          导出记录
        </el-button>
      </div>
    </div>

    <!-- 搜索过滤 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="订单号">
          <el-input v-model="searchForm.orderNumber" placeholder="输入订单号" clearable style="width: 200px" />
        </el-form-item>
        <el-form-item label="配送员">
          <el-input v-model="searchForm.courier" placeholder="输入配送员姓名" clearable style="width: 200px" />
        </el-form-item>
        <el-form-item label="操作员">
          <el-input v-model="searchForm.operator" placeholder="输入操作员姓名" clearable style="width: 200px" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable style="width: 120px">
            <el-option label="成功" value="success" />
            <el-option label="失败" value="failed" />
            <el-option label="处理中" value="pending" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon total">
                <el-icon size="24"><Document /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.total }}</div>
                <div class="stat-label">总调度次数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon success">
                <el-icon size="24"><Check /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.success }}</div>
                <div class="stat-label">成功调度</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon failed">
                <el-icon size="24"><Close /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.failed }}</div>
                <div class="stat-label">失败调度</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon rate">
                <el-icon size="24"><TrendCharts /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.successRate }}%</div>
                <div class="stat-label">成功率</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 调度记录表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="table-header">
          <span>调度记录 (共 {{ total }} 条)</span>
          <div class="table-actions">
            <el-button size="small" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <el-table :data="recordList" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="记录ID" width="80" />
        <el-table-column prop="orderNumber" label="订单号" width="140">
          <template #default="scope">
            <el-link type="primary" @click="viewOrder(scope.row.orderNumber)">
              {{ scope.row.orderNumber }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="fromCourier" label="原配送员" width="100">
          <template #default="scope">
            <span v-if="scope.row.fromCourier">{{ scope.row.fromCourier }}</span>
            <el-tag v-else type="info" size="small">新分配</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="toCourier" label="新配送员" width="100" />
        <el-table-column prop="reason" label="调度原因" show-overflow-tooltip />
        <el-table-column prop="operatorName" label="操作员" width="100" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="调度时间" width="160">
          <template #default="scope">
            {{ formatTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="viewDetail(scope.row)">
              详情
            </el-button>
            <el-dropdown @command="handleCommand($event, scope.row)">
              <el-button size="small" type="info">
                更多 <el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="timeline">调度时间线</el-dropdown-item>
                  <el-dropdown-item command="impact">影响分析</el-dropdown-item>
                  <el-dropdown-item command="rollback" v-if="scope.row.status === 'success'">回滚操作</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 调度详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="调度详情" width="800px">
      <div v-if="currentRecord">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="记录ID">{{ currentRecord.id }}</el-descriptions-item>
          <el-descriptions-item label="订单号">{{ currentRecord.orderNumber }}</el-descriptions-item>
          <el-descriptions-item label="原配送员">{{ currentRecord.fromCourier || '新分配' }}</el-descriptions-item>
          <el-descriptions-item label="新配送员">{{ currentRecord.toCourier }}</el-descriptions-item>
          <el-descriptions-item label="操作员">{{ currentRecord.operatorName }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentRecord.status)">
              {{ getStatusText(currentRecord.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="调度时间" :span="2">{{ formatTime(currentRecord.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="调度原因" :span="2">{{ currentRecord.reason }}</el-descriptions-item>
        </el-descriptions>

        <!-- 调度时间线 -->
        <div class="timeline-section">
          <h4>调度时间线</h4>
          <el-timeline>
            <el-timeline-item timestamp="2024-01-15 10:30:00" type="primary">
              订单创建，等待分配
            </el-timeline-item>
            <el-timeline-item timestamp="2024-01-15 11:00:00" type="success">
              分配给配送员：{{ currentRecord.fromCourier || currentRecord.toCourier }}
            </el-timeline-item>
            <el-timeline-item v-if="currentRecord.fromCourier" :timestamp="currentRecord.createTime" type="warning">
              重新分配给：{{ currentRecord.toCourier }}
              <br>原因：{{ currentRecord.reason }}
            </el-timeline-item>
            <el-timeline-item 
              v-if="currentRecord.status === 'success'" 
              timestamp="2024-01-15 11:05:00" 
              type="success"
            >
              调度完成
            </el-timeline-item>
            <el-timeline-item 
              v-if="currentRecord.status === 'failed'" 
              timestamp="2024-01-15 11:05:00" 
              type="danger"
            >
              调度失败
            </el-timeline-item>
          </el-timeline>
        </div>

        <!-- 影响分析 -->
        <div class="impact-section">
          <h4>影响分析</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="impact-item">
                <div class="impact-label">时间影响</div>
                <div class="impact-value">+15分钟</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="impact-item">
                <div class="impact-label">距离影响</div>
                <div class="impact-value">+5.2公里</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="impact-item">
                <div class="impact-label">成本影响</div>
                <div class="impact-value">+¥12</div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-dialog>

    <!-- 时间线对话框 -->
    <el-dialog v-model="showTimelineDialog" title="调度时间线" width="600px">
      <el-timeline>
        <el-timeline-item
          v-for="(item, index) in timelineData"
          :key="index"
          :timestamp="item.timestamp"
          :type="item.type"
        >
          {{ item.content }}
        </el-timeline-item>
      </el-timeline>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search, Refresh, Download, ArrowDown, Document, Check, Close, TrendCharts
} from '@element-plus/icons-vue'
import { operatorApi, type DispatchRecord } from '@/api/operator'

// 响应式数据
const recordList = ref<DispatchRecord[]>([])
const currentRecord = ref<DispatchRecord | null>(null)

// 统计数据
const stats = ref({
  total: 0,
  success: 0,
  failed: 0,
  successRate: 0
})

// 搜索表单
const searchForm = reactive({
  orderNumber: '',
  courier: '',
  operator: '',
  status: '',
  dateRange: null as [Date, Date] | null
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20
})

const total = ref(0)
const loading = ref(false)

// 对话框控制
const showDetailDialog = ref(false)
const showTimelineDialog = ref(false)

// 时间线数据
const timelineData = ref<Array<{
  timestamp: string
  type: string
  content: string
}>>([])

// 生命周期
onMounted(() => {
  loadData()
  loadStats()
})

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      orderNumber: searchForm.orderNumber || undefined,
      courier: searchForm.courier || undefined,
      operator: searchForm.operator || undefined,
      status: searchForm.status || undefined,
      startTime: searchForm.dateRange?.[0]?.toISOString(),
      endTime: searchForm.dateRange?.[1]?.toISOString()
    }
    
    // 清理空值参数
    Object.keys(params).forEach((key) => {
      if (params[key] === undefined || params[key] === '') {
        delete params[key]
      }
    })
    
    console.log('请求调度记录，参数:', params)
    
    const response = await operatorApi.getDispatchRecords(params)
    console.log('调度记录响应:', response)
    
    if (response.code === 200) {
      recordList.value = response.data.list || response.data.records || []
      total.value = response.data.total || 0
      console.log('调度记录加载成功:', recordList.value)
    } else {
      throw new Error(response.message || '获取调度记录失败')
    }
  } catch (error) {
    console.error('加载调度记录失败:', error)
    ElMessage.error('加载调度记录失败，请检查网络连接')
    recordList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  try {
    // 这里应该调用统计API
    stats.value = {
      total: 156,
      success: 142,
      failed: 14,
      successRate: 91
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const refreshData = () => {
  loadData()
  loadStats()
  ElMessage.success('数据已刷新')
}

const handleSearch = () => {
  pagination.page = 1
  loadData()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    orderNumber: '',
    courier: '',
    operator: '',
    status: '',
    dateRange: null
  })
  handleSearch()
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadData()
}

const viewDetail = (record: DispatchRecord) => {
  currentRecord.value = record
  showDetailDialog.value = true
}

const viewOrder = (orderNumber: string) => {
  ElMessage.info(`查看订单 ${orderNumber}`)
}

const handleCommand = (command: string, record: DispatchRecord) => {
  switch (command) {
    case 'timeline':
      showTimeline(record)
      break
    case 'impact':
      ElMessage.info('影响分析功能开发中')
      break
    case 'rollback':
      handleRollback(record)
      break
  }
}

const showTimeline = (record: DispatchRecord) => {
  timelineData.value = [
    {
      timestamp: '2024-01-15 10:30:00',
      type: 'primary',
      content: '订单创建，等待分配'
    },
    {
      timestamp: '2024-01-15 11:00:00',
      type: 'success',
      content: `分配给配送员：${record.fromCourier || record.toCourier}`
    }
  ]
  
  if (record.fromCourier) {
    timelineData.value.push({
      timestamp: record.createTime,
      type: 'warning',
      content: `重新分配给：${record.toCourier}，原因：${record.reason}`
    })
  }
  
  timelineData.value.push({
    timestamp: '2024-01-15 11:05:00',
    type: record.status === 'success' ? 'success' : 'danger',
    content: record.status === 'success' ? '调度完成' : '调度失败'
  })
  
  showTimelineDialog.value = true
}

const handleRollback = async (record: DispatchRecord) => {
  try {
    await ElMessageBox.confirm('确定要回滚这次调度操作吗？', '确认回滚', {
      type: 'warning'
    })
    
    ElMessage.success('回滚操作成功')
    loadData()
  } catch (error) {
    // 用户取消操作
  }
}

const exportData = () => {
  ElMessage.info('导出功能开发中')
}

// 工具方法
const getStatusType = (status: string) => {
  switch (status) {
    case 'success': return 'success'
    case 'failed': return 'danger'
    case 'pending': return 'warning'
    default: return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'success': return '成功'
    case 'failed': return '失败'
    case 'pending': return '处理中'
    default: return '未知'
  }
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString('zh-CN')
}
</script>

<style scoped>
.dispatch-records {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-card {
  margin-bottom: 20px;
}

.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  height: 100px;
}

.stat-item {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 10px;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.stat-icon.total { background-color: #ecf5ff; color: #409EFF; }
.stat-icon.success { background-color: #f0f9ff; color: #67C23A; }
.stat-icon.failed { background-color: #fef0f0; color: #F56C6C; }
.stat-icon.rate { background-color: #fdf6ec; color: #E6A23C; }

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.table-card {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border: none;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.timeline-section,
.impact-section {
  margin-top: 20px;
}

.timeline-section h4,
.impact-section h4 {
  margin: 0 0 15px 0;
  color: #333;
}

.impact-item {
  text-align: center;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.impact-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.impact-value {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}
</style> 