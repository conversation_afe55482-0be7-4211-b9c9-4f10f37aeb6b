# 后端实体与数据库表匹配检查报告

## 📋 **检查概述**

本报告详细对比了所有后端实体类与数据库表结构，确保完全匹配。

## ✅ **匹配结果总结**

| 模块 | 实体类 | 数据库表 | 匹配状态 | 备注 |
|------|--------|----------|----------|------|
| logistics-user | User | users | ✅ 完全匹配 | 包含所有字段 |
| logistics-user | Role | roles | ✅ 完全匹配 | 权限管理 |
| logistics-user | Permission | permissions | ✅ 完全匹配 | 权限管理 |
| logistics-order | Order | orders | ✅ 完全匹配 | 包含所有扩展字段 |
| logistics-delivery | Courier | couriers | ✅ 完全匹配 | 配送员信息 |
| logistics-delivery | DeliveryTask | delivery_tasks | ✅ 完全匹配 | 配送任务 |
| logistics-delivery | DeliveryRoute | delivery_routes | ✅ 完全匹配 | 配送路线 |
| logistics-notification | Notification | notifications | ✅ 完全匹配 | 通知记录 |
| logistics-notification | EmailTemplate | email_templates | ✅ 完全匹配 | 邮件模板 |
| logistics-notification | NotificationTemplate | notification_templates | ✅ 完全匹配 | 通知模板 |
| logistics-map | Location | - | ⚠️ 无对应表 | 仅用于数据传输 |
| logistics-logistics | TrackingInfo | - | ⚠️ MongoDB | 存储在MongoDB |
| logistics-logistics | TrackingNode | - | ⚠️ MongoDB | 嵌套在TrackingInfo中 |

## 🔍 **详细字段匹配检查**

### **1. User实体 ↔ users表**

| 实体字段 | 数据库字段 | 类型匹配 | 状态 |
|----------|------------|----------|------|
| id | id | BIGINT | ✅ |
| username | username | VARCHAR(50) | ✅ |
| password | password | VARCHAR(255) | ✅ |
| realName | real_name | VARCHAR(50) | ✅ |
| phone | phone | VARCHAR(20) | ✅ |
| email | email | VARCHAR(100) | ✅ |
| idCard | id_card | VARCHAR(18) | ✅ |
| userType | user_type | VARCHAR(20) | ✅ |
| status | status | VARCHAR(20) | ✅ |
| avatar | avatar | VARCHAR(255) | ✅ |
| lastLoginTime | last_login_time | TIMESTAMP | ✅ |
| lastLoginIp | last_login_ip | VARCHAR(50) | ✅ |
| gender | gender | TINYINT(1) | ✅ |
| birthday | birthday | DATE | ✅ |
| createTime | create_time | TIMESTAMP | ✅ |
| updateTime | update_time | TIMESTAMP | ✅ |

### **2. Order实体 ↔ orders表**

| 实体字段 | 数据库字段 | 类型匹配 | 状态 |
|----------|------------|----------|------|
| id | id | BIGINT | ✅ |
| orderNumber | order_number | VARCHAR(32) | ✅ |
| userId | user_id | BIGINT | ✅ |
| senderName | sender_name | VARCHAR(50) | ✅ |
| senderPhone | sender_phone | VARCHAR(20) | ✅ |
| senderAddress | sender_address | VARCHAR(255) | ✅ |
| senderProvince | sender_province | VARCHAR(50) | ✅ |
| senderCity | sender_city | VARCHAR(50) | ✅ |
| senderDistrict | sender_district | VARCHAR(50) | ✅ |
| senderLongitude | sender_longitude | DECIMAL(10,6) | ✅ |
| senderLatitude | sender_latitude | DECIMAL(10,6) | ✅ |
| receiverName | receiver_name | VARCHAR(50) | ✅ |
| receiverPhone | receiver_phone | VARCHAR(20) | ✅ |
| receiverAddress | receiver_address | VARCHAR(255) | ✅ |
| receiverProvince | receiver_province | VARCHAR(50) | ✅ |
| receiverCity | receiver_city | VARCHAR(50) | ✅ |
| receiverDistrict | receiver_district | VARCHAR(50) | ✅ |
| receiverLongitude | receiver_longitude | DECIMAL(10,6) | ✅ |
| receiverLatitude | receiver_latitude | DECIMAL(10,6) | ✅ |
| itemName | item_name | VARCHAR(100) | ✅ |
| itemType | item_type | VARCHAR(50) | ✅ |
| itemWeight | item_weight | DECIMAL(10,2) | ✅ |
| itemVolume | item_volume | DECIMAL(10,2) | ✅ |
| itemLength | item_length | DECIMAL(10,2) | ✅ |
| itemWidth | item_width | DECIMAL(10,2) | ✅ |
| itemHeight | item_height | DECIMAL(10,2) | ✅ |
| itemValue | item_value | DECIMAL(10,2) | ✅ |
| isFragile | is_fragile | TINYINT(1) | ✅ |
| serviceType | service_type | VARCHAR(20) | ✅ |
| shippingFee | shipping_fee | DECIMAL(10,2) | ✅ |
| insuranceFee | insurance_fee | DECIMAL(10,2) | ✅ |
| packingFee | packing_fee | DECIMAL(10,2) | ✅ |
| totalFee | total_fee | DECIMAL(10,2) | ✅ |
| paymentMethod | payment_method | VARCHAR(20) | ✅ |
| paymentStatus | payment_status | INT(11) | ✅ |
| orderStatus | order_status | VARCHAR(20) | ✅ |
| pickupTime | pickup_time | TIMESTAMP | ✅ |
| deliveryTime | delivery_time | TIMESTAMP | ✅ |
| signTime | sign_time | TIMESTAMP | ✅ |
| estimatedDeliveryTime | estimated_delivery_time | TIMESTAMP | ✅ |
| remarks | remarks | TEXT | ✅ |
| createTime | create_time | TIMESTAMP | ✅ |
| updateTime | update_time | TIMESTAMP | ✅ |

### **3. Courier实体 ↔ couriers表**

| 实体字段 | 数据库字段 | 类型匹配 | 状态 |
|----------|------------|----------|------|
| id | id | BIGINT | ✅ |
| userId | user_id | BIGINT | ✅ |
| courierCode | courier_code | VARCHAR(50) | ✅ |
| courierName | courier_name | VARCHAR(50) | ✅ |
| phone | phone | VARCHAR(20) | ✅ |
| idCard | id_card | VARCHAR(18) | ✅ |
| workNumber | work_number | VARCHAR(50) | ✅ |
| vehicleType | vehicle_type | VARCHAR(20) | ✅ |
| vehicleNumber | vehicle_number | VARCHAR(20) | ✅ |
| workArea | work_area | VARCHAR(100) | ✅ |
| maxWeight | max_weight | DECIMAL(10,2) | ✅ |
| maxVolume | max_volume | DECIMAL(10,2) | ✅ |
| status | status | INT(11) | ✅ |
| currentLongitude | current_longitude | DECIMAL(10,6) | ✅ |
| currentLatitude | current_latitude | DECIMAL(10,6) | ✅ |
| currentAddress | current_address | VARCHAR(255) | ✅ |
| lastLocationUpdate | last_location_update | TIMESTAMP | ✅ |
| rating | rating | DECIMAL(3,2) | ✅ |
| totalOrders | total_orders | INT(11) | ✅ |
| completedOrders | completed_orders | INT(11) | ✅ |
| hireDate | hire_date | DATE | ✅ |
| emergencyContact | emergency_contact | VARCHAR(50) | ✅ |
| emergencyPhone | emergency_phone | VARCHAR(20) | ✅ |
| createTime | create_time | TIMESTAMP | ✅ |
| updateTime | update_time | TIMESTAMP | ✅ |

### **4. DeliveryTask实体 ↔ delivery_tasks表**

| 实体字段 | 数据库字段 | 类型匹配 | 状态 |
|----------|------------|----------|------|
| id | id | BIGINT | ✅ |
| taskNumber | task_number | VARCHAR(32) | ✅ |
| orderId | order_id | BIGINT | ✅ |
| orderNumber | order_number | VARCHAR(32) | ✅ |
| courierId | courier_id | BIGINT | ✅ |
| taskType | task_type | VARCHAR(20) | ✅ |
| priority | priority | INT(11) | ✅ |
| weight | weight | DECIMAL(10,2) | ✅ |
| volume | volume | DECIMAL(10,2) | ✅ |
| goodsDescription | goods_description | VARCHAR(255) | ✅ |
| specialRequirements | special_requirements | TEXT | ✅ |
| deliveryFee | delivery_fee | DECIMAL(10,2) | ✅ |
| estimatedTime | estimated_time | TIMESTAMP | ✅ |
| actualStartTime | actual_start_time | TIMESTAMP | ✅ |
| actualEndTime | actual_end_time | TIMESTAMP | ✅ |
| taskStatus | task_status | VARCHAR(20) | ✅ |
| pickupAddress | pickup_address | VARCHAR(255) | ✅ |
| pickupContact | pickup_contact | VARCHAR(50) | ✅ |
| pickupPhone | pickup_phone | VARCHAR(20) | ✅ |
| deliveryAddress | delivery_address | VARCHAR(255) | ✅ |
| deliveryContact | delivery_contact | VARCHAR(50) | ✅ |
| deliveryPhone | delivery_phone | VARCHAR(20) | ✅ |
| routeId | route_id | BIGINT | ✅ |
| sequenceNumber | sequence_number | INT(11) | ✅ |
| completionProof | completion_proof | TEXT | ✅ |
| failureReason | failure_reason | VARCHAR(255) | ✅ |
| remarks | remarks | TEXT | ✅ |
| createTime | create_time | TIMESTAMP | ✅ |
| updateTime | update_time | TIMESTAMP | ✅ |

### **5. EmailTemplate实体 ↔ email_templates表**

| 实体字段 | 数据库字段 | 类型匹配 | 状态 |
|----------|------------|----------|------|
| id | id | BIGINT | ✅ |
| templateCode | template_code | VARCHAR(50) | ✅ |
| templateName | template_name | VARCHAR(100) | ✅ |
| subject | subject | VARCHAR(255) | ✅ |
| content | content | TEXT | ✅ |
| templateType | template_type | VARCHAR(20) | ✅ |
| isEnabled | is_enabled | TINYINT(1) | ✅ |
| createTime | create_time | TIMESTAMP | ✅ |
| updateTime | update_time | TIMESTAMP | ✅ |

## 🎯 **特殊说明**

### **MongoDB存储的实体**
- **TrackingInfo**: 存储在MongoDB的`tracking_info`集合中
- **TrackingNode**: 作为TrackingInfo的嵌套文档存储
- **LocationInfo**: 作为TrackingNode的嵌套文档存储

### **DTO/VO类**
- 不需要对应数据库表，仅用于数据传输和视图展示

### **BaseEntity**
- 所有继承BaseEntity的实体都包含：id、createTime、updateTime字段
- 在数据库表中都有对应的字段定义

## ✅ **匹配完成确认**

所有后端实体类与数据库表结构已完全匹配，包括：

1. **字段名称匹配** - 驼峰命名转下划线命名
2. **数据类型匹配** - Java类型与MySQL类型对应
3. **约束条件匹配** - 主键、唯一键、索引等
4. **关联关系匹配** - 外键关系正确定义
5. **默认值匹配** - 默认值设置一致

## 🚀 **使用说明**

1. 删除现有数据库：`DROP DATABASE IF EXISTS logistics;`
2. 执行完整脚本：`mysql -u root -p < complete_matched_database.sql`
3. 重启所有后端服务
4. 验证功能正常

数据库表结构现在与所有后端实体类完全匹配！
