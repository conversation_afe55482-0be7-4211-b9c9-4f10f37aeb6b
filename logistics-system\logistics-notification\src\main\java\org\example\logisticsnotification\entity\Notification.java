package org.example.logisticsnotification.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.logistics.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 通知记录实体
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("notifications")
public class Notification extends BaseEntity {

    /**
     * 通知ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 订单号
     */
    @TableField("order_number")
    private String orderNumber;

    /**
     * 业务ID（订单ID、任务ID等）
     */
    @TableField("business_id")
    private Long businessId;

    /**
     * 业务类型（ORDER、DELIVERY、SYSTEM等）
     */
    @TableField("business_type")
    private String businessType;

    /**
     * 通知类型：SMS、EMAIL、PUSH、WECHAT
     */
    @TableField("notification_type")
    private String notificationType;

    /**
     * 模板编码
     */
    @TableField("template_code")
    private String templateCode;

    /**
     * 接收者
     */
    @TableField("recipient")
    private String recipient;

    /**
     * 通知标题
     */
    @TableField("title")
    private String title;

    /**
     * 通知内容
     */
    @TableField("content")
    private String content;

    /**
     * 模板参数（JSON格式）
     */
    @TableField("template_params")
    private String templateParams;

    /**
     * 发送状态：0-待发送，1-发送中，2-发送成功，3-发送失败，4-已取消
     */
    @TableField("send_status")
    private Integer sendStatus;

    /**
     * 发送时间
     */
    @TableField("send_time")
    private LocalDateTime sendTime;

    /**
     * 失败原因
     */
    @TableField("failure_reason")
    private String failureReason;

    /**
     * 重试次数
     */
    @TableField("retry_count")
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    @TableField("max_retry_count")
    private Integer maxRetryCount;

    /**
     * 下次重试时间
     */
    @TableField("next_retry_time")
    private LocalDateTime nextRetryTime;

    /**
     * 优先级：1-低，2-中，3-高，4-紧急
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 是否已读：0-未读，1-已读
     */
    @TableField("is_read")
    private Integer isRead;

    /**
     * 扩展信息（JSON格式）
     */
    @TableField("ext_info")
    private String extInfo;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;
}
