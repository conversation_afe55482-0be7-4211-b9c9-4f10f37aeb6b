package org.example.logisticsmap.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 地理编码请求DTO
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
public class GeocodingRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 地址信息
     */
    @NotBlank(message = "地址信息不能为空")
    private String address;

    /**
     * 城市编码或名称
     */
    private String city;

    /**
     * 输出格式（json/xml）
     */
    private String output = "json";

    /**
     * 回调函数
     */
    private String callback;

    /**
     * 创建请求对象
     *
     * @param address 地址
     * @return 请求对象
     */
    public static GeocodingRequest of(String address) {
        GeocodingRequest request = new GeocodingRequest();
        request.setAddress(address);
        return request;
    }

    /**
     * 创建请求对象（带城市）
     *
     * @param address 地址
     * @param city    城市
     * @return 请求对象
     */
    public static GeocodingRequest of(String address, String city) {
        GeocodingRequest request = new GeocodingRequest();
        request.setAddress(address);
        request.setCity(city);
        return request;
    }
} 