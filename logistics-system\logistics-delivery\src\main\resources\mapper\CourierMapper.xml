<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.logisticsdelivery.mapper.CourierMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="org.example.logisticsdelivery.entity.Courier">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="courier_code" jdbcType="VARCHAR" property="courierCode"/>
        <result column="courier_name" jdbcType="VARCHAR" property="courierName"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="id_card" jdbcType="VARCHAR" property="idCard"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="work_number" jdbcType="VARCHAR" property="workNumber"/>
        <result column="vehicle_type" jdbcType="VARCHAR" property="vehicleType"/>
        <result column="vehicle_number" jdbcType="VARCHAR" property="vehicleNumber"/>
        <result column="work_area" jdbcType="VARCHAR" property="workArea"/>
        <result column="max_weight" jdbcType="DECIMAL" property="maxWeight"/>
        <result column="max_volume" jdbcType="DECIMAL" property="maxVolume"/>
        <result column="longitude" jdbcType="DECIMAL" property="longitude"/>
        <result column="latitude" jdbcType="DECIMAL" property="latitude"/>
        <result column="current_address" jdbcType="VARCHAR" property="currentAddress"/>
        <result column="last_location_time" jdbcType="TIMESTAMP" property="lastLocationTime"/>
        <result column="emergency_contact" jdbcType="VARCHAR" property="emergencyContact"/>
        <result column="emergency_phone" jdbcType="VARCHAR" property="emergencyPhone"/>
        <result column="hire_date" jdbcType="DATE" property="hireDate"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="rating" jdbcType="DECIMAL" property="rating"/>
        <result column="delivery_count" jdbcType="INTEGER" property="deliveryCount"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <!-- 详细结果映射（包含用户信息） -->
    <resultMap id="CourierDetailResultMap" type="org.example.logisticsdelivery.entity.Courier">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="courier_code" jdbcType="VARCHAR" property="courierCode"/>
        <result column="courier_name" jdbcType="VARCHAR" property="courierName"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="id_card" jdbcType="VARCHAR" property="idCard"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="work_area" jdbcType="VARCHAR" property="workArea"/>
        <result column="longitude" jdbcType="DECIMAL" property="longitude"/>
        <result column="latitude" jdbcType="DECIMAL" property="latitude"/>
        <result column="current_address" jdbcType="VARCHAR" property="currentAddress"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="rating" jdbcType="DECIMAL" property="rating"/>
        <result column="delivery_count" jdbcType="INTEGER" property="deliveryCount"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <!-- 分页查询配送员（联表查询） -->
    <select id="selectCourierPage" resultMap="CourierDetailResultMap">
        SELECT 
            c.id, c.courier_code, c.courier_name, c.phone, c.id_card, c.user_id,
            c.work_area, c.longitude, c.latitude, c.current_address, c.status,
            c.rating, c.delivery_count, c.create_time, c.update_time
        FROM couriers c
        LEFT JOIN users u ON c.user_id = u.id
        <where>
            <if test="status != null">
                AND c.status = #{status}
            </if>
            <if test="workArea != null and workArea != ''">
                AND c.work_area LIKE CONCAT('%', #{workArea}, '%')
            </if>
            <if test="name != null and name != ''">
                AND (c.courier_name LIKE CONCAT('%', #{name}, '%') 
                     OR u.real_name LIKE CONCAT('%', #{name}, '%'))
            </if>
            <if test="phone != null and phone != ''">
                AND c.phone LIKE CONCAT('%', #{phone}, '%')
            </if>
        </where>
        ORDER BY c.create_time DESC
    </select>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        id, courier_code, courier_name, phone, id_card, user_id, work_number,
        vehicle_type, vehicle_number, work_area, max_weight, max_volume,
        longitude, latitude, current_address, last_location_time,
        emergency_contact, emergency_phone, hire_date, status, rating, 
        delivery_count, create_time, update_time
    </sql>

    <!-- 根据状态查询配送员列表 -->
    <select id="findByStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM couriers
        WHERE status = #{status}
        ORDER BY create_time DESC
    </select>

    <!-- 根据工作区域查询配送员 -->
    <select id="findByWorkArea" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM couriers
        WHERE work_area = #{workArea}
        AND status != 0
        ORDER BY rating DESC
    </select>

    <!-- 查询指定范围内的配送员 -->
    <select id="findByLocationRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM couriers
        WHERE longitude BETWEEN #{minLng} AND #{maxLng}
        AND latitude BETWEEN #{minLat} AND #{maxLat}
        AND status = 1
        ORDER BY rating DESC
    </select>

    <!-- 查询可用的配送员（在线且不忙碌） -->
    <select id="findAvailableCouriers" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM couriers
        WHERE status = 1
        <if test="workArea != null and workArea != ''">
            AND (work_area = #{workArea} OR work_area IS NULL)
        </if>
        ORDER BY rating DESC, delivery_count ASC
    </select>

    <!-- 根据配送员编号查询 -->
    <select id="findByCourierCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM couriers
        WHERE courier_code = #{courierCode}
    </select>

    <!-- 根据用户ID查询配送员 -->
    <select id="findByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM couriers
        WHERE user_id = #{userId}
    </select>

    <!-- 更新配送员位置 -->
    <update id="updateLocation">
        UPDATE couriers
        SET longitude = #{longitude},
            latitude = #{latitude},
            current_address = #{address},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 更新配送员状态 -->
    <update id="updateStatus">
        UPDATE couriers
        SET status = #{status},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 分页查询配送员 -->
    <select id="findCouriersPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM couriers
        WHERE 1=1
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="workArea != null and workArea != ''">
            AND work_area = #{workArea}
        </if>
        <if test="courierName != null and courierName != ''">
            AND courier_name LIKE CONCAT('%', #{courierName}, '%')
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 统计各状态配送员数量 -->
    <select id="countByStatus" resultType="java.util.Map">
        SELECT 
            status,
            COUNT(*) as count
        FROM couriers
        GROUP BY status
    </select>

</mapper> 