<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.logisticsdelivery.mapper.DeliveryRouteMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="org.example.logisticsdelivery.entity.DeliveryRoute">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="route_number" jdbcType="VARCHAR" property="routeNumber"/>
        <result column="courier_id" jdbcType="BIGINT" property="courierId"/>
        <result column="route_name" jdbcType="VARCHAR" property="routeName"/>
        <result column="route_date" jdbcType="DATE" property="routeDate"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="route_status" jdbcType="VARCHAR" property="routeStatus"/>
        <result column="task_count" jdbcType="INTEGER" property="taskCount"/>
        <result column="completed_count" jdbcType="INTEGER" property="completedCount"/>
        <result column="total_distance" jdbcType="DECIMAL" property="totalDistance"/>
        <result column="estimated_duration" jdbcType="INTEGER" property="estimatedDuration"/>
        <result column="actual_duration" jdbcType="INTEGER" property="actualDuration"/>
        <result column="route_points" jdbcType="CLOB" property="routePoints"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        id, route_number, courier_id, route_name, route_date, start_time, end_time,
        route_status, task_count, completed_count, total_distance, estimated_duration,
        actual_duration, route_points, remarks, create_time, update_time
    </sql>

    <!-- 根据配送员ID查询路线 -->
    <select id="findByCourierId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM delivery_routes
        WHERE courier_id = #{courierId}
        ORDER BY route_date DESC, create_time DESC
    </select>

    <!-- 根据配送员和日期查询路线 -->
    <select id="findByCourierAndDate" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM delivery_routes
        WHERE courier_id = #{courierId}
        AND route_date = #{routeDate}
    </select>

    <!-- 根据路线状态查询 -->
    <select id="findByRouteStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM delivery_routes
        WHERE route_status = #{routeStatus}
        ORDER BY route_date DESC, create_time DESC
    </select>

    <!-- 查询指定日期的路线 -->
    <select id="findByRouteDate" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM delivery_routes
        WHERE route_date = #{routeDate}
        ORDER BY create_time DESC
    </select>

    <!-- 更新路线状态 -->
    <update id="updateRouteStatus">
        UPDATE delivery_routes
        SET route_status = #{routeStatus},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 更新路线任务统计 -->
    <update id="updateTaskCount">
        UPDATE delivery_routes
        SET task_count = #{taskCount},
            completed_count = #{completedCount},
            update_time = NOW()
        WHERE id = #{id}
    </update>

</mapper>