# 物流跟踪系统前端开发详细设计文档

## 📋 项目概述

**项目名称：** 物流跟踪系统前端  
**技术栈：** Vue 3.5 + TypeScript + Vite + Element Plus + Pinia  
**开发模式：** 分阶段迭代开发  
**目标用户：** 普通用户、操作员、配送员、管理员  

---

## 🎯 开发阶段规划

### 第一阶段：项目重构与基础架构 (2天)

#### 1.1 项目初始化命令
```bash
# 删除现有项目
cd /e:/桌面/springBoot/node
rm -rf logistics-frontend

# 创建新的 Vue 3 项目
npm create vue@latest logistics-frontend
# 选择配置：
# ✅ TypeScript
# ✅ Router
# ✅ Pinia
# ✅ ESLint
# ✅ Prettier

# 进入项目目录
cd logistics-frontend

# 安装依赖
npm install

# 安装额外依赖
npm install element-plus @element-plus/icons-vue
npm install axios dayjs lodash-es
npm install @types/lodash-es -D
npm install unplugin-auto-import unplugin-vue-components -D
```

#### 1.2 项目配置文件

**vite.config.ts**
```typescript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

export default defineConfig({
  plugins: [
    vue(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
      imports: ['vue', 'vue-router', 'pinia']
    }),
    Components({
      resolvers: [ElementPlusResolver()]
    })
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true
      }
    }
  }
})
```

**tsconfig.json**
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  },
  "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

#### 1.3 项目目录结构
```
src/
├── api/                    # API 接口
├── assets/                 # 静态资源
├── components/             # 公共组件
├── composables/            # 组合式函数
├── layouts/                # 布局模板
├── router/                 # 路由配置
├── stores/                 # 状态管理
├── types/                  # TypeScript 类型
├── utils/                  # 工具函数
├── views/                  # 页面视图
├── App.vue                 # 根组件
└── main.ts                 # 入口文件
```

### 第二阶段：基础架构搭建

#### 2.1 HTTP 请求封装

**src/utils/http.ts**
```typescript
import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import router from '@/router'

// 响应数据类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
}

// 创建 axios 实例
const http: AxiosInstance = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
http.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    const token = authStore.token
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
http.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { data } = response
    
    if (data.code === 200 || data.success) {
      return data
    } else {
      ElMessage.error(data.message || '请求失败')
      return Promise.reject(new Error(data.message || '请求失败'))
    }
  },
  (error) => {
    if (error.response?.status === 401) {
      const authStore = useAuthStore()
      authStore.logout()
      router.push('/login')
      ElMessage.error('登录已过期，请重新登录')
    } else {
      ElMessage.error(error.message || '网络错误')
    }
    return Promise.reject(error)
  }
)

export default http
```

#### 2.2 类型定义

**src/types/user.ts**
```typescript
// 用户信息
export interface UserInfo {
  id: number
  username: string
  realName: string
  phone: string
  email: string
  userType: 'CUSTOMER' | 'OPERATOR' | 'COURIER' | 'ADMIN'
  status: string
  avatar?: string
  lastLoginTime?: string
  roles: string[]
  permissions: string[]
  createTime: string
}

// 登录请求
export interface LoginRequest {
  username: string
  password: string
  loginType?: 'USERNAME' | 'PHONE' | 'EMAIL'
  rememberMe?: boolean
}

// 登录响应
export interface LoginResponse {
  token: string
  tokenType: string
  expiresIn: number
  userInfo: UserInfo
  refreshToken?: string
  loginTime: string
}

// 注册请求
export interface RegisterRequest {
  username: string
  password: string
  realName: string
  phone: string
  email: string
  idCard: string
  userType: 'CUSTOMER' | 'OPERATOR' | 'COURIER' | 'ADMIN'
  verificationCode: string
}
```

**src/types/order.ts**
```typescript
// 订单信息
export interface OrderInfo {
  id: number
  orderNumber: string
  userId: number
  senderName: string
  senderPhone: string
  senderAddress: string
  receiverName: string
  receiverPhone: string
  receiverAddress: string
  itemName?: string
  itemWeight?: number
  serviceType: 'STANDARD' | 'EXPRESS' | 'URGENT'
  shippingFee: number
  totalFee: number
  orderStatus: string
  createTime: string
  estimatedDeliveryTime?: string
}

// 创建订单请求
export interface CreateOrderRequest {
  senderName: string
  senderPhone: string
  senderAddress: string
  receiverName: string
  receiverPhone: string
  receiverAddress: string
  itemName: string
  itemWeight: number
  itemValue?: number
  serviceType: 'STANDARD' | 'EXPRESS' | 'URGENT'
  isFragile?: boolean
  remarks?: string
}

// 运费计算请求
export interface ShippingFeeRequest {
  senderAddress: string
  receiverAddress: string
  weight: number
  volume?: number
  serviceType: 'STANDARD' | 'EXPRESS' | 'URGENT'
  isFragile?: boolean
}
```

#### 2.3 API 接口封装

**src/api/auth.ts**
```typescript
import http, { type ApiResponse } from '@/utils/http'
import type { LoginRequest, LoginResponse, RegisterRequest } from '@/types/user'

export const authApi = {
  // 用户登录
  login(data: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    return http.post('/user/login', data)
  },

  // 用户注册
  register(data: RegisterRequest): Promise<ApiResponse<LoginResponse>> {
    return http.post('/user/register', data)
  },

  // 获取用户信息
  getUserInfo(): Promise<ApiResponse<any>> {
    return http.get('/user/profile')
  },

  // 检查用户名是否存在
  checkUsername(username: string): Promise<ApiResponse<boolean>> {
    return http.get('/user/check-username', { params: { username } })
  },

  // 检查手机号是否存在
  checkPhone(phone: string): Promise<ApiResponse<boolean>> {
    return http.get('/user/check-phone', { params: { phone } })
  },

  // 检查邮箱是否存在
  checkEmail(email: string): Promise<ApiResponse<boolean>> {
    return http.get('/user/check-email', { params: { email } })
  },

  // 用户登出
  logout(): Promise<ApiResponse<void>> {
    return http.post('/user/logout')
  }
}
```

**src/api/order.ts**
```typescript
import http, { type ApiResponse } from '@/utils/http'
import type { OrderInfo, CreateOrderRequest, ShippingFeeRequest } from '@/types/order'

export const orderApi = {
  // 创建订单
  createOrder(data: CreateOrderRequest): Promise<ApiResponse<OrderInfo>> {
    return http.post('/order/create', data)
  },

  // 获取订单列表
  getOrderList(params: {
    page?: number
    size?: number
    status?: string
    keyword?: string
  }): Promise<ApiResponse<{ records: OrderInfo[], total: number }>> {
    return http.get('/order/list', { params })
  },

  // 获取订单详情
  getOrderDetail(id: number): Promise<ApiResponse<OrderInfo>> {
    return http.get(`/order/${id}`)
  },

  // 计算运费
  calculateShippingFee(data: ShippingFeeRequest): Promise<ApiResponse<{ fee: number }>> {
    return http.post('/order/calculate-fee', data)
  },

  // 取消订单
  cancelOrder(id: number, reason?: string): Promise<ApiResponse<void>> {
    return http.delete(`/order/${id}`, { data: { reason } })
  },

  // 订单追踪
  trackOrder(orderNumber: string): Promise<ApiResponse<any>> {
    return http.get(`/order/track/${orderNumber}`)
  }
}
```

#### 2.4 状态管理

**src/stores/auth.ts**
```typescript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authApi } from '@/api/auth'
import type { UserInfo, LoginRequest, RegisterRequest } from '@/types/user'
import { ElMessage } from 'element-plus'
import router from '@/router'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string>(localStorage.getItem('token') || '')
  const userInfo = ref<UserInfo | null>(null)
  const loading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const userRole = computed(() => userInfo.value?.userType || '')
  const permissions = computed(() => userInfo.value?.permissions || [])

  // 登录
  const login = async (loginData: LoginRequest): Promise<boolean> => {
    try {
      loading.value = true
      const response = await authApi.login(loginData)
      
      if (response.success) {
        token.value = response.data.token
        userInfo.value = response.data.userInfo
        
        // 保存到本地存储
        localStorage.setItem('token', response.data.token)
        localStorage.setItem('userInfo', JSON.stringify(response.data.userInfo))
        
        ElMessage.success('登录成功')
        
        // 根据用户角色跳转到对应页面
        const redirectPath = getRedirectPath(response.data.userInfo.userType)
        router.push(redirectPath)
        
        return true
      }
      return false
    } catch (error) {
      console.error('登录失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (registerData: RegisterRequest): Promise<boolean> => {
    try {
      loading.value = true
      const response = await authApi.register(registerData)
      
      if (response.success) {
        ElMessage.success('注册成功，请登录')
        router.push('/login')
        return true
      }
      return false
    } catch (error) {
      console.error('注册失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 获取用户信息
  const fetchUserInfo = async (): Promise<void> => {
    try {
      const response = await authApi.getUserInfo()
      if (response.success) {
        userInfo.value = response.data
        localStorage.setItem('userInfo', JSON.stringify(response.data))
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      logout()
    }
  }

  // 登出
  const logout = (): void => {
    token.value = ''
    userInfo.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    router.push('/login')
    ElMessage.success('已退出登录')
  }

  // 初始化用户信息
  const initUserInfo = (): void => {
    const savedUserInfo = localStorage.getItem('userInfo')
    if (savedUserInfo) {
      try {
        userInfo.value = JSON.parse(savedUserInfo)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        logout()
      }
    }
  }

  // 根据用户角色获取重定向路径
  const getRedirectPath = (userType: string): string => {
    switch (userType) {
      case 'ADMIN':
        return '/admin/dashboard'
      case 'OPERATOR':
        return '/operator/dashboard'
      case 'COURIER':
        return '/courier/dashboard'
      case 'CUSTOMER':
      default:
        return '/customer/dashboard'
    }
  }

  // 检查权限
  const hasPermission = (permission: string): boolean => {
    return permissions.value.includes(permission)
  }

  // 检查角色
  const hasRole = (role: string): boolean => {
    return userRole.value === role
  }

  return {
    // 状态
    token,
    userInfo,
    loading,
    // 计算属性
    isLoggedIn,
    userRole,
    permissions,
    // 方法
    login,
    register,
    logout,
    fetchUserInfo,
    initUserInfo,
    hasPermission,
    hasRole
  }
})
```

#### 2.5 路由配置

**src/router/index.ts**
```typescript
import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      redirect: '/login'
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/auth/Login.vue'),
      meta: { requiresAuth: false, title: '用户登录' }
    },
    {
      path: '/register',
      name: 'Register',
      component: () => import('@/views/auth/Register.vue'),
      meta: { requiresAuth: false, title: '用户注册' }
    },
    {
      path: '/customer',
      component: () => import('@/layouts/CustomerLayout.vue'),
      meta: { requiresAuth: true, roles: ['CUSTOMER'] },
      children: [
        {
          path: 'dashboard',
          name: 'CustomerDashboard',
          component: () => import('@/views/customer/Dashboard.vue'),
          meta: { title: '客户首页' }
        },
        {
          path: 'order',
          children: [
            {
              path: 'create',
              name: 'CreateOrder',
              component: () => import('@/views/customer/order/CreateOrder.vue'),
              meta: { title: '创建订单' }
            },
            {
              path: 'list',
              name: 'OrderList',
              component: () => import('@/views/customer/order/OrderList.vue'),
              meta: { title: '订单列表' }
            },
            {
              path: ':id',
              name: 'OrderDetail',
              component: () => import('@/views/customer/order/OrderDetail.vue'),
              meta: { title: '订单详情' }
            }
          ]
        },
        {
          path: 'tracking',
          name: 'OrderTracking',
          component: () => import('@/views/customer/tracking/OrderTracking.vue'),
          meta: { title: '物流追踪' }
        },
        {
          path: 'profile',
          name: 'CustomerProfile',
          component: () => import('@/views/customer/profile/Profile.vue'),
          meta: { title: '个人中心' }
        }
      ]
    },
    {
      path: '/admin',
      component: () => import('@/layouts/AdminLayout.vue'),
      meta: { requiresAuth: true, roles: ['ADMIN'] },
      children: [
        {
          path: 'dashboard',
          name: 'AdminDashboard',
          component: () => import('@/views/admin/Dashboard.vue'),
          meta: { title: '管理后台' }
        },
        {
          path: 'user',
          children: [
            {
              path: 'list',
              name: 'UserList',
              component: () => import('@/views/admin/user/UserList.vue'),
              meta: { title: '用户管理' }
            }
          ]
        },
        {
          path: 'order',
          children: [
            {
              path: 'list',
              name: 'AdminOrderList',
              component: () => import('@/views/admin/order/OrderList.vue'),
              meta: { title: '订单管理' }
            }
          ]
        }
      ]
    },
    {
      path: '/operator',
      component: () => import('@/layouts/OperatorLayout.vue'),
      meta: { requiresAuth: true, roles: ['OPERATOR'] },
      children: [
        {
          path: 'dashboard',
          name: 'OperatorDashboard',
          component: () => import('@/views/operator/Dashboard.vue'),
          meta: { title: '操作员工作台' }
        }
      ]
    },
    {
      path: '/courier',
      component: () => import('@/layouts/CourierLayout.vue'),
      meta: { requiresAuth: true, roles: ['COURIER'] },
      children: [
        {
          path: 'dashboard',
          name: 'CourierDashboard',
          component: () => import('@/views/courier/Dashboard.vue'),
          meta: { title: '配送员工作台' }
        }
      ]
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/error/NotFound.vue'),
      meta: { title: '页面不存在' }
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 物流跟踪系统`
  }
  
  // 检查是否需要登录
  if (to.meta?.requiresAuth && !authStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    next('/login')
    return
  }
  
  // 检查角色权限
  if (to.meta?.roles && authStore.isLoggedIn) {
    const userRole = authStore.userRole
    const requiredRoles = to.meta.roles as string[]
    
    if (!requiredRoles.includes(userRole)) {
      ElMessage.error('没有访问权限')
      next('/login')
      return
    }
  }
  
  next()
})

export default router
```

### 第三阶段：用户认证模块

#### 3.1 登录页面

**src/views/auth/Login.vue**
```vue
<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <h1>物流跟踪系统</h1>
        <p>欢迎登录</p>
      </div>
      
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="用户名/手机号/邮箱"
            size="large"
            prefix-icon="User"
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="密码"
            size="large"
            prefix-icon="Lock"
            show-password
            @keyup.enter="handleLogin"
          />
        </el-form-item>
        
        <el-form-item>
          <div class="login-options">
            <el-checkbox v-model="loginForm.rememberMe">
              记住我
            </el-checkbox>
            <el-link type="primary" :underline="false">
              忘记密码？
            </el-link>
          </div>
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            :loading="authStore.loading"
            @click="handleLogin"
            class="login-button"
          >
            登 录
          </el-button>
        </el-form-item>
        
        <el-form-item>
          <div class="register-link">
            还没有账户？
            <router-link to="/register">立即注册</router-link>
          </div>
        </el-form-item>
      </el-form>
      
      <!-- 测试账号 -->
      <div class="test-accounts">
        <h3>测试账号</h3>
        <div class="account-list">
          <div class="account-item" @click="fillTestAccount('customer01')">
            <span>普通用户</span>
            <small>customer01 / Admin123456</small>
          </div>
          <div class="account-item" @click="fillTestAccount('operator01')">
            <span>操作员</span>
            <small>operator01 / Admin123456</small>
          </div>
          <div class="account-item" @click="fillTestAccount('courier01')">
            <span>配送员</span>
            <small>courier01 / Admin123456</small>
          </div>
          <div class="account-item" @click="fillTestAccount('admin')">
            <span>管理员</span>
            <small>admin / Admin123456</small>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useAuthStore } from '@/stores/auth'
import type { FormInstance, FormRules } from 'element-plus'
import type { LoginRequest } from '@/types/user'

const authStore = useAuthStore()
const loginFormRef = ref<FormInstance>()

const loginForm = reactive<LoginRequest>({
  username: '',
  password: '',
  loginType: 'USERNAME',
  rememberMe: false
})

const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  await loginFormRef.value.validate(async (valid) => {
    if (valid) {
      await authStore.login(loginForm)
    }
  })
}

const fillTestAccount = (username: string) => {
  loginForm.username = username
  loginForm.password = 'Admin123456'
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h1 {
  color: #303133;
  font-size: 28px;
  margin-bottom: 10px;
  font-weight: 600;
}

.login-header p {
  color: #909399;
  font-size: 16px;
}

.login-form {
  width: 100%;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.login-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
}

.register-link {
  text-align: center;
  color: #909399;
  font-size: 14px;
}

.register-link a {
  color: #409eff;
  text-decoration: none;
}

.test-accounts {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.test-accounts h3 {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
  text-align: center;
}

.account-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.account-item {
  padding: 8px 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  text-align: center;
}

.account-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.account-item span {
  display: block;
  font-size: 12px;
  font-weight: 500;
  color: #303133;
}

.account-item small {
  font-size: 10px;
  color: #909399;
  margin-top: 2px;
  display: block;
}
</style>
```

---

## 📋 完整实施步骤

### 步骤1：项目初始化
```bash
# 1. 删除现有项目
cd /e:/桌面/springBoot/node
rm -rf logistics-frontend

# 2. 创建新项目
npm create vue@latest logistics-frontend
# 选择：TypeScript、Router、Pinia、ESLint、Prettier

# 3. 安装依赖
cd logistics-frontend
npm install
npm install element-plus @element-plus/icons-vue axios dayjs lodash-es
npm install @types/lodash-es unplugin-auto-import unplugin-vue-components -D
```

### 步骤2：配置文件设置
1. 替换 `vite.config.ts`
2. 更新 `tsconfig.json`
3. 创建项目目录结构

### 步骤3：核心代码实现
1. 创建 HTTP 请求封装
2. 定义 TypeScript 类型
3. 实现 API 接口封装
4. 配置状态管理
5. 设置路由配置

### 步骤4：认证模块开发
1. 实现登录页面
2. 实现注册页面
3. 配置路由守卫

### 步骤5：测试验证
1. 启动后端服务
2. 执行测试数据SQL
3. 启动前端项目
4. 测试登录功能

这个设计文档提供了完整的前端开发方案，您可以按照步骤逐步实施。每个阶段都有详细的代码和配置，确保项目能够顺利搭建和运行。 