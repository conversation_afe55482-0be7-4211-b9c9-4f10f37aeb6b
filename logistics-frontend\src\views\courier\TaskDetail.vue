<template>
  <div class="courier-task-detail" v-loading="loading">
    <div class="page-header">
      <el-button @click="$router.back()" :icon="ArrowLeft">返回</el-button>
      <h2>任务详情</h2>
      <div class="header-actions">
        <el-button @click="refreshData" :icon="Refresh">刷新</el-button>
        <el-button @click="openNavigation" type="primary" :icon="Guide">导航</el-button>
      </div>
    </div>

    <div v-if="task" class="task-content">
      <!-- 任务基本信息 -->
      <el-card class="task-info-card">
        <template #header>
          <div class="card-header">
            <span>任务信息</span>
            <div class="task-status">
              <el-tag :type="getStatusTag(task.taskStatus)" size="large">
                {{ getStatusText(task.taskStatus) }}
              </el-tag>
            </div>
          </div>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-group">
              <h4>基本信息</h4>
              <div class="info-item">
                <label>任务编号：</label>
                <span>{{ task.taskNumber }}</span>
              </div>
              <div class="info-item">
                <label>订单编号：</label>
                <span>{{ task.orderNumber }}</span>
              </div>
              <div class="info-item">
                <label>任务类型：</label>
                <el-tag :type="getTaskTypeTag(task.taskType)">
                  {{ getTaskTypeText(task.taskType) }}
                </el-tag>
              </div>
              <div class="info-item">
                <label>优先级：</label>
                <el-tag :type="getPriorityTag(task.priority)">
                  {{ getPriorityText(task.priority) }}
                </el-tag>
              </div>
              <div class="info-item">
                <label>创建时间：</label>
                <span>{{ formatTime(task.createTime) }}</span>
              </div>
              <div class="info-item">
                <label>预计完成：</label>
                <span>{{ task.estimatedTime ? formatTime(task.estimatedTime) : '未设置' }}</span>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-group">
              <h4>货物信息</h4>
              <div class="info-item">
                <label>货物描述：</label>
                <span>{{ task.goodsDescription || '未填写' }}</span>
              </div>
              <div class="info-item">
                <label>重量：</label>
                <span>{{ task.weight ? task.weight + 'kg' : '未知' }}</span>
              </div>
              <div class="info-item">
                <label>体积：</label>
                <span>{{ task.volume ? task.volume + 'm³' : '未知' }}</span>
              </div>
              <div class="info-item">
                <label>配送费：</label>
                <span class="delivery-fee">¥{{ task.deliveryFee.toFixed(2) }}</span>
              </div>
              <div class="info-item" v-if="task.specialRequirements">
                <label>特殊要求：</label>
                <span>{{ task.specialRequirements }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 地址信息 -->
      <el-card>
        <template #header>
          <span>地址信息</span>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="address-section">
              <h4>
                <el-icon><Position /></el-icon>
                {{ task.taskType === 'PICKUP' ? '取件地址' : '发件地址' }}
              </h4>
              <div class="address-content">
                <div class="contact">
                  <strong>{{ task.senderName }}</strong>
                  <el-button 
                    type="text" 
                    :icon="Phone" 
                    @click="callContact(task.senderPhone)"
                    class="phone-btn"
                  >
                    {{ task.senderPhone }}
                  </el-button>
                </div>
                <div class="address">{{ task.senderAddress }}</div>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="address-section">
              <h4>
                <el-icon><LocationFilled /></el-icon>
                {{ task.taskType === 'PICKUP' ? '送达地址' : '收件地址' }}
              </h4>
              <div class="address-content">
                <div class="contact">
                  <strong>{{ task.receiverName }}</strong>
                  <el-button 
                    type="text" 
                    :icon="Phone" 
                    @click="callContact(task.receiverPhone)"
                    class="phone-btn"
                  >
                    {{ task.receiverPhone }}
                  </el-button>
                </div>
                <div class="address">{{ task.receiverAddress }}</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 任务进度 -->
      <el-card>
        <template #header>
          <span>任务进度</span>
        </template>

        <el-steps :active="getStepActive()" align-center>
          <el-step title="任务分配" :description="getStepTime('ASSIGNED')" />
          <el-step title="开始执行" :description="getStepTime('PICKED_UP')" />
          <el-step title="运输中" :description="getStepTime('IN_TRANSIT')" />
          <el-step title="任务完成" :description="getStepTime('COMPLETED')" />
        </el-steps>

        <div v-if="task.actualStartTime || task.actualEndTime" class="time-info">
          <div v-if="task.actualStartTime" class="time-item">
            <span>实际开始时间：{{ formatTime(task.actualStartTime) }}</span>
          </div>
          <div v-if="task.actualEndTime" class="time-item">
            <span>实际完成时间：{{ formatTime(task.actualEndTime) }}</span>
          </div>
        </div>
      </el-card>

      <!-- 特殊说明 -->
      <el-card v-if="task.specialInstructions || task.remarks">
        <template #header>
          <span>特殊说明</span>
        </template>

        <div v-if="task.specialInstructions" class="instruction-item">
          <h5>特殊说明：</h5>
          <p>{{ task.specialInstructions }}</p>
        </div>

        <div v-if="task.remarks" class="instruction-item">
          <h5>备注：</h5>
          <p>{{ task.remarks }}</p>
        </div>
      </el-card>

      <!-- 完成凭证 -->
      <el-card v-if="task.completionProof">
        <template #header>
          <span>完成凭证</span>
        </template>

        <div class="proof-section">
          <img :src="task.completionProof" alt="完成凭证" class="proof-image" />
        </div>
      </el-card>

      <!-- 操作按钮 -->
      <el-card class="action-card">
        <template #header>
          <span>任务操作</span>
        </template>

        <div class="action-buttons">
          <el-button 
            v-if="task.taskStatus === 'ASSIGNED'" 
            type="primary" 
            size="large"
            @click="startTask"
            :loading="actionLoading"
          >
            <el-icon><VideoPlay /></el-icon>
            开始任务
          </el-button>

          <el-button 
            v-if="['ASSIGNED', 'PICKED_UP', 'IN_TRANSIT'].includes(task.taskStatus)" 
            type="success" 
            size="large"
            @click="showCompleteDialog = true"
          >
            <el-icon><Check /></el-icon>
            完成任务
          </el-button>

          <el-button 
            v-if="['ASSIGNED', 'PICKED_UP', 'IN_TRANSIT'].includes(task.taskStatus)" 
            size="large"
            @click="updateTaskStatus('PICKED_UP')"
            :loading="actionLoading"
          >
            <el-icon><Box /></el-icon>
            更新为已揽收
          </el-button>

          <el-button 
            v-if="task.taskStatus === 'PICKED_UP'" 
            size="large"
            @click="updateTaskStatus('IN_TRANSIT')"
            :loading="actionLoading"
          >
            <el-icon><Truck /></el-icon>
            更新为运输中
          </el-button>

          <el-button 
            size="large"
            @click="reportProblem"
          >
            <el-icon><Warning /></el-icon>
            报告问题
          </el-button>

          <el-button 
            v-if="['ASSIGNED', 'PICKED_UP'].includes(task.taskStatus)" 
            type="danger" 
            size="large"
            @click="showCancelDialog = true"
          >
            <el-icon><Close /></el-icon>
            取消任务
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 完成任务对话框 -->
    <el-dialog v-model="showCompleteDialog" title="完成任务" width="500px">
      <el-form :model="completeForm" label-width="80px">
        <el-form-item label="任务编号">
          <el-input v-model="completeForm.taskNumber" readonly />
        </el-form-item>
        
        <el-form-item label="完成凭证" required>
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :on-change="handleFileChange"
            :show-file-list="false"
            accept="image/*"
          >
            <el-button>选择图片</el-button>
          </el-upload>
          <div v-if="completeForm.proofImage" class="proof-preview">
            <img :src="completeForm.proofImage" alt="完成凭证" />
          </div>
        </el-form-item>
        
        <el-form-item label="完成备注">
          <el-input 
            v-model="completeForm.remarks" 
            type="textarea" 
            placeholder="请输入完成备注"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCompleteDialog = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="confirmComplete"
          :loading="completing"
        >
          确认完成
        </el-button>
      </template>
    </el-dialog>

    <!-- 取消任务对话框 -->
    <el-dialog v-model="showCancelDialog" title="取消任务" width="400px">
      <el-form label-width="80px">
        <el-form-item label="取消原因" required>
          <el-select v-model="cancelReason" placeholder="请选择取消原因" style="width: 100%">
            <el-option label="客户要求取消" value="customer_cancel" />
            <el-option label="地址有误" value="wrong_address" />
            <el-option label="联系不上客户" value="cannot_contact" />
            <el-option label="货物损坏" value="goods_damaged" />
            <el-option label="其他原因" value="other" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="详细说明">
          <el-input 
            v-model="cancelDescription" 
            type="textarea" 
            placeholder="请详细说明取消原因"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCancelDialog = false">取消</el-button>
        <el-button 
          type="danger" 
          @click="confirmCancel"
          :loading="cancelling"
        >
          确认取消
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  ArrowLeft,
  Refresh,
  Guide,
  Position,
  LocationFilled,
  Phone,
  VideoPlay,
  Check,
  Box,
  Truck,
  Warning,
  Close,
} from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import { courierApi, type DeliveryTask } from '@/api/courier'
import dayjs from 'dayjs'

const route = useRoute()
const router = useRouter()

// 状态
const loading = ref(false)
const actionLoading = ref(false)
const completing = ref(false)
const cancelling = ref(false)
const task = ref<DeliveryTask | null>(null)
const showCompleteDialog = ref(false)
const showCancelDialog = ref(false)

// 表单
const completeForm = reactive({
  taskNumber: '',
  proofImage: '',
  remarks: '',
})

const cancelReason = ref('')
const cancelDescription = ref('')

// 加载任务详情
const loadTaskDetail = async () => {
  const taskId = route.params.id as string
  if (!taskId) return

  loading.value = true
  try {
    const response = await courierApi.getTaskDetail(Number(taskId))
    if (response.data.code === 200) {
      task.value = response.data.data
      completeForm.taskNumber = response.data.data.taskNumber
    } else {
      ElMessage.error('任务不存在')
      router.back()
    }
  } catch (error) {
    console.error('加载任务详情失败:', error)
    // 使用模拟数据
    task.value = {
      id: Number(taskId),
      taskNumber: 'T202401010001',
      orderNumber: 'O202401010001',
      courierId: 1,
      taskType: 'DELIVERY',
      taskStatus: 'ASSIGNED',
      priority: 2,
      weight: 2.5,
      volume: 0.1,
      goodsDescription: '重要文件',
      specialRequirements: '小心轻放，避免折叠',
      deliveryFee: 15.00,
      estimatedTime: '2024-01-01 14:00:00',
      actualStartTime: '',
      actualEndTime: '',
      senderName: '李四',
      senderPhone: '13800138002',
      senderAddress: '北京市朝阳区建国门外大街1号国贸大厦A座1001室',
      receiverName: '王五',
      receiverPhone: '13800138003',
      receiverAddress: '北京市朝阳区三里屯路2号太古里南区S6-32',
      contactName: '王五',
      contactPhone: '13800138003',
      specialInstructions: '请在工作日上午9-12点送达，联系收件人后再送货上门',
      retryCount: 0,
      remarks: '客户要求当日达',
      createTime: '2024-01-01 10:00:00',
      updateTime: '2024-01-01 10:00:00',
    }
    completeForm.taskNumber = task.value.taskNumber
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  loadTaskDetail()
}

// 开始任务
const startTask = async () => {
  if (!task.value) return

  actionLoading.value = true
  try {
    await courierApi.startTask(task.value.id)
    ElMessage.success('任务已开始')
    loadTaskDetail()
  } catch (error) {
    console.error('开始任务失败:', error)
    ElMessage.error('开始任务失败')
  } finally {
    actionLoading.value = false
  }
}

// 更新任务状态
const updateTaskStatus = async (status: string) => {
  if (!task.value) return

  actionLoading.value = true
  try {
    await courierApi.updateTaskStatus(task.value.id, status)
    ElMessage.success('状态更新成功')
    loadTaskDetail()
  } catch (error) {
    console.error('更新状态失败:', error)
    ElMessage.error('更新状态失败')
  } finally {
    actionLoading.value = false
  }
}

// 完成任务
const confirmComplete = async () => {
  if (!completeForm.proofImage) {
    ElMessage.warning('请上传完成凭证')
    return
  }

  if (!task.value) return

  completing.value = true
  try {
    await courierApi.completeTask({
      taskId: task.value.id,
      completionProof: completeForm.proofImage,
      remarks: completeForm.remarks,
    })
    
    ElMessage.success('任务完成成功')
    showCompleteDialog.value = false
    loadTaskDetail()
  } catch (error) {
    console.error('完成任务失败:', error)
    ElMessage.error('完成任务失败')
  } finally {
    completing.value = false
  }
}

// 取消任务
const confirmCancel = async () => {
  if (!cancelReason.value) {
    ElMessage.warning('请选择取消原因')
    return
  }

  if (!task.value) return

  cancelling.value = true
  try {
    const reason = `${cancelReason.value}: ${cancelDescription.value}`
    await courierApi.cancelTask(task.value.id, reason)
    
    ElMessage.success('任务已取消')
    showCancelDialog.value = false
    loadTaskDetail()
  } catch (error) {
    console.error('取消任务失败:', error)
    ElMessage.error('取消任务失败')
  } finally {
    cancelling.value = false
  }
}

// 报告问题
const reportProblem = () => {
  if (!task.value) return
  router.push(`/courier/task/report/${task.value.id}`)
}

// 打电话联系
const callContact = (phone: string) => {
  if (phone) {
    window.open(`tel:${phone}`)
  } else {
    ElMessage.warning('电话号码不存在')
  }
}

// 打开导航
const openNavigation = () => {
  if (!task.value) return
  
  const address = task.value.taskType === 'PICKUP' 
    ? task.value.senderAddress 
    : task.value.receiverAddress
    
  // 使用高德地图或百度地图导航
  const encodedAddress = encodeURIComponent(address)
  const mapUrl = `https://uri.amap.com/navigation?to=${encodedAddress}&mode=car&policy=1&src=myapp&coordinate=gaode&callnative=0`
  
  window.open(mapUrl, '_blank')
}

// 处理文件上传
const handleFileChange = (file: any) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    completeForm.proofImage = e.target?.result as string
  }
  reader.readAsDataURL(file.raw)
}

// 获取步骤激活状态
const getStepActive = () => {
  if (!task.value) return 0
  
  const statusMap = {
    'PENDING': 0,
    'ASSIGNED': 1,
    'PICKED_UP': 2,
    'IN_TRANSIT': 3,
    'COMPLETED': 4,
    'CANCELLED': 1,
  }
  
  return statusMap[task.value.taskStatus] || 0
}

// 获取步骤时间
const getStepTime = (status: string) => {
  if (!task.value) return ''
  
  switch (status) {
    case 'ASSIGNED':
      return task.value.createTime ? formatTime(task.value.createTime) : ''
    case 'PICKED_UP':
      return task.value.actualStartTime ? formatTime(task.value.actualStartTime) : ''
    case 'IN_TRANSIT':
      return task.value.taskStatus === 'IN_TRANSIT' ? '进行中' : ''
    case 'COMPLETED':
      return task.value.actualEndTime ? formatTime(task.value.actualEndTime) : ''
    default:
      return ''
  }
}

// 工具函数
const getTaskTypeText = (type: string) => {
  const textMap = {
    PICKUP: '揽件',
    DELIVERY: '派送',
  }
  return textMap[type] || type
}

const getTaskTypeTag = (type: string) => {
  const tagMap = {
    PICKUP: 'warning',
    DELIVERY: 'success',
  }
  return tagMap[type] || ''
}

const getStatusText = (status: string) => {
  const textMap = {
    PENDING: '待分配',
    ASSIGNED: '已分配',
    PICKED_UP: '已揽收',
    IN_TRANSIT: '运输中',
    COMPLETED: '已完成',
    CANCELLED: '已取消',
  }
  return textMap[status] || status
}

const getStatusTag = (status: string) => {
  const tagMap = {
    PENDING: 'info',
    ASSIGNED: 'warning',
    PICKED_UP: 'primary',
    IN_TRANSIT: 'warning',
    COMPLETED: 'success',
    CANCELLED: 'danger',
  }
  return tagMap[status] || ''
}

const getPriorityText = (priority: number) => {
  const textMap = {
    1: '低',
    2: '中',
    3: '高',
    4: '紧急',
  }
  return textMap[priority] || '普通'
}

const getPriorityTag = (priority: number) => {
  const tagMap = {
    1: 'info',
    2: '',
    3: 'warning',
    4: 'danger',
  }
  return tagMap[priority] || ''
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm')
}

onMounted(() => {
  loadTaskDetail()
})
</script>

<style scoped>
.courier-task-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 0 15px;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.task-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-status {
  display: flex;
  align-items: center;
}

.info-group {
  margin-bottom: 20px;
}

.info-group h4 {
  margin: 0 0 15px 0;
  color: #333;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 10px;
}

.info-item {
  display: flex;
  margin-bottom: 10px;
  align-items: center;
}

.info-item label {
  min-width: 100px;
  color: #666;
  font-weight: 500;
}

.delivery-fee {
  font-weight: bold;
  color: #f56c6c;
  font-size: 16px;
}

.address-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.address-content {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.contact {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.contact strong {
  margin-right: 10px;
}

.phone-btn {
  padding: 0;
  color: #409eff;
}

.address {
  color: #666;
  line-height: 1.5;
}

.time-info {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.time-item {
  margin-bottom: 8px;
  color: #666;
}

.instruction-item {
  margin-bottom: 15px;
}

.instruction-item h5 {
  margin: 0 0 8px 0;
  color: #333;
}

.instruction-item p {
  margin: 0;
  color: #666;
  line-height: 1.6;
}

.proof-section {
  text-align: center;
}

.proof-image {
  max-width: 400px;
  max-height: 400px;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.action-card {
  position: sticky;
  bottom: 20px;
  background: white;
  box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
}

.proof-preview {
  margin-top: 10px;
}

.proof-preview img {
  max-width: 200px;
  max-height: 200px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons .el-button {
    width: 100%;
  }
}
</style> 