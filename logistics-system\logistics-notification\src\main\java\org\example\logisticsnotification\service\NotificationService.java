package org.example.logisticsnotification.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.example.logisticsnotification.entity.Notification;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 通知服务接口
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
public interface NotificationService {

    /**
     * 创建通知
     */
    Notification createNotification(Notification notification);

    /**
     * 批量创建通知
     */
    boolean batchCreateNotifications(List<Notification> notifications);

    /**
     * 发送通知
     */
    boolean sendNotification(Long notificationId);

    /**
     * 批量发送通知
     */
    int batchSendNotifications(List<Long> notificationIds);

    /**
     * 创建并发送通知
     */
    Notification createAndSendNotification(Notification notification);

    /**
     * 发送业务通知
     */
    boolean sendBusinessNotification(String businessType, Long businessId, String templateCode, 
                                   String recipient, Map<String, Object> params);

    /**
     * 发送系统通知
     */
    boolean sendSystemNotification(Long userId, String templateCode, Map<String, Object> params);

    /**
     * 更新通知
     */
    boolean updateNotification(Notification notification);

    /**
     * 更新通知状态
     */
    boolean updateNotificationStatus(Long notificationId, Integer status, String failureReason);

    /**
     * 批量更新通知状态
     */
    int batchUpdateNotificationStatus(List<Long> notificationIds, Integer status, String failureReason);

    /**
     * 更新重试信息
     */
    boolean updateRetryInfo(Long notificationId, Integer retryCount, LocalDateTime nextRetryTime, String failureReason);

    /**
     * 标记为已读
     */
    boolean markAsRead(List<Long> notificationIds, Long userId);

    /**
     * 标记所有为已读
     */
    boolean markAllAsRead(Long userId);

    /**
     * 删除通知
     */
    boolean deleteNotification(Long notificationId);

    /**
     * 批量删除通知
     */
    boolean batchDeleteNotifications(List<Long> notificationIds);

    /**
     * 根据ID查询通知
     */
    Notification getNotificationById(Long notificationId);

    /**
     * 根据用户ID查询通知列表
     */
    List<Notification> getNotificationsByUserId(Long userId, Integer isRead);

    /**
     * 根据业务信息查询通知
     */
    List<Notification> getNotificationsByBusiness(String businessType, Long businessId);

    /**
     * 根据通知类型查询通知列表
     */
    List<Notification> getNotificationsByType(String notificationType, Integer sendStatus);

    /**
     * 查询待发送的通知
     */
    List<Notification> getPendingNotifications(String notificationType, Integer limit);

    /**
     * 查询需要重试的通知
     */
    List<Notification> getRetryNotifications(Integer limit);

    /**
     * 查询失败的通知
     */
    List<Notification> getFailedNotifications(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 分页查询通知
     */
    IPage<Notification> getNotificationsPage(Page<Notification> page, Long userId, String notificationType,
                                           Integer sendStatus, String businessType, 
                                           LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计通知数据
     */
    Map<String, Object> getNotificationStatistics(Long userId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计各状态通知数量
     */
    List<Map<String, Object>> countNotificationsByStatus(String notificationType);

    /**
     * 统计各类型通知数量
     */
    List<Map<String, Object>> countNotificationsByType(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询用户未读通知数量
     */
    Long countUnreadNotifications(Long userId);

    /**
     * 处理通知发送
     */
    void processNotificationSending();

    /**
     * 处理通知重试
     */
    void processNotificationRetry();

    /**
     * 清理过期通知
     */
    int cleanExpiredNotifications(LocalDateTime expireTime);

    /**
     * 检查通知发送状态
     */
    boolean checkNotificationStatus(Long notificationId);

    /**
     * 获取通知发送统计
     */
    Map<String, Object> getSendingStatistics(LocalDateTime startTime, LocalDateTime endTime);
} 