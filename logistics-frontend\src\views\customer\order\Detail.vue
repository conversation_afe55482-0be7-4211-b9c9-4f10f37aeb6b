<template>
  <div class="order-detail" v-loading="loading">
    <div class="page-header">
      <el-button @click="$router.back()" :icon="ArrowLeft">返回</el-button>
      <h2>订单详情</h2>
      <div class="header-actions">
        <el-button @click="trackOrder" :icon="Search">物流追踪</el-button>
        <el-button v-if="order?.orderStatus === 'PENDING'" type="danger" @click="cancelOrder">
          取消订单
        </el-button>
        <el-button
          v-if="order?.paymentStatus === 0 && order?.orderStatus !== 'CANCELLED'"
          type="primary"
          @click="payOrder"
        >
          立即支付
        </el-button>
      </div>
    </div>

    <div v-if="order" class="order-content">
      <!-- 订单状态 -->
      <el-card class="status-card">
        <div class="status-info">
          <div class="status-icon">
            <el-icon size="40" :color="getStatusColor(order.orderStatus)">
              <component :is="getStatusIcon(order.orderStatus)" />
            </el-icon>
          </div>
          <div class="status-text">
            <h3>{{ getStatusText(order.orderStatus) }}</h3>
            <p v-if="order.estimatedDeliveryTime">
              预计送达时间：{{ formatTime(order.estimatedDeliveryTime) }}
            </p>
          </div>
        </div>
      </el-card>

      <!-- 订单信息 -->
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><Document /></el-icon>
            <span>订单信息</span>
          </div>
        </template>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">{{ order.orderNumber }}</el-descriptions-item>
          <el-descriptions-item label="下单时间">{{
            formatTime(order.createTime)
          }}</el-descriptions-item>
          <el-descriptions-item label="服务类型">{{
            getServiceTypeText(order.serviceType)
          }}</el-descriptions-item>
          <el-descriptions-item label="支付方式">{{
            getPaymentMethodText(order.paymentMethod)
          }}</el-descriptions-item>
          <el-descriptions-item label="支付状态">
            <el-tag :type="getPaymentStatusTagType(order.paymentStatus)">
              {{ getPaymentStatusText(order.paymentStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getStatusTagType(order.orderStatus)">
              {{ getStatusText(order.orderStatus) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <div v-if="order.remarks" class="remarks">
          <h4>备注信息</h4>
          <p>{{ order.remarks }}</p>
        </div>
      </el-card>

      <!-- 寄收件信息 -->
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><LocationInformation /></el-icon>
            <span>寄收件信息</span>
          </div>
        </template>

        <div class="address-container">
          <div class="address-section">
            <h4>寄件人信息</h4>
            <div class="address-info">
              <p>
                <strong>{{ order.senderName }}</strong> {{ order.senderPhone }}
              </p>
              <p>{{ order.senderAddress }}</p>
            </div>
          </div>

          <div class="address-arrow">
            <el-icon size="24"><Right /></el-icon>
          </div>

          <div class="address-section">
            <h4>收件人信息</h4>
            <div class="address-info">
              <p>
                <strong>{{ order.receiverName }}</strong> {{ order.receiverPhone }}
              </p>
              <p>{{ order.receiverAddress }}</p>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 物品信息 -->
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><Box /></el-icon>
            <span>物品信息</span>
          </div>
        </template>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="物品名称">{{
            order.itemName || '未填写'
          }}</el-descriptions-item>
          <el-descriptions-item label="物品类型">{{
            order.itemType || '未填写'
          }}</el-descriptions-item>
          <el-descriptions-item label="重量">{{
            order.itemWeight ? `${order.itemWeight}kg` : '未填写'
          }}</el-descriptions-item>
          <el-descriptions-item label="体积">{{
            order.itemVolume ? `${order.itemVolume}m³` : '未填写'
          }}</el-descriptions-item>
          <el-descriptions-item label="尺寸" span="2">
            <span v-if="order.itemLength && order.itemWidth && order.itemHeight">
              {{ order.itemLength }}cm × {{ order.itemWidth }}cm × {{ order.itemHeight }}cm
            </span>
            <span v-else>未填写</span>
          </el-descriptions-item>
          <el-descriptions-item label="物品价值">{{
            order.itemValue ? `¥${order.itemValue}` : '未填写'
          }}</el-descriptions-item>
          <el-descriptions-item label="易碎品">{{
            order.isFragile ? '是' : '否'
          }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 费用信息 -->
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><Money /></el-icon>
            <span>费用信息</span>
          </div>
        </template>

        <div class="fee-details">
          <div class="fee-item">
            <span>运费：</span>
            <span>¥{{ order.shippingFee.toFixed(2) }}</span>
          </div>
          <div class="fee-item">
            <span>保险费：</span>
            <span>¥{{ order.insuranceFee.toFixed(2) }}</span>
          </div>
          <div class="fee-item">
            <span>包装费：</span>
            <span>¥{{ order.packingFee.toFixed(2) }}</span>
          </div>
          <div class="fee-item total">
            <span>总计：</span>
            <span class="total-amount">¥{{ order.totalFee.toFixed(2) }}</span>
          </div>
        </div>
      </el-card>

      <!-- 时间轴 -->
      <el-card v-if="timelineData.length > 0">
        <template #header>
          <div class="card-header">
            <el-icon><Clock /></el-icon>
            <span>订单轨迹</span>
          </div>
        </template>

        <el-timeline>
          <el-timeline-item
            v-for="item in timelineData"
            :key="item.timestamp"
            :timestamp="item.timestamp"
            :type="item.type"
          >
            {{ item.content }}
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Search,
  Document,
  LocationInformation,
  Box,
  Money,
  Clock,
  Right,
  CircleCheck,
  Warning,
  Van,
  Timer,
} from '@element-plus/icons-vue'
import { orderApi } from '@/api/order'
import type { Order, OrderStatus, ServiceType, PaymentMethod, PaymentStatus } from '@/types/order'
import dayjs from 'dayjs'

const route = useRoute()
const router = useRouter()

// 状态
const loading = ref(false)
const order = ref<Order | null>(null)

// 时间轴数据
const timelineData = computed(() => {
  if (!order.value) return []

  const timeline = []

  // 订单创建
  timeline.push({
    timestamp: formatTime(order.value.createTime),
    content: '订单已创建',
    type: 'primary',
  })

  // 根据订单状态添加时间轴项
  if (order.value.pickupTime) {
    timeline.push({
      timestamp: formatTime(order.value.pickupTime),
      content: '货物已揽收',
      type: 'success',
    })
  }

  if (order.value.deliveryTime) {
    timeline.push({
      timestamp: formatTime(order.value.deliveryTime),
      content: '开始配送',
      type: 'warning',
    })
  }

  if (order.value.signTime) {
    timeline.push({
      timestamp: formatTime(order.value.signTime),
      content: '货物已签收',
      type: 'success',
    })
  }

  return timeline.reverse()
})

// 加载订单详情
const loadOrderDetail = async () => {
  const orderId = route.params.id as string
  if (!orderId) return

  loading.value = true
  try {
    const response = await orderApi.getOrderById(Number(orderId))
    if (response.code === 200) {
      order.value = response.data
    } else {
      ElMessage.error('订单不存在')
      router.back()
    }
  } catch (error) {
    console.error('加载订单详情失败:', error)
    ElMessage.error('加载订单详情失败')
    router.back()
  } finally {
    loading.value = false
  }
}

// 物流追踪
const trackOrder = () => {
  if (order.value) {
    router.push(`/customer/tracking/detail?orderNumber=${order.value.orderNumber}`)
  }
}

// 取消订单
const cancelOrder = async () => {
  if (!order.value) return

  try {
    await ElMessageBox.confirm('确定要取消这个订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const response = await orderApi.cancelOrder(order.value.id)
    if (response.data.code === 200) {
      ElMessage.success('订单已取消')
      loadOrderDetail()
    } else {
      ElMessage.error(response.data.message || '取消失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消订单失败:', error)
      ElMessage.error('取消失败')
    }
  }
}

// 支付订单
const payOrder = async () => {
  if (!order.value) return

  try {
    const response = await orderApi.payOrder(order.value.id, 'ONLINE')
    if (response.data.code === 200) {
      ElMessage.success('支付成功')
      loadOrderDetail()
    } else {
      ElMessage.error(response.data.message || '支付失败')
    }
  } catch (error) {
    console.error('支付失败:', error)
    ElMessage.error('支付失败')
  }
}

// 工具函数
const getStatusIcon = (status: OrderStatus) => {
  const iconMap = {
    PENDING: Timer,
    PICKED_UP: CircleCheck,
    IN_TRANSIT: Van,
    OUT_FOR_DELIVERY: Van,
    DELIVERED: CircleCheck,
    CANCELLED: Warning,
    RETURNED: Warning,
  }
  return iconMap[status] || Timer
}

const getStatusColor = (status: OrderStatus) => {
  const colorMap = {
    PENDING: '#909399',
    PICKED_UP: '#409EFF',
    IN_TRANSIT: '#E6A23C',
    OUT_FOR_DELIVERY: '#E6A23C',
    DELIVERED: '#67C23A',
    CANCELLED: '#F56C6C',
    RETURNED: '#F56C6C',
  }
  return colorMap[status] || '#909399'
}

const getStatusTagType = (status: OrderStatus) => {
  const typeMap = {
    PENDING: '',
    PICKED_UP: 'info',
    IN_TRANSIT: 'warning',
    OUT_FOR_DELIVERY: 'warning',
    DELIVERED: 'success',
    CANCELLED: 'danger',
    RETURNED: 'danger',
  }
  return typeMap[status] || ''
}

const getStatusText = (status: OrderStatus) => {
  const textMap = {
    PENDING: '待发货',
    PICKED_UP: '已揽收',
    IN_TRANSIT: '运输中',
    OUT_FOR_DELIVERY: '派送中',
    DELIVERED: '已送达',
    CANCELLED: '已取消',
    RETURNED: '已退回',
  }
  return textMap[status] || status
}

const getServiceTypeText = (serviceType: ServiceType) => {
  const textMap = {
    STANDARD: '标准快递',
    EXPRESS: '特快专递',
    URGENT: '加急服务',
  }
  return textMap[serviceType] || serviceType
}

const getPaymentMethodText = (paymentMethod: PaymentMethod) => {
  const textMap = {
    ONLINE: '在线支付',
    COD: '货到付款',
  }
  return textMap[paymentMethod] || paymentMethod
}

const getPaymentStatusText = (paymentStatus: PaymentStatus) => {
  const textMap = {
    0: '未支付',
    1: '已支付',
    2: '已退款',
  }
  return textMap[paymentStatus] || '未知'
}

const getPaymentStatusTagType = (paymentStatus: PaymentStatus) => {
  const typeMap = {
    0: 'warning',
    1: 'success',
    2: 'info',
  }
  return typeMap[paymentStatus] || ''
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

onMounted(() => {
  loadOrderDetail()
})
</script>

<style scoped>
.order-detail {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.order-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.status-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.status-text h3 {
  margin: 0 0 10px 0;
  font-size: 24px;
}

.status-text p {
  margin: 0;
  opacity: 0.9;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.remarks {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.remarks h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.remarks p {
  margin: 0;
  color: #666;
  line-height: 1.6;
}

.address-container {
  display: flex;
  align-items: center;
  gap: 30px;
}

.address-section {
  flex: 1;
}

.address-section h4 {
  margin: 0 0 15px 0;
  color: #333;
}

.address-info p {
  margin: 5px 0;
  color: #666;
}

.address-arrow {
  color: #409eff;
}

.fee-details {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 6px;
}

.fee-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 14px;
}

.fee-item.total {
  font-size: 18px;
  font-weight: bold;
  border-top: 1px solid #e4e7ed;
  padding-top: 15px;
  margin-top: 15px;
}

.total-amount {
  color: #f56c6c;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .address-container {
    flex-direction: column;
    gap: 20px;
  }

  .address-arrow {
    transform: rotate(90deg);
  }

  .status-info {
    flex-direction: column;
    text-align: center;
  }
}
</style>
