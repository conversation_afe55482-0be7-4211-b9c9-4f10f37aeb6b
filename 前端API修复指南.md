# 前端API修复指南

## 概述
本文档记录了前端API与后端Controller的对接修复工作，确保前后端接口完全匹配。

## 修复原则
1. 以后端实际实现的Controller为准
2. 保持前端接口签名的向下兼容性
3. 统一错误处理和响应格式
4. 确保类型定义的准确性

## 主要修复内容

### 1. 用户API修复 (user.ts)

#### 后端接口路径 (UserController)
- `/user/register` - 用户注册
- `/user/login` - 用户登录  
- `/user/profile` - 获取用户信息
- `/user/profile/edit` - 获取编辑信息
- `/user/profile` (PUT) - 更新用户信息
- `/user/change-password` - 修改密码
- `/user/upload/avatar` - 上传头像
- `/user/addresses` - 地址管理相关

#### 主要修改
- 状态字段改为number类型 (0/1)
- 添加登录注册接口
- 修正上传路径为 `/user/upload/avatar`
- 统一响应格式

### 2. 地图API修复 (map.ts)

#### 后端接口路径 (FrontendMapController)
- `/map/config` - 获取地图配置
- `/map/address-to-location` - 地址转坐标
- `/map/location-to-address` - 坐标转地址
- `/map/distance` - 距离计算
- `/map/route/best` - 最优路径
- `/map/regions` - 省市区数据
- `/api/map/poi/search` - POI搜索
- `/api/map/poi/around` - 周边搜索
- `/api/map/weather` - 天气信息

#### 主要修改
- 修正接口路径
- 调整参数格式 (lat/lng vs latitude/longitude)
- 更新省市区数据结构

### 3. 配送员API修复 (courier.ts)

#### 后端接口路径 (CourierController)
- `/api/delivery/courier` - CRUD操作
- `/api/delivery/courier/page` - 分页查询
- `/api/delivery/courier/{id}` - 根据ID查询
- `/api/delivery/courier/user/{userId}` - 根据用户ID查询
- `/api/delivery/courier/{id}/location` - 更新位置
- `/api/delivery/courier/{id}/status` - 更新状态
- `/api/delivery/courier/available` - 可用配送员
- `/api/delivery/courier/assign-best` - 自动分配
- `/api/delivery/courier/statistics/status` - 状态统计

#### 主要修改
- 移除模拟数据，使用真实API
- 调整数据结构匹配后端实体
- 修正接口路径和参数格式

### 4. 订单API修复 (order.ts)

#### 后端接口路径 (OrderController)
- `/order/create` - 创建订单
- `/order/calculate-price` - 价格计算
- `/order/{id}` - 订单详情
- `/order/number/{orderNumber}` - 根据订单号查询
- `/order/page` - 分页查询
- `/order/{id}/pay` - 支付订单
- `/order/{id}/cancel` - 取消订单
- `/order/{id}/confirm` - 确认收货

#### 主要修改
- 价格计算接口数据格式修复
- 创建订单数据转换优化
- 统一响应处理

### 5. 地址API修复 (address.ts)

#### 后端接口路径 (AddressController)
- `/user/addresses` - 地址CRUD
- `/user/addresses/{id}` - 地址详情
- `/user/addresses/{id}/default` - 设置默认地址

#### 主要修改
- 使用真实的地址管理接口
- 省市区数据通过地图模块获取
- 修正数据结构

### 6. 通知API修复 (notification.ts)

#### 后端接口路径 (NotificationController)
- `/api/notification` - 通知CRUD
- `/api/notification/page` - 分页查询
- `/api/notification/{id}/read` - 标记已读
- `/api/notification/read-all` - 全部已读

#### 主要修改
- 修正接口路径前缀
- 调整查询参数格式

### 7. 物流跟踪API修复 (tracking.ts)

#### 后端接口路径 (TrackingController)
- `/logistics/tracking/order/{orderNumber}` - 根据订单号查询
- `/logistics/tracking/{id}/nodes` - 轨迹节点
- `/logistics/tracking/{id}/update` - 更新位置
- `/logistics/location/range` - 范围查询
- `/logistics/location/nearby` - 附近查询

#### 主要修改
- 修正接口路径
- 调整数据结构匹配后端

## 通用修复模式

### 1. 响应格式统一
```typescript
interface ApiResponse<T> {
  code: number
  message: string
  data: T
}
```

### 2. 分页参数统一
```typescript
// 前端发送
{ pageNum: 1, pageSize: 10 }

// 后端返回
{
  records: T[],
  total: number,
  current: number,
  size: number
}
```

### 3. 错误处理统一
```typescript
.catch((error: any) => {
  const errorMsg = error.response?.data?.message || error.message || '操作失败'
  ElMessage.error(errorMsg)
})
```

## 测试验证

### 1. 接口测试
- 启动后端服务
- 前端调用各API接口
- 验证数据格式和响应

### 2. 功能测试  
- 用户登录注册
- 订单创建和管理
- 配送员操作
- 地图功能
- 通知系统

### 3. 集成测试
- 完整业务流程测试
- 错误场景处理
- 性能测试

## 注意事项

1. **向下兼容**: 修改时保持前端组件调用的兼容性
2. **类型安全**: 确保TypeScript类型定义准确
3. **错误处理**: 统一错误提示和处理逻辑
4. **性能优化**: 避免不必要的API调用
5. **文档更新**: 及时更新API文档

## 后续工作

1. 完善单元测试
2. 添加API接口文档
3. 优化错误处理机制
4. 性能监控和优化
5. 安全性加固 