# 前端界面修改指南

## 🎯 前端修改概述

**好消息！** 你的前端代码已经非常完善，只需要进行少量修改来支持新的订单状态。

## 📝 需要修改的文件

### 1. 更新订单状态枚举 (必须修改)

**文件**: `logistics-frontend/src/utils/orderStatus.ts`

需要添加新的状态到状态映射中：

```typescript
// 在ORDER_STATUS_MAP中添加新状态
export const ORDER_STATUS_MAP: Record<string, string> = {
  // ... 现有状态
  SORTING: '分拣中',           // 新增
  DISPATCHING: '发车中',       // 新增  
  TRANSFERRING: '中转中',      // 新增
  // ... 其他状态
}

// 在ORDER_STATUS_TYPE_MAP中添加新状态的UI类型
export const ORDER_STATUS_TYPE_MAP: Record<string, string> = {
  // ... 现有状态
  SORTING: 'warning',          // 新增
  DISPATCHING: 'warning',      // 新增
  TRANSFERRING: 'warning',     // 新增
  // ... 其他状态
}

// 更新订单状态流程
export function getOrderStatusFlow(): Array<{ status: OrderStatus; text: string; type: string }> {
  return [
    { status: OrderStatus.PENDING, text: '待处理', type: 'warning' },
    { status: OrderStatus.PAID, text: '已支付', type: 'primary' },
    { status: OrderStatus.PICKUP_ASSIGNED, text: '已分配揽件员', type: 'primary' },
    { status: OrderStatus.PICKUP, text: '已揽收', type: 'success' },
    { status: 'SORTING', text: '分拣中', type: 'warning' },        // 新增
    { status: 'DISPATCHING', text: '发车中', type: 'warning' },    // 新增
    { status: 'TRANSFERRING', text: '中转中', type: 'warning' },   // 新增
    { status: OrderStatus.IN_TRANSIT, text: '运输中', type: 'success' },
    { status: OrderStatus.ARRIVED, text: '到达目的地', type: 'success' },
    { status: OrderStatus.OUT_FOR_DELIVERY, text: '派送中', type: 'success' },
    { status: OrderStatus.DELIVERED, text: '已配送', type: 'success' },
  ]
}
```

### 2. 更新各页面的状态显示 (建议修改)

#### 2.1 客户订单列表页面
**文件**: `logistics-frontend/src/views/customer/order/List.vue`

```typescript
// 更新getStatusText函数
const getStatusText = (status: OrderStatus) => {
  const textMap = {
    PENDING: '待发货',
    PAID: '已支付',
    PICKUP_ASSIGNED: '已分配揽件员',
    PICKED_UP: '已揽收',
    PICKUP: '已揽收',
    SORTING: '分拣中',        // 新增
    DISPATCHING: '发车中',    // 新增
    TRANSFERRING: '中转中',   // 新增
    IN_TRANSIT: '运输中',
    ARRIVED: '到达目的地',
    OUT_FOR_DELIVERY: '派送中',
    DELIVERED: '已送达',
    SIGNED: '已签收',
    CANCELLED: '已取消',
    RETURNED: '已退回',
  }
  return textMap[status] || status
}
```

#### 2.2 订单详情页面
**文件**: `logistics-frontend/src/views/customer/order/Detail.vue`

```typescript
// 更新状态相关函数
const getStatusText = (status: OrderStatus) => {
  const textMap = {
    PENDING: '待发货',
    PAID: '已支付',
    PICKUP_ASSIGNED: '已分配揽件员',
    PICKED_UP: '已揽收',
    PICKUP: '已揽收',
    SORTING: '分拣中',        // 新增
    DISPATCHING: '发车中',    // 新增
    TRANSFERRING: '中转中',   // 新增
    IN_TRANSIT: '运输中',
    ARRIVED: '到达目的地',
    OUT_FOR_DELIVERY: '派送中',
    DELIVERED: '已送达',
    SIGNED: '已签收',
    CANCELLED: '已取消',
    RETURNED: '已退回',
  }
  return textMap[status] || status
}

const getStatusColor = (status: OrderStatus) => {
  const colorMap = {
    PENDING: '#909399',
    PAID: '#409EFF',
    PICKUP_ASSIGNED: '#409EFF',
    PICKED_UP: '#409EFF',
    PICKUP: '#409EFF',
    SORTING: '#E6A23C',       // 新增
    DISPATCHING: '#E6A23C',   // 新增
    TRANSFERRING: '#E6A23C',  // 新增
    IN_TRANSIT: '#E6A23C',
    ARRIVED: '#67C23A',
    OUT_FOR_DELIVERY: '#E6A23C',
    DELIVERED: '#67C23A',
    SIGNED: '#67C23A',
    CANCELLED: '#F56C6C',
    RETURNED: '#F56C6C',
  }
  return colorMap[status] || '#909399'
}
```

### 3. 新增邮件通知设置页面 (可选)

**新建文件**: `logistics-frontend/src/views/customer/settings/Notifications.vue`

```vue
<template>
  <div class="notification-settings">
    <el-card>
      <template #header>
        <div class="card-header">
          <el-icon><Bell /></el-icon>
          <span>通知设置</span>
        </div>
      </template>

      <el-form :model="notificationForm" label-width="120px">
        <el-form-item label="邮件通知">
          <el-switch
            v-model="notificationForm.emailEnabled"
            active-text="开启"
            inactive-text="关闭"
          />
        </el-form-item>

        <el-form-item label="邮箱地址" v-if="notificationForm.emailEnabled">
          <el-input
            v-model="notificationForm.email"
            placeholder="请输入邮箱地址"
            style="width: 300px"
          />
        </el-form-item>

        <el-form-item label="通知类型">
          <el-checkbox-group v-model="notificationForm.notificationTypes">
            <el-checkbox label="ORDER_CREATED">订单创建</el-checkbox>
            <el-checkbox label="PAYMENT_SUCCESS">支付成功</el-checkbox>
            <el-checkbox label="STATUS_UPDATE">状态更新</el-checkbox>
            <el-checkbox label="DELIVERY_COMPLETED">配送完成</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="saveSettings">保存设置</el-button>
          <el-button @click="testEmail">发送测试邮件</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Bell } from '@element-plus/icons-vue'

const notificationForm = ref({
  emailEnabled: true,
  email: '',
  notificationTypes: ['ORDER_CREATED', 'PAYMENT_SUCCESS', 'STATUS_UPDATE', 'DELIVERY_COMPLETED']
})

const saveSettings = async () => {
  try {
    // 调用保存设置的API
    ElMessage.success('设置保存成功')
  } catch (error) {
    ElMessage.error('设置保存失败')
  }
}

const testEmail = async () => {
  try {
    // 调用发送测试邮件的API
    ElMessage.success('测试邮件发送成功，请查收')
  } catch (error) {
    ElMessage.error('测试邮件发送失败')
  }
}

onMounted(() => {
  // 加载用户的通知设置
})
</script>
```

### 4. 操作员端新增网点管理页面 (可选)

**新建文件**: `logistics-frontend/src/views/operator/StationManagement.vue`

```vue
<template>
  <div class="station-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <el-icon><OfficeBuilding /></el-icon>
          <span>网点管理</span>
          <el-button type="primary" @click="showAddDialog">新增网点</el-button>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="网点类型">
          <el-select v-model="searchForm.stationType" placeholder="请选择">
            <el-option label="全部" value="" />
            <el-option label="揽件点" value="PICKUP" />
            <el-option label="中转站" value="TRANSIT" />
            <el-option label="派送点" value="DELIVERY" />
          </el-select>
        </el-form-item>

        <el-form-item label="城市">
          <el-input v-model="searchForm.city" placeholder="请输入城市" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 网点列表 -->
      <el-table :data="stationList" v-loading="loading">
        <el-table-column prop="stationCode" label="网点编码" />
        <el-table-column prop="stationName" label="网点名称" />
        <el-table-column prop="stationType" label="网点类型">
          <template #default="{ row }">
            <el-tag :type="getStationTypeTag(row.stationType)">
              {{ getStationTypeText(row.stationType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="city" label="城市" />
        <el-table-column prop="contactPerson" label="联系人" />
        <el-table-column prop="contactPhone" label="联系电话" />
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button size="small" @click="editStation(row)">编辑</el-button>
            <el-button size="small" type="danger" @click="deleteStation(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        @current-change="loadStationList"
        @size-change="loadStationList"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { OfficeBuilding } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const stationList = ref([])
const searchForm = ref({
  stationType: '',
  city: ''
})
const pagination = ref({
  page: 1,
  size: 10,
  total: 0
})

// 方法
const loadStationList = async () => {
  loading.value = true
  try {
    // 调用获取网点列表的API
    // const response = await stationApi.getStationList(...)
    // stationList.value = response.data.records
    // pagination.value.total = response.data.total
  } catch (error) {
    ElMessage.error('加载网点列表失败')
  } finally {
    loading.value = false
  }
}

const getStationTypeText = (type: string) => {
  const typeMap = {
    PICKUP: '揽件点',
    TRANSIT: '中转站',
    DELIVERY: '派送点'
  }
  return typeMap[type] || type
}

const getStationTypeTag = (type: string) => {
  const tagMap = {
    PICKUP: 'primary',
    TRANSIT: 'warning',
    DELIVERY: 'success'
  }
  return tagMap[type] || 'info'
}

onMounted(() => {
  loadStationList()
})
</script>
```

## 📋 修改步骤总结

### 第一步：更新状态枚举 (必须)
1. 修改 `orderStatus.ts` 文件，添加新状态
2. 更新状态映射和UI类型映射
3. 更新状态流程函数

### 第二步：更新页面显示 (建议)
1. 更新客户端订单相关页面的状态显示函数
2. 更新管理员端订单页面的状态显示
3. 确保所有状态显示一致

### 第三步：新增功能页面 (可选)
1. 添加邮件通知设置页面
2. 添加网点管理页面
3. 更新路由配置

### 第四步：测试验证
1. 测试新状态的显示效果
2. 测试状态流转的UI反馈
3. 验证邮件通知功能

## ⚠️ 注意事项

1. **状态一致性**：确保前后端状态枚举完全一致
2. **UI适配**：新状态的颜色和图标要与现有风格保持一致
3. **用户体验**：状态变更要有明确的视觉反馈
4. **测试覆盖**：每个新状态都要测试显示效果

## 🎯 前端修改优先级

### 高优先级 (必须修改)
- ✅ 更新 `orderStatus.ts` 状态枚举
- ✅ 更新订单列表和详情页面的状态显示

### 中优先级 (建议修改)  
- 🔄 添加邮件通知设置页面
- 🔄 优化状态流程展示

### 低优先级 (可选修改)
- ⭕ 添加网点管理页面
- ⭕ 添加更多统计图表

总的来说，你的前端代码结构非常好，只需要少量修改就能完美支持新的业务流程！
