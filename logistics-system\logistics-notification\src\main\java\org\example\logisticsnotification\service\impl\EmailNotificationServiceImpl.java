package org.example.logisticsnotification.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.example.logisticsnotification.constants.NotificationType;
import org.example.logisticsnotification.entity.Notification;
import org.example.logisticsnotification.service.EmailNotificationService;
import org.example.logisticsnotification.service.NotificationService;
import org.example.logisticsnotification.service.NotificationTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.mail.internet.MimeMessage;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 邮件通知服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Slf4j
@Service
public class EmailNotificationServiceImpl implements EmailNotificationService {

    @Autowired
    private JavaMailSender mailSender;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private NotificationTemplateService templateService;

    @Override
    public boolean sendOrderStatusNotification(String orderNumber, String customerEmail, 
                                             String customerName, String oldStatus, String newStatus) {
        Map<String, Object> params = new HashMap<>();
        params.put("customerName", customerName);
        params.put("orderNumber", orderNumber);
        params.put("oldStatus", getStatusDescription(oldStatus));
        params.put("newStatus", getStatusDescription(newStatus));
        params.put("updateTime", LocalDateTime.now().toString());

        return sendEmailWithTemplate(customerEmail, "ORDER_STATUS_UPDATE", params);
    }

    @Override
    public boolean sendCourierTaskNotification(String courierEmail, String courierName, 
                                             String orderNumber, String taskType) {
        Map<String, Object> params = new HashMap<>();
        params.put("courierName", courierName);
        params.put("orderNumber", orderNumber);
        params.put("taskType", getTaskTypeDescription(taskType));
        params.put("assignTime", LocalDateTime.now().toString());

        return sendEmailWithTemplate(courierEmail, "COURIER_TASK_ASSIGNED", params);
    }

    @Override
    public boolean sendOrderCreatedNotification(String customerEmail, String customerName, 
                                              String orderNumber, String totalFee) {
        Map<String, Object> params = new HashMap<>();
        params.put("customerName", customerName);
        params.put("orderNumber", orderNumber);
        params.put("totalFee", totalFee);
        params.put("createTime", LocalDateTime.now().toString());

        return sendEmailWithTemplate(customerEmail, "ORDER_CREATED", params);
    }

    @Override
    public boolean sendPaymentSuccessNotification(String customerEmail, String customerName, 
                                                String orderNumber, String paymentAmount) {
        Map<String, Object> params = new HashMap<>();
        params.put("customerName", customerName);
        params.put("orderNumber", orderNumber);
        params.put("paymentAmount", paymentAmount);
        params.put("paymentTime", LocalDateTime.now().toString());

        return sendEmailWithTemplate(customerEmail, "PAYMENT_SUCCESS", params);
    }

    @Override
    public boolean sendDeliveryCompletedNotification(String customerEmail, String customerName, 
                                                   String orderNumber, String deliveryTime) {
        Map<String, Object> params = new HashMap<>();
        params.put("customerName", customerName);
        params.put("orderNumber", orderNumber);
        params.put("deliveryTime", deliveryTime);

        return sendEmailWithTemplate(customerEmail, "DELIVERY_COMPLETED", params);
    }

    @Override
    public boolean sendEmail(String to, String subject, String content) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            
            helper.setFrom("<EMAIL>", "物流跟踪系统");
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(content, true);
            
            mailSender.send(message);
            log.info("邮件发送成功: {} -> {}", to, subject);
            return true;
        } catch (Exception e) {
            log.error("邮件发送失败: {} -> {}", to, subject, e);
            return false;
        }
    }

    @Override
    public boolean sendEmailWithTemplate(String to, String templateCode, Map<String, Object> params) {
        try {
            // 生成邮件内容
            Map<String, String> content = templateService.generateNotificationContent(templateCode, params);
            if (content == null) {
                log.error("模板不存在或已禁用: {}", templateCode);
                return false;
            }

            // 发送邮件
            boolean success = sendEmail(to, content.get("title"), content.get("content"));
            
            // 记录通知
            if (success) {
                Notification notification = new Notification();
                notification.setRecipient(to);
                notification.setNotificationType(NotificationType.EMAIL.getCode());
                notification.setTitle(content.get("title"));
                notification.setContent(content.get("content"));
                notification.setTemplateCode(templateCode);
                notification.setSendStatus(1); // 发送成功
                notification.setSendTime(LocalDateTime.now());
                
                notificationService.createNotification(notification);
            }
            
            return success;
        } catch (Exception e) {
            log.error("使用模板发送邮件失败: {} -> {}", to, templateCode, e);
            return false;
        }
    }

    /**
     * 获取状态描述
     */
    private String getStatusDescription(String status) {
        Map<String, String> statusMap = new HashMap<>();
        statusMap.put("PENDING", "待处理");
        statusMap.put("PAID", "已支付");
        statusMap.put("PICKUP_ASSIGNED", "已分配揽件员");
        statusMap.put("PICKUP", "已揽收");
        statusMap.put("SORTING", "分拣中");
        statusMap.put("DISPATCHING", "发车中");
        statusMap.put("TRANSFERRING", "中转中");
        statusMap.put("TRANSIT", "运输中");
        statusMap.put("ARRIVED", "到达目的地");
        statusMap.put("DELIVERING", "派送中");
        statusMap.put("SIGNED", "已签收");
        statusMap.put("CANCELLED", "已取消");
        statusMap.put("EXCEPTION", "异常");
        
        return statusMap.getOrDefault(status, status);
    }

    /**
     * 获取任务类型描述
     */
    private String getTaskTypeDescription(String taskType) {
        Map<String, String> typeMap = new HashMap<>();
        typeMap.put("PICKUP", "揽件任务");
        typeMap.put("DELIVERY", "派送任务");
        
        return typeMap.getOrDefault(taskType, taskType);
    }
}
