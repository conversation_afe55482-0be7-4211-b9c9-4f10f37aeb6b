package org.example.logisticsnotification.service;

import org.example.logisticsnotification.entity.Notification;

import java.util.Map;

/**
 * 邮件通知服务接口
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
public interface EmailNotificationService {

    /**
     * 发送订单状态变更通知
     */
    boolean sendOrderStatusNotification(String orderNumber, String customerEmail, 
                                      String customerName, String oldStatus, String newStatus);

    /**
     * 发送配送员任务通知
     */
    boolean sendCourierTaskNotification(String courierEmail, String courierName, 
                                      String orderNumber, String taskType);

    /**
     * 发送订单创建通知
     */
    boolean sendOrderCreatedNotification(String customerEmail, String customerName, 
                                       String orderNumber, String totalFee);

    /**
     * 发送支付成功通知
     */
    boolean sendPaymentSuccessNotification(String customerEmail, String customerName, 
                                         String orderNumber, String paymentAmount);

    /**
     * 发送配送完成通知
     */
    boolean sendDeliveryCompletedNotification(String customerEmail, String customerName, 
                                            String orderNumber, String deliveryTime);

    /**
     * 通用邮件发送方法
     */
    boolean sendEmail(String to, String subject, String content);

    /**
     * 使用模板发送邮件
     */
    boolean sendEmailWithTemplate(String to, String templateCode, Map<String, Object> params);
}
