# 前端核心文件代码

## 📁 主要文件代码

### 1. 主入口文件

**src/main.ts**
```typescript
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import App from './App.vue'
import router from './router'
import { useAuthStore } from './stores/auth'

const app = createApp(App)

// 注册 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(createPinia())
app.use(router)
app.use(ElementPlus)

// 初始化用户信息
const authStore = useAuthStore()
authStore.initUserInfo()

app.mount('#app')
```

### 2. 根组件

**src/App.vue**
```vue
<template>
  <router-view />
</template>

<script setup lang="ts">
// 应用根组件
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  background-color: #f5f5f5;
}

#app {
  min-height: 100vh;
}

.page-container {
  padding: 20px;
}

.card-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}
</style>
```

### 3. 注册页面

**src/views/auth/Register.vue**
```vue
<template>
  <div class="register-container">
    <div class="register-card">
      <div class="register-header">
        <h1>用户注册</h1>
        <p>创建您的物流跟踪系统账户</p>
      </div>
      
      <el-form
        ref="registerFormRef"
        :model="registerForm"
        :rules="registerRules"
        class="register-form"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="registerForm.username"
            placeholder="请输入用户名"
            @blur="checkUsernameExists"
          />
        </el-form-item>
        
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="registerForm.password"
            type="password"
            placeholder="至少8位，包含大小写字母和数字"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="registerForm.confirmPassword"
            type="password"
            placeholder="请再次输入密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="registerForm.realName" placeholder="请输入真实姓名" />
        </el-form-item>
        
        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model="registerForm.phone"
            placeholder="请输入手机号"
            @blur="checkPhoneExists"
          />
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="registerForm.email"
            placeholder="请输入邮箱"
            @blur="checkEmailExists"
          />
        </el-form-item>
        
        <el-form-item label="身份证号" prop="idCard">
          <el-input v-model="registerForm.idCard" placeholder="请输入身份证号" />
        </el-form-item>
        
        <el-form-item label="用户类型" prop="userType">
          <el-select v-model="registerForm.userType" placeholder="请选择用户类型" style="width: 100%">
            <el-option label="普通用户" value="CUSTOMER" />
            <el-option label="配送员" value="COURIER" />
            <el-option label="操作员" value="OPERATOR" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="验证码" prop="verificationCode">
          <div class="verification-code-container">
            <el-input
              v-model="registerForm.verificationCode"
              placeholder="请输入验证码"
              class="verification-input"
            />
            <el-button 
              @click="sendVerificationCode" 
              :disabled="countdown > 0"
              class="verification-button"
            >
              {{ countdown > 0 ? `${countdown}秒后重试` : '获取验证码' }}
            </el-button>
          </div>
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            :loading="authStore.loading"
            @click="handleRegister"
            class="register-button"
          >
            注 册
          </el-button>
        </el-form-item>
        
        <el-form-item>
          <div class="login-link">
            已有账户？
            <router-link to="/login">立即登录</router-link>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { authApi } from '@/api/auth'
import type { RegisterRequest } from '@/types/user'

const authStore = useAuthStore()
const registerFormRef = ref<FormInstance>()
const countdown = ref(0)

const registerForm = reactive<RegisterRequest>({
  username: '',
  password: '',
  confirmPassword: '',
  realName: '',
  phone: '',
  email: '',
  idCard: '',
  userType: 'CUSTOMER',
  verificationCode: ''
})

// 验证函数
const validatePassword = (rule: any, value: string, callback: any) => {
  const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/
  if (!regex.test(value)) {
    callback(new Error('密码必须包含大小写字母和数字，且长度至少8位'))
  } else {
    callback()
  }
}

const validateConfirmPassword = (rule: any, value: string, callback: any) => {
  if (value !== registerForm.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

const validateIdCard = (rule: any, value: string, callback: any) => {
  const regex = /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/
  if (!regex.test(value)) {
    callback(new Error('身份证号格式不正确'))
  } else {
    callback()
  }
}

const registerRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { validator: validatePassword, trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ],
  realName: [{ required: true, message: '请输入真实姓名', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
  ],
  idCard: [
    { required: true, message: '请输入身份证号', trigger: 'blur' },
    { validator: validateIdCard, trigger: 'blur' }
  ],
  userType: [{ required: true, message: '请选择用户类型', trigger: 'change' }],
  verificationCode: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
}

// 检查用户名是否存在
const checkUsernameExists = async () => {
  if (registerForm.username) {
    try {
      const res = await authApi.checkUsername(registerForm.username)
      if (res.data) {
        ElMessage.warning('用户名已存在')
      }
    } catch (error) {
      console.error('检查用户名失败:', error)
    }
  }
}

// 检查手机号是否存在
const checkPhoneExists = async () => {
  if (registerForm.phone && /^1[3-9]\d{9}$/.test(registerForm.phone)) {
    try {
      const res = await authApi.checkPhone(registerForm.phone)
      if (res.data) {
        ElMessage.warning('手机号已被注册')
      }
    } catch (error) {
      console.error('检查手机号失败:', error)
    }
  }
}

// 检查邮箱是否存在
const checkEmailExists = async () => {
  if (registerForm.email && /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/.test(registerForm.email)) {
    try {
      const res = await authApi.checkEmail(registerForm.email)
      if (res.data) {
        ElMessage.warning('邮箱已被注册')
      }
    } catch (error) {
      console.error('检查邮箱失败:', error)
    }
  }
}

// 发送验证码
const sendVerificationCode = () => {
  ElMessage.info('验证码功能暂未开放，请输入 123456')
  
  countdown.value = 60
  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}

// 处理注册
const handleRegister = async () => {
  if (!registerFormRef.value) return
  
  await registerFormRef.value.validate(async (valid) => {
    if (valid) {
      // 临时验证码处理
      if (registerForm.verificationCode !== '123456') {
        ElMessage.error('验证码错误')
        return
      }
      
      await authStore.register(registerForm)
    }
  })
}
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow-y: auto;
  padding: 20px;
}

.register-card {
  background: white;
  border-radius: 10px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 600px;
  margin: auto;
}

.register-header {
  text-align: center;
  margin-bottom: 30px;
}

.register-header h1 {
  color: #303133;
  font-size: 24px;
  margin-bottom: 10px;
}

.register-header p {
  color: #909399;
  font-size: 14px;
}

.register-form {
  width: 100%;
}

.verification-code-container {
  display: flex;
  gap: 10px;
}

.verification-input {
  flex: 1;
}

.verification-button {
  white-space: nowrap;
}

.register-button {
  width: 100%;
}

.login-link {
  text-align: center;
  color: #909399;
  font-size: 14px;
}

.login-link a {
  color: #409eff;
  text-decoration: none;
}

.login-link a:hover {
  color: #66b1ff;
}
</style>
```

### 4. 客户端布局

**src/layouts/CustomerLayout.vue**
```vue
<template>
  <el-container class="layout-container">
    <el-header class="layout-header">
      <div class="header-left">
        <img src="/logo.png" alt="Logo" class="logo" />
        <h1>物流跟踪系统</h1>
      </div>
      
      <div class="header-right">
        <el-dropdown @command="handleCommand">
          <span class="user-info">
            <el-avatar :size="32" :src="userInfo?.avatar">
              {{ userInfo?.realName?.charAt(0) }}
            </el-avatar>
            <span class="username">{{ userInfo?.realName }}</span>
            <el-icon><ArrowDown /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">个人中心</el-dropdown-item>
              <el-dropdown-item command="settings">设置</el-dropdown-item>
              <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>
    
    <el-container>
      <el-aside width="200px" class="layout-aside">
        <el-menu
          :default-active="activeMenu"
          class="side-menu"
          router
        >
          <el-menu-item index="/customer/dashboard">
            <el-icon><House /></el-icon>
            <span>首页</span>
          </el-menu-item>
          
          <el-sub-menu index="order">
            <template #title>
              <el-icon><Document /></el-icon>
              <span>订单管理</span>
            </template>
            <el-menu-item index="/customer/order/create">创建订单</el-menu-item>
            <el-menu-item index="/customer/order/list">订单列表</el-menu-item>
          </el-sub-menu>
          
          <el-menu-item index="/customer/tracking">
            <el-icon><Location /></el-icon>
            <span>物流追踪</span>
          </el-menu-item>
          
          <el-menu-item index="/customer/profile">
            <el-icon><User /></el-icon>
            <span>个人中心</span>
          </el-menu-item>
        </el-menu>
      </el-aside>
      
      <el-main class="layout-main">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessageBox } from 'element-plus'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

const userInfo = computed(() => authStore.userInfo)
const activeMenu = computed(() => route.path)

const handleCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/customer/profile')
      break
    case 'settings':
      // 跳转到设置页面
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        authStore.logout()
      } catch {
        // 用户取消
      }
      break
  }
}
</script>

<style scoped>
.layout-container {
  height: 100vh;
}

.layout-header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo {
  width: 32px;
  height: 32px;
  margin-right: 10px;
}

.header-left h1 {
  font-size: 18px;
  color: #303133;
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.username {
  margin: 0 8px;
  font-size: 14px;
  color: #303133;
}

.layout-aside {
  background: #fff;
  border-right: 1px solid #e4e7ed;
}

.side-menu {
  border-right: none;
}

.layout-main {
  background: #f5f5f5;
  padding: 20px;
}
</style>
```

### 5. 客户端首页

**src/views/customer/Dashboard.vue**
```vue
<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h2>欢迎回来，{{ userInfo?.realName }}</h2>
      <p>今天是 {{ currentDate }}，祝您工作愉快！</p>
    </div>
    
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#409EFF"><Document /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.totalOrders }}</div>
            <div class="stat-label">总订单数</div>
          </div>
        </div>
      </el-col>
      
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#67C23A"><Clock /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.pendingOrders }}</div>
            <div class="stat-label">待处理订单</div>
          </div>
        </div>
      </el-col>
      
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#E6A23C"><Truck /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.inTransitOrders }}</div>
            <div class="stat-label">运输中订单</div>
          </div>
        </div>
      </el-col>
      
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#F56C6C"><SuccessFilled /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.completedOrders }}</div>
            <div class="stat-label">已完成订单</div>
          </div>
        </div>
      </el-col>
    </el-row>
    
    <el-row :gutter="20">
      <el-col :span="16">
        <div class="card-container">
          <div class="card-header">
            <h3>最近订单</h3>
            <el-button type="primary" @click="$router.push('/customer/order/list')">
              查看全部
            </el-button>
          </div>
          
          <el-table :data="recentOrders" style="width: 100%">
            <el-table-column prop="orderNumber" label="订单号" width="150" />
            <el-table-column prop="receiverName" label="收件人" width="100" />
            <el-table-column prop="receiverAddress" label="收货地址" show-overflow-tooltip />
            <el-table-column prop="orderStatus" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.orderStatus)">
                  {{ getStatusText(row.orderStatus) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="160">
              <template #default="{ row }">
                {{ formatDate(row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="{ row }">
                <el-button 
                  type="primary" 
                  size="small" 
                  @click="viewOrder(row.id)"
                >
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
      
      <el-col :span="8">
        <div class="card-container">
          <div class="card-header">
            <h3>快速操作</h3>
          </div>
          
          <div class="quick-actions">
            <div class="action-item" @click="$router.push('/customer/order/create')">
              <el-icon size="24"><Plus /></el-icon>
              <span>创建订单</span>
            </div>
            
            <div class="action-item" @click="$router.push('/customer/tracking')">
              <el-icon size="24"><Search /></el-icon>
              <span>物流追踪</span>
            </div>
            
            <div class="action-item" @click="$router.push('/customer/profile')">
              <el-icon size="24"><User /></el-icon>
              <span>个人中心</span>
            </div>
            
            <div class="action-item" @click="handleCustomerService">
              <el-icon size="24"><Service /></el-icon>
              <span>客服咨询</span>
            </div>
          </div>
        </div>
        
        <div class="card-container" style="margin-top: 20px;">
          <div class="card-header">
            <h3>系统公告</h3>
          </div>
          
          <div class="notice-list">
            <div class="notice-item" v-for="notice in notices" :key="notice.id">
              <div class="notice-title">{{ notice.title }}</div>
              <div class="notice-time">{{ formatDate(notice.createTime) }}</div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { orderApi } from '@/api/order'
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'

const router = useRouter()
const authStore = useAuthStore()

const userInfo = computed(() => authStore.userInfo)
const currentDate = computed(() => dayjs().format('YYYY年MM月DD日'))

const stats = ref({
  totalOrders: 0,
  pendingOrders: 0,
  inTransitOrders: 0,
  completedOrders: 0
})

const recentOrders = ref([])

const notices = ref([
  {
    id: 1,
    title: '系统升级公告',
    createTime: '2024-12-25 10:00:00'
  },
  {
    id: 2,
    title: '春节放假通知',
    createTime: '2024-12-24 15:30:00'
  },
  {
    id: 3,
    title: '新功能上线',
    createTime: '2024-12-23 09:15:00'
  }
])

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'PENDING': 'info',
    'CONFIRMED': 'warning',
    'IN_TRANSIT': 'primary',
    'DELIVERED': 'success',
    'CANCELLED': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'PENDING': '待处理',
    'CONFIRMED': '已确认',
    'IN_TRANSIT': '运输中',
    'DELIVERED': '已送达',
    'CANCELLED': '已取消'
  }
  return statusMap[status] || status
}

const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

const viewOrder = (orderId: number) => {
  router.push(`/customer/order/${orderId}`)
}

const handleCustomerService = () => {
  ElMessage.info('客服功能开发中...')
}

const loadDashboardData = async () => {
  try {
    // 加载最近订单
    const orderResponse = await orderApi.getOrderList({
      page: 1,
      size: 5
    })
    
    if (orderResponse.success) {
      recentOrders.value = orderResponse.data.records
      
      // 计算统计数据
      const allOrdersResponse = await orderApi.getOrderList({
        page: 1,
        size: 1000
      })
      
      if (allOrdersResponse.success) {
        const orders = allOrdersResponse.data.records
        stats.value = {
          totalOrders: orders.length,
          pendingOrders: orders.filter(o => o.orderStatus === 'PENDING').length,
          inTransitOrders: orders.filter(o => o.orderStatus === 'IN_TRANSIT').length,
          completedOrders: orders.filter(o => o.orderStatus === 'DELIVERED').length
        }
      }
    }
  } catch (error) {
    console.error('加载仪表板数据失败:', error)
  }
}

onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.dashboard-header {
  margin-bottom: 24px;
}

.dashboard-header h2 {
  font-size: 24px;
  color: #303133;
  margin-bottom: 8px;
}

.dashboard-header p {
  color: #909399;
  font-size: 14px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  margin-right: 16px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.card-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.card-header h3 {
  font-size: 16px;
  color: #303133;
  margin: 0;
}

.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.action-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.action-item span {
  margin-top: 8px;
  font-size: 14px;
  color: #303133;
}

.notice-list {
  max-height: 200px;
  overflow-y: auto;
}

.notice-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.notice-item:last-child {
  border-bottom: none;
}

.notice-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.notice-time {
  font-size: 12px;
  color: #909399;
}
</style>
```

### 6. 404 错误页面

**src/views/error/NotFound.vue**
```vue
<template>
  <div class="not-found">
    <div class="not-found-content">
      <div class="error-code">404</div>
      <div class="error-message">页面不存在</div>
      <div class="error-description">
        抱歉，您访问的页面不存在或已被删除
      </div>
      <div class="error-actions">
        <el-button type="primary" @click="goHome">
          返回首页
        </el-button>
        <el-button @click="goBack">
          返回上页
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const goHome = () => {
  const userType = authStore.userRole
  const homeRoutes: Record<string, string> = {
    'ADMIN': '/admin/dashboard',
    'OPERATOR': '/operator/dashboard',
    'COURIER': '/courier/dashboard',
    'CUSTOMER': '/customer/dashboard'
  }
  
  router.push(homeRoutes[userType] || '/login')
}

const goBack = () => {
  router.go(-1)
}
</script>

<style scoped>
.not-found {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
}

.not-found-content {
  text-align: center;
  padding: 40px;
}

.error-code {
  font-size: 120px;
  font-weight: 600;
  color: #409eff;
  line-height: 1;
  margin-bottom: 20px;
}

.error-message {
  font-size: 24px;
  color: #303133;
  margin-bottom: 12px;
}

.error-description {
  font-size: 16px;
  color: #909399;
  margin-bottom: 30px;
}

.error-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}
</style>
```

## 📋 文件创建清单

### 核心配置文件
1. `vite.config.ts` - Vite 配置
2. `tsconfig.json` - TypeScript 配置
3. `src/main.ts` - 应用入口
4. `src/App.vue` - 根组件

### 工具和类型
5. `src/utils/http.ts` - HTTP 请求封装
6. `src/types/user.ts` - 用户类型定义
7. `src/types/order.ts` - 订单类型定义

### API 接口
8. `src/api/auth.ts` - 认证接口
9. `src/api/order.ts` - 订单接口

### 状态管理
10. `src/stores/auth.ts` - 认证状态管理

### 路由配置
11. `src/router/index.ts` - 路由配置

### 页面组件
12. `src/views/auth/Login.vue` - 登录页面
13. `src/views/auth/Register.vue` - 注册页面
14. `src/views/customer/Dashboard.vue` - 客户首页
15. `src/views/error/NotFound.vue` - 404 页面

### 布局组件
16. `src/layouts/CustomerLayout.vue` - 客户端布局

## 🚀 下一步开发计划

1. **创建管理员布局和页面**
2. **实现订单管理功能**
3. **集成地图服务**
4. **添加物流追踪功能**
5. **完善配送员工作台**
6. **优化用户体验**

这些代码文件构成了前端项目的核心架构，您可以按照顺序创建这些文件，然后逐步完善各个功能模块。 