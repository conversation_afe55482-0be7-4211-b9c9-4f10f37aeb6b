package org.example.logisticsorder.service.impl;

import org.example.logisticsorder.service.PricingService;
import org.example.logisticsorder.service.OrderService;
import org.example.logisticsorder.dto.CreateOrderDTO;
import org.example.logisticsorder.constants.ServiceType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 价格计算服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Service
public class PricingServiceImpl implements PricingService {

    // 价格计算相关配置参数
    @Value("${logistics.pricing.base-fee:10.00}")
    private BigDecimal baseFee;

    @Value("${logistics.pricing.weight-fee:2.00}")
    private BigDecimal weightFeePerKg;

    @Value("${logistics.pricing.distance-fee:0.50}")
    private BigDecimal distanceFeePerKm;

    @Value("${logistics.pricing.free-weight:1.00}")
    private BigDecimal freeWeight;

    @Value("${logistics.pricing.free-distance:10.00}")
    private BigDecimal freeDistance;

    @Value("${logistics.pricing.insurance-rate:0.01}")
    private BigDecimal insuranceRate;

    @Value("${logistics.pricing.packing-fee:5.00}")
    private BigDecimal packingFee;

    @Override
    public BigDecimal calculateShippingFee(CreateOrderDTO orderDTO) {
        BigDecimal totalFee = baseFee;

        // 计算重量费用
        BigDecimal weightFee = calculateWeightFee(orderDTO.getItemWeight());
        totalFee = totalFee.add(weightFee);

        // 计算距离费用（这里可以集成地图API计算实际距离）
        BigDecimal distance = calculateDistance(orderDTO);
        BigDecimal distanceFee = calculateDistanceFee(distance);
        totalFee = totalFee.add(distanceFee);

        // 服务类型倍数
        Double multiplier = getServiceTypeMultiplier(orderDTO.getServiceType());
        totalFee = totalFee.multiply(BigDecimal.valueOf(multiplier));

        return totalFee.setScale(2, RoundingMode.HALF_UP);
    }

    @Override
    public BigDecimal calculateInsuranceFee(BigDecimal itemValue) {
        if (itemValue == null || itemValue.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal insuranceFee = itemValue.multiply(insuranceRate);
        return insuranceFee.setScale(2, RoundingMode.HALF_UP);
    }

    @Override
    public BigDecimal calculatePackingFee(CreateOrderDTO orderDTO) {
        // 易碎品包装费加倍
        if (Boolean.TRUE.equals(orderDTO.getIsFragile())) {
            return packingFee.multiply(BigDecimal.valueOf(1.5));
        }
        return packingFee;
    }

    @Override
    public OrderService.PriceResult calculateTotalPrice(CreateOrderDTO orderDTO) {
        BigDecimal shippingFee = calculateShippingFee(orderDTO);
        BigDecimal insuranceFee = calculateInsuranceFee(orderDTO.getItemValue());
        BigDecimal packingFeeResult = calculatePackingFee(orderDTO);

        BigDecimal totalFee = shippingFee.add(insuranceFee).add(packingFeeResult);

        return new OrderService.PriceResult(shippingFee, insuranceFee, packingFeeResult, totalFee);
    }

    @Override
    public BigDecimal calculateDistanceFee(BigDecimal distance) {
        if (distance == null || distance.compareTo(freeDistance) <= 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal chargeableDistance = distance.subtract(freeDistance);
        return chargeableDistance.multiply(distanceFeePerKm).setScale(2, RoundingMode.HALF_UP);
    }

    @Override
    public BigDecimal calculateWeightFee(BigDecimal weight) {
        if (weight == null || weight.compareTo(freeWeight) <= 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal chargeableWeight = weight.subtract(freeWeight);
        return chargeableWeight.multiply(weightFeePerKg).setScale(2, RoundingMode.HALF_UP);
    }

    @Override
    public Double getServiceTypeMultiplier(String serviceType) {
        try {
            ServiceType type = ServiceType.fromCode(serviceType);
            return type.getPriceMultiplier();
        } catch (IllegalArgumentException e) {
            // 默认使用标准快递
            return ServiceType.STANDARD.getPriceMultiplier();
        }
    }

    /**
     * 计算距离（可集成地图API）
     */
    private BigDecimal calculateDistance(CreateOrderDTO orderDTO) {
        // 如果有经纬度坐标，使用精确计算
        if (orderDTO.getSenderLongitude() != null && orderDTO.getSenderLatitude() != null &&
                orderDTO.getReceiverLongitude() != null && orderDTO.getReceiverLatitude() != null) {

            // 使用Haversine公式计算两点间距离
            double lat1 = orderDTO.getSenderLatitude().doubleValue();
            double lon1 = orderDTO.getSenderLongitude().doubleValue();
            double lat2 = orderDTO.getReceiverLatitude().doubleValue();
            double lon2 = orderDTO.getReceiverLongitude().doubleValue();

            double distance = calculateHaversineDistance(lat1, lon1, lat2, lon2);
            return BigDecimal.valueOf(distance);
        }

        // 否则根据城市估算距离
        return estimateDistanceByCity(orderDTO.getSenderCity(), orderDTO.getReceiverCity());
    }

    /**
     * 使用Haversine公式计算距离
     */
    private double calculateHaversineDistance(double lat1, double lon1, double lat2, double lon2) {
        final double R = 6371; // 地球半径（公里）

        double latDistance = Math.toRadians(lat2 - lat1);
        double lonDistance = Math.toRadians(lon2 - lon1);
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return R * c;
    }

    /**
     * 根据城市估算距离
     */
    private BigDecimal estimateDistanceByCity(String senderCity, String receiverCity) {
        if (senderCity != null && senderCity.equals(receiverCity)) {
            return BigDecimal.valueOf(20); // 同城默认20公里
        }
        return BigDecimal.valueOf(500); // 跨城默认500公里
    }
}