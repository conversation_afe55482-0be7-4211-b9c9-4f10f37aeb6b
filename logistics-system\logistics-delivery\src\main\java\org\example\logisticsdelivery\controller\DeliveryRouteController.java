package org.example.logisticsdelivery.controller;

import com.logistics.common.result.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.logisticsdelivery.entity.DeliveryRoute;
import org.example.logisticsdelivery.service.DeliveryRouteService;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 配送路线管理控制器
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Slf4j
@RestController
@RequestMapping("/delivery/route")
@RequiredArgsConstructor
public class DeliveryRouteController {

    private final DeliveryRouteService routeService;

    /**
     * 创建配送路线
     */
    @PostMapping
    public Result<DeliveryRoute> createRoute(@RequestBody DeliveryRoute route) {
        try {
            DeliveryRoute result = routeService.createRoute(route);
            return Result.success(result);
        } catch (Exception e) {
            log.error("创建配送路线失败", e);
            return Result.error("创建配送路线失败: " + e.getMessage());
        }
    }

    /**
     * 更新配送路线
     */
    @PutMapping("/{id}")
    public Result<DeliveryRoute> updateRoute(@PathVariable Long id, @RequestBody DeliveryRoute route) {
        try {
            route.setId(id);
            DeliveryRoute result = routeService.updateRoute(route);
            return Result.success(result);
        } catch (Exception e) {
            log.error("更新配送路线失败，ID: {}", id, e);
            return Result.error("更新配送路线失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询路线
     */
    @GetMapping("/{id}")
    public Result<DeliveryRoute> getRouteById(@PathVariable Long id) {
        try {
            DeliveryRoute route = routeService.getRouteById(id);
            if (route != null) {
                return Result.success(route);
            } else {
                return Result.error("配送路线不存在");
            }
        } catch (Exception e) {
            log.error("查询配送路线失败，ID: {}", id, e);
            return Result.error("查询配送路线失败: " + e.getMessage());
        }
    }

    /**
     * 根据配送员ID查询路线
     */
    @GetMapping("/courier/{courierId}")
    public Result<List<DeliveryRoute>> getRoutesByCourierId(@PathVariable Long courierId) {
        try {
            List<DeliveryRoute> routes = routeService.getRoutesByCourierId(courierId);
            return Result.success(routes);
        } catch (Exception e) {
            log.error("查询配送员路线失败，配送员ID: {}", courierId, e);
            return Result.error("查询配送员路线失败: " + e.getMessage());
        }
    }

    /**
     * 根据配送员和日期查询路线
     */
    @GetMapping("/courier/{courierId}/date/{routeDate}")
    public Result<DeliveryRoute> getRouteByCourierAndDate(
            @PathVariable Long courierId,
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate routeDate) {
        try {
            DeliveryRoute route = routeService.getRouteByCourierAndDate(courierId, routeDate);
            if (route != null) {
                return Result.success(route);
            } else {
                return Result.error("该配送员当日没有路线");
            }
        } catch (Exception e) {
            log.error("查询配送员当日路线失败，配送员ID: {}, 日期: {}", courierId, routeDate, e);
            return Result.error("查询配送员当日路线失败: " + e.getMessage());
        }
    }

    /**
     * 根据路线状态查询
     */
    @GetMapping("/status/{routeStatus}")
    public Result<List<DeliveryRoute>> getRoutesByStatus(@PathVariable String routeStatus) {
        try {
            List<DeliveryRoute> routes = routeService.getRoutesByStatus(routeStatus);
            return Result.success(routes);
        } catch (Exception e) {
            log.error("查询路线列表失败，状态: {}", routeStatus, e);
            return Result.error("查询路线列表失败: " + e.getMessage());
        }
    }

    /**
     * 查询指定日期的路线
     */
    @GetMapping("/date/{routeDate}")
    public Result<List<DeliveryRoute>> getRoutesByDate(
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate routeDate) {
        try {
            List<DeliveryRoute> routes = routeService.getRoutesByDate(routeDate);
            return Result.success(routes);
        } catch (Exception e) {
            log.error("查询指定日期路线失败，日期: {}", routeDate, e);
            return Result.error("查询指定日期路线失败: " + e.getMessage());
        }
    }

    /**
     * 更新路线状态
     */
    @PutMapping("/{id}/status")
    public Result<String> updateRouteStatus(@PathVariable Long id, @RequestParam String routeStatus) {
        try {
            boolean success = routeService.updateRouteStatus(id, routeStatus);
            if (success) {
                return Result.success("路线状态更新成功");
            } else {
                return Result.error("路线状态更新失败");
            }
        } catch (Exception e) {
            log.error("更新路线状态失败，ID: {}", id, e);
            return Result.error("更新路线状态失败: " + e.getMessage());
        }
    }

    /**
     * 更新路线任务统计
     */
    @PutMapping("/{id}/task-count")
    public Result<String> updateTaskCount(
            @PathVariable Long id,
            @RequestParam Integer taskCount,
            @RequestParam Integer completedCount) {
        try {
            boolean success = routeService.updateTaskCount(id, taskCount, completedCount);
            if (success) {
                return Result.success("路线任务统计更新成功");
            } else {
                return Result.error("路线任务统计更新失败");
            }
        } catch (Exception e) {
            log.error("更新路线任务统计失败，ID: {}", id, e);
            return Result.error("更新路线任务统计失败: " + e.getMessage());
        }
    }

    /**
     * 开始路线
     */
    @PutMapping("/{routeId}/start")
    public Result<String> startRoute(@PathVariable Long routeId) {
        try {
            boolean success = routeService.startRoute(routeId);
            if (success) {
                return Result.success("路线开始成功");
            } else {
                return Result.error("路线开始失败");
            }
        } catch (Exception e) {
            log.error("开始路线失败，路线ID: {}", routeId, e);
            return Result.error("开始路线失败: " + e.getMessage());
        }
    }

    /**
     * 完成路线
     */
    @PutMapping("/{routeId}/complete")
    public Result<String> completeRoute(@PathVariable Long routeId) {
        try {
            boolean success = routeService.completeRoute(routeId);
            if (success) {
                return Result.success("路线完成成功");
            } else {
                return Result.error("路线完成失败");
            }
        } catch (Exception e) {
            log.error("完成路线失败，路线ID: {}", routeId, e);
            return Result.error("完成路线失败: " + e.getMessage());
        }
    }

    /**
     * 取消路线
     */
    @PutMapping("/{routeId}/cancel")
    public Result<String> cancelRoute(@PathVariable Long routeId, @RequestParam(required = false) String reason) {
        try {
            boolean success = routeService.cancelRoute(routeId, reason);
            if (success) {
                return Result.success("路线取消成功");
            } else {
                return Result.error("路线取消失败");
            }
        } catch (Exception e) {
            log.error("取消路线失败，路线ID: {}", routeId, e);
            return Result.error("取消路线失败: " + e.getMessage());
        }
    }

    /**
     * 为配送员生成当日路线
     */
    @PostMapping("/generate")
    public Result<DeliveryRoute> generateDailyRoute(
            @RequestParam Long courierId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate routeDate) {
        try {
            DeliveryRoute route = routeService.generateDailyRoute(courierId, routeDate);
            return Result.success(route);
        } catch (Exception e) {
            log.error("生成当日路线失败，配送员ID: {}, 日期: {}", courierId, routeDate, e);
            return Result.error("生成当日路线失败: " + e.getMessage());
        }
    }

    /**
     * 优化路线
     */
    @PostMapping("/{routeId}/optimize")
    public Result<DeliveryRoute> optimizeRoute(@PathVariable Long routeId) {
        try {
            DeliveryRoute route = routeService.optimizeRoute(routeId);
            return Result.success(route);
        } catch (Exception e) {
            log.error("路线优化失败，路线ID: {}", routeId, e);
            return Result.error("路线优化失败: " + e.getMessage());
        }
    }

    /**
     * 删除路线
     */
    @DeleteMapping("/{routeId}")
    public Result<String> deleteRoute(@PathVariable Long routeId) {
        try {
            boolean success = routeService.deleteRoute(routeId);
            if (success) {
                return Result.success("删除路线成功");
            } else {
                return Result.error("删除路线失败");
            }
        } catch (Exception e) {
            log.error("删除路线失败，路线ID: {}", routeId, e);
            return Result.error("删除路线失败: " + e.getMessage());
        }
    }
}