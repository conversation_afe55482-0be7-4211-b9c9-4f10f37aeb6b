# 🚚 **智慧物流订单跟踪系统**

## 📋 **项目简介**

智慧物流订单跟踪系统是一个基于微服务架构的现代化物流管理平台，支持完整的物流业务流程，从订单创建到最终签收的全生命周期管理。

### 🎯 **核心特性**

- 🏗️ **微服务架构**：8个独立服务，高可用性和可扩展性
- 🗺️ **高德地图集成**：实时配送路径展示和位置服务
- 📧 **智能通知系统**：邮件 + 短信双重通知保障
- 📊 **数据驱动**：MongoDB轨迹存储 + Redis缓存优化
- 🔄 **异步处理**：RabbitMQ消息队列处理
- 📝 **操作审计**：AOP敏感操作日志记录
- 🎨 **现代化UI**：Vue 3 + TypeScript + Element Plus

### 🏢 **业务场景**

- **多角色协作**：客户、操作员、配送员、管理员
- **完整物流流程**：下单 → 支付 → 揽件 → 运输 → 派送 → 签收
- **实时追踪**：GPS定位 + 轨迹记录 + 状态推送
- **智能调度**：自动分配配送员 + 路径优化
- **数据分析**：订单统计 + 绩效分析 + 运营报表

## 🏗️ **系统架构**

### **微服务架构图**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   API网关       │    │   注册中心      │
│  Vue 3 + TS     │◄──►│   Spring Cloud  │◄──►│    Nacos        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
        ┌───────▼──────┐ ┌──────▼──────┐ ┌─────▼──────┐
        │   订单服务   │ │   配送服务  │ │  物流服务  │
        │ :8081        │ │  :8082      │ │  :8083     │
        └──────────────┘ └─────────────┘ └────────────┘
                │               │               │
        ┌───────▼──────┐ ┌──────▼──────┐ ┌─────▼──────┐
        │   通知服务   │ │   用户服务  │ │  支付服务  │
        │ :8084        │ │  :8085      │ │  :8086     │
        └──────────────┘ └─────────────┘ └────────────┘
                │
        ┌───────▼──────┐
        │   邮件服务   │
        │ :8087        │
        └──────────────┘
```

### **技术栈**

#### **后端技术**
- **框架**: Spring Boot 2.7.x + Spring Cloud
- **数据库**: MySQL 8.0 + MongoDB 4.4
- **缓存**: Redis 6.0
- **消息队列**: RabbitMQ 3.9
- **服务注册**: Nacos 2.1
- **地图服务**: 高德地图API
- **构建工具**: Maven 3.8

#### **前端技术**
- **框架**: Vue 3.3 + TypeScript 4.9
- **UI组件**: Element Plus 2.4
- **构建工具**: Vite 4.5
- **状态管理**: Pinia 2.1
- **路由**: Vue Router 4.2

## 🚀 **快速开始**

### **环境要求**

- **Java**: JDK 11+
- **Node.js**: 16.0+
- **MySQL**: 8.0+
- **MongoDB**: 4.4+
- **Redis**: 6.0+
- **RabbitMQ**: 3.9+

### **1. 克隆项目**
```bash
git clone https://github.com/your-repo/logistics-system.git
cd logistics-system
```

### **2. 数据库初始化**
```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE logistics CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 导入基础数据
mysql -u root -p logistics < sql/init_base_data.sql
```

### **3. 配置文件修改**
```bash
# 修改各服务的application.yml
# 更新数据库连接信息
# 配置邮件服务（QQ邮箱授权码）
# 设置高德地图API密钥
```

### **4. 启动后端服务**
```bash
# 启动注册中心（如果使用Nacos）
# 按顺序启动各微服务
cd logistics-order && mvn spring-boot:run &
cd logistics-delivery && mvn spring-boot:run &
cd logistics-logistics && mvn spring-boot:run &
cd logistics-notification && mvn spring-boot:run &
cd logistics-user && mvn spring-boot:run &
cd logistics-payment && mvn spring-boot:run &
cd logistics-email && mvn spring-boot:run &
```

### **5. 启动前端应用**
```bash
cd logistics-frontend
npm install
npm run dev
```

### **6. 访问系统**
- **前端地址**: http://localhost:5173
- **API文档**: http://localhost:8081/swagger-ui.html

## 📚 **详细文档**

### **业务文档**
- [业务流程设计](docs/business-flow.md)
- [角色权限说明](docs/user-roles.md)
- [订单状态流转](docs/order-status.md)

### **技术文档**
- [API接口文档](docs/api-documentation.md)
- [数据库设计](docs/database-design.md)
- [部署指南](docs/deployment-guide.md)

### **开发文档**
- [开发环境搭建](docs/development-setup.md)
- [代码规范](docs/coding-standards.md)
- [测试指南](docs/testing-guide.md)

## 🧪 **测试**

### **自动化测试**
```bash
# 运行自动化测试脚本
chmod +x testing/automated-test-script.sh
./testing/automated-test-script.sh
```

### **手动测试**
```bash
# 测试用户账号
管理员: admin / 123456
操作员: operator001 / 123456
配送员: courier001 / 123456
客户: customer001 / 123456
```

## 📊 **功能特性**

### **订单管理**
- ✅ 在线下单和费用计算
- ✅ 多种支付方式支持
- ✅ 订单状态实时追踪
- ✅ 异常订单处理

### **配送管理**
- ✅ 智能配送员分配
- ✅ 实时位置追踪
- ✅ 路径规划优化
- ✅ 配送任务管理

### **通知系统**
- ✅ 邮件通知（HTML模板）
- ✅ 短信通知（异步处理）
- ✅ 系统内消息推送
- ✅ 通知模板管理

### **数据分析**
- ✅ 订单统计报表
- ✅ 配送员绩效分析
- ✅ 网点效率统计
- ✅ 实时数据大屏

## 🔧 **配置说明**

### **邮件服务配置**
```yaml
spring:
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: your-auth-code
```

### **高德地图配置**
```yaml
amap:
  api-key: your-amap-key
  web-key: your-web-key
```

### **Redis配置**
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    database: 0
```

## 🐛 **问题排查**

### **常见问题**
1. **邮件发送失败**: 检查QQ邮箱授权码配置
2. **地图加载失败**: 检查高德地图API密钥
3. **服务启动失败**: 检查端口占用和数据库连接
4. **前端页面空白**: 检查API接口是否正常

### **日志查看**
```bash
# 查看服务日志
tail -f logs/logistics-order.log
tail -f logs/logistics-email.log
```

## 🤝 **贡献指南**

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 **许可证**

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 **联系我们**

- **项目维护者**: 物流系统开发团队
- **邮箱**: <EMAIL>
- **文档**: [在线文档](https://docs.logistics-system.com)

## 🎉 **致谢**

感谢所有为这个项目做出贡献的开发者和测试人员！

---

**智慧物流订单跟踪系统** - 让物流管理更智能、更高效！
