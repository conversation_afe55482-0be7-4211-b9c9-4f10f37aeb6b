package org.example.logisticsuser.service;

import org.example.logisticsuser.dto.AddressDTO;
import org.example.logisticsuser.vo.AddressVO;

import java.util.List;

/**
 * 地址服务接口
 */
public interface AddressService {

    /**
     * 获取用户地址列表
     */
    List<AddressVO> getUserAddresses(Long userId, Integer addressType);

    /**
     * 获取地址详情
     */
    AddressVO getAddressById(Long addressId, Long userId);

    /**
     * 创建地址
     */
    AddressVO createAddress(Long userId, AddressDTO addressDTO);

    /**
     * 更新地址
     */
    AddressVO updateAddress(Long addressId, Long userId, AddressDTO addressDTO);

    /**
     * 删除地址
     */
    void deleteAddress(Long addressId, Long userId);

    /**
     * 设置默认地址
     */
    void setDefaultAddress(Long addressId, Long userId);

    /**
     * 获取省市区数据
     */
    Object getRegionData();
}