package org.example.logisticsorder.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.logistics.common.constants.OrderStatus;
import org.example.logisticsorder.mapper.OrderMapper;
import org.example.logisticsorder.service.OrderService;
import org.example.logisticsorder.dto.CreateOrderDTO;
import org.example.logisticsorder.dto.OrderQueryDTO;
import org.example.logisticsorder.dto.OrderStatusUpdateDTO;
import org.example.logisticsorder.vo.OrderVO;
import org.example.logisticsorder.vo.OrderListVO;
import org.example.logisticsorder.entity.OrderStatusLog;
import com.logistics.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单控制器
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Slf4j
@RestController
@RequestMapping("/order")
@CrossOrigin(origins = "*")
public class OrderController {

    @Autowired
    private OrderService orderService;

    @Autowired
    private OrderMapper orderMapper;

    /**
     * 测试接口
     */
    @GetMapping("/test/hello")
    public Result<String> hello() {
        return Result.success("订单服务启动成功！端口：8002");
    }

    /**
     * 创建订单
     */
    @PostMapping("/create")
    public Result<OrderVO> createOrder(@RequestHeader("userId") Long userId,
                                       @Valid @RequestBody CreateOrderDTO createOrderDTO) {
        try {
            log.info("创建订单请求，用户ID: {}", userId);
            OrderVO orderVO = orderService.createOrder(userId, createOrderDTO);
            return Result.success("订单创建成功", orderVO);
        } catch (Exception e) {
            log.error("创建订单失败: {}", e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 计算订单价格
     */
    @PostMapping("/calculate-price")
    public Result<OrderService.PriceResult> calculatePrice(@Valid @RequestBody CreateOrderDTO createOrderDTO) {
        try {
            OrderService.PriceResult priceResult = orderService.calculatePrice(createOrderDTO);
            return Result.success("价格计算成功", priceResult);
        } catch (Exception e) {
            log.error("价格计算失败: {}", e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 根据订单ID获取订单详情
     */
    @GetMapping("/{orderId}")
    public Result<OrderVO> getOrderById(@PathVariable Long orderId) {
        try {
            OrderVO orderVO = orderService.getOrderById(orderId);
            return Result.success("获取订单详情成功", orderVO);
        } catch (Exception e) {
            log.error("获取订单详情失败，订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 根据订单号获取订单详情
     */
    @GetMapping("/number/{orderNumber}")
    public Result<OrderVO> getOrderByNumber(@PathVariable String orderNumber) {
        try {
            OrderVO orderVO = orderService.getOrderByNumber(orderNumber);
            return Result.success("获取订单详情成功", orderVO);
        } catch (Exception e) {
            log.error("获取订单详情失败，订单号: {}, 错误: {}", orderNumber, e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 分页查询订单列表
     */
    @GetMapping("/page")
    public Result<IPage<OrderListVO>> getOrderPage(OrderQueryDTO queryDTO) {
        try {
            IPage<OrderListVO> page = orderService.getOrderPage(queryDTO);
            return Result.success("查询订单列表成功", page);
        } catch (Exception e) {
            log.error("查询订单列表失败: {}", e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取用户订单列表
     */
    @GetMapping("/user/{userId}")
    public Result<List<OrderListVO>> getUserOrders(@PathVariable Long userId,
                                                   @RequestParam(required = false, defaultValue = "10") Integer limit) {
        try {
            List<OrderListVO> orders = orderService.getUserOrders(userId, limit);
            return Result.success("获取用户订单成功", orders);
        } catch (Exception e) {
            log.error("获取用户订单失败，用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取当前用户订单列表
     */
    @GetMapping("/my-orders")
    public Result<List<OrderListVO>> getMyOrders(@RequestHeader("userId") Long userId,
                                                 @RequestParam(required = false, defaultValue = "10") Integer limit) {
        try {
            List<OrderListVO> orders = orderService.getUserOrders(userId, limit);
            return Result.success("获取我的订单成功", orders);
        } catch (Exception e) {
            log.error("获取用户订单失败，用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 更新订单状态
     */
    @PutMapping("/status")
    public Result<Boolean> updateOrderStatus(@Valid @RequestBody OrderStatusUpdateDTO updateDTO) {
        try {
            boolean success = orderService.updateOrderStatus(updateDTO);
            return Result.success("订单状态更新成功", success);
        } catch (Exception e) {
            log.error("订单状态更新失败: {}", e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 取消订单
     */
    @PutMapping("/{orderId}/cancel")
    public Result<Boolean> cancelOrder(@PathVariable Long orderId,
                                       @RequestHeader("userId") Long userId,
                                       @RequestParam(required = false) String reason) {
        try {
            boolean success = orderService.cancelOrder(orderId, userId, reason);
            return Result.success("订单取消成功", success);
        } catch (Exception e) {
            log.error("订单取消失败，订单ID: {}, 用户ID: {}, 错误: {}", orderId, userId, e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 支付成功回调
     */
    @PostMapping("/payment/success")
    public Result<Boolean> paymentSuccess(@RequestParam String orderNumber,
                                          @RequestParam String paymentMethod) {
        try {
            boolean success = orderService.paymentSuccess(orderNumber, paymentMethod);
            return Result.success("支付回调处理成功", success);
        } catch (Exception e) {
            log.error("支付回调处理失败，订单号: {}, 错误: {}", orderNumber, e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 分配揽件员
     */
    @PutMapping("/{orderId}/assign-pickup")
    public Result<Boolean> assignPickupCourier(@PathVariable Long orderId,
                                               @RequestParam Long courierId) {
        try {
            boolean success = orderService.assignPickupCourier(orderId, courierId);
            return Result.success("分配揽件员成功", success);
        } catch (Exception e) {
            log.error("分配揽件员失败，订单ID: {}, 配送员ID: {}, 错误: {}", orderId, courierId, e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 分配配送员
     */
    @PutMapping("/{orderId}/assign-delivery")
    public Result<Boolean> assignDeliveryCourier(@PathVariable Long orderId,
                                                 @RequestParam Long courierId) {
        try {
            boolean success = orderService.assignDeliveryCourier(orderId, courierId);
            return Result.success("分配配送员成功", success);
        } catch (Exception e) {
            log.error("分配配送员失败，订单ID: {}, 配送员ID: {}, 错误: {}", orderId, courierId, e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 订单签收
     */
    @PutMapping("/{orderId}/sign")
    public Result<Boolean> signOrder(@PathVariable Long orderId,
                                     @RequestParam String signProof) {
        try {
            boolean success = orderService.signOrder(orderId, signProof);
            return Result.success("订单签收成功", success);
        } catch (Exception e) {
            log.error("订单签收失败，订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取订单状态变更日志
     */
    @GetMapping("/{orderId}/status-log")
    public Result<List<OrderStatusLog>> getOrderStatusLog(@PathVariable Long orderId) {
        try {
            List<OrderStatusLog> logs = orderService.getOrderStatusLog(orderId);
            return Result.success("获取状态日志成功", logs);
        } catch (Exception e) {
            log.error("获取状态日志失败，订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取订单统计信息
     */
    @GetMapping("/statistics")
    public Result<OrderService.OrderStatistics> getOrderStatistics(@RequestParam(required = false) Long userId) {
        try {
            OrderService.OrderStatistics statistics = orderService.getOrderStatistics(userId);
            return Result.success("获取订单统计成功", statistics);
        } catch (Exception e) {
            log.error("获取订单统计失败，用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 批量更新订单状态（管理员接口）
     */
    @PutMapping("/batch-status")
    public Result<Boolean> batchUpdateStatus(@RequestParam List<Long> orderIds,
                                             @RequestParam String newStatus,
                                             @RequestParam(required = false) String reason) {
        try {
            boolean success = orderService.batchUpdateStatus(orderIds, newStatus, reason);
            return Result.success("批量更新状态成功", success);
        } catch (Exception e) {
            log.error("批量更新状态失败: {}", e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 取消超时未支付订单（系统接口）
     */
    @PostMapping("/cancel-timeout")
    public Result<Integer> cancelTimeoutOrders(@RequestParam(defaultValue = "30") Integer timeoutMinutes) {
        try {
            int cancelledCount = orderService.cancelTimeoutOrders(timeoutMinutes);
            return Result.success("取消超时订单成功", cancelledCount);
        } catch (Exception e) {
            log.error("取消超时订单失败: {}", e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取订单列表
     */
    @GetMapping("/list")
    public Result<IPage<OrderListVO>> getOrderList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String orderStatus,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestHeader(value = "userId", required = false) Long userId) {
        try {
            OrderQueryDTO queryDTO = new OrderQueryDTO();
            queryDTO.setPageNum(page);
            queryDTO.setPageSize(size);
            queryDTO.setOrderStatus(orderStatus);
            queryDTO.setUserId(userId); // 只查询当前用户的订单

            // 如果有关键字，可以用来搜索订单号或收件人信息
            if (keyword != null && !keyword.trim().isEmpty()) {
                // 这里可以根据需要扩展搜索逻辑
            }

            IPage<OrderListVO> page_result = orderService.getOrderPage(queryDTO);
            return Result.success("查询订单列表成功", page_result);
        } catch (Exception e) {
            log.error("查询订单列表失败: {}", e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 支付订单
     */
    @PostMapping("/{orderId}/pay")
    public Result<Boolean> payOrder(@PathVariable Long orderId,
                                    @RequestHeader("userId") Long userId,
                                    @RequestBody(required = false) PayOrderRequest request) {
        try {
            log.info("用户支付订单，订单ID: {}, 用户ID: {}", orderId, userId);

            // 验证订单是否属于当前用户
            OrderVO order = orderService.getOrderById(orderId);
            if (!order.getUserId().equals(userId)) {
                return Result.error("无权操作此订单");
            }

            String paymentMethod = request != null ? request.getPaymentMethod() : "ONLINE";

            // 这里应该调用支付服务，简化处理直接标记为支付成功
            boolean success = orderService.paymentSuccess(order.getOrderNumber(), paymentMethod);
            return Result.success("支付成功", success);
        } catch (Exception e) {
            log.error("支付订单失败，订单ID: {}, 用户ID: {}, 错误: {}", orderId, userId, e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 确认收货
     */
    @PutMapping("/{orderId}/confirm")
    public Result<Boolean> confirmOrder(@PathVariable Long orderId,
                                        @RequestHeader("userId") Long userId) {
        try {
            log.info("用户确认收货，订单ID: {}, 用户ID: {}", orderId, userId);

            // 验证订单是否属于当前用户
            OrderVO order = orderService.getOrderById(orderId);
            if (!order.getUserId().equals(userId)) {
                return Result.error("无权操作此订单");
            }

            boolean success = orderService.signOrder(orderId, "用户确认收货");
            return Result.success("确认收货成功", success);
        } catch (Exception e) {
            log.error("确认收货失败，订单ID: {}, 用户ID: {}, 错误: {}", orderId, userId, e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 支付订单请求体
     */
    public static class PayOrderRequest {
        private String paymentMethod = "ONLINE";

        public String getPaymentMethod() {
            return paymentMethod;
        }

        public void setPaymentMethod(String paymentMethod) {
            this.paymentMethod = paymentMethod;
        }
    }

    /**
     * 获取操作员统计数据
     */
    @GetMapping("/operator/statistics")
    public Result<Map<String, Object>> getOperatorStatistics() {
        try {
            Map<String, Object> stats = new HashMap<>();

            // 今日订单统计
            LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
            LocalDateTime endOfDay = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59);

            // 待处理订单数
            Long pendingCount = orderMapper.countByStatusAndTimeRange(OrderStatus.PENDING.getCode(), null, null);
            stats.put("pendingCount", pendingCount);

            // 今日完成订单数（使用DELIVERED状态）
            Long todayProcessed = orderMapper.countByStatusAndTimeRange(OrderStatus.DELIVERED.getCode(), startOfDay, endOfDay);
            stats.put("todayProcessed", todayProcessed);

            // 异常订单数
            Long exceptionCount = orderMapper.countByStatusAndTimeRange(OrderStatus.EXCEPTION.getCode(), null, null);
            stats.put("exceptionCount", exceptionCount);

            // 今日收入（只计算已完成的订单）
            BigDecimal todayRevenue = orderMapper.sumAmountByTimeRange(startOfDay, endOfDay);
            stats.put("todayRevenue", todayRevenue != null ? todayRevenue : BigDecimal.ZERO);

            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取操作员统计失败", e);
            return Result.error("获取操作员统计失败: " + e.getMessage());
        }
    }
}
