package org.example.logisticsdelivery;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
@MapperScan("org.example.logisticsdelivery.mapper")
public class LogisticsDeliveryApplication {

    public static void main(String[] args) {
        SpringApplication.run(LogisticsDeliveryApplication.class, args);
    }

}
