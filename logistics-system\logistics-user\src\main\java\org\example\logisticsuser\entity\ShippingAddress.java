package org.example.logisticsuser.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.logistics.common.entity.BaseEntity;

import java.math.BigDecimal;

/**
 * 收发货地址实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("shipping_addresses")
public class ShippingAddress extends BaseEntity {

    /**
     * 地址ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 联系人姓名
     */
    @TableField("contact_name")
    private String contactName;

    /**
     * 联系人电话
     */
    @TableField("contact_phone")
    private String contactPhone;

    /**
     * 省份
     */
    @TableField("province")
    private String province;

    /**
     * 城市
     */
    @TableField("city")
    private String city;

    /**
     * 区域
     */
    @TableField("district")
    private String district;

    /**
     * 详细地址
     */
    @TableField("detailed_address")
    private String detailedAddress;

    /**
     * 邮政编码
     */
    @TableField("postal_code")
    private String postalCode;

    /**
     * 经度
     */
    @TableField("longitude")
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @TableField("latitude")
    private BigDecimal latitude;

    /**
     * 地址类型：1-收货地址，2-发货地址
     */
    @TableField("address_type")
    private Integer addressType;

    /**
     * 是否默认地址：0-否，1-是
     */
    @TableField("is_default")
    private Boolean isDefault;
}