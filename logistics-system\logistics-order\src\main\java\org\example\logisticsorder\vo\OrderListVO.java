package org.example.logisticsorder.vo;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单列表响应VO
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
public class OrderListVO {

    /**
     * 订单ID
     */
    private Long id;

    /**
     * 订单数
     */
    private String orderNumber;

    /**
     * 寄件人信息
     */
    private String senderName;
    private String senderPhone;
    private String senderCity;

    /**
     * 收件人信息
     */
    private String receiverName;
    private String receiverPhone;
    private String receiverCity;

    /**
     * 物品信息
     */
    private String itemName;
    private BigDecimal itemWeight;

    /**
     * 服务类型
     */
    private String serviceType;
    private String serviceTypeDesc;

    /**
     * 费用信息
     */
    private BigDecimal totalFee;

    /**
     * 支付状态
     */
    private Integer paymentStatus;
    private String paymentStatusDesc;

    /**
     * 订单状态
     */
    private String orderStatus;
    private String orderStatusDesc;

    /**
     * 预计送达时间
     */
    private LocalDateTime estimatedDeliveryTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 是否可以取消
     */
    private Boolean canCancel;
}
