/**
 * 真实数据API接口
 * 确保前端完全使用后端接口，移除所有模拟数据
 */

import request from '@/utils/request'
import type { ApiResponse, PageResult } from '@/types/api'

// ========== 订单相关API ==========
export const orderApi = {
  // 获取订单列表（真实数据）
  getOrderList(params: {
    page?: number
    size?: number
    status?: string
    orderNumber?: string
    senderName?: string
    receiverName?: string
    startDate?: string
    endDate?: string
  }): Promise<ApiResponse<PageResult<any>>> {
    return request.get('/order/list', { params })
  },

  // 获取订单详情
  getOrderDetail(orderId: string): Promise<ApiResponse<any>> {
    return request.get(`/order/${orderId}`)
  },

  // 创建订单
  createOrder(data: {
    senderName: string
    senderPhone: string
    senderAddress: string
    receiverName: string
    receiverPhone: string
    receiverAddress: string
    itemName: string
    itemWeight: number
    itemValue?: number
    serviceType: string
    remarks?: string
  }): Promise<ApiResponse<any>> {
    return request.post('/order/create', data)
  },

  // 计算运费
  calculateFee(data: {
    senderAddress: string
    receiverAddress: string
    weight: number
    serviceType: string
  }): Promise<ApiResponse<any>> {
    return request.post('/order/calculate-fee', data)
  },

  // 支付订单
  payOrder(orderId: string, paymentMethod: string): Promise<ApiResponse<any>> {
    return request.post(`/order/${orderId}/pay`, { paymentMethod })
  },

  // 取消订单
  cancelOrder(orderId: string, reason: string): Promise<ApiResponse<any>> {
    return request.post(`/order/${orderId}/cancel`, { reason })
  },

  // 获取订单统计
  getOrderStatistics(params?: {
    startDate?: string
    endDate?: string
    groupBy?: string
  }): Promise<ApiResponse<any>> {
    return request.get('/order/statistics', { params })
  }
}

// ========== 物流轨迹API ==========
export const trackingApi = {
  // 获取订单物流轨迹
  getOrderTracking(orderNumber: string): Promise<ApiResponse<any[]>> {
    return request.get(`/logistics/tracking/${orderNumber}`)
  },

  // 添加轨迹节点
  addTrackingNode(data: {
    orderNumber: string
    status: string
    description: string
    location?: string
    longitude?: number
    latitude?: number
    operatorName?: string
    operatorType?: string
    images?: string[]
    remarks?: string
  }): Promise<ApiResponse<any>> {
    return request.post('/logistics/tracking/add', data)
  },

  // 获取实时位置
  getRealTimeLocation(orderNumber: string): Promise<ApiResponse<any>> {
    return request.get(`/logistics/location/${orderNumber}`)
  },

  // 获取路径规划
  getRoutePlanning(params: {
    fromLng: number
    fromLat: number
    toLng: number
    toLat: number
    strategy?: string
  }): Promise<ApiResponse<any>> {
    return request.get('/logistics/route-planning', { params })
  }
}

// ========== 配送任务API ==========
export const deliveryApi = {
  // 获取配送任务列表
  getTaskList(params: {
    page?: number
    size?: number
    status?: string
    taskType?: string
    courierId?: string
    startDate?: string
    endDate?: string
  }): Promise<ApiResponse<PageResult<any>>> {
    return request.get('/delivery/tasks', { params })
  },

  // 获取配送员任务
  getCourierTasks(courierId: string, status?: string): Promise<ApiResponse<any[]>> {
    return request.get(`/delivery/courier/${courierId}/tasks`, { 
      params: { status } 
    })
  },

  // 接受任务
  acceptTask(taskId: string): Promise<ApiResponse<any>> {
    return request.post(`/delivery/task/${taskId}/accept`)
  },

  // 开始配送
  startDelivery(taskId: string, data: {
    startLocation?: string
    longitude?: number
    latitude?: number
    remarks?: string
  }): Promise<ApiResponse<any>> {
    return request.post(`/delivery/task/${taskId}/start`, data)
  },

  // 完成配送
  completeDelivery(taskId: string, data: {
    completionLocation?: string
    longitude?: number
    latitude?: number
    signatureImage?: string
    receiverName?: string
    remarks?: string
  }): Promise<ApiResponse<any>> {
    return request.post(`/delivery/task/${taskId}/complete`, data)
  },

  // 更新配送员位置
  updateCourierLocation(courierId: string, data: {
    longitude: number
    latitude: number
    address?: string
    updateType: 'GPS' | 'MANUAL'
  }): Promise<ApiResponse<any>> {
    return request.post(`/delivery/courier/${courierId}/location`, data)
  }
}

// ========== 用户管理API ==========
export const userApi = {
  // 获取用户列表
  getUserList(params: {
    page?: number
    size?: number
    role?: string
    status?: string
    keyword?: string
  }): Promise<ApiResponse<PageResult<any>>> {
    return request.get('/user/list', { params })
  },

  // 获取配送员列表
  getCourierList(params?: {
    status?: string
    serviceArea?: string
    vehicleType?: string
  }): Promise<ApiResponse<any[]>> {
    return request.get('/user/couriers', { params })
  },

  // 获取操作员列表
  getOperatorList(params?: {
    status?: string
    stationCode?: string
  }): Promise<ApiResponse<any[]>> {
    return request.get('/user/operators', { params })
  },

  // 获取用户详情
  getUserDetail(userId: string): Promise<ApiResponse<any>> {
    return request.get(`/user/${userId}`)
  },

  // 更新用户信息
  updateUser(userId: string, data: any): Promise<ApiResponse<any>> {
    return request.put(`/user/${userId}`, data)
  }
}

// ========== 网点管理API ==========
export const stationApi = {
  // 获取网点列表
  getStationList(params?: {
    stationType?: string
    city?: string
    status?: string
  }): Promise<ApiResponse<any[]>> {
    return request.get('/station/list', { params })
  },

  // 获取网点详情
  getStationDetail(stationCode: string): Promise<ApiResponse<any>> {
    return request.get(`/station/${stationCode}`)
  },

  // 获取网点统计
  getStationStatistics(stationCode: string, params?: {
    startDate?: string
    endDate?: string
  }): Promise<ApiResponse<any>> {
    return request.get(`/station/${stationCode}/statistics`, { params })
  }
}

// ========== 地址管理API ==========
export const addressApi = {
  // 获取用户地址列表
  getUserAddresses(userId?: string): Promise<ApiResponse<any[]>> {
    const url = userId ? `/address/user/${userId}` : '/address/my'
    return request.get(url)
  },

  // 添加地址
  addAddress(data: {
    contactName: string
    contactPhone: string
    province: string
    city: string
    district: string
    detailedAddress: string
    postalCode?: string
    isDefault?: boolean
    addressType?: string
  }): Promise<ApiResponse<any>> {
    return request.post('/address/add', data)
  },

  // 更新地址
  updateAddress(addressId: string, data: any): Promise<ApiResponse<any>> {
    return request.put(`/address/${addressId}`, data)
  },

  // 删除地址
  deleteAddress(addressId: string): Promise<ApiResponse<any>> {
    return request.delete(`/address/${addressId}`)
  },

  // 设置默认地址
  setDefaultAddress(addressId: string): Promise<ApiResponse<any>> {
    return request.post(`/address/${addressId}/default`)
  }
}

// ========== 位置服务API ==========
export const locationApi = {
  // 搜索地点
  searchLocations(params: {
    keyword: string
    city?: string
    limit?: number
  }): Promise<ApiResponse<any[]>> {
    return request.get('/location/search', { params })
  },

  // 逆地理编码
  reverseGeocode(longitude: number, latitude: number): Promise<ApiResponse<any>> {
    return request.get('/location/reverse-geocode', {
      params: { longitude, latitude }
    })
  },

  // 地理编码
  geocode(address: string): Promise<ApiResponse<any>> {
    return request.get('/location/geocode', {
      params: { address }
    })
  },

  // 获取常用地点
  getFrequentLocations(): Promise<ApiResponse<any[]>> {
    return request.get('/location/frequent')
  },

  // 保存常用地点
  saveFrequentLocation(data: {
    locationName: string
    address: string
    longitude: number
    latitude: number
    locationType: string
  }): Promise<ApiResponse<any>> {
    return request.post('/location/frequent', data)
  },

  // 删除常用地点
  removeFrequentLocation(locationId: string): Promise<ApiResponse<any>> {
    return request.delete(`/location/frequent/${locationId}`)
  },

  // 获取可用网点
  getAvailableStations(): Promise<ApiResponse<any[]>> {
    return request.get('/location/stations')
  },

  // 计算距离
  calculateDistance(params: {
    fromLng: number
    fromLat: number
    toLng: number
    toLat: number
  }): Promise<ApiResponse<any>> {
    return request.get('/location/distance', { params })
  }
}

// ========== 统计分析API ==========
export const analyticsApi = {
  // 获取仪表板数据
  getDashboardData(params?: {
    dateRange?: string
    granularity?: string
  }): Promise<ApiResponse<any>> {
    return request.get('/analytics/dashboard', { params })
  },

  // 获取订单趋势
  getOrderTrends(params: {
    startDate: string
    endDate: string
    granularity: 'day' | 'week' | 'month'
  }): Promise<ApiResponse<any>> {
    return request.get('/analytics/order-trends', { params })
  },

  // 获取配送员绩效
  getCourierPerformance(params?: {
    courierId?: string
    startDate?: string
    endDate?: string
  }): Promise<ApiResponse<any>> {
    return request.get('/analytics/courier-performance', { params })
  },

  // 获取网点效率
  getStationEfficiency(params?: {
    stationCode?: string
    startDate?: string
    endDate?: string
  }): Promise<ApiResponse<any>> {
    return request.get('/analytics/station-efficiency', { params })
  }
}

// ========== 系统配置API ==========
export const systemApi = {
  // 获取系统配置
  getSystemConfig(): Promise<ApiResponse<any>> {
    return request.get('/system/config')
  },

  // 更新系统配置
  updateSystemConfig(data: any): Promise<ApiResponse<any>> {
    return request.put('/system/config', data)
  },

  // 获取系统状态
  getSystemStatus(): Promise<ApiResponse<any>> {
    return request.get('/system/status')
  },

  // 获取操作日志
  getOperationLogs(params: {
    page?: number
    size?: number
    operation?: string
    operator?: string
    startDate?: string
    endDate?: string
  }): Promise<ApiResponse<PageResult<any>>> {
    return request.get('/system/logs', { params })
  }
}
