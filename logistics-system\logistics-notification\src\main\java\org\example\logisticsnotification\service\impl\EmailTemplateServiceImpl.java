package org.example.logisticsnotification.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.example.logisticsnotification.entity.EmailTemplate;
import org.example.logisticsnotification.mapper.EmailTemplateMapper;
import org.example.logisticsnotification.service.EmailTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.mail.internet.MimeMessage;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 邮件模板服务实现类
 * 增强现有notification模块的邮件功能
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Slf4j
@Service
public class EmailTemplateServiceImpl extends ServiceImpl<EmailTemplateMapper, EmailTemplate> implements EmailTemplateService {

    @Autowired
    private JavaMailSender mailSender;

    @Value("${notification.email.from:<EMAIL>}")
    private String fromEmail;

    @Value("${notification.email.fromName:物流跟踪系统}")
    private String fromName;

    // 模板变量正则表达式：{{variableName}}
    private static final Pattern TEMPLATE_PATTERN = Pattern.compile("\\{\\{(\\w+)\\}\\}");

    @Override
    public EmailTemplate getTemplateByCode(String templateCode) {
        if (!StringUtils.hasText(templateCode)) {
            return null;
        }
        
        LambdaQueryWrapper<EmailTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EmailTemplate::getTemplateCode, templateCode)
               .eq(EmailTemplate::getIsEnabled, true);
        
        return this.getOne(wrapper);
    }

    @Override
    public List<EmailTemplate> getAllEnabledTemplates() {
        LambdaQueryWrapper<EmailTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EmailTemplate::getIsEnabled, true)
               .orderByAsc(EmailTemplate::getTemplateCode);
        
        return this.list(wrapper);
    }

    @Override
    public String renderTemplate(String templateCode, Map<String, Object> variables) {
        EmailTemplate template = getTemplateByCode(templateCode);
        if (template == null) {
            log.warn("邮件模板不存在: {}", templateCode);
            return null;
        }

        String content = template.getContent();
        if (variables == null || variables.isEmpty()) {
            return content;
        }

        // 替换模板变量
        Matcher matcher = TEMPLATE_PATTERN.matcher(content);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            String variableName = matcher.group(1);
            Object value = variables.get(variableName);
            String replacement = value != null ? value.toString() : "";
            matcher.appendReplacement(result, replacement);
        }
        matcher.appendTail(result);

        return result.toString();
    }

    @Override
    public boolean sendTemplateEmail(String to, String templateCode, Map<String, Object> variables) {
        try {
            EmailTemplate template = getTemplateByCode(templateCode);
            if (template == null) {
                log.error("邮件模板不存在: {}", templateCode);
                return false;
            }

            // 渲染模板
            String subject = renderString(template.getSubject(), variables);
            String content = renderTemplate(templateCode, variables);

            // 发送邮件
            return sendEmail(to, subject, content);
            
        } catch (Exception e) {
            log.error("发送模板邮件失败: templateCode={}, to={}", templateCode, to, e);
            return false;
        }
    }

    @Override
    public int sendBatchTemplateEmail(List<String> recipients, String templateCode, Map<String, Object> variables) {
        if (recipients == null || recipients.isEmpty()) {
            return 0;
        }

        EmailTemplate template = getTemplateByCode(templateCode);
        if (template == null) {
            log.error("邮件模板不存在: {}", templateCode);
            return 0;
        }

        // 预渲染模板
        String subject = renderString(template.getSubject(), variables);
        String content = renderTemplate(templateCode, variables);

        AtomicInteger successCount = new AtomicInteger(0);

        // 异步批量发送
        List<CompletableFuture<Boolean>> futures = recipients.stream()
                .map(recipient -> CompletableFuture.supplyAsync(() -> {
                    try {
                        boolean success = sendEmail(recipient, subject, content);
                        if (success) {
                            successCount.incrementAndGet();
                        }
                        return success;
                    } catch (Exception e) {
                        log.error("批量发送邮件失败: to={}", recipient, e);
                        return false;
                    }
                }))
                .toList();

        // 等待所有邮件发送完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        log.info("批量发送邮件完成: 总数={}, 成功={}", recipients.size(), successCount.get());
        return successCount.get();
    }

    @Override
    public EmailTemplate saveTemplate(EmailTemplate template) {
        if (template.getId() == null) {
            // 新增
            this.save(template);
        } else {
            // 更新
            this.updateById(template);
        }
        return template;
    }

    @Override
    public boolean deleteTemplate(String templateCode) {
        if (!StringUtils.hasText(templateCode)) {
            return false;
        }

        LambdaQueryWrapper<EmailTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EmailTemplate::getTemplateCode, templateCode);
        
        return this.remove(wrapper);
    }

    @Override
    public boolean toggleTemplate(String templateCode, boolean enabled) {
        if (!StringUtils.hasText(templateCode)) {
            return false;
        }

        EmailTemplate template = new EmailTemplate();
        template.setIsEnabled(enabled);

        LambdaQueryWrapper<EmailTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EmailTemplate::getTemplateCode, templateCode);

        return this.update(template, wrapper);
    }

    /**
     * 发送邮件
     */
    private boolean sendEmail(String to, String subject, String content) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setFrom(fromEmail, fromName);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(content, true); // true表示HTML格式

            mailSender.send(message);
            log.info("邮件发送成功: to={}, subject={}", to, subject);
            return true;

        } catch (Exception e) {
            log.error("邮件发送失败: to={}, subject={}", to, subject, e);
            return false;
        }
    }

    /**
     * 渲染字符串模板
     */
    private String renderString(String template, Map<String, Object> variables) {
        if (!StringUtils.hasText(template) || variables == null || variables.isEmpty()) {
            return template;
        }

        Matcher matcher = TEMPLATE_PATTERN.matcher(template);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            String variableName = matcher.group(1);
            Object value = variables.get(variableName);
            String replacement = value != null ? value.toString() : "";
            matcher.appendReplacement(result, replacement);
        }
        matcher.appendTail(result);

        return result.toString();
    }
}
