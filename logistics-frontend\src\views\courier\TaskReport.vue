<template>
  <div class="task-report">
    <div class="page-header">
      <el-button @click="$router.back()" :icon="ArrowLeft">返回</el-button>
      <h2>报告问题</h2>
    </div>

    <el-card>
      <el-form :model="reportForm" :rules="reportRules" ref="formRef" label-width="100px">
        <el-form-item label="任务编号">
          <el-input v-model="taskNumber" readonly />
        </el-form-item>

        <el-form-item label="问题类型" prop="type" required>
          <el-select v-model="reportForm.type" placeholder="请选择问题类型" style="width: 100%">
            <el-option label="客户联系不上" value="contact_failed" />
            <el-option label="地址错误" value="wrong_address" />
            <el-option label="客户拒收" value="customer_refused" />
            <el-option label="货物损坏" value="goods_damaged" />
            <el-option label="交通问题" value="traffic_issue" />
            <el-option label="车辆故障" value="vehicle_breakdown" />
            <el-option label="其他问题" value="other" />
          </el-select>
        </el-form-item>

        <el-form-item label="问题描述" prop="description" required>
          <el-input 
            v-model="reportForm.description" 
            type="textarea" 
            :rows="4"
            placeholder="请详细描述遇到的问题"
          />
        </el-form-item>

        <el-form-item label="现场照片">
          <el-upload
            v-model:file-list="fileList"
            :action="uploadAction"
            :before-upload="beforeUpload"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            list-type="picture-card"
            :limit="5"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
        </el-form-item>

        <el-form-item label="紧急程度">
          <el-radio-group v-model="reportForm.urgency">
            <el-radio label="low">一般</el-radio>
            <el-radio label="medium">紧急</el-radio>
            <el-radio label="high">非常紧急</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="建议解决方案">
          <el-input 
            v-model="reportForm.solution" 
            type="textarea" 
            :rows="3"
            placeholder="如果您有解决建议，请填写"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitReport" :loading="submitting">
            提交报告
          </el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { ArrowLeft, Plus } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import { courierApi } from '@/api/courier'

const route = useRoute()
const router = useRouter()

// 状态
const submitting = ref(false)
const taskNumber = ref('')
const formRef = ref<FormInstance>()
const fileList = ref([])

// 表单数据
const reportForm = reactive({
  type: '',
  description: '',
  urgency: 'low',
  solution: '',
  images: [] as string[],
})

// 表单验证规则
const reportRules: FormRules = {
  type: [
    { required: true, message: '请选择问题类型', trigger: 'change' },
  ],
  description: [
    { required: true, message: '请填写问题描述', trigger: 'blur' },
    { min: 10, message: '问题描述至少10个字符', trigger: 'blur' },
  ],
}

// 上传配置
const uploadAction = '/api/upload/image'

// 上传前检查
const beforeUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

// 上传成功
const handleUploadSuccess = (response: any) => {
  if (response.code === 200) {
    reportForm.images.push(response.data.url)
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error('图片上传失败')
  }
}

// 上传失败
const handleUploadError = () => {
  ElMessage.error('图片上传失败')
}

// 提交报告
const submitReport = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        const taskId = Number(route.params.id)
        await courierApi.reportProblem(taskId, {
          type: reportForm.type,
          description: reportForm.description,
          images: reportForm.images,
        })
        
        ElMessage.success('问题报告提交成功')
        router.back()
      } catch (error) {
        console.error('提交报告失败:', error)
        ElMessage.error('提交报告失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  reportForm.images = []
  fileList.value = []
}

// 加载任务信息
const loadTaskInfo = async () => {
  try {
    const taskId = Number(route.params.id)
    const response = await courierApi.getTaskDetail(taskId)
    if (response.data.code === 200) {
      taskNumber.value = response.data.data.taskNumber
    }
  } catch (error) {
    console.error('加载任务信息失败:', error)
    taskNumber.value = `T${route.params.id}`
  }
}

onMounted(() => {
  loadTaskInfo()
})
</script>

<style scoped>
.task-report {
  padding: 20px;
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 0 15px;
  color: #333;
}
</style> 