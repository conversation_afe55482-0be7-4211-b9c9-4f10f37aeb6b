package org.example.logisticsdelivery.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.logistics.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 配送员实体
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("couriers")
public class Courier extends BaseEntity {

    /**
     * 配送员ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 配送员编号
     */
    @TableField("courier_code")
    private String courierCode;

    /**
     * 配送员姓名
     */
    @TableField("courier_name")
    private String courierName;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 身份证号
     */
    @TableField("id_card")
    private String idCard;

    /**
     * 工号
     */
    @TableField("work_number")
    private String workNumber;

    /**
     * 车辆类型：BIKE-自行车，MOTORCYCLE-摩托车，CAR-汽车
     */
    @TableField("vehicle_type")
    private String vehicleType;

    /**
     * 车牌号
     */
    @TableField("vehicle_number")
    private String vehicleNumber;

    /**
     * 工作区域
     */
    @TableField("work_area")
    private String workArea;

    /**
     * 最大承重(kg)
     */
    @TableField("max_weight")
    private BigDecimal maxWeight;

    /**
     * 最大体积(m³)
     */
    @TableField("max_volume")
    private BigDecimal maxVolume;

    /**
     * 状态：0-离线，1-在线，2-忙碌，3-休息
     */
    @TableField("status")
    private Integer status;

    /**
     * 当前经度
     */
    @TableField("longitude")
    private BigDecimal longitude;

    /**
     * 当前纬度
     */
    @TableField("latitude")
    private BigDecimal latitude;

    /**
     * 当前地址
     */
    @TableField("current_address")
    private String currentAddress;

    /**
     * 最后定位时间
     */
    @TableField("last_location_time")
    private LocalDateTime lastLocationTime;

    /**
     * 紧急联系人
     */
    @TableField("emergency_contact")
    private String emergencyContact;

    /**
     * 紧急联系电话
     */
    @TableField("emergency_phone")
    private String emergencyPhone;

    /**
     * 入职日期
     */
    @TableField("hire_date")
    private LocalDate hireDate;

    /**
     * 评分
     */
    @TableField("rating")
    private BigDecimal rating;

    /**
     * 配送数量
     */
    @TableField("delivery_count")
    private Integer deliveryCount;
}