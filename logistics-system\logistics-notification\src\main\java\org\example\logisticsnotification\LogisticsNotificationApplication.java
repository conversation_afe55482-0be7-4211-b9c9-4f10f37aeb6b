package org.example.logisticsnotification;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 通知服务启动类
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
@EnableAsync
@EnableScheduling
@MapperScan("org.example.logisticsnotification.mapper")
public class LogisticsNotificationApplication {

    public static void main(String[] args) {
        SpringApplication.run(LogisticsNotificationApplication.class, args);
        System.out.println("===============================================");
        System.out.println("通知服务启动成功！");
        System.out.println("服务端口: 8005");
        System.out.println("健康检查: http://localhost:8005/api/system/health");
        System.out.println("===============================================");
    }
}
