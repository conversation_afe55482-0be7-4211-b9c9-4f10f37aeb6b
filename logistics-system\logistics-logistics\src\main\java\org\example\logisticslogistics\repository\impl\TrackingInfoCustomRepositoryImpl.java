package org.example.logisticslogistics.repository.impl;

import org.example.logisticslogistics.entity.TrackingInfo;
import org.example.logisticslogistics.repository.TrackingInfoCustomRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.*;

/**
 * 物流轨迹信息自定义数据访问层实现
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Repository
public class TrackingInfoCustomRepositoryImpl implements TrackingInfoCustomRepository {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public List<TrackingInfo> findByLocationRange(double minLng, double maxLng, double minLat, double maxLat) {
        Criteria criteria = Criteria.where("currentLocation.longitude").gte(minLng).lte(maxLng)
                .and("currentLocation.latitude").gte(minLat).lte(maxLat);
        Query query = new Query(criteria);
        return mongoTemplate.find(query, TrackingInfo.class);
    }

    @Override
    public List<TrackingInfo> findNearby(double longitude, double latitude, double radiusKm) {
        // 使用地理空间查询，需要先创建地理索引
        // 这里使用简单的矩形范围查询作为近似
        double latRange = radiusKm / 111.0; // 1度约等于111公里
        double lngRange = radiusKm / (111.0 * Math.cos(Math.toRadians(latitude)));
        
        return findByLocationRange(
            longitude - lngRange, longitude + lngRange,
            latitude - latRange, latitude + latRange
        );
    }

    @Override
    public List<TrackingInfo> findDeliveryRoute(Long courierId, LocalDateTime startTime, LocalDateTime endTime) {
        Criteria criteria = Criteria.where("courierId").is(courierId)
                .and("lastUpdateTime").gte(startTime).lte(endTime);
        Query query = new Query(criteria).with(org.springframework.data.domain.Sort.by("lastUpdateTime"));
        return mongoTemplate.find(query, TrackingInfo.class);
    }

    @Override
    @SuppressWarnings({"unchecked", "rawtypes"})
    public Map<String, Long> countByStatus() {
        Aggregation aggregation = newAggregation(
                group("currentStatus").count().as("count"),
                project("count").and("currentStatus").previousOperation()
        );
        
        AggregationResults results = mongoTemplate.aggregate(
                aggregation, "tracking_info", Map.class);
        
        Map<String, Long> statusCounts = new HashMap<>();
        for (Object obj : results.getMappedResults()) {
            Map result = (Map) obj;
            statusCounts.put((String) result.get("_id"), 
                           ((Number) result.get("count")).longValue());
        }
        return statusCounts;
    }

    @Override
    @SuppressWarnings({"unchecked", "rawtypes"})
    public Map<String, Long> countByCity() {
        Aggregation aggregation = newAggregation(
                group("currentLocation.city").count().as("count"),
                project("count").and("city").previousOperation()
        );
        
        AggregationResults results = mongoTemplate.aggregate(
                aggregation, "tracking_info", Map.class);
        
        Map<String, Long> cityCounts = new HashMap<>();
        for (Object obj : results.getMappedResults()) {
            Map result = (Map) obj;
            cityCounts.put((String) result.get("_id"), 
                          ((Number) result.get("count")).longValue());
        }
        return cityCounts;
    }

    @Override
    public Map<String, Object> getCourierPerformance(Long courierId, LocalDateTime startTime, LocalDateTime endTime) {
        // 统计配送员绩效
        Criteria criteria = Criteria.where("courierId").is(courierId)
                .and("createTime").gte(startTime).lte(endTime);
        
        Query query = new Query(criteria);
        List<TrackingInfo> trackings = mongoTemplate.find(query, TrackingInfo.class);
        
        Map<String, Object> performance = new HashMap<>();
        performance.put("totalOrders", trackings.size());
        performance.put("completedOrders", trackings.stream()
                .filter(t -> "DELIVERED".equals(t.getCurrentStatus()) || 
                           "SELF_PICKUP".equals(t.getCurrentStatus()))
                .count());
        performance.put("exceptionOrders", trackings.stream()
                .filter(TrackingInfo::getIsException)
                .count());
        performance.put("totalDistance", trackings.stream()
                .mapToDouble(t -> t.getTotalDistance() != null ? t.getTotalDistance() : 0.0)
                .sum());
        
        return performance;
    }

    @Override
    public Map<String, Object> getRealTimeMonitorData() {
        Map<String, Object> monitorData = new HashMap<>();
        
        // 获取各种统计数据
        monitorData.put("totalOrders", mongoTemplate.count(new Query(), TrackingInfo.class));
        monitorData.put("deliveryInProgress", mongoTemplate.count(
                new Query(Criteria.where("currentStatus").in("OUT_FOR_DELIVERY", "DELIVERY_ATTEMPT")), 
                TrackingInfo.class));
        monitorData.put("exceptionOrders", mongoTemplate.count(
                new Query(Criteria.where("isException").is(true)), 
                TrackingInfo.class));
        monitorData.put("completedToday", mongoTemplate.count(
                new Query(Criteria.where("actualDeliveryTime")
                        .gte(LocalDateTime.now().withHour(0).withMinute(0).withSecond(0))), 
                TrackingInfo.class));
        
        return monitorData;
    }

    @Override
    public Page<TrackingInfo> findByComplexConditions(String status, String city, Long courierId, 
            Boolean isException, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable) {
        
        Criteria criteria = new Criteria();
        
        if (status != null && !status.isEmpty()) {
            criteria.and("currentStatus").is(status);
        }
        if (city != null && !city.isEmpty()) {
            criteria.and("currentLocation.city").is(city);
        }
        if (courierId != null) {
            criteria.and("courierId").is(courierId);
        }
        if (isException != null) {
            criteria.and("isException").is(isException);
        }
        if (startTime != null && endTime != null) {
            criteria.and("createTime").gte(startTime).lte(endTime);
        }
        
        Query query = new Query(criteria).with(pageable);
        List<TrackingInfo> trackings = mongoTemplate.find(query, TrackingInfo.class);
        
        long total = mongoTemplate.count(new Query(criteria), TrackingInfo.class);
        
        return new PageImpl<>(trackings, pageable, total);
    }

    @Override
    @SuppressWarnings({"unchecked", "rawtypes"})
    public List<Map<String, Object>> getHeatMapData(String city, LocalDateTime startTime, LocalDateTime endTime) {
        Criteria criteria = Criteria.where("currentLocation.city").is(city)
                .and("createTime").gte(startTime).lte(endTime);
        
        Aggregation aggregation = newAggregation(
                match(criteria),
                group("currentLocation.district").count().as("count")
                        .first("currentLocation.longitude").as("longitude")
                        .first("currentLocation.latitude").as("latitude"),
                project("count", "longitude", "latitude")
                        .and("district").previousOperation()
        );
        
        AggregationResults results = mongoTemplate.aggregate(
                aggregation, "tracking_info", Map.class);
        
        return (List<Map<String, Object>>) results.getMappedResults();
    }

    @Override
    public void batchUpdateLocation(List<TrackingInfo> trackingInfos) {
        for (TrackingInfo tracking : trackingInfos) {
            Query query = new Query(Criteria.where("id").is(tracking.getId()));
            Update update = new Update()
                    .set("currentLocation", tracking.getCurrentLocation())
                    .set("lastUpdateTime", LocalDateTime.now());
            mongoTemplate.updateFirst(query, update, TrackingInfo.class);
        }
    }

    @Override
    public long deleteExpiredData(LocalDateTime expireTime) {
        Query query = new Query(Criteria.where("createTime").lt(expireTime));
        return mongoTemplate.remove(query, TrackingInfo.class).getDeletedCount();
    }

    @Override
    public List<TrackingInfo> findDeliveryInProgress() {
        Criteria criteria = Criteria.where("currentStatus").in("OUT_FOR_DELIVERY", "DELIVERY_ATTEMPT");
        Query query = new Query(criteria);
        return mongoTemplate.find(query, TrackingInfo.class);
    }

    @Override
    public List<TrackingInfo> findByCourierAndDate(Long courierId, LocalDateTime startOfDay, LocalDateTime endOfDay) {
        Criteria criteria = Criteria.where("courierId").is(courierId)
                .and("createTime").gte(startOfDay).lt(endOfDay);
        Query query = new Query(criteria).with(org.springframework.data.domain.Sort.by("createTime"));
        return mongoTemplate.find(query, TrackingInfo.class);
    }
} 