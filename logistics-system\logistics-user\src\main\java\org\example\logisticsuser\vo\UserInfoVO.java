package org.example.logisticsuser.vo;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户信息VO
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
public class UserInfoVO {

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 手机号（脱敏）
     */
    private String phone;

    /**
     * 邮箱（脱敏）
     */
    private String email;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 用户状态
     */
    private String status;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 用户角色列表
     */
    private List<String> roles;

    /**
     * 用户权限列表
     */
    private List<String> permissions;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
