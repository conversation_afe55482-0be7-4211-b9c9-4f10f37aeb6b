package org.example.logisticslogistics.service;

import org.example.logisticslogistics.entity.LocationInfo;
import org.example.logisticslogistics.entity.TrackingInfo;
import org.example.logisticslogistics.entity.TrackingNode;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 物流轨迹管理服务接口
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
public interface TrackingService {

    /**
     * 创建轨迹信息
     */
    TrackingInfo createTracking(String orderNumber, Long orderId);

    /**
     * 根据订单号查询轨迹信息
     */
    TrackingInfo getTrackingByOrderNumber(String orderNumber);

    /**
     * 根据订单ID查询轨迹信息
     */
    TrackingInfo getTrackingByOrderId(Long orderId);

    /**
     * 更新轨迹状态
     */
    TrackingInfo updateTrackingStatus(String trackingId, String status, String description, 
                                     LocationInfo location, Long operatorId, String operatorName);

    /**
     * 添加轨迹节点
     */
    TrackingInfo addTrackingNode(String trackingId, TrackingNode node);

    /**
     * 批量添加轨迹节点
     */
    TrackingInfo addTrackingNodes(String trackingId, List<TrackingNode> nodes);

    /**
     * 更新当前位置
     */
    TrackingInfo updateCurrentLocation(String trackingId, LocationInfo location);

    /**
     * 设置配送员信息
     */
    TrackingInfo assignCourier(String trackingId, Long courierId, String courierName, String courierPhone);

    /**
     * 设置预计到达时间
     */
    TrackingInfo updateEstimatedDeliveryTime(String trackingId, LocalDateTime estimatedTime);

    /**
     * 标记异常
     */
    TrackingInfo markAsException(String trackingId, String exceptionDescription);

    /**
     * 清除异常状态
     */
    TrackingInfo clearException(String trackingId);

    /**
     * 完成配送
     */
    TrackingInfo completeDelivery(String trackingId, String completionNotes);

    /**
     * 根据状态查询轨迹信息
     */
    List<TrackingInfo> getTrackingsByStatus(String status);

    /**
     * 根据配送员查询轨迹信息
     */
    Page<TrackingInfo> getTrackingsByCourier(Long courierId, Pageable pageable);

    /**
     * 查询异常轨迹信息
     */
    Page<TrackingInfo> getExceptionTrackings(Pageable pageable);

    /**
     * 查询配送中的轨迹信息
     */
    List<TrackingInfo> getDeliveryInProgress();

    /**
     * 复杂条件查询
     */
    Page<TrackingInfo> searchTrackings(String status, String city, Long courierId, 
                                      Boolean isException, LocalDateTime startTime, 
                                      LocalDateTime endTime, Pageable pageable);

    /**
     * 获取配送员当日任务
     */
    List<TrackingInfo> getCourierDailyTasks(Long courierId, LocalDateTime date);

    /**
     * 计算总里程
     */
    Double calculateTotalDistance(String trackingId);

    /**
     * 获取轨迹时间线
     */
    List<TrackingNode> getTrackingTimeline(String trackingId);

    /**
     * 批量更新位置
     */
    void batchUpdateLocations(List<TrackingInfo> trackingInfos);

    /**
     * 删除过期数据
     */
    long cleanExpiredData(int expireDays);

    /**
     * 检查轨迹是否存在
     */
    boolean trackingExists(String orderNumber);

    /**
     * 获取轨迹详情（包含完整节点列表）
     */
    TrackingInfo getTrackingDetails(String trackingId);
} 