<template>
  <div class="delivery-map-container">
    <div class="map-header">
      <div class="header-left">
        <h2>配送地图</h2>
        <div class="map-stats">
          <el-tag type="success">在线配送员: {{ onlineCouriers }}</el-tag>
          <el-tag type="warning">进行中订单: {{ activeOrders }}</el-tag>
          <el-tag type="info">今日完成: {{ todayCompleted }}</el-tag>
        </div>
      </div>
      <div class="header-right">
        <el-button-group>
          <el-button
            :type="viewMode === 'couriers' ? 'primary' : 'default'"
            @click="viewMode = 'couriers'"
          >
            配送员视图
          </el-button>
          <el-button
            :type="viewMode === 'orders' ? 'primary' : 'default'"
            @click="viewMode = 'orders'"
          >
            订单视图
          </el-button>
          <el-button
            :type="viewMode === 'routes' ? 'primary' : 'default'"
            @click="viewMode = 'routes'"
          >
            路线视图
          </el-button>
        </el-button-group>
        
        <el-divider direction="vertical" />
        
        <!-- 实时追踪控制 -->
        <el-button-group>
          <el-button 
            :type="realTimeTracking ? 'success' : 'default'"
            @click="realTimeTracking ? stopRealTimeTracking() : startRealTimeTracking()"
          >
            <el-icon><Location /></el-icon>
            {{ realTimeTracking ? '停止追踪' : '实时追踪' }}
          </el-button>
          <el-button 
            type="info" 
            @click="clearTrajectories"
            :disabled="!realTimeTracking"
          >
            <el-icon><Delete /></el-icon>
            清除轨迹
          </el-button>
        </el-button-group>
        
        <el-divider direction="vertical" />
        
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <div class="map-content">
      <div class="map-container">
        <BaseMap
          ref="mapRef"
          :markers="mapMarkers"
          :polylines="mapPolylines"
          :center="mapCenter"
          :zoom="mapZoom"
          @marker-click="handleMarkerClick"
          @map-click="handleMapClick"
        />
      </div>

      <div class="map-sidebar">
        <el-tabs v-model="activeTab" class="sidebar-tabs">
          <el-tab-pane label="配送员" name="couriers">
            <div class="courier-list">
              <div class="list-header">
                <el-input v-model="courierSearch" placeholder="搜索配送员" size="small" clearable>
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </div>
              <div class="courier-items">
                <div
                  v-for="courier in filteredCouriers"
                  :key="courier.id"
                  class="courier-item"
                  :class="{ active: selectedCourier?.id === courier.id }"
                  @click="selectCourier(courier)"
                >
                  <div class="courier-info">
                    <div class="courier-name">{{ courier.name }}</div>
                    <div class="courier-status">
                      <el-tag :type="getStatusType(courier.status)" size="small">
                        {{ getStatusText(courier.status) }}
                      </el-tag>
                    </div>
                  </div>
                  <div class="courier-stats">
                    <span>今日: {{ courier.todayCompleted }}单</span>
                    <span>评分: {{ courier.rating }}</span>
                  </div>
                  <div class="courier-actions" v-if="realTimeTracking">
                    <el-button 
                      size="small" 
                      type="text" 
                      @click.stop="showCourierTrajectory(courier.id)"
                    >
                      显示轨迹
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="订单" name="orders">
            <div class="order-list">
              <div class="list-header">
                <el-select
                  v-model="orderStatusFilter"
                  placeholder="订单状态"
                  size="small"
                  clearable
                >
                  <el-option label="全部" value="" />
                  <el-option label="待分配" value="pending" />
                  <el-option label="配送中" value="delivering" />
                  <el-option label="已完成" value="completed" />
                </el-select>
              </div>
              <div class="order-items">
                <div
                  v-for="order in filteredOrders"
                  :key="order.id"
                  class="order-item"
                  :class="{ active: selectedOrder?.id === order.id }"
                  @click="selectOrder(order)"
                >
                  <div class="order-info">
                    <div class="order-number">{{ order.orderNumber }}</div>
                    <div class="order-status">
                      <el-tag :type="getOrderStatusType(order.status)" size="small">
                        {{ getOrderStatusText(order.status) }}
                      </el-tag>
                    </div>
                  </div>
                  <div class="order-addresses">
                    <div class="address-item">
                      <el-icon><LocationFilled /></el-icon>
                      <span>{{ order.pickupAddress }}</span>
                    </div>
                    <div class="address-item">
                      <el-icon><Location /></el-icon>
                      <span>{{ order.deliveryAddress }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 配送员详情弹窗 -->
    <el-dialog v-model="courierDetailVisible" title="配送员详情" width="600px">
      <div v-if="selectedCourier" class="courier-detail">
        <div class="detail-header">
          <div class="courier-avatar">
            <el-avatar :size="60" :src="selectedCourier.avatar">
              {{ selectedCourier.name.charAt(0) }}
            </el-avatar>
          </div>
          <div class="courier-basic">
            <h3>{{ selectedCourier.name }}</h3>
            <p>电话: {{ selectedCourier.phone }}</p>
            <p>
              状态:
              <el-tag :type="getStatusType(selectedCourier.status)">
                {{ getStatusText(selectedCourier.status) }}
              </el-tag>
            </p>
          </div>
        </div>

        <div class="detail-stats">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="stat-item">
                <div class="stat-value">{{ selectedCourier.todayCompleted }}</div>
                <div class="stat-label">今日完成</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-item">
                <div class="stat-value">{{ selectedCourier.currentOrders }}</div>
                <div class="stat-label">当前订单</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-item">
                <div class="stat-value">{{ selectedCourier.rating }}</div>
                <div class="stat-label">评分</div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Search, LocationFilled, Location, Delete } from '@element-plus/icons-vue'
import BaseMap from '@/components/Map/BaseMap.vue'
import { courierApi, type CourierInfo } from '@/api/courier'
import { orderApi, type Order } from '@/api/order'
import http from '@/utils/http'

// 响应式数据
const mapRef = ref()
const viewMode = ref<'couriers' | 'orders' | 'routes'>('couriers')
const activeTab = ref('couriers')
const courierSearch = ref('')
const orderStatusFilter = ref('')

// 地图相关
const mapCenter = ref({ lat: 39.9042, lng: 116.4074 })
const mapZoom = ref(12)
const mapMarkers = ref<any[]>([])
const mapPolylines = ref<any[]>([])

// 数据
const couriers = ref<CourierInfo[]>([])
const orders = ref<Order[]>([])
const selectedCourier = ref<CourierInfo | null>(null)
const selectedOrder = ref<Order | null>(null)
const courierDetailVisible = ref(false)

// 实时追踪相关
const realTimeTracking = ref(false)
const trackingInterval = ref<number | null>(null)
const courierTrajectories = ref<Map<number, Array<{lat: number, lng: number, time: string}>>>(new Map())

// 统计数据
const onlineCouriers = computed(() => couriers.value.filter((c) => c.status === 'online').length)
const activeOrders = computed(() => orders.value.filter((o) => o.status === 'delivering').length)
const todayCompleted = computed(() => couriers.value.reduce((sum, c) => sum + c.todayCompleted, 0))

// 过滤数据
const filteredCouriers = computed(() => {
  if (!courierSearch.value) return couriers.value
  return couriers.value.filter(
    (c) => c.name.includes(courierSearch.value) || c.phone.includes(courierSearch.value),
  )
})

const filteredOrders = computed(() => {
  if (!orderStatusFilter.value) return orders.value
  return orders.value.filter((o) => o.status === orderStatusFilter.value)
})

// 方法
const refreshData = async () => {
  await Promise.all([loadCouriers(), loadOrders()])
  updateMapData()
}

const loadCouriers = async () => {
  try {
    const response = await courierApi.getAllCouriers({
      page: 1,
      size: 100,
    })
    if (response.code === 200) {
      couriers.value = response.data.records.map((courier: any) => ({
        id: courier.id,
        name: courier.courierName,
        phone: courier.phone,
        status: getStatusFromCode(courier.status), // 使用状态映射函数
        location: courier.workArea,
        currentOrders: 0,
        todayCompleted: 0,
        rating: courier.rating || 5.0,
        workingHours: '09:00-18:00',
        lastActiveTime: courier.updateTime,
      }))
    }
  } catch (error) {
    console.error('加载配送员数据失败:', error)
    couriers.value = []
    ElMessage.error('加载配送员数据失败')
  }
}

const loadOrders = async () => {
  try {
    const response = await orderApi.getOrderPage({
      page: 1,
      size: 100,
      orderStatus: 'IN_TRANSIT', // 使用正确的状态值
    })
    if (response.code === 200) {
      orders.value = response.data.records.map((order: any) => ({
        id: order.id,
        orderNumber: order.orderNumber,
        status: getOrderStatusFromCode(order.orderStatus), // 状态映射
        pickupLat: order.senderLatitude || 39.9042,
        pickupLng: order.senderLongitude || 116.4074,
        deliveryLat: order.receiverLatitude || 39.9042,
        deliveryLng: order.receiverLongitude || 116.4074,
        pickupAddress: order.senderAddress,
        deliveryAddress: order.receiverAddress,
      }))
    }
  } catch (error) {
    console.error('加载订单数据失败:', error)
    orders.value = []
    ElMessage.error('加载订单数据失败')
  }
}

// 添加状态映射函数
const getStatusFromCode = (statusCode: number): string => {
  const statusMap: Record<number, string> = {
    1: 'online',
    2: 'busy',
    3: 'offline',
    0: 'rest',
  }
  return statusMap[statusCode] || 'offline'
}

const getOrderStatusFromCode = (statusCode: string): string => {
  const statusMap: Record<string, string> = {
    PENDING: 'pending',
    IN_TRANSIT: 'delivering',
    DELIVERED: 'completed',
    CANCELLED: 'cancelled',
  }
  return statusMap[statusCode] || 'pending'
}

const updateMapData = () => {
  const markers: any[] = []
  const polylines: any[] = []

  if (viewMode.value === 'couriers') {
    // 显示配送员位置
    couriers.value.forEach((courier) => {
      const marker = {
        id: `courier-${courier.id}`,
        lat: courier.lat || (39.9042 + Math.random() * 0.1),
        lng: courier.lng || (116.4074 + Math.random() * 0.1),
        type: 'courier',
        title: courier.name,
        status: courier.status,
        data: courier,
        icon: getStatusIcon(courier.status)
      }
      
      markers.push(marker)
    })
    
    // 如果开启实时追踪，显示轨迹
    if (realTimeTracking.value) {
      courierTrajectories.value.forEach((trajectory, courierId) => {
        if (trajectory.length > 1) {
          polylines.push({
            id: `trajectory-${courierId}`,
            path: trajectory,
            strokeColor: '#FF6B6B',
            strokeWeight: 2,
            strokeOpacity: 0.6
          })
        }
      })
    }
  } else if (viewMode.value === 'orders') {
    // 显示订单位置
    orders.value.forEach((order) => {
      if (order.pickupLat && order.pickupLng) {
        markers.push({
          id: `pickup-${order.id}`,
          lat: order.pickupLat,
          lng: order.pickupLng,
          type: 'pickup',
          title: `取件: ${order.orderNumber}`,
          data: order,
          icon: 'pickup'
        })
      }
      if (order.deliveryLat && order.deliveryLng) {
        markers.push({
          id: `delivery-${order.id}`,
          lat: order.deliveryLat,
          lng: order.deliveryLng,
          type: 'delivery',
          title: `送件: ${order.orderNumber}`,
          data: order,
          icon: 'delivery'
        })
      }
    })
  } else if (viewMode.value === 'routes') {
    // 路线视图：同时显示配送员和订单，以及路线
    couriers.value.forEach((courier) => {
      if (courier.status === 'busy') {
        markers.push({
          id: `courier-${courier.id}`,
          lat: courier.lat || (39.9042 + Math.random() * 0.1),
          lng: courier.lng || (116.4074 + Math.random() * 0.1),
          type: 'courier',
          title: courier.name,
          status: courier.status,
          data: courier,
          icon: 'courier-busy'
        })
      }
    })
    
    orders.value.forEach((order) => {
      // 显示配送路线
      if (order.pickupLat && order.pickupLng && order.deliveryLat && order.deliveryLng) {
        markers.push(
          {
            id: `pickup-${order.id}`,
            lat: order.pickupLat,
            lng: order.pickupLng,
            type: 'pickup',
            title: `取件: ${order.orderNumber}`,
            data: order,
            icon: 'pickup'
          },
          {
            id: `delivery-${order.id}`,
            lat: order.deliveryLat,
            lng: order.deliveryLng,
            type: 'delivery',
            title: `送件: ${order.orderNumber}`,
            data: order,
            icon: 'delivery'
          }
        )
        
        polylines.push({
          id: `route-${order.id}`,
          path: [
            { lat: order.pickupLat, lng: order.pickupLng },
            { lat: order.deliveryLat, lng: order.deliveryLng }
          ],
          strokeColor: getRouteColor(order.status),
          strokeWeight: 3,
          strokeOpacity: 0.8
        })
      }
    })
  }

  mapMarkers.value = markers
  mapPolylines.value = [...mapPolylines.value.filter(p => p.id.startsWith('trajectory-') || p.id.startsWith('route-')), ...polylines]
}

const selectCourier = (courier: CourierInfo) => {
  selectedCourier.value = courier
  courierDetailVisible.value = true

  // 在地图上高亮显示
  const marker = mapMarkers.value.find((m) => m.id === `courier-${courier.id}`)
  if (marker && mapRef.value) {
    mapRef.value.centerToMarker(marker)
  }
}

const selectOrder = (order: Order) => {
  selectedOrder.value = order

  // 在地图上显示订单路线
  const pickupMarker = mapMarkers.value.find((m) => m.id === `pickup-${order.id}`)
  const deliveryMarker = mapMarkers.value.find((m) => m.id === `delivery-${order.id}`)

  if (pickupMarker && deliveryMarker && mapRef.value) {
    mapRef.value.showRoute([pickupMarker, deliveryMarker])
    
    // 获取详细路线规划
    getDeliveryRoute(order)
  }
}

const handleMarkerClick = (marker: any) => {
  if (marker.type === 'courier') {
    selectCourier(marker.data)
  } else if (marker.type === 'pickup' || marker.type === 'delivery') {
    selectOrder(marker.data)
  }
}

const handleMapClick = () => {
  // 清除选择
  selectedCourier.value = null
  selectedOrder.value = null
}

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    online: 'success',
    busy: 'warning',
    offline: 'info',
    rest: 'danger',
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    online: '在线',
    busy: '忙碌',
    offline: '离线',
    rest: '休息',
  }
  return statusMap[status] || status
}

const getOrderStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    delivering: 'primary',
    completed: 'success',
    cancelled: 'danger',
  }
  return statusMap[status] || 'info'
}

const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待分配',
    delivering: '配送中',
    completed: '已完成',
    cancelled: '已取消',
  }
  return statusMap[status] || status
}

// 获取状态图标
const getStatusIcon = (status: string) => {
  const iconMap: Record<string, string> = {
    online: 'courier-online',
    busy: 'courier-busy',
    offline: 'courier-offline',
    rest: 'courier-rest'
  }
  return iconMap[status] || 'courier-default'
}

// 获取路线颜色
const getRouteColor = (status: string) => {
  const colorMap: Record<string, string> = {
    pending: '#E6A23C',
    delivering: '#409EFF',
    completed: '#67C23A',
    cancelled: '#F56C6C'
  }
  return colorMap[status] || '#409EFF'
}

// 获取配送路线规划
const getDeliveryRoute = async (order: any) => {
  try {
    const response = await http.post('/map/route', {
      origin: { lat: order.pickupLat, lng: order.pickupLng },
      destination: { lat: order.deliveryLat, lng: order.deliveryLng }
    })
    
    if (response.code === 200) {
      const routeData = response.data
      
      // 绘制规划路线
      const routePolyline = {
        id: `route-${order.id}`,
        path: routeData.path || [
          { lat: order.pickupLat, lng: order.pickupLng },
          { lat: order.deliveryLat, lng: order.deliveryLng }
        ],
        strokeColor: '#409EFF',
        strokeWeight: 4,
        strokeOpacity: 0.9
      }
      
      mapPolylines.value = [...mapPolylines.value.filter(p => !p.id.startsWith('route-')), routePolyline]
      
      ElMessage.success(`路线规划完成，距离：${routeData.distance || '未知'}km，预计用时：${routeData.duration || '未知'}分钟`)
    }
  } catch (error) {
    console.error('路线规划失败:', error)
    ElMessage.error('路线规划失败')
  }
}

// 开始实时追踪
const startRealTimeTracking = () => {
  if (realTimeTracking.value) return
  
  realTimeTracking.value = true
  ElMessage.success('已开启实时追踪')
  
  // 每30秒更新一次位置
  trackingInterval.value = setInterval(async () => {
    await updateCourierLocations()
    await updateOrderStatus()
  }, 30000)
}

// 停止实时追踪
const stopRealTimeTracking = () => {
  if (!realTimeTracking.value) return
  
  realTimeTracking.value = false
  if (trackingInterval.value) {
    clearInterval(trackingInterval.value)
    trackingInterval.value = null
  }
  ElMessage.info('已停止实时追踪')
}

// 更新配送员位置
const updateCourierLocations = async () => {
  try {
    const activeCouriers = couriers.value.filter(c => c.status === 'online' || c.status === 'busy')
    
    for (const courier of activeCouriers) {
      try {
        const response = await courierApi.getCourierLocation(courier.id)
        if (response.code === 200) {
          const location = response.data
          
          // 更新配送员位置
          courier.lat = location.latitude
          courier.lng = location.longitude
          courier.lastActiveTime = location.updateTime
          
          // 记录轨迹
          if (!courierTrajectories.value.has(courier.id)) {
            courierTrajectories.value.set(courier.id, [])
          }
          
          const trajectory = courierTrajectories.value.get(courier.id)!
          trajectory.push({
            lat: location.latitude,
            lng: location.longitude,
            time: location.updateTime
          })
          
          // 保持最近100个位置点
          if (trajectory.length > 100) {
            trajectory.splice(0, trajectory.length - 100)
          }
        }
      } catch (error) {
        console.error(`更新配送员${courier.id}位置失败:`, error)
      }
    }
    
    // 更新地图显示
    updateMapData()
  } catch (error) {
    console.error('批量更新配送员位置失败:', error)
  }
}

// 更新订单状态
const updateOrderStatus = async () => {
  try {
    const response = await orderApi.getOrderPage({
      page: 1,
      size: 100,
      orderStatus: 'IN_TRANSIT,PICKUP_ASSIGNED,OUT_FOR_DELIVERY'
    })
    
    if (response.code === 200) {
      const updatedOrders = response.data.records.map((order: any) => ({
        id: order.id,
        orderNumber: order.orderNumber,
        status: getOrderStatusFromCode(order.orderStatus),
        pickupLat: order.senderLatitude || 39.9042,
        pickupLng: order.senderLongitude || 116.4074,
        deliveryLat: order.receiverLatitude || 39.9042,
        deliveryLng: order.receiverLongitude || 116.4074,
        pickupAddress: order.senderAddress,
        deliveryAddress: order.receiverAddress,
        courierName: order.courierName,
        estimatedTime: order.estimatedDeliveryTime
      }))
      
      orders.value = updatedOrders
      updateMapData()
    }
  } catch (error) {
    console.error('更新订单状态失败:', error)
  }
}

// 显示配送员轨迹
const showCourierTrajectory = (courierId: number) => {
  const trajectory = courierTrajectories.value.get(courierId)
  if (!trajectory || trajectory.length < 2) {
    ElMessage.warning('该配送员暂无轨迹数据')
    return
  }
  
  // 在地图上绘制轨迹线
  const trajectoryPolyline = {
    id: `trajectory-${courierId}`,
    path: trajectory,
    strokeColor: '#FF6B6B',
    strokeWeight: 3,
    strokeOpacity: 0.8
  }
  
  mapPolylines.value = [...mapPolylines.value.filter(p => !p.id.startsWith('trajectory-')), trajectoryPolyline]
  
  ElMessage.success('已显示配送员轨迹')
}

// 清除轨迹显示
const clearTrajectories = () => {
  mapPolylines.value = mapPolylines.value.filter(p => !p.id.startsWith('trajectory-'))
  ElMessage.info('已清除轨迹显示')
}

// 监听视图模式变化
watch(viewMode, () => {
  updateMapData()
})

// 初始化
onMounted(() => {
  refreshData()
})

// 在组件卸载时清理定时器
onUnmounted(() => {
  stopRealTimeTracking()
})
</script>

<style scoped>
.delivery-map-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
}

.header-left h2 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
}

.map-stats {
  display: flex;
  gap: 8px;
}

.header-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.map-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.map-container {
  flex: 1;
  position: relative;
}

.map-sidebar {
  width: 320px;
  background: #fff;
  border-left: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}

.sidebar-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.sidebar-tabs :deep(.el-tabs__content) {
  flex: 1;
  overflow: hidden;
}

.sidebar-tabs :deep(.el-tab-pane) {
  height: 100%;
  overflow: hidden;
}

.courier-list,
.order-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.list-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.courier-items,
.order-items {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.courier-item,
.order-item {
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.courier-item:hover,
.order-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
}

.courier-item.active,
.order-item.active {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.courier-info,
.order-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.courier-name,
.order-number {
  font-weight: 600;
  color: #303133;
}

.courier-stats {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
}

.order-addresses {
  font-size: 12px;
  color: #606266;
}

.address-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.address-item .el-icon {
  margin-right: 4px;
  font-size: 14px;
}

.courier-detail {
  padding: 16px;
}

.detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.courier-avatar {
  margin-right: 16px;
}

.courier-basic h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
}

.courier-basic p {
  margin: 4px 0;
  color: #606266;
}

.detail-stats {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 6px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}
</style>
