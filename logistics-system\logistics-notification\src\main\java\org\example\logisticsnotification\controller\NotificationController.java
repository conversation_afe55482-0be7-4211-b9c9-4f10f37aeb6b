package org.example.logisticsnotification.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logistics.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.example.logisticsnotification.entity.Notification;
import org.example.logisticsnotification.service.NotificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 通知管理控制器
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Slf4j
@RestController
@RequestMapping("/api/notification")
public class NotificationController {

    @Autowired
    private NotificationService notificationService;

    /**
     * 创建通知
     */
    @PostMapping
    public Result<Notification> createNotification(@RequestBody Notification notification) {
        try {
            Notification created = notificationService.createNotification(notification);
            return created != null ? Result.success(created) : Result.error("创建通知失败");
        } catch (Exception e) {
            log.error("创建通知异常", e);
            return Result.error("创建通知异常：" + e.getMessage());
        }
    }

    /**
     * 批量创建通知
     */
    @PostMapping("/batch")
    public Result<Boolean> batchCreateNotifications(@RequestBody List<Notification> notifications) {
        try {
            boolean success = notificationService.batchCreateNotifications(notifications);
            return Result.success(success);
        } catch (Exception e) {
            log.error("批量创建通知异常", e);
            return Result.error("批量创建通知异常：" + e.getMessage());
        }
    }

    /**
     * 创建并发送通知
     */
    @PostMapping("/create-and-send")
    public Result<Notification> createAndSendNotification(@RequestBody Notification notification) {
        try {
            Notification created = notificationService.createAndSendNotification(notification);
            return created != null ? Result.success(created) : Result.error("创建并发送通知失败");
        } catch (Exception e) {
            log.error("创建并发送通知异常", e);
            return Result.error("创建并发送通知异常：" + e.getMessage());
        }
    }

    /**
     * 发送业务通知
     */
    @PostMapping("/send-business")
    public Result<Boolean> sendBusinessNotification(
            @RequestParam String businessType,
            @RequestParam(required = false) Long businessId,
            @RequestParam String templateCode,
            @RequestParam String recipient,
            @RequestBody(required = false) Map<String, Object> params) {
        try {
            boolean success = notificationService.sendBusinessNotification(businessType, businessId, templateCode, recipient, params);
            return Result.success(success);
        } catch (Exception e) {
            log.error("发送业务通知异常", e);
            return Result.error("发送业务通知异常：" + e.getMessage());
        }
    }

    /**
     * 发送系统通知
     */
    @PostMapping("/send-system")
    public Result<Boolean> sendSystemNotification(
            @RequestParam Long userId,
            @RequestParam String templateCode,
            @RequestBody(required = false) Map<String, Object> params) {
        try {
            boolean success = notificationService.sendSystemNotification(userId, templateCode, params);
            return Result.success(success);
        } catch (Exception e) {
            log.error("发送系统通知异常", e);
            return Result.error("发送系统通知异常：" + e.getMessage());
        }
    }

    /**
     * 发送通知
     */
    @PostMapping("/{id}/send")
    public Result<Boolean> sendNotification(@PathVariable Long id) {
        try {
            boolean success = notificationService.sendNotification(id);
            return Result.success(success);
        } catch (Exception e) {
            log.error("发送通知异常: {}", id, e);
            return Result.error("发送通知异常：" + e.getMessage());
        }
    }

    /**
     * 批量发送通知
     */
    @PostMapping("/batch-send")
    public Result<Integer> batchSendNotifications(@RequestBody List<Long> notificationIds) {
        try {
            int successCount = notificationService.batchSendNotifications(notificationIds);
            return Result.success(successCount);
        } catch (Exception e) {
            log.error("批量发送通知异常", e);
            return Result.error("批量发送通知异常：" + e.getMessage());
        }
    }

    /**
     * 更新通知
     */
    @PutMapping("/{id}")
    public Result<Boolean> updateNotification(
            @PathVariable Long id,
            @RequestBody Notification notification) {
        try {
            notification.setId(id);
            boolean success = notificationService.updateNotification(notification);
            return Result.success(success);
        } catch (Exception e) {
            log.error("更新通知异常: {}", id, e);
            return Result.error("更新通知异常：" + e.getMessage());
        }
    }

    /**
     * 更新通知状态
     */
    @PutMapping("/{id}/status")
    public Result<Boolean> updateNotificationStatus(
            @PathVariable Long id,
            @RequestParam Integer status,
            @RequestParam(required = false) String failureReason) {
        try {
            boolean success = notificationService.updateNotificationStatus(id, status, failureReason);
            return Result.success(success);
        } catch (Exception e) {
            log.error("更新通知状态异常: {}", id, e);
            return Result.error("更新通知状态异常：" + e.getMessage());
        }
    }

    /**
     * 批量更新通知状态
     */
    @PutMapping("/batch-status")
    public Result<Integer> batchUpdateNotificationStatus(
            @RequestParam List<Long> notificationIds,
            @RequestParam Integer status,
            @RequestParam(required = false) String failureReason) {
        try {
            int updateCount = notificationService.batchUpdateNotificationStatus(notificationIds, status, failureReason);
            return Result.success(updateCount);
        } catch (Exception e) {
            log.error("批量更新通知状态异常", e);
            return Result.error("批量更新通知状态异常：" + e.getMessage());
        }
    }

    /**
     * 标记为已读
     */
    @PutMapping("/mark-read")
    public Result<Boolean> markAsRead(
            @RequestParam List<Long> notificationIds,
            @RequestParam Long userId) {
        try {
            boolean success = notificationService.markAsRead(notificationIds, userId);
            return Result.success(success);
        } catch (Exception e) {
            log.error("标记已读异常", e);
            return Result.error("标记已读异常：" + e.getMessage());
        }
    }

    /**
     * 标记所有为已读
     */
    @PutMapping("/mark-all-read")
    public Result<Boolean> markAllAsRead(@RequestParam Long userId) {
        try {
            boolean success = notificationService.markAllAsRead(userId);
            return Result.success(success);
        } catch (Exception e) {
            log.error("标记所有已读异常", e);
            return Result.error("标记所有已读异常：" + e.getMessage());
        }
    }

    /**
     * 删除通知
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteNotification(@PathVariable Long id) {
        try {
            boolean success = notificationService.deleteNotification(id);
            return Result.success(success);
        } catch (Exception e) {
            log.error("删除通知异常: {}", id, e);
            return Result.error("删除通知异常：" + e.getMessage());
        }
    }

    /**
     * 批量删除通知
     */
    @DeleteMapping("/batch")
    public Result<Boolean> batchDeleteNotifications(@RequestBody List<Long> notificationIds) {
        try {
            boolean success = notificationService.batchDeleteNotifications(notificationIds);
            return Result.success(success);
        } catch (Exception e) {
            log.error("批量删除通知异常", e);
            return Result.error("批量删除通知异常：" + e.getMessage());
        }
    }

    /**
     * 根据ID查询通知
     */
    @GetMapping("/{id}")
    public Result<Notification> getNotificationById(@PathVariable Long id) {
        try {
            Notification notification = notificationService.getNotificationById(id);
            return notification != null ? Result.success(notification) : Result.error("通知不存在");
        } catch (Exception e) {
            log.error("查询通知异常: {}", id, e);
            return Result.error("查询通知异常：" + e.getMessage());
        }
    }

    /**
     * 根据用户ID查询通知列表
     */
    @GetMapping("/user/{userId}")
    public Result<List<Notification>> getNotificationsByUserId(
            @PathVariable Long userId,
            @RequestParam(required = false) Integer isRead) {
        try {
            List<Notification> notifications = notificationService.getNotificationsByUserId(userId, isRead);
            return Result.success(notifications);
        } catch (Exception e) {
            log.error("查询用户通知异常: {}", userId, e);
            return Result.error("查询用户通知异常：" + e.getMessage());
        }
    }

    /**
     * 根据业务信息查询通知
     */
    @GetMapping("/business")
    public Result<List<Notification>> getNotificationsByBusiness(
            @RequestParam String businessType,
            @RequestParam(required = false) Long businessId) {
        try {
            List<Notification> notifications = notificationService.getNotificationsByBusiness(businessType, businessId);
            return Result.success(notifications);
        } catch (Exception e) {
            log.error("查询业务通知异常", e);
            return Result.error("查询业务通知异常：" + e.getMessage());
        }
    }

    /**
     * 根据通知类型查询通知列表
     */
    @GetMapping("/type/{notificationType}")
    public Result<List<Notification>> getNotificationsByType(
            @PathVariable String notificationType,
            @RequestParam(required = false) Integer sendStatus) {
        try {
            List<Notification> notifications = notificationService.getNotificationsByType(notificationType, sendStatus);
            return Result.success(notifications);
        } catch (Exception e) {
            log.error("查询类型通知异常", e);
            return Result.error("查询类型通知异常：" + e.getMessage());
        }
    }

    /**
     * 查询待发送的通知
     */
    @GetMapping("/pending")
    public Result<List<Notification>> getPendingNotifications(
            @RequestParam(required = false) String notificationType,
            @RequestParam(required = false, defaultValue = "100") Integer limit) {
        try {
            List<Notification> notifications = notificationService.getPendingNotifications(notificationType, limit);
            return Result.success(notifications);
        } catch (Exception e) {
            log.error("查询待发送通知异常", e);
            return Result.error("查询待发送通知异常：" + e.getMessage());
        }
    }

    /**
     * 查询需要重试的通知
     */
    @GetMapping("/retry")
    public Result<List<Notification>> getRetryNotifications(
            @RequestParam(required = false, defaultValue = "50") Integer limit) {
        try {
            List<Notification> notifications = notificationService.getRetryNotifications(limit);
            return Result.success(notifications);
        } catch (Exception e) {
            log.error("查询重试通知异常", e);
            return Result.error("查询重试通知异常：" + e.getMessage());
        }
    }

    /**
     * 查询失败的通知
     */
    @GetMapping("/failed")
    public Result<List<Notification>> getFailedNotifications(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        try {
            List<Notification> notifications = notificationService.getFailedNotifications(startTime, endTime);
            return Result.success(notifications);
        } catch (Exception e) {
            log.error("查询失败通知异常", e);
            return Result.error("查询失败通知异常：" + e.getMessage());
        }
    }

    /**
     * 分页查询通知
     */
    @GetMapping("/page")
    public Result<IPage<Notification>> getNotificationsPage(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) String notificationType,
            @RequestParam(required = false) Integer sendStatus,
            @RequestParam(required = false) String businessType,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        try {
            Page<Notification> page = new Page<>(current, size);
            IPage<Notification> result = notificationService.getNotificationsPage(page, userId, notificationType, sendStatus, businessType, startTime, endTime);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询通知异常", e);
            return Result.error("分页查询通知异常：" + e.getMessage());
        }
    }

    /**
     * 统计通知数据
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getNotificationStatistics(
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        try {
            Map<String, Object> statistics = notificationService.getNotificationStatistics(userId, startTime, endTime);
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取通知统计异常", e);
            return Result.error("获取通知统计异常：" + e.getMessage());
        }
    }

    /**
     * 统计各状态通知数量
     */
    @GetMapping("/count-by-status")
    public Result<List<Map<String, Object>>> countNotificationsByStatus(
            @RequestParam(required = false) String notificationType) {
        try {
            List<Map<String, Object>> statistics = notificationService.countNotificationsByStatus(notificationType);
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("状态统计异常", e);
            return Result.error("状态统计异常：" + e.getMessage());
        }
    }

    /**
     * 统计各类型通知数量
     */
    @GetMapping("/count-by-type")
    public Result<List<Map<String, Object>>> countNotificationsByType(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        try {
            List<Map<String, Object>> statistics = notificationService.countNotificationsByType(startTime, endTime);
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("类型统计异常", e);
            return Result.error("类型统计异常：" + e.getMessage());
        }
    }

    /**
     * 查询用户未读通知数量
     */
    @GetMapping("/unread-count/{userId}")
    public Result<Long> countUnreadNotifications(@PathVariable Long userId) {
        try {
            Long count = notificationService.countUnreadNotifications(userId);
            return Result.success(count);
        } catch (Exception e) {
            log.error("查询未读数量异常: {}", userId, e);
            return Result.error("查询未读数量异常：" + e.getMessage());
        }
    }

    /**
     * 检查通知发送状态
     */
    @GetMapping("/{id}/check-status")
    public Result<Boolean> checkNotificationStatus(@PathVariable Long id) {
        try {
            boolean success = notificationService.checkNotificationStatus(id);
            return Result.success(success);
        } catch (Exception e) {
            log.error("检查通知状态异常: {}", id, e);
            return Result.error("检查通知状态异常：" + e.getMessage());
        }
    }

    /**
     * 获取发送统计
     */
    @GetMapping("/sending-statistics")
    public Result<Map<String, Object>> getSendingStatistics(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        try {
            Map<String, Object> statistics = notificationService.getSendingStatistics(startTime, endTime);
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取发送统计异常", e);
            return Result.error("获取发送统计异常：" + e.getMessage());
        }
    }

    /**
     * 清理过期通知
     */
    @PostMapping("/clean-expired")
    public Result<Integer> cleanExpiredNotifications(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime expireTime) {
        try {
            int cleanCount = notificationService.cleanExpiredNotifications(expireTime);
            return Result.success(cleanCount);
        } catch (Exception e) {
            log.error("清理过期通知异常", e);
            return Result.error("清理过期通知异常：" + e.getMessage());
        }
    }
} 