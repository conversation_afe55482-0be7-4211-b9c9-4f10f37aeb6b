package com.logistics.common.utils;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.security.Key;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT工具类 - 统一版本
 */
@Slf4j
@Component
public class JwtUtils {

    @Value("${jwt.secret:logistics-secret-key-2024-for-microservice-authentication-system}")
    private String secret;

    @Value("${jwt.expiration:86400}")
    private Long expiration;

    private Key key;

    @PostConstruct
    public void init() {
        // 确保密钥长度足够HS512算法使用（至少64字节）
        byte[] keyBytes = secret.getBytes();
        if (keyBytes.length < 64) {
            // 如果密钥长度不够，使用Keys.secretKeyFor生成安全密钥
            this.key = Keys.secretKeyFor(SignatureAlgorithm.HS512);
            log.info("JWT密钥长度不足，已生成安全密钥");
        } else {
            this.key = Keys.hmacShaKeyFor(keyBytes);
            log.info("JWT工具类初始化完成，密钥长度: {} bits", keyBytes.length * 8);
        }
    }

    /**
     * 生成JWT token
     */
    public String generateToken(String username) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("username", username);
        return createToken(claims, username);
    }

    /**
     * 创建token
     */
    private String createToken(Map<String, Object> claims, String subject) {
        Date now = new Date();
        Date expirationDate = new Date(now.getTime() + expiration * 1000);

        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(now)
                .setExpiration(expirationDate)
                .signWith(key, SignatureAlgorithm.HS512)
                .compact();
    }

    /**
     * 从token中获取用户名
     */
    public String getUsernameFromToken(String token) {
        return getClaimsFromToken(token).getSubject();
    }

    /**
     * 获取token过期时间
     */
    public Date getExpirationDateFromToken(String token) {
        return getClaimsFromToken(token).getExpiration();
    }

    /**
     * 验证token是否过期
     */
    public Boolean isTokenExpired(String token) {
        final Date expiration = getExpirationDateFromToken(token);
        return expiration.before(new Date());
    }

    /**
     * 验证token
     */
    public Boolean validateToken(String token) {
        try {
            return !isTokenExpired(token);
        } catch (Exception e) {
            log.error("JWT token验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 刷新token
     */
    public String refreshToken(String token) {
        try {
            final Claims claims = getClaimsFromToken(token);
            String username = claims.getSubject();
            return generateToken(username);
        } catch (Exception e) {
            log.error("JWT token刷新失败: {}", e.getMessage());
            throw new RuntimeException("Token刷新失败");
        }
    }

    /**
     * 从token中获取Claims
     */
    private Claims getClaimsFromToken(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(key)
                .build()
                .parseClaimsJws(token)
                .getBody();
    }
}