<template>
  <div class="order-list">
    <div class="page-header">
      <h2>我的订单</h2>
      <el-button type="primary" @click="$router.push('/customer/order/create')">
        <el-icon><Plus /></el-icon>
        创建订单
      </el-button>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="订单状态">
          <el-select v-model="searchForm.orderStatus" placeholder="全部状态" clearable>
            <el-option label="待发货" value="PENDING" />
            <el-option label="已揽收" value="PICKED_UP" />
            <el-option label="运输中" value="IN_TRANSIT" />
            <el-option label="派送中" value="OUT_FOR_DELIVERY" />
            <el-option label="已送达" value="DELIVERED" />
            <el-option label="已取消" value="CANCELLED" />
          </el-select>
        </el-form-item>

        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="订单号/收件人/手机号"
            style="width: 200px"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 订单列表 -->
    <div class="order-cards" v-loading="loading">
      <el-card
        v-for="order in orderList"
        :key="order.id"
        class="order-card"
        @click="viewOrderDetail(order.id)"
      >
        <div class="order-header">
          <div class="order-info">
            <span class="order-number">{{ order.orderNumber }}</span>
            <el-tag :type="getStatusTagType(order.orderStatus)">
              {{ getStatusText(order.orderStatus) }}
            </el-tag>
          </div>
          <div class="order-time">{{ formatTime(order.createTime) }}</div>
        </div>

        <div class="order-content">
          <div class="address-info">
            <div class="sender">
              <el-icon><User /></el-icon>
              <span>{{ order.senderName }} {{ order.senderPhone }}</span>
              <span class="address">{{ order.senderAddress }}</span>
            </div>
            <div class="arrow">
              <el-icon><Right /></el-icon>
            </div>
            <div class="receiver">
              <el-icon><LocationInformation /></el-icon>
              <span>{{ order.receiverName }} {{ order.receiverPhone }}</span>
              <span class="address">{{ order.receiverAddress }}</span>
            </div>
          </div>

          <div class="item-info">
            <el-icon><Box /></el-icon>
            <span>{{ order.itemName || '未填写' }}</span>
            <span class="weight" v-if="order.itemWeight">{{ order.itemWeight }}kg</span>
            <span class="service">{{ getServiceTypeText(order.serviceType) }}</span>
          </div>
        </div>

        <div class="order-footer">
          <div class="fee-info">
            <span class="total-fee">¥{{ order.totalFee.toFixed(2) }}</span>
            <span class="payment-status">{{ getPaymentStatusText(order.paymentStatus) }}</span>
          </div>

          <div class="order-actions" @click.stop>
            <el-button type="text" @click="viewOrderDetail(order.id)">详情</el-button>
            <el-button type="text" @click="trackOrder(order.orderNumber)">追踪</el-button>

            <el-button
              v-if="order.orderStatus === 'PENDING'"
              type="text"
              @click="cancelOrder(order.id)"
            >
              取消
            </el-button>

            <el-button
              v-if="order.orderStatus === 'OUT_FOR_DELIVERY'"
              type="text"
              @click="confirmDelivery(order.id)"
            >
              确认收货
            </el-button>

            <el-button
              v-if="order.paymentStatus === 0 && order.orderStatus !== 'CANCELLED'"
              type="text"
              @click="payOrder(order.id)"
            >
              去支付
            </el-button>
          </div>
        </div>
      </el-card>

      <div v-if="orderList.length === 0 && !loading" class="empty-state">
        <el-empty description="暂无订单数据">
          <el-button type="primary" @click="$router.push('/customer/order/create')">
            立即下单
          </el-button>
        </el-empty>
      </div>
    </div>

    <!-- 分页 -->
    <el-pagination
      v-if="total > 0"
      v-model:current-page="pagination.current"
      v-model:page-size="pagination.size"
      :total="total"
      :page-sizes="[10, 20, 50]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, User, LocationInformation, Box, Right } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { orderApi } from '@/api/order'
import type {
  Order,
  OrderQueryParams,
  OrderStatus,
  ServiceType,
  PaymentStatus,
} from '@/types/order'
import dayjs from 'dayjs'

const router = useRouter()

// 状态
const loading = ref(false)
const orderList = ref<Order[]>([])
const total = ref(0)

// 搜索表单
const searchForm = reactive({
  orderStatus: '',
  keyword: '',
})

// 日期范围
const dateRange = ref<[string, string] | null>(null)

// 分页
const pagination = reactive({
  current: 1,
  size: 10,
})

// 计算搜索参数
const searchParams = computed((): OrderQueryParams => {
  const params: OrderQueryParams = {
    page: pagination.current,
    size: pagination.size,
  }

  if (searchForm.orderStatus) {
    params.orderStatus = searchForm.orderStatus as OrderStatus
  }

  if (searchForm.keyword) {
    params.keyword = searchForm.keyword
  }

  if (dateRange.value) {
    params.startDate = dateRange.value[0]
    params.endDate = dateRange.value[1]
  }

  return params
})

// 加载订单列表
// 加载订单列表
const loadOrderList = async () => {
  loading.value = true
  try {
    console.log('加载订单列表，参数:', searchParams.value)
    const response = await orderApi.getOrderList(searchParams.value)
    console.log('订单列表响应:', response)

    if (response.code === 200) {
      // 修改这里：从response.data.code改为response.code
      const data = response.data
      // 检查数据结构
      if (data.records) {
        // 分页数据结构
        orderList.value = data.records
        total.value = data.total
        pagination.current = data.current
        pagination.size = data.size
      } else if (Array.isArray(data)) {
        // 简单数组结构
        orderList.value = data
        total.value = data.length
      } else {
        console.error('未知的数据结构:', data)
        orderList.value = []
        total.value = 0
      }
    } else {
      ElMessage.error(response.message || '加载订单列表失败')
    }
  } catch (error) {
    console.error('加载订单列表失败:', error)
    ElMessage.error('加载订单列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadOrderList()
}

// 重置
const handleReset = () => {
  searchForm.orderStatus = ''
  searchForm.keyword = ''
  dateRange.value = null
  pagination.current = 1
  loadOrderList()
}

// 分页变化
const handleSizeChange = () => {
  pagination.current = 1
  loadOrderList()
}

const handleCurrentChange = () => {
  loadOrderList()
}

// 查看订单详情
const viewOrderDetail = (orderId: number) => {
  router.push(`/customer/order/detail/${orderId}`)
}

// 追踪订单
const trackOrder = (orderNumber: string) => {
  router.push(`/customer/tracking/detail?orderNumber=${orderNumber}`)
}

// 取消订单
const cancelOrder = async (orderId: number) => {
  try {
    await ElMessageBox.confirm('确定要取消这个订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const response = await orderApi.cancelOrder(orderId)
    if (response.data.code === 200) {
      ElMessage.success('订单已取消')
      loadOrderList()
    } else {
      ElMessage.error(response.data.message || '取消失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消订单失败:', error)
      ElMessage.error('取消失败')
    }
  }
}

// 确认收货
const confirmDelivery = async (orderId: number) => {
  try {
    await ElMessageBox.confirm('确认已收到货物吗？', '确认收货', {
      confirmButtonText: '确认收货',
      cancelButtonText: '取消',
      type: 'info',
    })

    const response = await orderApi.confirmOrder(orderId)
    if (response.data.code === 200) {
      ElMessage.success('确认收货成功')
      loadOrderList()
    } else {
      ElMessage.error(response.data.message || '确认收货失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('确认收货失败:', error)
      ElMessage.error('确认收货失败')
    }
  }
}

// 支付订单
const payOrder = async (orderId: number) => {
  try {
    const response = await orderApi.payOrder(orderId, 'ONLINE')
    if (response.data.code === 200) {
      ElMessage.success('支付成功')
      loadOrderList()
    } else {
      ElMessage.error(response.data.message || '支付失败')
    }
  } catch (error) {
    console.error('支付失败:', error)
    ElMessage.error('支付失败')
  }
}

// 工具函数
const getStatusTagType = (status: OrderStatus) => {
  const typeMap = {
    PENDING: '',
    PICKED_UP: 'info',
    IN_TRANSIT: 'warning',
    OUT_FOR_DELIVERY: 'warning',
    DELIVERED: 'success',
    CANCELLED: 'danger',
    RETURNED: 'danger',
  }
  return typeMap[status] || ''
}

const getStatusText = (status: OrderStatus) => {
  const textMap = {
    PENDING: '待发货',
    PICKED_UP: '已揽收',
    IN_TRANSIT: '运输中',
    OUT_FOR_DELIVERY: '派送中',
    DELIVERED: '已送达',
    CANCELLED: '已取消',
    RETURNED: '已退回',
  }
  return textMap[status] || status
}

const getServiceTypeText = (serviceType: ServiceType) => {
  const textMap = {
    STANDARD: '标准快递',
    EXPRESS: '特快专递',
    URGENT: '加急服务',
  }
  return textMap[serviceType] || serviceType
}

const getPaymentStatusText = (paymentStatus: PaymentStatus) => {
  const textMap = {
    0: '未支付',
    1: '已支付',
    2: '已退款',
  }
  return textMap[paymentStatus] || '未知'
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm')
}

onMounted(() => {
  loadOrderList()
})
</script>

<style scoped>
.order-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.search-card {
  margin-bottom: 20px;
}

.order-cards {
  margin-bottom: 20px;
}

.order-card {
  margin-bottom: 15px;
  cursor: pointer;
  transition: all 0.3s;
}

.order-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.order-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.order-number {
  font-weight: bold;
  color: #333;
}

.order-time {
  color: #999;
  font-size: 14px;
}

.order-content {
  margin-bottom: 15px;
}

.address-info {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 15px;
}

.sender,
.receiver {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.sender span,
.receiver span {
  font-size: 14px;
}

.address {
  color: #666;
  font-size: 12px;
}

.arrow {
  color: #999;
}

.item-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
}

.weight,
.service {
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 10px;
  border-top: 1px solid #f0f0f0;
}

.fee-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.total-fee {
  font-size: 18px;
  font-weight: bold;
  color: #f56c6c;
}

.payment-status {
  font-size: 12px;
  color: #999;
}

.order-actions {
  display: flex;
  gap: 5px;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}
</style>
